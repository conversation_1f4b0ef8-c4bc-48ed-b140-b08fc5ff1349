import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useGetCommunityMessagesQuery,
  useGetCommunityImagesQuery,
  useSendCommunityMessageMutation,
  useUploadCommunityImageMutation,
  useGetThreadsQuery,
  useCreateThreadMutation,
  useAddReplyMutation
} from './studentCommunity.slice'; // Adjust path as needed

// Base URL for backend API (not needed for RTK Query, as baseUrl is in createApi)
const VITE_BASE_URL = import.meta.env.VITE_BASE_URL;
const WS_BASE_URL = VITE_BASE_URL.replace(/^http/, 'ws')// For WebSocket

// Utility to get token from localStorage
const getToken = () => sessionStorage.getItem('token');

// Mock login function
const mockLogin = (token) => {
  sessionStorage.getItem('token', token);
  window.location.reload();
};

const StudentCommunity = () => {
  const [token, setToken] = useState(getToken());
  const [newMessage, setNewMessage] = useState('');
  const [selectedThread, setSelectedThread] = useState(null);
  const [newReply, setNewReply] = useState('');
  const [ws, setWs] = useState(null);
  const [user, setUser] = useState(null);
  const [onlineUsers, setOnlineUsers] = useState(0);
  const [activeTab, setActiveTab] = useState('chat');
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [uploadDescription, setUploadDescription] = useState('');
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);

  // RTK Query hooks
  const { data: messages = [], refetch: refetchMessages } = useGetCommunityMessagesQuery(
    undefined,
    { skip: !token }
  );
  const { data: threads = [], refetch: refetchThreads } = useGetThreadsQuery(undefined, {
    skip: !token
  });
  const { data: images = [], refetch: refetchImages } = useGetCommunityImagesQuery(undefined, {
    skip: !token
  });
  const [sendMessage] = useSendCommunityMessageMutation();
  const [createThread] = useCreateThreadMutation();
  const [addReply] = useAddReplyMutation();
  const [uploadImage] = useUploadCommunityImageMutation();

  // Fetch user from token
  useEffect(() => {
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUser({ id: payload.user_id, role: payload.role, username: payload.username });
      } catch (e) {
        console.error('Invalid token');
      }
    }
  }, [token]);

  // WebSocket connection
  useEffect(() => {
    if (token) {
      const websocket = new WebSocket(`${WS_BASE_URL}/ws/chat?token=${token}`);
      websocket.onopen = () => console.log('WebSocket connected');
      websocket.onmessage = (event) => {
        const data = event.data;
        if (data.includes('joined') || data.includes('left')) {
          setOnlineUsers((prev) => (data.includes('joined') ? prev + 1 : Math.max(0, prev - 1)));
        } else {
          // Add message to local state immediately for instant feedback
          const [sender, content] = data.split(': ');
          const newMessage = {
            id: Date.now().toString(),
            sender_id: sender === user?.username ? user?.id : '',
            content: content,
            first_name: sender,
            created_at: new Date().toISOString()
          };
          setMessages(prev => [...prev, newMessage]);
        }
      };
      websocket.onclose = () => console.log('WebSocket disconnected');
      setWs(websocket);

      return () => websocket.close();
    }
  }, [token, user]);

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;
    try {
      await sendMessage({ content: newMessage }).unwrap();
      setNewMessage('');
      refetchMessages();
      refetchThreads();
    } catch (e) {
      console.error('Failed to send message');
    }
  };

  const handleCreateThread = async () => {
    if (!newMessage.trim()) return;
    try {
      await createThread({ content: newMessage }).unwrap();
      setNewMessage('');
      refetchThreads();
    } catch (e) {
      console.error('Failed to create thread');
    }
  };

  const handleAddReply = async (parentId) => {
    if (!newReply.trim()) return;
    try {
      await addReply({ content: newReply, parent_id: parentId }).unwrap();
      setNewReply('');
      refetchThreads();
      if (selectedThread) {
        setSelectedThread(threads.find((t) => t.id === selectedThread.id));
      }
    } catch (e) {
      console.error('Failed to add reply');
    }
  };

  const handleUploadImage = async (file, description) => {
    const formData = new FormData();
    formData.append('file', file);
    if (description) formData.append('description', description);
    try {
      await uploadImage(formData).unwrap();
      refetchImages();
      setShowUploadDialog(false);
      setUploadDescription('');
    } catch (e) {
      console.error('Failed to upload image');
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (!token) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white/80 backdrop-blur-md shadow-xl rounded-xl p-6 w-full max-w-md">
          <h2 className="text-2xl font-bold mb-4 text-center">Enter Token to Access Community</h2>
          <input
            className="w-full p-2 border border-gray-300 rounded-md mb-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="Paste your JWT token here"
            onChange={(e) => setToken(e.target.value)}
          />
          <button
            className="w-full bg-indigo-600 text-white py-2 rounded-md hover:bg-indigo-700 transition-colors"
            onClick={() => mockLogin(token)}>
            Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-md p-4 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-indigo-600">Community Channel</h1>
        <div className="flex items-center space-x-4">
          <div className="relative group">
            <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full cursor-help">
              Online: {onlineUsers}
            </div>
            <div className="absolute hidden group-hover:block bg-black text-white text-xs rounded py-1 px-2 -bottom-8 left-1/2 -translate-x-1/2">
              Active users
            </div>
          </div>
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-lg font-bold">
            {user?.username?.[0]}
          </div>
          <button
            className="border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-100 transition-colors"
            onClick={() => {
              localStorage.removeItem('token');
              window.location.reload();
            }}>
            Logout
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 p-4">
        <div className="space-y-4">
          <div className="bg-white rounded-lg shadow-md flex">
            <button
              className={`flex-1 py-2 ${activeTab === 'chat' ? 'bg-indigo-600 text-white' : 'bg-white text-indigo-600'} rounded-tl-lg font-semibold`}
              onClick={() => setActiveTab('chat')}>
              Chat
            </button>
            <button
              className={`flex-1 py-2 ${activeTab === 'threads' ? 'bg-indigo-600 text-white' : 'bg-white text-indigo-600'} font-semibold`}
              onClick={() => setActiveTab('threads')}>
              Discussions
            </button>
            <button
              className={`flex-1 py-2 ${activeTab === 'gallery' ? 'bg-indigo-600 text-white' : 'bg-white text-indigo-600'} rounded-tr-lg font-semibold`}
              onClick={() => setActiveTab('gallery')}>
              Gallery
            </button>
          </div>

          {/* Chat Tab */}
          {activeTab === 'chat' && (
            <div className="flex flex-col h-[calc(100vh-12rem)] bg-white/80 backdrop-blur-md shadow-xl rounded-xl overflow-hidden">
              <div className="p-4 border-b flex items-center space-x-4">
                <h2 className="text-xl font-bold flex-1">Live Chat</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>{onlineUsers} online</span>
                </div>
              </div>
              <div className="flex-1 overflow-y-auto p-4 space-y-4" style={{backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%239C92AC\' fill-opacity=\'0.05\' fill-rule=\'evenodd\'%3E%3Ccircle cx=\'3\' cy=\'3\' r=\'3\'/%3E%3Ccircle cx=\'13\' cy=\'13\' r=\'3\'/%3E%3C/g%3E%3C/svg%3E")'}}>
                <AnimatePresence>
                  {messages.map((msg) => (
                    <motion.div
                      key={msg.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className={`flex ${msg.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-[70%] ${msg.sender_id === user?.id ? 'bg-indigo-500 text-white' : 'bg-white'} rounded-2xl px-4 py-2 shadow-md`}>
                        <div className="font-medium text-sm mb-1">{msg.first_name || 'Anonymous'}</div>
                        <div className="break-words">{msg.content}</div>
                        <div className="text-xs mt-1 opacity-75">
                          {new Date(msg.created_at).toLocaleTimeString()}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                <div ref={messagesEndRef} />
              </div>
              <div className="p-4 border-t bg-white">
                <div className="flex space-x-2">
                  <input
                    className="flex-1 p-3 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  />
                  <button
                    className="bg-indigo-500 text-white p-3 rounded-full hover:bg-indigo-600 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    onClick={handleSendMessage}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Threads Tab */}
          {activeTab === 'threads' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white/80 backdrop-blur-md shadow-xl rounded-xl">
                <div className="p-4 border-b">
                  <h2 className="text-xl font-bold">Threads</h2>
                </div>
                <div className="p-4">
                  <div className="h-[500px] overflow-y-auto">
                    {threads.map((thread) => (
                      <motion.div
                        key={thread.id}
                        whileHover={{ scale: 1.02 }}
                        className="mb-4 p-3 border-b cursor-pointer"
                        onClick={() => setSelectedThread(thread)}>
                        <div className="font-semibold">{thread.first_name || 'Anonymous'}</div>
                        <div>{thread.content}</div>
                        <div className="text-xs text-gray-500">
                          Replies: {thread.subthreads.length}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  <div className="mt-4 flex space-x-2">
                    <input
                      className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      placeholder="Start a new thread..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleCreateThread()}
                    />
                    <button
                      className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                      onClick={handleCreateThread}>
                      Send
                    </button>
                  </div>
                </div>
              </div>

              {selectedThread && (
                <div className="bg-white/80 backdrop-blur-md shadow-xl rounded-xl">
                  <div className="p-4 border-b">
                    <h2 className="text-xl font-bold">
                      Thread: {selectedThread.content.slice(0, 50)}...
                    </h2>
                  </div>
                  <div className="p-4">
                    <div className="h-[400px] overflow-y-auto mb-4">
                      <div className="p-3 bg-gray-100 rounded-lg mb-4">
                        <div className="font-semibold">
                          {selectedThread.first_name || 'Anonymous'}
                        </div>
                        <div>{selectedThread.content}</div>
                      </div>
                      <AnimatePresence>
                        {selectedThread.subthreads.map((sub) => (
                          <motion.div
                            key={sub.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="ml-6 mb-3 p-2 border-l-2 border-indigo-300">
                            <div className="font-semibold">{sub.first_name || 'Anonymous'}</div>
                            <div>{sub.content}</div>
                          </motion.div>
                        ))}
                      </AnimatePresence>
                    </div>
                    <div className="flex space-x-2">
                      <input
                        className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        placeholder="Reply..."
                        value={newReply}
                        onChange={(e) => setNewReply(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAddReply(selectedThread.id)}
                      />
                      <button
                        className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                        onClick={() => handleAddReply(selectedThread.id)}>
                        Reply
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Gallery Tab */}
          {activeTab === 'gallery' && (
            <div className="bg-white/80 backdrop-blur-md shadow-xl rounded-xl">
              <div className="p-4 border-b flex justify-between items-center">
                <h2 className="text-xl font-bold">Image Gallery</h2>
                <button
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                  onClick={() => setShowUploadDialog(true)}>
                  Upload Image
                </button>
              </div>
              <div className="p-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  <AnimatePresence>
                    {images.map((img) => (
                      <motion.div
                        key={img.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0 }}
                        className="relative overflow-hidden rounded-lg shadow-md">
                        <img
                          src={img.cloudfront_url}
                          alt={img.description}
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2">
                          <div className="font-semibold">{img.first_name || 'Anonymous'}</div>
                          <div className="text-sm">{img.description}</div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </div>

              {showUploadDialog && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                  <div className="bg-white rounded-xl p-6 w-full max-w-md">
                    <h2 className="text-xl font-bold mb-4">Upload Community Image</h2>
                    <div className="space-y-4">
                      <label className="block text-sm font-medium">Choose Image</label>
                      <input
                        type="file"
                        ref={fileInputRef}
                        accept="image/*"
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                      <label className="block text-sm font-medium">Description (optional)</label>
                      <textarea
                        className="w-full p-2 border border-gray-300 rounded-md"
                        placeholder="Describe the image..."
                        value={uploadDescription}
                        onChange={(e) => setUploadDescription(e.target.value)}
                      />
                      <div className="flex space-x-2">
                        <button
                          className="flex-1 bg-gray-300 text-black py-2 rounded-md hover:bg-gray-400 transition-colors"
                          onClick={() => setShowUploadDialog(false)}>
                          Cancel
                        </button>
                        <button
                          className="flex-1 bg-indigo-600 text-white py-2 rounded-md hover:bg-indigo-700 transition-colors"
                          onClick={() => {
                            const file = fileInputRef.current.files[0];
                            if (file) handleUploadImage(file, uploadDescription);
                          }}>
                          Upload
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default StudentCommunity;
