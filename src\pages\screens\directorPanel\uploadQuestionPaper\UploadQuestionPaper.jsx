import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useUploadQuestionPaperMutation } from './uploadQuestionPaper.slice'; // Adjust the import path as needed

const UploadQuestionPaper = () => {
  const [selectedExam, setSelectedExam] = useState('neet');
  const [year, setYear] = useState('');
  const [file, setFile] = useState(null);
  const [uploadStatus, setUploadStatus] = useState(null);
  const [yearError, setYearError] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const [uploadQuestionPaper, { isLoading }] = useUploadQuestionPaperMutation();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2,
        ease: 'easeOut'
      }
    }
  };

  const childVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.5, type: 'spring', stiffness: 120 } }
  };

  const parallaxVariants = {
    hidden: { y: 0 },
    visible: {
      y: [0, -10, 0],
      transition: {
        duration: 10,
        repeat: Infinity,
        repeatType: 'reverse',
        ease: 'easeInOut'
      }
    }
  };

  // Loading overlay animations
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
    exit: { opacity: 0, transition: { duration: 0.5 } }
  };

  const loadingContainerVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    },
    exit: {
      opacity: 0,
      scale: 1.2,
      transition: { duration: 0.5 }
    }
  };

  const loadingDotVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        repeat: Infinity,
        repeatType: 'reverse',
        ease: 'easeInOut'
      }
    }
  };

  const documentVariants = {
    hidden: { opacity: 0, y: 50, rotate: -10 },
    visible: {
      opacity: 1,
      y: 0,
      rotate: 0,
      transition: {
        duration: 0.8,
        type: 'spring',
        stiffness: 100
      }
    }
  };

  const progressBarVariants = {
    hidden: { width: 0 },
    visible: {
      width: '100%',
      transition: {
        duration: 3,
        ease: 'easeInOut',
        repeat: Infinity,
        repeatType: 'loop'
      }
    }
  };

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const handleYearChange = (e) => {
    const value = e.target.value;

    // Only allow numbers
    if (!/^\d*$/.test(value)) {
      setYearError('Only numbers are allowed');
      return;
    }

    // Limit to 4 digits
    if (value.length > 4) return;

    setYear(value);
    setYearError('');

    // Validate if we have 4 digits
    if (value.length === 4) {
      const currentYear = new Date().getFullYear();
      const minYear = 2013; // Reasonable minimum year

      if (parseInt(value) < minYear || parseInt(value) > currentYear) {
        setYearError(`Year must be between ${minYear} and ${currentYear}`);
      } else {
        setYearError('');
      }
    }
  };

  const handleUpload = async () => {
    if (!file || !year || !selectedExam) {
      setUploadStatus('Please select exam type, year, and upload a PDF file.');
      return;
    }

    if (!/^\d{4}$/.test(year)) {
      setUploadStatus('Year must be a 4-digit number.');
      return;
    }

    if (!file.name.endsWith('.pdf')) {
      setUploadStatus('File must be a PDF.');
      return;
    }

    setIsUploading(true);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('exam_type', selectedExam.toLowerCase());
    formData.append('year', year);

    try {
      const result = await uploadQuestionPaper(formData).unwrap();
      setUploadStatus(`Upload successful! Processed ${result.questions?.length || 0} questions.`);
    } catch (error) {
      setUploadStatus(`Upload failed: ${error.message || 'Unknown error'}`);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4 md:p-8 relative overflow-hidden">
      {/* Animated background elements */}
      <motion.div
        className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,_rgba(95,92,250,0.2),_rgba(255,255,255,0))]"
        variants={parallaxVariants}
        initial="hidden"
        animate="visible"
      />

      {/* Floating particles */}
      {[...Array(15)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 bg-indigo-300 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`
          }}
          animate={{
            y: [0, -20, 0],
            x: [0, Math.random() * 10 - 5, 0],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{
            duration: 3 + Math.random() * 4,
            repeat: Infinity,
            delay: Math.random() * 2
          }}
        />
      ))}

      {/* Full-page loading overlay */}
      <AnimatePresence>
        {isUploading && (
          <motion.div
            className="fixed inset-0 bg-[var(--color-director)] backdrop-blur-md z-50 flex flex-col items-center justify-center p-8"

            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit">
            <motion.div
              className="text-center mb-12"
              variants={loadingContainerVariants}
              initial="hidden"
              animate="visible">
              {/* Animated document icon */}
              <motion.div className="relative mb-8 mx-auto w-24 h-24" variants={documentVariants}>
                <div className="absolute inset-0 bg-white rounded-lg shadow-2xl flex items-center justify-center">
                  <svg
                    className="w-12 h-12 text-indigo-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      fillRule="evenodd"
                      d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>

                {/* Floating pages animation */}
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="absolute w-20 h-24 bg-white/80 rounded-lg -z-10"
                    initial={{ y: 0, rotate: 0, opacity: 0 }}
                    animate={{
                      y: [0, -20, 0],
                      rotate: [0, -5, 0],
                      opacity: [0, 0.7, 0]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.3,
                      ease: 'easeInOut'
                    }}
                    style={{
                      left: `${5 + i * 2}px`,
                      top: `${5 + i * 2}px`
                    }}
                  />
                ))}
              </motion.div>

              <motion.h2
                className="text-3xl font-bold text-white mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}>
                Processing Your Question Paper
              </motion.h2>

              <motion.p
                className="text-blue-100 text-lg mb-8 max-w-md mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}>
                We're extracting questions and analyzing content. This may take a moment...
              </motion.p>

              {/* Animated progress bar */}
              <div className="h-2 bg-blue-500/30 rounded-full overflow-hidden max-w-md mx-auto mb-8">
                <motion.div
                  className="h-full bg-white rounded-full"
                  variants={progressBarVariants}
                  initial="hidden"
                  animate="visible"
                />
              </div>

              {/* Animated dots */}
              <div className="flex justify-center space-x-2">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="w-3 h-3 bg-white rounded-full"
                    variants={loadingDotVariants}
                    transition={{ delay: i * 0.2 }}
                  />
                ))}
              </div>
            </motion.div>

            {/* Floating elements */}
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-4 h-4 bg-white rounded-full opacity-20"
                style={{
                  left: `${10 + Math.random() * 80}%`,
                  top: `${10 + Math.random() * 80}%`
                }}
                animate={{
                  y: [0, -30, 0],
                  scale: [1, 1.5, 1],
                  opacity: [0.2, 0.5, 0.2]
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 1
                }}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        className="w-full max-w-4xl bg-white/80 backdrop-blur-md rounded-3xl shadow-2xl overflow-hidden border border-white/50 z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible">
        {/* Header */}
        <motion.header
          className="p-6 md:p-8 bg-[var(--color-director)] text-white rounded-t-3xl shadow-inner relative overflow-hidden"
          variants={childVariants}>
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>

          <h1 className="text-3xl md:text-4xl font-bold tracking-tight relative z-10">
            <span className="inline-block mr-2">📄</span> Upload Question Paper
          </h1>
          <p className="mt-2 text-lg opacity-90 relative z-10">
            Upload previous year question papers for processing and storage.
          </p>
        </motion.header>

        {/* Main Content */}
        <motion.main className="p-6 md:p-8 flex flex-col gap-8" variants={childVariants}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Exam Selection */}
            <motion.div
              className="bg-white/40 backdrop-blur-lg p-6 rounded-2xl shadow-md border border-white/30 relative overflow-hidden"
              variants={childVariants}
              whileHover={{ scale: 1.02, rotate: 0.5, boxShadow: '0 10px 20px rgba(0,0,0,0.1)' }}
              transition={{ type: 'spring', stiffness: 300 }}>
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-indigo-100 rounded-full"></div>
              <h2 className="text-xl font-semibold mb-4 text-indigo-800 flex items-center">
                <span className="inline-block mr-2">📝</span> Select Exam Type
              </h2>
              <select
                value={selectedExam}
                onChange={(e) => setSelectedExam(e.target.value)}
                className="w-full p-3 bg-white/80 border border-indigo-200 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none transition-all hover:shadow-md">
                <option value="neet">NEET</option>
                <option value="jee_mains">JEE Mains</option>
                <option value="jee_advanced">JEE Advanced</option>
              </select>
            </motion.div>

            {/* Year Input */}
            <motion.div
              className="bg-white/40 backdrop-blur-lg p-6 rounded-2xl shadow-md border border-white/30 relative overflow-hidden"
              variants={childVariants}
              whileHover={{ scale: 1.02, rotate: -0.5, boxShadow: '0 10px 20px rgba(0,0,0,0.1)' }}
              transition={{ type: 'spring', stiffness: 300 }}>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-blue-100 rounded-full"></div>
              <h2 className="text-xl font-semibold mb-4 text-indigo-800 flex items-center">
                <span className="inline-block mr-2">📅</span> Enter Year
              </h2>
              <div className="relative">
                <input
                  type="text"
                  value={year}
                  onChange={handleYearChange}
                  placeholder="e.g., 2023"
                  maxLength="4"
                  className="w-full p-3 bg-white/80 border border-indigo-200 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none transition-all hover:shadow-md pr-12"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center">
                  <span className="text-gray-400 text-sm bg-gray-100 px-2 py-1 rounded">
                    {year.length}/4
                  </span>
                </div>
              </div>
              {yearError && (
                <motion.p
                  className="text-red-500 text-sm mt-2 flex items-center"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}>
                  <span className="inline-block mr-1">⚠️</span> {yearError}
                </motion.p>
              )}
              {!yearError && year.length === 4 && (
                <motion.p
                  className="text-green-500 text-sm mt-2 flex items-center"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}>
                  <span className="inline-block mr-1">✅</span> Valid year format
                </motion.p>
              )}
            </motion.div>
          </div>

          {/* File Upload Section */}
          <motion.div
            className="bg-white/40 backdrop-blur-lg p-6 rounded-2xl shadow-md border border-white/30 relative overflow-hidden"
            variants={childVariants}
            whileHover={{ scale: 1.01, boxShadow: '0 10px 20px rgba(0,0,0,0.1)' }}>
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-indigo-600"></div>
            <h2 className="text-xl font-semibold mb-4 text-indigo-800 flex items-center">
              <span className="inline-block mr-2">📂</span> Upload PDF File
            </h2>
            <div className="border-2 border-dashed border-indigo-200 rounded-lg p-6 text-center transition-all hover:border-indigo-400 hover:bg-indigo-50/50">
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileChange}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                id="file-upload"
              />
              <div className="flex flex-col items-center justify-center">
                <svg
                  className="w-12 h-12 text-indigo-400 mb-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <p className="text-gray-600">
                  {file ? file.name : 'Drag & drop or click to select a PDF file'}
                </p>
                <p className="text-xs text-gray-500 mt-2">Only PDF files are accepted</p>
              </div>
            </div>
          </motion.div>

          {/* Upload Status Display */}
          {uploadStatus && (
            <motion.div
              className={`p-4 rounded-lg text-center ${uploadStatus.includes('successful') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}>
              {uploadStatus.includes('successful') ? '✅ ' : '❌ '}
              {uploadStatus}
            </motion.div>
          )}
        </motion.main>

        {/* Footer */}
        <motion.footer
          className="p-6 bg-white/50 border-t border-white/30 flex justify-end"
          variants={childVariants}>
          <button
            onClick={handleUpload}
            disabled={isLoading || !file || !year || yearError || year.length !== 4}
            className="relative overflow-hidden bg-[var(--color-director)] text-white rounded-full px-6 py-3 shadow-md transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 group">
            {isLoading ? (
              <>
                <svg
                  className="animate-spin h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Preparing...
              </>
            ) : (
              <>
                <span className="relative z-10">Upload Paper</span>
                <span className="absolute inset-0 bg-[var(--color-director)] opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></span>
              </>
            )}
          </button>
        </motion.footer>
      </motion.div>
    </div>
  );
};

export default UploadQuestionPaper;
