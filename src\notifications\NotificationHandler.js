import React, { useEffect } from 'react';
import { useLocation } from 'react-router';
import { requestNotificationPermission, onMessageListener } from './pushNotifications';

const NotificationHandler = () => {
  const location = useLocation();
  const isOnSasthraPage = location.pathname === '/sasthra';

  useEffect(() => {
    if (isOnSasthraPage) {
      const handleNotificationSetup = async () => {
        try {
          const token = await requestNotificationPermission();
          if (token) {
            // Get user details from sessionStorage, handle undefined as null
            const getUserDetail = (key) => {
              const value = sessionStorage.getItem(key);
              return value === 'undefined' || !value ? null : value;
            };

            const userDetails = {
              token,
              userId: getUserDetail('userId'),
              name: getUserDetail('name'),
              phone: getUserDetail('phone'),
              role: getUserDetail('role'),
              designation: getUserDetail('designation'),
              centername: getUserDetail('centername'),
              centercode: getUserDetail('centercode'),
              course: getUserDetail('course'),
              batch: getUserDetail('batch'),
              batchname: getUserDetail('batchname')
            };

            const response = await fetch('https://sasthra.in/api/save-token', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(userDetails)
            });

            const data = await response.json();
            console.log('User subscribed to notifications:', data);
          }
        } catch (error) {
          console.error('Error setting up notifications:', error);
        }
      };

      handleNotificationSetup();
    }

    // Listen for foreground notifications
    onMessageListener().then((payload) => {
      alert(`Notification: ${payload.notification.title} - ${payload.notification.body}`);
    });
  }, [isOnSasthraPage]);

  return null;
};

export default NotificationHandler;
