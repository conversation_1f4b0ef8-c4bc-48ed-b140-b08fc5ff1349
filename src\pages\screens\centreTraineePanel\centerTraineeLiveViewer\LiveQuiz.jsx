import React, { useEffect, useRef, useState, useCallback } from 'react';
import io from 'socket.io-client';
import {
  X,
  BarChart3,
  Video,
  VideoOff,
  Wifi,
  WifiOff,
  Clock,
  Users,
  Target,
  Trophy,
  Medal,
  Award,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useStartQuizMutation, useNextQuestionMutation } from './centerTraineeLive.slice';
import applauseAudio from '../../../../assets/audio/applause-180037.mp3';
import clockTickingAudio from '../../../../assets/audio/clock-ticking-down-376897.mp3';

// API Configuration
const CAPTURE_INTERVAL_MS = 200;
const DURATION_PER_OPTION_S = 10;
const QUESTION_READING_TIME_S = 15;

// Enhanced Circular Option Display Component with Animated Countdown
const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
  const circumference = 2 * Math.PI * 120;
  const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - progress;
  const getTimerColor = () => {
    const percentage = timeRemaining / totalTime;
    if (percentage > 0.6) return '#10B981';
    if (percentage > 0.3) return '#F59E0B';
    return '#EF4444';
  };
  const getScale = () => {
    const percentage = timeRemaining / totalTime;
    return 1 + (1 - percentage) * 0.2;
  };
  if (!isActive) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />
      <div className="absolute inset-0">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
            style={{
              top: `${10 + i * 7}%`,
              left: `${5 + i * 8}%`,
              animationDelay: `${i * 0.3}s`,
              animationDuration: `${3 + (i % 3)}s`
            }}
          />
        ))}
      </div>
      <div
        className="relative transition-all duration-300 ease-out"
        style={{ transform: `scale(${getScale()})` }}
      >
        <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />
        <div className="relative w-64 h-64 flex items-center justify-center">
          <svg
            className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
            viewBox="0 0 260 260"
          >
            <circle
              cx="130"
              cy="130"
              r="120"
              fill="none"
              stroke="rgba(255, 255, 255, 0.3)"
              strokeWidth="12"
              className="drop-shadow-lg"
            />
            <circle
              cx="130"
              cy="130"
              r="120"
              fill="none"
              stroke={getTimerColor()}
              strokeWidth="12"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className="transition-all duration-1000 ease-out drop-shadow-xl"
              style={{
                filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
              }}
            />
            <circle
              cx="130"
              cy="130"
              r="100"
              fill="none"
              stroke="rgba(255, 255, 255, 0.2)"
              strokeWidth="2"
              strokeDasharray="5,5"
              className="animate-spin-reverse"
            />
            <defs>
              <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" />
                <stop offset="50%" stopColor="#8B5CF6" />
                <stop offset="100%" stopColor="#EC4899" />
              </linearGradient>
              <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor="#FFFFFF" />
                <stop offset="100%" stopColor="#F8FAFC" />
              </radialGradient>
            </defs>
          </svg>
          <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
            <div
              className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
              style={{
                transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
                textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
              }}
            >
              {option}
            </div>
            <div
              className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
              style={{
                transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
              }}
            >
              {timeRemaining}s
            </div>
            <div className="flex gap-1 mt-2">
              {[...Array(totalTime)].map((_, i) => (
                <div
                  key={i}
                  className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                    i < totalTime - timeRemaining
                      ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
                      : 'bg-gray-300 scale-100'
                  }`}
                />
              ))}
            </div>
          </div>
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
                style={{
                  top: `${15 + i * 10}%`,
                  left: `${10 + i * 12}%`,
                  animationDelay: `${i * 0.3}s`,
                  animationDuration: `${2 + (i % 2)}s`
                }}
              />
            ))}
          </div>
          <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
        </div>
      </div>
    </div>
  );
};

// Enhanced Reading Timer Component
const ReadingTimer = ({ timeRemaining, isActive }) => {
  if (!isActive) return null;
  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
      <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
        <div className="text-center">
          <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
            <Clock className="animate-spin" size={32} />
            Reading Time
          </div>
          <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
          <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
        </div>
      </div>
    </div>
  );
};

// Enhanced Hand Raise Animation Component
const HandRaiseNotification = ({ logs }) => {
  const [showNotification, setShowNotification] = useState(false);
  const [latestLog, setLatestLog] = useState(null);
  useEffect(() => {
    if (logs.length > 0) {
      const newest = logs[logs.length - 1];
      setLatestLog(newest);
      setShowNotification(true);
      const timer = setTimeout(() => {
        setShowNotification(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [logs]);
  if (!showNotification || !latestLog) return null;
  return (
    <div className="fixed top-24 right-8 z-50 transform animate-slideInRight">
      <div className="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 text-white px-8 py-6 rounded-2xl shadow-2xl border-4 border-white/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-300">
        <div className="flex items-center gap-4">
          <div className="text-4xl animate-bounce">🙋‍♂️</div>
          <div>
            <div className="font-bold text-xl drop-shadow-sm">{latestLog.student_name}</div>
            <div className="text-sm opacity-90 bg-white/20 px-3 py-1 rounded-full mt-1">
              Option {latestLog.option ? latestLog.option.toUpperCase() : 'N/A'} • {latestLog.responseTime}s
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const LiveQuiz = ({ quizId: inputQuizId, sessionId: inputSessionId, onClose }) => {
  const [quizId, setQuizId] = useState(inputQuizId);
  const [sessionId, setSessionId] = useState(inputSessionId || '');
  const [clientId, setClientId] = useState('');
  const [centerCode, setCenterCode] = useState(null);
  const [questionData, setQuestionData] = useState(null);
  const [quizStatus, setQuizStatus] = useState('');
  const [summary, setSummary] = useState(null);
  const [finalResults, setFinalResults] = useState(null);
  const [liveFeedLogs, setLiveFeedLogs] = useState([]);
  const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
  const [socketStatus, setSocketStatus] = useState('Disconnected');
  const [error, setError] = useState(null);
  const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
  const [currentOption, setCurrentOption] = useState('');
  const [questionTimer, setQuestionTimer] = useState(null);
  const [isReadingQuestion, setIsReadingQuestion] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);
  const [cameraStatus, setCameraStatus] = useState('Not initialized');
  const [showManualInput, setShowManualInput] = useState(!inputQuizId);
  const [manualQuizId, setManualQuizId] = useState('');
  const [manualSessionId, setManualSessionId] = useState('');
  const [startQuizMutation, { isLoading: isStartingQuiz }] = useStartQuizMutation();
  const [nextQuestionMutation, { isLoading: isFetchingNextQuestion }] = useNextQuestionMutation();
  const [dashboardData, setDashboardData] = useState(null);
  const [isDashboardLoading, setIsDashboardLoading] = useState(false);
  
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const socketRef = useRef(null);
  const videoStreamRef = useRef(null);
  const timerIntervalRef = useRef(null);
  const applauseAudioRef = useRef(null);
  const clockTickingAudioRef = useRef(null);
  
  // Track if socket has been initialized
  const [isSocketInitialized, setIsSocketInitialized] = useState(false);
  
  // Get center code from session storage
  useEffect(() => {
    const storedCenterCode = sessionStorage.getItem('centercode');
    if (storedCenterCode && !centerCode) {
      setCenterCode(storedCenterCode);
      console.log(`🔒 Center code retrieved from session storage: ${storedCenterCode}`);
    }
  }, [centerCode]);
  
  useEffect(() => {
    if (quizId && videoRef.current && !isSocketInitialized) {
      console.log('🎯 QuizId changed, initializing camera:', quizId);
      initCamera();
    }
  }, [quizId, isSocketInitialized]);
  
  // Cleanup function - now only disconnects socket when component unmounts
  useEffect(() => {
    return () => {
      stopCamera();
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      if (applauseAudioRef.current) {
        applauseAudioRef.current.pause();
        applauseAudioRef.current.currentTime = 0;
      }
      if (clockTickingAudioRef.current) {
        clockTickingAudioRef.current.pause();
        clockTickingAudioRef.current.currentTime = 0;
      }
    };
  }, []);
  
  // Initialize socket connection ONCE when component mounts
  useEffect(() => {
    const initSocket = () => {
      if (socketRef.current || isSocketInitialized) return;
      
      // Dynamically construct the Socket.IO URL based on the current environment
      let socketUrl;
      if (process.env.NODE_ENV === 'development') {
        socketUrl = `${window.location.protocol}//${window.location.hostname}:8036`;
      } else {
        socketUrl = `${window.location.protocol}//${window.location.hostname}`;
      }
      console.log(`🔌 Initializing socket connection to: ${socketUrl}`);
      
      socketRef.current = io(socketUrl, {
        path: '/socketio2/socket.io',
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true,
        timeout: 20000,
        forceNew: false, // Critical change - don't force new connection
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 10, // Increased attempts
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        randomizationFactor: 0.5
      });
      
      // Set up socket event listeners
      socketRef.current.on('connect', () => {
        setSocketStatus('Connected');
        console.log(`✅ Connected to Socket.IO server at ${socketUrl}`);
      });
      
      socketRef.current.on('disconnect', (reason) => {
        setSocketStatus(`Disconnected: ${reason}`);
        console.log(`❌ Socket disconnected: ${reason}`);
        
        // Only try to reconnect if it wasn't intentional
        if (reason !== 'io client disconnect') {
          setTimeout(() => {
            if (socketRef.current && !socketRef.current.connected) {
              console.log('🔄 Attempting to reconnect...');
              socketRef.current.connect();
            }
          }, 2000);
        }
      });
      
      socketRef.current.on('connect_error', (error) => {
        setSocketStatus(`Connection Error: ${error.message}`);
        console.error('❌ Socket.IO connection error:', error);
      });
      
      socketRef.current.on('reconnect', (attemptNumber) => {
        setSocketStatus(`Reconnected (attempt ${attemptNumber})`);
        console.log(`🔄 Reconnected to Socket.IO server. Attempt: ${attemptNumber}`);
        
        // If we have session info, rejoin the room
        if (sessionId && clientId && centerCode) {
          console.log(`🚪 Rejoining room with session=${sessionId}, client=${clientId}, center=${centerCode}`);
          socketRef.current.emit('join_quiz_room', { 
            session_id: sessionId, 
            client_id: clientId,
            center_code: centerCode
          });
        }
      });
      
      socketRef.current.on('reconnect_attempt', (attempt) => {
        setSocketStatus(`Reconnecting... (attempt ${attempt})`);
        console.log(`🔄 Reconnection attempt #${attempt}`);
      });
      
      socketRef.current.on('reconnect_error', (error) => {
        console.error('❌ Socket.IO reconnection error:', error);
      });
      
      socketRef.current.on('reconnect_failed', () => {
        setSocketStatus('Reconnection Failed');
        console.error('❌ Socket.IO reconnection failed');
      });
      
      socketRef.current.on('hand_raised', (data) => {
        console.log('🙋 Hand raised event received:', data);
        setLiveFeedLogs((logs) => {
          const newLog = {
            ...data,
            responseTime: currentQuestionStartTime
              ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000)
              : 0
          };
          return [...logs, newLog];
        });
      });
      
      socketRef.current.on('center_identified', (data) => {
        if (data.center_code && !centerCode) {
          setCenterCode(data.center_code);
          console.log(`🔒 This client is now locked to Center: ${data.center_code}`);
        }
      });
      
      socketRef.current.onAny((eventName, ...args) => {
        console.log(`📡 Socket event received: ${eventName}`, args);
      });
      
      setIsSocketInitialized(true);
      return socketRef.current;
    };
    
    const socket = initSocket();
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, []);
  
  const generateUUID = () => crypto.randomUUID();
  
  const startQuiz = async () => {
    if (!quizId) {
      setError('Missing Quiz ID');
      return;
    }
    // Get center code from session storage
    const storedCenterCode = sessionStorage.getItem('centercode');
    if (!storedCenterCode) {
      setError('Center code not found. Please log in again.');
      return;
    }
    let currentSessionId = sessionId;
    if (!currentSessionId) {
      currentSessionId = generateUUID();
      setSessionId(currentSessionId);
    }
    const currentClientId = generateUUID();
    setClientId(currentClientId);
    
    try {
      setQuizStatus('Starting quiz...');
      
      // First, join the socket room
      await new Promise((resolve, reject) => {
        if (!socketRef.current) {
          reject('Socket not initialized');
          return;
        }
        socketRef.current.emit('join_quiz_room', { 
          session_id: currentSessionId, 
          client_id: currentClientId,
          center_code: storedCenterCode
        }, (ack) => {
          if (ack && ack.success) {
            console.log('✅ Successfully joined quiz room');
            resolve();
          } else {
            console.error('❌ Failed to join quiz room:', ack ? ack.error : 'No acknowledgment');
            reject(ack ? ack.error : 'No acknowledgment received');
          }
        });
      });
      
      // Then, start the quiz via API
      const response = await startQuizMutation({ 
        quiz_id: quizId, 
        session_id: currentSessionId, 
        client_id: currentClientId,
        center_code: storedCenterCode
      }).unwrap();
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      setQuestionData(response);
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);
      console.log('🎯 Starting quiz, initializing camera immediately');
      await initCamera();
      initializeAudio();
    } catch (error) {
      setError(`Error starting quiz: ${error.message || error.data?.error || 'Unknown error'}`);
    }
  };
  
  const handleNextQuestion = async () => {
    if (!sessionId || !clientId || !centerCode) return;
    try {
      await startCaptureCycle();
      setQuizStatus('Processing results and fetching next question...');
      // Include center code in the request
      const response = await nextQuestionMutation({ 
        session_id: sessionId, 
        client_id: clientId,
        center_code: centerCode
      }).unwrap();
      if (response.status === 'completed') {
        setFinalResults(response);
        stopCamera();
        if (socketRef.current) {
          socketRef.current.disconnect();
          socketRef.current = null;
        }
        setQuizStatus('Quiz completed! View results for details.');
        if (applauseAudioRef.current) {
          console.log('🎉 Playing applause sound for quiz completion');
          applauseAudioRef.current.currentTime = 0;
          applauseAudioRef.current.volume = 0.8;
          applauseAudioRef.current.play()
            .then(() => {
              console.log('✅ Applause audio started successfully');
            })
            .catch((error) => {
              console.error('❌ Error playing applause audio:', error);
            });
          setTimeout(() => {
            if (applauseAudioRef.current) {
              console.log('⏹️ Stopping applause audio after 4 seconds');
              applauseAudioRef.current.pause();
              applauseAudioRef.current.currentTime = 0;
            }
          }, 4000);
        }
      } else if (response.question) {
        setQuestionData(response);
        setQuizStatus('Ready to start capture for this question.');
        setQuestionCount((prev) => prev + 1);
      }
    } catch (error) {
      setError(`Error during quiz progression: ${error.message || error.data?.error || 'Unknown error'}`);
      setQuizStatus(`Error: ${error.message || error.data?.error || 'Unknown error'}`);
    }
  };
  
  // const handleFinalize = async () => {
  //   if (!sessionId) return;
  //   if (!window.confirm('Are you sure you want to end this session for ALL centers?')) return;
  //   try {
  //     const response = await finalizeSessionMutation({ session_id: sessionId }).unwrap();
  //     console.log('Finalize response:', response);
  //     setShowDashboard(true);
  //   } catch (error) {
  //     setError(`Error finalizing session: ${error.message || error.data?.error || 'Unknown error'}`);
  //   }
  // };
  
  const handleShowResults = async () => {
    if (!sessionId || !centerCode) return;
    setIsDashboardLoading(true);
    try {
      const response = await fetch(`https://testing.sasthra.in/api/content/engagement_dashboard?session_id=${sessionId}&center_code=${centerCode}`);
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }
      const data = await response.json();
      if (data.success === false) {
        throw new Error(data.error || 'Unknown error');
      }
      setDashboardData(data);
      setShowDashboard(true);
    } catch (err) {
      setError(`Error fetching results: ${err.message}`);
    } finally {
      setIsDashboardLoading(false);
    }
  };
  
  const startCaptureCycle = async () => {
    setLiveFeedLogs([]);
    setIsReadingQuestion(true);
    setQuestionTimer(QUESTION_READING_TIME_S);
    setCurrentQuestionStartTime(Date.now());
    timerIntervalRef.current = setInterval(() => {
      setQuestionTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerIntervalRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));
    setIsReadingQuestion(false);
    setQuestionTimer(null);
    const options = ['a', 'b', 'c', 'd'];
    for (let i = 0; i < options.length; i++) {
      setQuizStatus(
        `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
      );
      await processSingleOption(options[i]);
    }
    setQuizStatus('Capture complete for this question. Results sent.');
    setCurrentOptionTimer(null);
    setCurrentOption('');
  };
  
  const processSingleOption = (optionChar) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let frameCount = 0;
      let clockAudioTimeout = null;
      setCurrentOption(optionChar.toUpperCase());
      setCurrentOptionTimer(DURATION_PER_OPTION_S);
      if (clockTickingAudioRef.current) {
        console.log(`🔊 Playing clock ticking sound for option ${optionChar.toUpperCase()}`);
        clockTickingAudioRef.current.currentTime = 0;
        clockTickingAudioRef.current.volume = 0.7;
        clockTickingAudioRef.current.play()
          .then(() => {
            console.log('✅ Clock ticking audio started successfully');
          })
          .catch((error) => {
            console.error('❌ Error playing clock ticking audio:', error);
          });
        clockAudioTimeout = setTimeout(() => {
          if (clockTickingAudioRef.current) {
            console.log('⏹️ Stopping clock ticking audio after 10 seconds');
            clockTickingAudioRef.current.pause();
            clockTickingAudioRef.current.currentTime = 0;
          }
        }, 10000);
      }
      timerIntervalRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
        setCurrentOptionTimer(remaining);
      }, 1000);
      const intervalId = setInterval(() => {
        const responseTime = (Date.now() - startTime) / 1000;
        captureAndSendFrame(optionChar, responseTime);
        frameCount++;
      }, CAPTURE_INTERVAL_MS);
      setTimeout(() => {
        clearInterval(intervalId);
        if (timerIntervalRef.current) {
          clearInterval(timerIntervalRef.current);
        }
        if (clockAudioTimeout) {
          clearTimeout(clockAudioTimeout);
        }
        if (clockTickingAudioRef.current) {
          console.log(`⏹️ Stopping clock ticking audio for option ${optionChar.toUpperCase()}`);
          clockTickingAudioRef.current.pause();
          clockTickingAudioRef.current.currentTime = 0;
        }
        resolve();
      }, DURATION_PER_OPTION_S * 1000);
    });
  };
  
  const captureAndSendFrame = useCallback((optionChar, responseTime) => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    if (!video || !canvas) {
      console.warn('⚠️ No video or canvas element available');
      return;
    }
    if (!videoStreamRef.current) {
      console.warn('⚠️ No video stream available');
      return;
    }
    if (!socketRef.current?.connected) {
      console.warn('⚠️ Socket not connected - cannot send frame');
      return;
    }
    const ctx = canvas.getContext('2d');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frameData = canvas.toDataURL('image/jpeg', 0.6);
    console.log(
      `📸 Sending frame: session=${sessionId}, option=${optionChar}, time=${responseTime.toFixed(2)}s`
    );
    socketRef.current.emit('process_frame', {
      session_id: sessionId,
      client_id: clientId,
      frame: frameData,
      option_char: optionChar,
      response_time_seconds: responseTime
    });
  }, [sessionId, clientId]);
  
  const initCamera = async () => {
    if (videoStreamRef.current) return;
    try {
      console.log('🎥 Requesting camera access...');
      setCameraStatus('Requesting access...');
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoStreamRef.current = stream;
        console.log('✅ Camera initialized successfully');
        setCameraStatus('Active');
      }
    } catch (error) {
      console.error('❌ Camera error:', error);
      setCameraStatus('Error');
      setError(`Camera error: ${error.message}`);
    }
  };
  
  const stopCamera = () => {
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setCameraStatus('Not initialized');
  };
  
  const initializeAudio = () => {
    if (applauseAudioRef.current) {
      applauseAudioRef.current.load();
      console.log('🔊 Applause audio initialized');
    }
    if (clockTickingAudioRef.current) {
      clockTickingAudioRef.current.load();
      console.log('🔊 Clock ticking audio initialized');
    }
  };
  
  const testSocketConnection = () => {
    if (socketRef.current) {
      socketRef.current.emit('test_connection', { message: 'Hello from client' });
    }
  };
  
  const handleCloseDashboard = () => {
    setShowDashboard(false);
  };
  
  const handleManualQuizStart = async () => {
    if (manualQuizId.trim()) {
      setError(null);
      setShowManualInput(false);
      setQuizId(manualQuizId.trim());
      // Get center code from session storage
      const storedCenterCode = sessionStorage.getItem('centercode');
      if (!storedCenterCode) {
        setError('Center code not found. Please log in again.');
        setShowManualInput(true);
        return;
      }
      let currentSessionId = manualSessionId.trim();
      if (!currentSessionId) {
        currentSessionId = generateUUID();
        setManualSessionId(currentSessionId);
        setSessionId(currentSessionId);
      } else {
        setSessionId(currentSessionId);
      }
      await startQuiz();
    } else {
      setError('Please enter a valid Quiz ID');
    }
  };
  
  useEffect(() => {
    if (inputQuizId) {
      startQuiz();
    }
  }, [inputQuizId]);
  
  if (showDashboard && sessionId && centerCode) {
    if (isDashboardLoading) {
      return (
        <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col items-center justify-center p-16">
            <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-6"></div>
            <h2 className="text-3xl font-bold text-gray-800">Loading Results...</h2>
            <p className="text-lg text-gray-600 mt-2">Analyzing quiz performance data</p>
          </div>
        </div>
      );
    }
    if (!dashboardData) {
      return (
        <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-md p-12 text-center">
            <div className="text-6xl mb-6">⚠️</div>
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Failed to Load Results</h2>
            <p className="text-gray-600 mb-8">There was an error retrieving the quiz results. Please try again later.</p>
            <button
              onClick={() => setShowDashboard(false)}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-lg font-bold"
            >
              Return to Quiz
            </button>
          </div>
        </div>
      );
    }
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl border-4 border-white/20 w-full max-w-6xl max-h-[95vh] flex flex-col">
          <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 animate-pulse" />
            <div className="relative z-10 flex justify-between items-center">
              <div className="flex items-center gap-6">
                <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                  <BarChart3 className="text-yellow-400" size={40} />
                </div>
                <div>
                  <h2 className="text-4xl font-black mb-2">📊 Quiz Results</h2>
                  <p className="text-xl opacity-90">Engagement Dashboard</p>
                </div>
              </div>
              <button
                onClick={handleCloseDashboard}
                className="text-white/80 hover:text-white transition-all duration-300 p-3 rounded-full hover:bg-white/20 hover:scale-110 transform"
              >
                <X size={32} />
              </button>
            </div>
          </div>
          <div className="p-6 bg-gradient-to-r from-slate-800 to-slate-700 border-b border-white/10">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-white">
              <div className="text-center">
                <div className="text-3xl font-black text-blue-400">
                  {dashboardData?.total_questions_in_quiz || 0}
                </div>
                <div className="text-sm opacity-80">Total Questions</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black text-green-400">
                  {dashboardData?.engagement_summary_stats?.active_students || 0}
                </div>
                <div className="text-sm opacity-80">Active Students</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black text-purple-400">
                  {dashboardData?.engagement_summary_stats?.total_participations || 0}
                </div>
                <div className="text-sm opacity-80">Total Participations</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black text-yellow-400">
                  {dashboardData?.engagement_summary_stats?.average_frequency?.toFixed(1) || 0}
                </div>
                <div className="text-sm opacity-80">Avg Frequency</div>
              </div>
            </div>
          </div>
          <div className="flex-1 overflow-y-auto p-8 space-y-4 bg-gradient-to-b from-slate-800 to-slate-900">
            <h3 className="text-2xl font-black text-white mb-6 flex items-center gap-3">
              <Trophy className="text-yellow-400" size={28} />
              Top Performers
            </h3>
            <div className="space-y-3">
              {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
                dashboardData.leaderboard.map((student, index) => (
                  <div
                    key={index}
                    className="transform transition-all duration-300 hover:scale-[1.02] bg-gradient-to-r from-slate-700 to-slate-800 p-6 rounded-2xl shadow-xl border-2 border-white/20"
                  >
                    <div className="flex items-center justify-between text-white">
                      <div className="flex items-center gap-6">
                        <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center text-white font-black text-xl">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="text-2xl font-black mb-1">{student.first_name} {student.last_name}</div>
                          <div className="text-sm opacity-90 flex gap-4">
                            <span>Score: {student.score}</span>
                            <span>Accuracy: {student.accuracy_percentage}%</span>
                            <span>Avg Time: {student.avg_response_time_seconds}s</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-4xl font-black text-yellow-400">#{index + 1}</div>
                          <div className="text-xs opacity-80 bg-white/20 px-2 py-1 rounded-full">
                            {student.questions_attempted} Questions
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12 bg-slate-800 rounded-xl">
                  <div className="text-5xl mb-4">📊</div>
                  <h3 className="text-2xl font-bold text-white mb-2">No Results Available</h3>
                  <p className="text-gray-400">The quiz results are still being processed or no students participated.</p>
                </div>
              )}
            </div>
            
            <h3 className="text-2xl font-black text-white mt-10 mb-6 flex items-center gap-3">
              <Users className="text-blue-400" size={28} />
              Student Performance
            </h3>
            
            <div className="bg-slate-800 rounded-xl overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-slate-700">
                      <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Rank</th>
                      <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Student Name</th>
                      <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Score</th>
                      <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Accuracy</th>
                      <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Questions Attempted</th>
                      <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Avg. Response Time</th>
                    </tr>
                  </thead>
                  <tbody>
                    {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
                      dashboardData.leaderboard.map((student, index) => (
                        <tr 
                          key={index}
                          className={`border-b border-slate-700 hover:bg-slate-700/50 transition-colors ${
                            index % 2 === 0 ? 'bg-slate-800' : 'bg-slate-850'
                          }`}
                        >
                          <td className="py-4 px-6">
                            <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold">
                              {index + 1}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-white font-medium">
                            {student.first_name} {student.last_name}
                          </td>
                          <td className="py-4 px-6 text-green-400 font-bold">
                            {student.score}
                          </td>
                          <td className="py-4 px-6 text-blue-300">
                            {student.accuracy_percentage}%
                          </td>
                          <td className="py-4 px-6 text-purple-300">
                            {student.questions_attempted}
                          </td>
                          <td className="py-4 px-6 text-cyan-300">
                            {student.avg_response_time_seconds}s
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="6" className="py-8 text-center text-gray-400">
                          No student performance data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
            
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-slate-800 rounded-xl p-6">
                <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                  <Target className="text-blue-400" size={24} />
                  Question Performance
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Most Challenging Question</span>
                    <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
                      #{dashboardData?.most_challenging_question || 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Easiest Question</span>
                    <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
                      #{dashboardData?.easiest_question || 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Average Accuracy</span>
                    <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
                      {dashboardData?.average_accuracy?.toFixed(1) || '0.0'}%
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="bg-slate-800 rounded-xl p-6">
                <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                  <Clock className="text-purple-400" size={24} />
                  Response Analysis
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Fastest Response</span>
                    <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
                      {dashboardData?.fastest_response?.toFixed(1) || '0.0'}s
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Slowest Response</span>
                    <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
                      {dashboardData?.slowest_response?.toFixed(1) || '0.0'}s
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Average Response Time</span>
                    <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
                      {dashboardData?.avg_response_time?.toFixed(1) || '0.0'}s
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="bg-slate-800 rounded-xl p-6">
                <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                  <Users className="text-green-400" size={24} />
                  Engagement Metrics
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Peak Engagement</span>
                    <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">
                      {dashboardData?.peak_engagement?.toFixed(1) || '0.0'}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Engagement Trend</span>
                    <span className={`px-3 py-1 rounded-full ${
                      dashboardData?.engagement_trend === 'increasing' 
                        ? 'bg-green-500/20 text-green-300' 
                        : dashboardData?.engagement_trend === 'decreasing' 
                          ? 'bg-red-500/20 text-red-300' 
                          : 'bg-yellow-500/20 text-yellow-300'
                    }`}>
                      {dashboardData?.engagement_trend || 'stable'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Active Students</span>
                    <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
                      {dashboardData?.engagement_summary_stats?.active_students || 0}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="p-6 bg-slate-900 border-t border-white/10">
            <button
              onClick={onClose}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-xl"
            >
              <X size={24} />
              Close Results & Exit Quiz
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-30 animate-float"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${4 + (i % 3)}s`
            }}
          />
        ))}
      </div>
      <CircularOptionDisplay
        option={currentOption}
        timeRemaining={currentOptionTimer}
        totalTime={DURATION_PER_OPTION_S}
        isActive={!!currentOption && currentOptionTimer !== null}
      />
      <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />
      <HandRaiseNotification logs={liveFeedLogs} />
      <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col shadow-2xl">
        <div className="flex justify-between items-center p-8 border-b border-gray-200/50 flex-shrink-0 bg-gradient-to-r from-blue-100/50 via-purple-100/50 to-pink-100/50">
          <div className="flex items-center gap-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-all duration-300">
              <Target className="text-white" size={32} />
            </div>
            <div>
              <h2 className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Live Quiz Experience
              </h2>
              <p className="text-gray-600 text-lg font-medium">Real-time engagement monitoring</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-red-500 transition-all duration-300 p-3 rounded-full hover:bg-red-50 hover:scale-105 transform"
          >
            <X size={28} />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto p-8 space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
                socketStatus.startsWith('Connected')
                  ? 'bg-gradient-to-r from-green-100 to-emerald-100 border-green-300 text-green-800 shadow-green-200/50'
                  : 'bg-gradient-to-r from-red-100 to-pink-100 border-red-300 text-red-800 shadow-red-200/50'
              } shadow-xl`}
            >
              <div className="flex items-center gap-4">
                <div
                  className={`p-3 rounded-xl ${socketStatus.startsWith('Connected') ? 'bg-green-200' : 'bg-red-200'}`}
                >
                  {socketStatus.startsWith('Connected') ? <Wifi size={24} /> : <WifiOff size={24} />}
                </div>
                <div>
                  <div className="font-bold text-lg">Socket Status</div>
                  <div className="text-sm opacity-80 font-medium">{socketStatus}</div>
                </div>
              </div>
            </div>
            <div
              className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
                cameraStatus === 'Active'
                  ? 'bg-gradient-to-r from-blue-100 to-cyan-100 border-blue-300 text-blue-800 shadow-blue-200/50'
                  : 'bg-gradient-to-r from-yellow-100 to-orange-100 border-yellow-300 text-yellow-800 shadow-yellow-200/50'
              } shadow-xl`}
            >
              <div className="flex items-center gap-4">
                <div
                  className={`p-3 rounded-xl ${cameraStatus === 'Active' ? 'bg-blue-200' : 'bg-yellow-200'}`}
                >
                  {cameraStatus === 'Active' ? <Video size={24} /> : <VideoOff size={24} />}
                </div>
                <div>
                  <div className="font-bold text-lg">Camera Status</div>
                  <div className="text-sm opacity-80 font-medium">{cameraStatus}</div>
                </div>
              </div>
            </div>
            <div className="p-6 rounded-2xl border-2 bg-gradient-to-r from-purple-100 to-pink-100 border-purple-300 text-purple-800 shadow-xl shadow-purple-200/50 transition-all duration-500 transform hover:scale-105">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black text-xl shadow-lg">
                  {questionCount}
                </div>
                <div>
                  <div className="font-bold text-lg">Question Progress</div>
                  <div className="text-sm opacity-80 font-medium">Question {questionCount}</div>
                </div>
              </div>
            </div>
          </div>
          {socketRef.current && (
            <button
              onClick={testSocketConnection}
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl transform hover:scale-105"
            >
              Test Socket Connection
            </button>
          )}
          {error && (
            <div className="text-center text-red-600 p-8 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 animate-pulse shadow-xl">
              <div className="text-6xl mb-4">⚠️</div>
              <p className="text-xl font-bold">{error}</p>
            </div>
          )}
          {showManualInput && (
            <div className="bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-8 border-2 border-blue-200 backdrop-blur-sm shadow-xl">
              <h3 className="text-3xl font-black text-gray-800 mb-6 flex items-center gap-4">
                <span className="text-4xl">🎯</span>
                Enter Quiz Details
              </h3>
              <div className="flex flex-col gap-6">
                <div>
                  <label className="block text-lg font-bold text-gray-700 mb-3">Quiz ID</label>
                  <input
                    type="text"
                    value={manualQuizId}
                    onChange={(e) => setManualQuizId(e.target.value)}
                    placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
                    className="w-full px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
                  />
                </div>
                <div>
                  <label className="block text-lg font-bold text-gray-700 mb-3">Session ID (Generate or paste)</label>
                  <div className="flex gap-4">
                    <input
                      type="text"
                      value={manualSessionId}
                      onChange={(e) => setManualSessionId(e.target.value)}
                      placeholder="Session ID"
                      className="flex-1 px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
                    />
                    <button
                      onClick={() => setManualSessionId(generateUUID())}
                      className="px-6 py-4 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 transform hover:scale-105"
                    >
                      Generate
                    </button>
                  </div>
                </div>
                <button
                  onClick={handleManualQuizStart}
                  disabled={!manualQuizId.trim() || isStartingQuiz}
                  className="px-10 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-teal-500 hover:from-purple-600 hover:via-blue-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 text-lg"
                >
                  {isStartingQuiz ? 'Starting...' : 'Start Quiz'}
                </button>
              </div>
              <p className="text-gray-600 text-lg mt-4 font-medium">
                Enter the Quiz ID provided by your teacher to start the quiz.
              </p>
            </div>
          )}
          {!showManualInput && questionData && (
            <div>
              <button
                onClick={() => {
                  setShowManualInput(true);
                  setQuestionData(null);
                  setQuizId(null);
                  setError(null);
                }}
                className="px-6 py-3 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white rounded-xl text-lg font-medium transition-all duration-300 backdrop-blur-sm shadow-lg transform hover:scale-105"
              >
                Enter Different Quiz ID
              </button>
            </div>
          )}
          {questionData && (
            <div className="text-gray-800 space-y-8">
              <div className="status-bar p-6 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-lg">
                  <div className="font-bold">
                    <strong>Session ID:</strong> <span className="text-blue-600">{sessionId}</span>
                  </div>
                  <div className="font-bold">
                    <strong>Status:</strong> <span className="text-purple-600">{quizStatus}</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="flex-1">
                  <div className="relative rounded-2xl overflow-hidden border-4 border-white/50 bg-gradient-to-br from-gray-100 to-gray-200 backdrop-blur-sm shadow-2xl">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="w-full max-w-2xl rounded-2xl"
                    />
                    <div
                      className={`absolute top-6 left-6 px-4 py-3 rounded-xl text-lg font-bold backdrop-blur-sm shadow-lg ${
                        cameraStatus === 'Active'
                          ? 'bg-green-500/90 text-white'
                          : cameraStatus === 'Error'
                            ? 'bg-red-500/90 text-white'
                            : 'bg-yellow-500/90 text-white'
                      }`}
                    >
                      📹 Camera: {cameraStatus}
                    </div>
                  </div>
                  <canvas ref={canvasRef} className="hidden" />
                </div>
                {liveFeedLogs.length > 0 && (
                  <div className="w-full lg:w-80 bg-gradient-to-b from-white/90 to-blue-50/90 rounded-2xl p-6 border-2 border-blue-200 max-h-96 overflow-y-auto backdrop-blur-sm shadow-xl">
                    <h3 className="text-xl font-black mb-4 text-gray-800 flex items-center gap-3">
                      <Users className="text-blue-500" size={24} />
                      Live Hand Raises ({liveFeedLogs.length})
                    </h3>
                    <div className="space-y-3">
                      {liveFeedLogs
                        .sort(
                          (a, b) =>
                            new Date(a.detection_timestamp) - new Date(b.detection_timestamp)
                        )
                        .map((log, idx) => (
                          <div
                            key={idx}
                            className="bg-gradient-to-r from-white/80 to-blue-50/80 p-4 rounded-xl text-sm backdrop-blur-sm transform hover:scale-105 transition-all duration-300 shadow-lg border border-blue-200"
                          >
                            <div className="flex justify-between items-start mb-2">
                              <span className="font-bold text-gray-800 text-lg">
                                #{idx + 1} {log.student_name}
                              </span>
                              <span className="text-sm text-gray-600 bg-blue-100 px-3 py-1 rounded-full font-medium">
                                {log.responseTime}s
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-blue-600 font-bold text-lg">
                                Option {log.option ? log.option.toUpperCase() : 'N/A'}
                              </span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
              <div className="space-y-6">
                <h2 className="text-3xl font-black bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent">
                  {questionData.sub_topic_name}
                </h2>
                <div className="bg-gradient-to-r from-white/80 to-purple-50/80 p-8 rounded-2xl border-2 border-purple-200 backdrop-blur-sm shadow-xl">
                  <p className="text-2xl text-gray-800 leading-relaxed font-medium">
                    {questionData.question_number}. {questionData.question}
                  </p>
                </div>
                {questionData.question_image && questionData.question_image.trim() && (
                  <div className="bg-gradient-to-r from-white/80 to-blue-50/80 p-6 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
                    <img
                      src={questionData.question_image.trim()}
                      alt="Question Image"
                      className="max-w-full h-auto rounded-xl shadow-lg border-2 border-white/50"
                      onError={(e) => {
                        console.error('Failed to load question image:', e.target.src);
                        if (e.target.src.includes('?X-Amz-')) {
                          const baseUrl = e.target.src.split('?X-Amz-')[0];
                          console.log('Trying base URL:', baseUrl);
                          e.target.src = baseUrl;
                        } else {
                          e.target.style.display = 'none';
                        }
                      }}
                      onLoad={() => {
                        console.log(
                          'Question image loaded successfully:',
                          questionData.question_image
                        );
                      }}
                    />
                  </div>
                )}
                <div className="grid gap-4">
                  {questionData.options.map((opt, idx) => {
                    const optionLetter = String.fromCharCode(97 + idx);
                    const isCurrentOption = currentOption.toLowerCase() === optionLetter;
                    return (
                      <div key={idx} className="relative">
                        <div
                          className={`p-6 rounded-2xl border-2 transition-all duration-500 transform ${
                            isCurrentOption
                              ? 'bg-gradient-to-r from-green-100 via-emerald-100 to-teal-100 border-green-400 shadow-2xl scale-105 shadow-green-300/50'
                              : 'bg-gradient-to-r from-white/80 to-gray-50/80 border-gray-300 hover:bg-gray-100/80 hover:scale-102 shadow-lg'
                          } backdrop-blur-sm`}
                        >
                          <span className="font-bold text-gray-800 text-xl">
                            {optionLetter.toUpperCase()}. {opt}
                          </span>
                          {questionData.option_images && questionData.option_images[idx] && (
                            <img
                              src={questionData.option_images[idx]}
                              alt={`Option ${optionLetter.toUpperCase()}`}
                              className="max-w-90% max-h-150 mt-10 rounded-4"
                            />
                          )}
                        </div>
                        {isCurrentOption && currentOptionTimer !== null && (
                          <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-lg font-black animate-pulse shadow-xl">
                            {currentOptionTimer}s
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
        {questionData && (
          <div className="border-t border-gray-200/50 p-8 flex-shrink-0 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm">
            <div className="flex flex-col sm:flex-row gap-6 justify-between items-center">
              <div className="text-lg text-gray-700 flex items-center gap-4 font-medium">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black">
                  {questionCount}
                </div>
                Question {questionCount} • Session ID:{' '}
                <span className="text-blue-600 font-bold">{sessionId}</span>
              </div>
              <div className="flex gap-4">
                {questionCount >= 5 && (
                  <button
                    onClick={() => setShowDashboard(true)}
                    className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
                  >
                    <BarChart3 size={24} />
                    View Results
                  </button>
                )}
                {finalResults ? (
                  <button
                    onClick={handleShowResults}
                    className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
                  >
                    <BarChart3 size={24} />
                    Show Results
                  </button>
                ) : null}
                <button
                  onClick={() => {
                    initializeAudio();
                    handleNextQuestion();
                  }}
                  className="px-10 py-4 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-black shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 disabled:transform-none text-lg"
                  disabled={
                    isFetchingNextQuestion ||
                    quizStatus.includes('Capturing') ||
                    quizStatus.includes('Processing') ||
                    isReadingQuestion
                  }
                >
                  {isFetchingNextQuestion ? 'Processing...' : 'Start Capture & Next Question'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      <audio ref={applauseAudioRef} preload="auto">
        <source src={applauseAudio} type="audio/mpeg" />
      </audio>
      <audio ref={clockTickingAudioRef} preload="auto">
        <source src={clockTickingAudio} type="audio/mpeg" />
      </audio>
      <style jsx>{`
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
          }
        }
        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        @keyframes spin-reverse {
          from {
            transform: rotate(360deg);
          }
          to {
            transform: rotate(0deg);
          }
        }
        .animate-slideInRight {
          animation: slideInRight 0.6s ease-out;
        }
        .animate-float {
          animation: float 4s ease-in-out infinite;
        }
        .animate-spin-slow {
          animation: spin-slow 8s linear infinite;
        }
        .animate-spin-reverse {
          animation: spin-reverse 6s linear infinite;
        }
      `}</style>
    </div>
  );
};

export default LiveQuiz;















































// import React, { useEffect, useRef, useState } from 'react';
// import io from 'socket.io-client';
// import {
//   X,
//   BarChart3,
//   Video,
//   VideoOff,
//   Wifi,
//   WifiOff,
//   Clock,
//   Users,
//   Target,
//   Trophy,
//   Medal,
//   Award,
//   ChevronDown,
//   ChevronUp
// } from 'lucide-react';
// import EngagementDashboard from './EngagementDashboard';
// import applauseAudio from '../../../../assets/audio/applause-180037.mp3';
// import clockTickingAudio from '../../../../assets/audio/clock-ticking-down-376897.mp3';
// import { useStartQuizMutation, useNextQuestionMutation } from './centerTraineeLive.slice';

// // API Configuration
// const CAPTURE_INTERVAL_MS = 200;
// const DURATION_PER_OPTION_S = 10;
// const QUESTION_READING_TIME_S = 15;

// // Enhanced Circular Option Display Component with Animated Countdown
// const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
//   const circumference = 2 * Math.PI * 120;
//   const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
//   const strokeDasharray = circumference;
//   const strokeDashoffset = circumference - progress;

//   const getTimerColor = () => {
//     const percentage = timeRemaining / totalTime;
//     if (percentage > 0.6) return '#10B981';
//     if (percentage > 0.3) return '#F59E0B';
//     return '#EF4444';
//   };

//   const getScale = () => {
//     const percentage = timeRemaining / totalTime;
//     return 1 + (1 - percentage) * 0.2;
//   };

//   if (!isActive) return null;

//   return (
//     <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
//       <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />
//       <div className="absolute inset-0">
//         {[...Array(12)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
//             style={{
//               top: `${10 + i * 7}%`,
//               left: `${5 + i * 8}%`,
//               animationDelay: `${i * 0.3}s`,
//               animationDuration: `${3 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <div
//         className="relative transition-all duration-300 ease-out"
//         style={{ transform: `scale(${getScale()})` }}
//       >
//         <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />
//         <div className="relative w-64 h-64 flex items-center justify-center">
//           <svg
//             className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
//             viewBox="0 0 260 260"
//           >
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.3)"
//               strokeWidth="12"
//               className="drop-shadow-lg"
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke={getTimerColor()}
//               strokeWidth="12"
//               strokeLinecap="round"
//               strokeDasharray={strokeDasharray}
//               strokeDashoffset={strokeDashoffset}
//               className="transition-all duration-1000 ease-out drop-shadow-xl"
//               style={{
//                 filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
//               }}
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="100"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.2)"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               className="animate-spin-reverse"
//             />
//             <defs>
//               <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
//                 <stop offset="0%" stopColor="#3B82F6" />
//                 <stop offset="50%" stopColor="#8B5CF6" />
//                 <stop offset="100%" stopColor="#EC4899" />
//               </linearGradient>
//               <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
//                 <stop offset="0%" stopColor="#FFFFFF" />
//                 <stop offset="100%" stopColor="#F8FAFC" />
//               </radialGradient>
//             </defs>
//           </svg>
//           <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
//             <div
//               className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
//                 textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
//               }}
//             >
//               {option}
//             </div>
//             <div
//               className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
//                 filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
//               }}
//             >
//               {timeRemaining}s
//             </div>
//             <div className="flex gap-1 mt-2">
//               {[...Array(totalTime)].map((_, i) => (
//                 <div
//                   key={i}
//                   className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
//                     i < totalTime - timeRemaining
//                       ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
//                       : 'bg-gray-300 scale-100'
//                   }`}
//                 />
//               ))}
//             </div>
//           </div>
//           <div className="absolute inset-0 pointer-events-none">
//             {[...Array(8)].map((_, i) => (
//               <div
//                 key={i}
//                 className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
//                 style={{
//                   top: `${15 + i * 10}%`,
//                   left: `${10 + i * 12}%`,
//                   animationDelay: `${i * 0.3}s`,
//                   animationDuration: `${2 + (i % 2)}s`
//                 }}
//               />
//             ))}
//           </div>
//           <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Reading Timer Component
// const ReadingTimer = ({ timeRemaining, isActive }) => {
//   if (!isActive) return null;

//   return (
//     <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
//       <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
//         <div className="text-center">
//           <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
//             <Clock className="animate-spin" size={32} />
//             Reading Time
//           </div>
//           <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
//           <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Hand Raise Animation Component
// const HandRaiseNotification = ({ logs }) => {
//   const [showNotification, setShowNotification] = useState(false);
//   const [latestLog, setLatestLog] = useState(null);

//   useEffect(() => {
//     if (logs.length > 0) {
//       const newest = logs[logs.length - 1];
//       setLatestLog(newest);
//       setShowNotification(true);

//       const timer = setTimeout(() => {
//         setShowNotification(false);
//       }, 4000);

//       return () => clearTimeout(timer);
//     }
//   }, [logs]);

//   if (!showNotification || !latestLog) return null;

//   return (
//     <div className="fixed top-24 right-8 z-50 transform animate-slideInRight">
//       <div className="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 text-white px-8 py-6 rounded-2xl shadow-2xl border-4 border-white/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-300">
//         <div className="flex items-center gap-4">
//           <div className="text-4xl animate-bounce">🙋‍♂️</div>
//           <div>
//             <div className="font-bold text-xl drop-shadow-sm">{latestLog.student_name}</div>
//             <div className="text-sm opacity-90 bg-white/20 px-3 py-1 rounded-full mt-1">
//               Option {latestLog.option.toUpperCase()} • {latestLog.responseTime}s
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// // F1-Style Animated Leaderboard Component
// const F1Leaderboard = ({ finalResults, onClose }) => {
//   const [showAllParticipants, setShowAllParticipants] = useState(false);
//   const [animationPhase, setAnimationPhase] = useState('entering');

//   useEffect(() => {
//     const timer1 = setTimeout(() => {
//       setAnimationPhase('displaying');
//     }, 1000);

//     return () => clearTimeout(timer1);
//   }, []);

//   if (!finalResults?.overall_summary?.top_engaged_students) return null;

//   const topStudents = finalResults.overall_summary.top_engaged_students.slice(0, 5);
//   const allStudents = finalResults.overall_summary.top_engaged_students;
//   const overallSummary = finalResults.overall_summary;

//   const getRankIcon = (rank) => {
//     switch (rank) {
//       case 1:
//         return <Trophy className="text-yellow-500" size={32} />;
//       case 2:
//         return <Medal className="text-gray-400" size={28} />;
//       case 3:
//         return <Award className="text-amber-600" size={24} />;
//       default:
//         return (
//           <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center font-bold text-lg">
//             {rank}
//           </div>
//         );
//     }
//   };

//   const getPerformanceColor = (rank) => {
//     switch (rank) {
//       case 1:
//         return 'from-yellow-400 via-yellow-500 to-yellow-600';
//       case 2:
//         return 'from-gray-300 via-gray-400 to-gray-500';
//       case 3:
//         return 'from-amber-500 via-amber-600 to-amber-700';
//       default:
//         return 'from-blue-400 via-blue-500 to-blue-600';
//     }
//   };

//   return (
//     <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
//       <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 rounded-3xl border-4 border-white/20 w-full max-w-6xl shadow-2xl flex flex-col max-h-[95vh]">
//         <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-8 text-white relative overflow-hidden">
//           <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 animate-pulse" />
//           <div className="relative z-10 flex justify-between items-center">
//             <div className="flex items-center gap-6">
//               <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
//                 <Trophy className="text-yellow-400" size={40} />
//               </div>
//               <div>
//                 <h2 className="text-4xl font-black mb-2">🏁 Quiz Results</h2>
//                 <p className="text-xl opacity-90">F1-Style Leaderboard</p>
//               </div>
//             </div>
//             <button
//               onClick={onClose}
//               className="text-white/80 hover:text-white transition-all duration-300 p-3 rounded-full hover:bg-white/20 hover:scale-110 transform"
//             >
//               <X size={32} />
//             </button>
//           </div>
//         </div>
//         <div className="p-6 bg-gradient-to-r from-slate-800 to-slate-700 border-b border-white/10">
//           <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-white">
//             <div className="text-center">
//               <div className="text-3xl font-black text-blue-400">
//                 {overallSummary.total_questions_in_quiz}
//               </div>
//               <div className="text-sm opacity-80">Total Questions</div>
//             </div>
//             <div className="text-center">
//               <div className="text-3xl font-black text-green-400">
//                 {overallSummary.engagement_summary_stats.active_students}
//               </div>
//               <div className="text-sm opacity-80">Active Students</div>
//             </div>
//             <div className="text-center">
//               <div className="text-3xl font-black text-purple-400">
//                 {overallSummary.engagement_summary_stats.total_participations}
//               </div>
//               <div className="text-sm opacity-80">Total Participations</div>
//             </div>
//             <div className="text-center">
//               <div className="text-3xl font-black text-yellow-400">
//                 {overallSummary.engagement_summary_stats.average_frequency.toFixed(1)}
//               </div>
//               <div className="text-sm opacity-80">Avg Frequency</div>
//             </div>
//           </div>
//         </div>
//         <div className="flex-1 overflow-y-auto p-8 space-y-4 bg-gradient-to-b from-slate-800 to-slate-900">
//           <h3 className="text-2xl font-black text-white mb-6 flex items-center gap-3">
//             <Trophy className="text-yellow-400" size={28} />
//             Top 5 Performers
//           </h3>
//           <div className="space-y-3">
//             {topStudents.map((student, index) => (
//               <div
//                 key={student.name + student.rank}
//                 className={`transform transition-all duration-1000 ease-out ${
//                   animationPhase === 'entering'
//                     ? 'translate-x-full opacity-0'
//                     : 'translate-x-0 opacity-100'
//                 }`}
//                 style={{
//                   transitionDelay: `${index * 200}ms`,
//                   animationDelay: `${index * 200}ms`
//                 }}
//               >
//                 <div
//                   className={`bg-gradient-to-r ${getPerformanceColor(student.rank)} p-6 rounded-2xl shadow-xl border-2 border-white/20 hover:scale-105 transition-all duration-300`}
//                 >
//                   <div className="flex items-center justify-between text-white">
//                     <div className="flex items-center gap-6">
//                       <div className="flex-shrink-0">
//                         {getRankIcon(student.rank)}
//                       </div>
//                       <div className="flex-1">
//                         <div className="text-2xl font-black mb-1">{student.name}</div>
//                         <div className="text-sm opacity-90 flex gap-4">
//                           <span>Score: {student.score}</span>
//                           <span>Accuracy: {student.accuracy_percentage}%</span>
//                           <span>Avg Time: {student.avg_response_time_seconds.toFixed(1)}s</span>
//                         </div>
//                       </div>
//                       <div className="text-right">
//                         <div className="text-4xl font-black">#{student.rank}</div>
//                         <div className="text-xs opacity-80 bg-white/20 px-2 py-1 rounded-full">
//                           {student.questions_attempted} Questions
//                         </div>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             ))}
//           </div>
//         </div>
//         <div className="p-6 bg-slate-900 border-t border-white/10">
//           <button
//             onClick={() => setShowAllParticipants(!showAllParticipants)}
//             className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-xl"
//           >
//             <Users size={24} />
//             {showAllParticipants ? 'Hide' : 'Show'} All Participants ({allStudents.length})
//             {showAllParticipants ? <ChevronUp size={24} /> : <ChevronDown size={24} />}
//           </button>
//           {showAllParticipants && (
//             <div className="mt-6 max-h-80 overflow-y-auto space-y-2 bg-slate-800 rounded-xl p-4 scrollbar-thin scrollbar-thumb-blue-500 scrollbar-track-slate-700">
//               {allStudents.map((student, index) => (
//                 <div
//                   key={student.name + index}
//                   className="bg-slate-700 p-4 rounded-lg text-white hover:bg-slate-600 transition-all duration-200"
//                 >
//                   <div className="flex justify-between items-center">
//                     <div className="flex items-center gap-4">
//                       <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center font-bold">
//                         {student.rank}
//                       </div>
//                       <div>
//                         <div className="font-bold text-lg">{student.name}</div>
//                         <div className="text-sm opacity-80">{student.performance_message}</div>
//                       </div>
//                     </div>
//                     <div className="text-right text-sm">
//                       <div>
//                         Score: <span className="font-bold">{student.score}</span>
//                       </div>
//                       <div>
//                         Accuracy: <span className="font-bold">{student.accuracy_percentage}%</span>
//                       </div>
//                       <div>
//                         Correct: <span className="font-bold">{student.correct_responses}</span>
//                       </div>
//                       <div>
//                         Attempted: <span className="font-bold">{student.questions_attempted}</span>
//                       </div>
//                       <div>
//                         Avg Time:{' '}
//                         <span className="font-bold">
//                           {student.avg_response_time_seconds.toFixed(1)}s
//                         </span>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               ))}
//             </div>
//           )}
//         </div>
//       </div>
//       <style jsx>{`
//         .scrollbar-thin::-webkit-scrollbar {
//           width: 8px;
//         }
//         .scrollbar-thin::-webkit-scrollbar-track {
//           background: #1e293b;
//           border-radius: 4px;
//         }
//         .scrollbar-thin::-webkit-scrollbar-thumb {
//           background: #3b82f6;
//           border-radius: 4px;
//         }
//         .scrollbar-thin::-webkit-scrollbar-thumb:hover {
//           background: #2563eb;
//         }
//       `}</style>
//     </div>
//   );
// };

// const LiveQuiz = ({ quizId: inputQuizId, onClose }) => {
//   const [quizId, setQuizId] = useState(inputQuizId);
//   const [questionData, setQuestionData] = useState(null);
//   const [quizStatus, setQuizStatus] = useState('');
//   const [summary, setSummary] = useState(null);
//   const [finalResults, setFinalResults] = useState(null);
//   const [liveFeedLogs, setLiveFeedLogs] = useState([]);
//   const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
//   const [socketStatus, setSocketStatus] = useState('Disconnected');
//   const [error, setError] = useState(null);
//   const [centerCode] = useState(sessionStorage.getItem('centercode') || '');
//   const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
//   const [currentOption, setCurrentOption] = useState('');
//   const [questionTimer, setQuestionTimer] = useState(null);
//   const [isReadingQuestion, setIsReadingQuestion] = useState(false);
//   const [showDashboard, setShowDashboard] = useState(false);
//   const [questionCount, setQuestionCount] = useState(0);
//   const [cameraStatus, setCameraStatus] = useState('Not initialized');
//   const [showF1Leaderboard, setShowF1Leaderboard] = useState(false);
//   const [showManualInput, setShowManualInput] = useState(!inputQuizId);
//   const [manualQuizId, setManualQuizId] = useState('');

//   const [startQuizMutation, { isLoading: isStartingQuiz }] = useStartQuizMutation();
//   const [nextQuestionMutation, { isLoading: isFetchingNextQuestion }] = useNextQuestionMutation();

//   const videoRef = useRef(null);
//   const canvasRef = useRef(null);
//   const socketRef = useRef(null);
//   const videoStreamRef = useRef(null);
//   const timerIntervalRef = useRef(null);
//   const applauseAudioRef = useRef(null);
//   const clockTickingAudioRef = useRef(null);

//   useEffect(() => {
//     if (quizId && videoRef.current) {
//       console.log('🎯 QuizId changed, initializing camera:', quizId);
//       initCamera();
//     }
//   }, [quizId]);

//   useEffect(() => {
//     return () => {
//       stopCamera();
//       if (socketRef.current) {
//         socketRef.current.disconnect();
//       }
//       if (timerIntervalRef.current) {
//         clearInterval(timerIntervalRef.current);
//       }
//       if (applauseAudioRef.current) {
//         applauseAudioRef.current.pause();
//         applauseAudioRef.current.currentTime = 0;
//       }
//       if (clockTickingAudioRef.current) {
//         clockTickingAudioRef.current.pause();
//         clockTickingAudioRef.current.currentTime = 0;
//       }
//     };
//   }, []);

//   const startQuiz = async () => {
//     if (!inputQuizId) {
//       setError('Missing Quiz ID');
//       return;
//     }

//     try {
//       setQuizStatus('Starting quiz...');
//       const response = await startQuizMutation(inputQuizId).unwrap();

//       if (response.error) {
//         throw new Error(response.error);
//       }

//       setQuestionData(response);
//       setQuizId(response.quiz_id);
//       setQuizStatus('Ready to start capture for this question.');
//       setQuestionCount(1);
//       console.log('🎯 Starting quiz, initializing camera immediately');
//       await initCamera();
//       initializeAudio();
//       setupSocketIO();
//     } catch (error) {
//       setError(`Error starting quiz: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };

//   const handleNextQuestion = async () => {
//     if (!quizId) return;

//     try {
//       await startCaptureCycle();
//       setQuizStatus('Processing results and fetching next question...');
//       const response = await nextQuestionMutation({ quizId, processSelectorId: inputQuizId }).unwrap();

//       if (response.overall_summary) {
//         setFinalResults(response);
//         stopCamera();
//         socketRef.current?.disconnect();
//         setQuizStatus('Quiz completed! View leaderboard for results.');

//         if (applauseAudioRef.current) {
//           console.log('🎉 Playing applause sound for quiz completion');
//           applauseAudioRef.current.currentTime = 0;
//           applauseAudioRef.current.volume = 0.8;
//           applauseAudioRef.current.play()
//             .then(() => {
//               console.log('✅ Applause audio started successfully');
//             })
//             .catch((error) => {
//               console.error('❌ Error playing applause audio:', error);
//             });
//           setTimeout(() => {
//             if (applauseAudioRef.current) {
//               console.log('⏹️ Stopping applause audio after 4 seconds');
//               applauseAudioRef.current.pause();
//               applauseAudioRef.current.currentTime = 0;
//             }
//           }, 4000);
//         }

//         setShowF1Leaderboard(true);
//       } else if (response.question) {
//         setQuestionData(response);
//         setQuizStatus('Ready to start capture for this question.');
//         setQuestionCount((prev) => prev + 1);
//       }
//     } catch (error) {
//       setError(`Error during quiz progression: ${error.message || error.data?.error || 'Unknown error'}`);
//       setQuizStatus(`Error: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };

//   const startCaptureCycle = async () => {
//     setLiveFeedLogs([]);
//     setIsReadingQuestion(true);
//     setQuestionTimer(QUESTION_READING_TIME_S);
//     setCurrentQuestionStartTime(Date.now());

//     timerIntervalRef.current = setInterval(() => {
//       setQuestionTimer((prev) => {
//         if (prev <= 1) {
//           clearInterval(timerIntervalRef.current);
//           return 0;
//         }
//         return prev - 1;
//       });
//     }, 1000);

//     await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));
//     setIsReadingQuestion(false);
//     setQuestionTimer(null);

//     const options = ['a', 'b', 'c', 'd'];

//     for (let i = 0; i < options.length; i++) {
//       setQuizStatus(
//         `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
//       );
//       await processSingleOption(options[i]);
//     }

//     setQuizStatus('Capture complete for this question. Results sent.');
//     setCurrentOptionTimer(null);
//     setCurrentOption('');
//   };

//   const processSingleOption = (optionChar) => {
//     return new Promise((resolve) => {
//       const startTime = Date.now();
//       let frameCount = 0;
//       let clockAudioTimeout = null;

//       setCurrentOption(optionChar.toUpperCase());
//       setCurrentOptionTimer(DURATION_PER_OPTION_S);

//       if (clockTickingAudioRef.current) {
//         console.log(`🔊 Playing clock ticking sound for option ${optionChar.toUpperCase()}`);
//         clockTickingAudioRef.current.currentTime = 0;
//         clockTickingAudioRef.current.volume = 0.7;
//         clockTickingAudioRef.current.play()
//           .then(() => {
//             console.log('✅ Clock ticking audio started successfully');
//           })
//           .catch((error) => {
//             console.error('❌ Error playing clock ticking audio:', error);
//           });
//         clockAudioTimeout = setTimeout(() => {
//           if (clockTickingAudioRef.current) {
//             console.log('⏹️ Stopping clock ticking audio after 10 seconds');
//             clockTickingAudioRef.current.pause();
//             clockTickingAudioRef.current.currentTime = 0;
//           }
//         }, 10000);
//       }

//       timerIntervalRef.current = setInterval(() => {
//         const elapsed = Math.floor((Date.now() - startTime) / 1000);
//         const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
//         setCurrentOptionTimer(remaining);
//       }, 1000);

//       const intervalId = setInterval(() => {
//         const responseTime = (Date.now() - startTime) / 1000;
//         captureAndSendFrame(optionChar, responseTime);
//         frameCount++;
//       }, CAPTURE_INTERVAL_MS);

//       setTimeout(() => {
//         clearInterval(intervalId);
//         if (timerIntervalRef.current) {
//           clearInterval(timerIntervalRef.current);
//         }
//         if (clockAudioTimeout) {
//           clearTimeout(clockAudioTimeout);
//         }
//         if (clockTickingAudioRef.current) {
//           console.log(`⏹️ Stopping clock ticking audio for option ${optionChar.toUpperCase()}`);
//           clockTickingAudioRef.current.pause();
//           clockTickingAudioRef.current.currentTime = 0;
//         }
//         resolve();
//       }, DURATION_PER_OPTION_S * 1000);
//     });
//   };

//   const captureAndSendFrame = (optionChar, responseTime) => {
//     const video = videoRef.current;
//     const canvas = canvasRef.current;

//     if (!video || !canvas) {
//       console.warn('⚠️ No video or canvas element available');
//       return;
//     }

//     if (!videoStreamRef.current) {
//       console.warn('⚠️ No video stream available');
//       return;
//     }

//     if (!socketRef.current?.connected) {
//       console.warn('⚠️ Socket not connected');
//       return;
//     }

//     const ctx = canvas.getContext('2d');
//     canvas.width = video.videoWidth;
//     canvas.height = video.videoHeight;
//     ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
//     const frameData = canvas.toDataURL('image/jpeg', 0.6);

//     console.log(
//       `📸 Sending frame: quiz=${quizId}, option=${optionChar}, time=${responseTime.toFixed(2)}s`
//     );

//     socketRef.current.emit('process_frame', {
//       quiz_id: quizId,
//       frame: frameData,
//       option_char: optionChar,
//       response_time_seconds: responseTime
//     });
//   };

//   const setupSocketIO = () => {
//     if (socketRef.current) {
//       socketRef.current.disconnect();
//     }

//     // Dynamically construct the Socket.IO URL based on the current environment
//     const socketUrl = `${window.location.protocol}//${window.location.host}`;

//     socketRef.current = io(socketUrl, {
//       path: '/socketio2/socket.io',
//       transports: ['websocket', 'polling'],
//       upgrade: true,
//       rememberUpgrade: true,
//       timeout: 20000,
//       forceNew: true,
//       autoConnect: true,
//       reconnection: true,
//       reconnectionAttempts: 5,
//       reconnectionDelay: 1000,
//       reconnectionDelayMax: 5000,
//       maxReconnectionAttempts: 5
//     });

//     socketRef.current.on('connect', () => {
//       setSocketStatus('Connected');
//       console.log(`✅ Connected to Socket.IO server at ${socketUrl}`);
//     });

//     socketRef.current.on('disconnect', (reason) => {
//       console.log('❌ Disconnected from Socket.IO server. Reason:', reason);
//       setSocketStatus('Disconnected');
//     });

//     socketRef.current.on('connect_error', (error) => {
//       console.error('❌ Socket.IO connection error:', error);
//       setSocketStatus('Connection Error');
//     });

//     socketRef.current.on('reconnect', (attemptNumber) => {
//       console.log('🔄 Reconnected to Socket.IO server. Attempt:', attemptNumber);
//       setSocketStatus('Reconnected');
//     });

//     socketRef.current.on('reconnect_error', (error) => {
//       console.error('❌ Socket.IO reconnection error:', error);
//     });

//     socketRef.current.on('reconnect_failed', () => {
//       console.error('❌ Socket.IO reconnection failed');
//       setSocketStatus('Reconnection Failed');
//     });

//     socketRef.current.on('hand_raised', (data) => {
//       console.log('🙋 Hand raised event received:', data);
//       setLiveFeedLogs((logs) => {
//         const newLog = {
//           ...data,
//           responseTime: currentQuestionStartTime
//             ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000)
//             : 0
//         };
//         return [...logs, newLog];
//       });
//     });

//     socketRef.current.onAny((eventName, ...args) => {
//       console.log(`📡 Socket event received: ${eventName}`, args);
//     });
//   };

//   const initCamera = async () => {
//     if (videoStreamRef.current) return;

//     try {
//       console.log('🎥 Requesting camera access...');
//       setCameraStatus('Requesting access...');
//       const stream = await navigator.mediaDevices.getUserMedia({
//         video: {
//           width: { ideal: 1280 },
//           height: { ideal: 720 }
//         }
//       });

//       if (videoRef.current) {
//         videoRef.current.srcObject = stream;
//         videoStreamRef.current = stream;
//         console.log('✅ Camera initialized successfully');
//         setCameraStatus('Active');
//       }
//     } catch (error) {
//       console.error('❌ Camera error:', error);
//       setCameraStatus('Error');
//       setError(`Camera error: ${error.message}`);
//     }
//   };

//   const stopCamera = () => {
//     if (videoStreamRef.current) {
//       videoStreamRef.current.getTracks().forEach((track) => track.stop());
//       videoStreamRef.current = null;
//     }
//     if (videoRef.current) {
//       videoRef.current.srcObject = null;
//     }
//     setCameraStatus('Not initialized');
//   };

//   const initializeAudio = () => {
//     if (applauseAudioRef.current) {
//       applauseAudioRef.current.load();
//       console.log('🔊 Applause audio initialized');
//     }
//     if (clockTickingAudioRef.current) {
//       clockTickingAudioRef.current.load();
//       console.log('🔊 Clock ticking audio initialized');
//     }
//   };

//   const testSocketConnection = () => {
//     if (socketRef.current) {
//       socketRef.current.emit('test_connection', { message: 'Hello from client' });
//     }
//   };

//   const handleCloseDashboard = () => {
//     setShowDashboard(false);
//   };

//   const handleManualQuizStart = async () => {
//     if (manualQuizId.trim()) {
//       setError(null);
//       setShowManualInput(false);
//       setQuizId(manualQuizId.trim());
//       await startQuizWithId(manualQuizId.trim());
//     } else {
//       setError('Please enter a valid Quiz ID');
//     }
//   };

//   const startQuizWithId = async (quizIdToUse) => {
//     try {
//       setQuizStatus('Starting quiz...');
//       const response = await startQuizMutation(quizIdToUse).unwrap();

//       if (response.error) {
//         throw new Error(response.error);
//       }

//       setQuestionData(response);
//       setQuizId(response.quiz_id);
//       setQuizStatus('Ready to start capture for this question.');
//       setQuestionCount(1);
//       initializeAudio();
//       setupSocketIO();
//     } catch (error) {
//       setError(`Error starting quiz: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };

//   useEffect(() => {
//     if (inputQuizId) {
//       startQuiz();
//     }
//   }, [inputQuizId]);

//   if (showF1Leaderboard && finalResults) {
//     return (
//       <F1Leaderboard finalResults={finalResults} onClose={() => setShowF1Leaderboard(false)} />
//     );
//   }

//   if (showDashboard && quizId) {
//     return <EngagementDashboard quizId={quizId} onClose={handleCloseDashboard} />;
//   }

//   return (
//     <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//       <div className="absolute inset-0 overflow-hidden">
//         {[...Array(20)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-2 h-2 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-30 animate-float"
//             style={{
//               top: `${Math.random() * 100}%`,
//               left: `${Math.random() * 100}%`,
//               animationDelay: `${i * 0.5}s`,
//               animationDuration: `${4 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <CircularOptionDisplay
//         option={currentOption}
//         timeRemaining={currentOptionTimer}
//         totalTime={DURATION_PER_OPTION_S}
//         isActive={!!currentOption && currentOptionTimer !== null}
//       />
//       <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />
//       <HandRaiseNotification logs={liveFeedLogs} />
//       <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col shadow-2xl">
//         <div className="flex justify-between items-center p-8 border-b border-gray-200/50 flex-shrink-0 bg-gradient-to-r from-blue-100/50 via-purple-100/50 to-pink-100/50">
//           <div className="flex items-center gap-6">
//             <div className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-all duration-300">
//               <Target className="text-white" size={32} />
//             </div>
//             <div>
//               <h2 className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
//                 Live Quiz Experience
//               </h2>
//               <p className="text-gray-600 text-lg font-medium">Real-time engagement monitoring</p>
//             </div>
//           </div>
//           <button
//             onClick={onClose}
//             className="text-gray-500 hover:text-red-500 transition-all duration-300 p-3 rounded-full hover:bg-red-50 hover:scale-110 transform"
//           >
//             <X size={28} />
//           </button>
//         </div>
//         <div className="flex-1 overflow-y-auto p-8 space-y-8">
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 socketStatus === 'Connected'
//                   ? 'bg-gradient-to-r from-green-100 to-emerald-100 border-green-300 text-green-800 shadow-green-200/50'
//                   : 'bg-gradient-to-r from-red-100 to-pink-100 border-red-300 text-red-800 shadow-red-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${socketStatus === 'Connected' ? 'bg-green-200' : 'bg-red-200'}`}
//                 >
//                   {socketStatus === 'Connected' ? <Wifi size={24} /> : <WifiOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Socket Status</div>
//                   <div className="text-sm opacity-80 font-medium">{socketStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 cameraStatus === 'Active'
//                   ? 'bg-gradient-to-r from-blue-100 to-cyan-100 border-blue-300 text-blue-800 shadow-blue-200/50'
//                   : 'bg-gradient-to-r from-yellow-100 to-orange-100 border-yellow-300 text-yellow-800 shadow-yellow-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${cameraStatus === 'Active' ? 'bg-blue-200' : 'bg-yellow-200'}`}
//                 >
//                   {cameraStatus === 'Active' ? <Video size={24} /> : <VideoOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Camera Status</div>
//                   <div className="text-sm opacity-80 font-medium">{cameraStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div className="p-6 rounded-2xl border-2 bg-gradient-to-r from-purple-100 to-pink-100 border-purple-300 text-purple-800 shadow-xl shadow-purple-200/50 transition-all duration-500 transform hover:scale-105">
//               <div className="flex items-center gap-4">
//                 <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black text-xl shadow-lg">
//                   {questionCount}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Question Progress</div>
//                   <div className="text-sm opacity-80 font-medium">Question {questionCount}</div>
//                 </div>
//               </div>
//             </div>
//           </div>
//           {socketRef.current && (
//             <button
//               onClick={testSocketConnection}
//               className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl transform hover:scale-105"
//             >
//               Test Socket Connection
//             </button>
//           )}
//           {error && (
//             <div className="text-center text-red-600 p-8 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 animate-pulse shadow-xl">
//               <div className="text-6xl mb-4">⚠️</div>
//               <p className="text-xl font-bold">{error}</p>
//             </div>
//           )}
//           {showManualInput && (
//             <div className="bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-8 border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//               <h3 className="text-3xl font-black text-gray-800 mb-6 flex items-center gap-4">
//                 <span className="text-4xl">🎯</span>
//                 Enter Quiz ID
//               </h3>
//               <div className="flex gap-6 items-end">
//                 <div className="flex-1">
//                   <label className="block text-lg font-bold text-gray-700 mb-3">Quiz ID</label>
//                   <input
//                     type="text"
//                     value={manualQuizId}
//                     onChange={(e) => setManualQuizId(e.target.value)}
//                     placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
//                     className="w-full px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
//                     onKeyDown={(e) => e.key === 'Enter' && handleManualQuizStart()}
//                   />
//                 </div>
//                 <button
//                   onClick={handleManualQuizStart}
//                   disabled={!manualQuizId.trim() || isStartingQuiz}
//                   className="px-10 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-teal-500 hover:from-purple-600 hover:via-blue-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 text-lg"
//                 >
//                   {isStartingQuiz ? 'Starting...' : 'Start Quiz'}
//                 </button>
//               </div>
//               <p className="text-gray-600 text-lg mt-4 font-medium">
//                 Enter the Quiz ID provided by your teacher to start the quiz.
//               </p>
//             </div>
//           )}
//           {!showManualInput && questionData && (
//             <div>
//               <button
//                 onClick={() => {
//                   setShowManualInput(true);
//                   setQuestionData(null);
//                   setQuizId(null);
//                   setError(null);
//                 }}
//                 className="px-6 py-3 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white rounded-xl text-lg font-medium transition-all duration-300 backdrop-blur-sm shadow-lg transform hover:scale-105"
//               >
//                 Enter Different Quiz ID
//               </button>
//             </div>
//           )}
//           {questionData && (
//             <div className="text-gray-800 space-y-8">
//               <div className="status-bar p-6 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-lg">
//                   <div className="font-bold">
//                     <strong>Quiz ID:</strong> <span className="text-blue-600">{quizId}</span>
//                   </div>
//                   <div className="font-bold">
//                     <strong>Status:</strong> <span className="text-purple-600">{quizStatus}</span>
//                   </div>
//                 </div>
//               </div>
//               <div className="flex flex-col lg:flex-row gap-8">
//                 <div className="flex-1">
//                   <div className="relative rounded-2xl overflow-hidden border-4 border-white/50 bg-gradient-to-br from-gray-100 to-gray-200 backdrop-blur-sm shadow-2xl">
//                     <video
//                       ref={videoRef}
//                       autoPlay
//                       playsInline
//                       muted
//                       className="w-full max-w-2xl rounded-2xl"
//                     />
//                     <div
//                       className={`absolute top-6 left-6 px-4 py-3 rounded-xl text-lg font-bold backdrop-blur-sm shadow-lg ${
//                         cameraStatus === 'Active'
//                           ? 'bg-green-500/90 text-white'
//                           : cameraStatus === 'Error'
//                             ? 'bg-red-500/90 text-white'
//                             : 'bg-yellow-500/90 text-white'
//                       }`}
//                     >
//                       📹 Camera: {cameraStatus}
//                     </div>
//                   </div>
//                   <canvas ref={canvasRef} className="hidden" />
//                 </div>
//                 {liveFeedLogs.length > 0 && (
//                   <div className="w-full lg:w-80 bg-gradient-to-b from-white/90 to-blue-50/90 rounded-2xl p-6 border-2 border-blue-200 max-h-96 overflow-y-auto backdrop-blur-sm shadow-xl">
//                     <h3 className="text-xl font-black mb-4 text-gray-800 flex items-center gap-3">
//                       <Users className="text-blue-500" size={24} />
//                       Live Hand Raises ({liveFeedLogs.length})
//                     </h3>
//                     <div className="space-y-3">
//                       {liveFeedLogs
//                         .sort(
//                           (a, b) =>
//                             new Date(a.detection_timestamp) - new Date(b.detection_timestamp)
//                         )
//                         .map((log, idx) => (
//                           <div
//                             key={idx}
//                             className="bg-gradient-to-r from-white/80 to-blue-50/80 p-4 rounded-xl text-sm backdrop-blur-sm transform hover:scale-105 transition-all duration-300 shadow-lg border border-blue-200"
//                           >
//                             <div className="flex justify-between items-start mb-2">
//                               <span className="font-bold text-gray-800 text-lg">
//                                 #{idx + 1} {log.student_name}
//                               </span>
//                               <span className="text-sm text-gray-600 bg-blue-100 px-3 py-1 rounded-full font-medium">
//                                 {log.responseTime}s
//                               </span>
//                             </div>
//                             <div className="flex justify-between items-center">
//                               <span className="text-blue-600 font-bold text-lg">
//                                 Option {log.option.toUpperCase()}
//                               </span>
//                             </div>
//                           </div>
//                         ))}
//                     </div>
//                   </div>
//                 )}
//               </div>
//               <div className="space-y-6">
//                 <h2 className="text-3xl font-black bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent">
//                   {questionData.sub_topic_name}
//                 </h2>
//                 <div className="bg-gradient-to-r from-white/80 to-purple-50/80 p-8 rounded-2xl border-2 border-purple-200 backdrop-blur-sm shadow-xl">
//                   <p className="text-2xl text-gray-800 leading-relaxed font-medium">
//                     {questionData.question_number}. {questionData.question}
//                   </p>
//                 </div>
//                 {questionData.question_image && questionData.question_image.trim() && (
//                   <div className="bg-gradient-to-r from-white/80 to-blue-50/80 p-6 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                     <img
//                       src={questionData.question_image.trim()}
//                       alt="Question Image"
//                       className="max-w-full h-auto rounded-xl shadow-lg border-2 border-white/50"
//                       onError={(e) => {
//                         console.error('Failed to load question image:', e.target.src);
//                         if (e.target.src.includes('?X-Amz-')) {
//                           const baseUrl = e.target.src.split('?X-Amz-')[0];
//                           console.log('Trying base URL:', baseUrl);
//                           e.target.src = baseUrl;
//                         } else {
//                           e.target.style.display = 'none';
//                         }
//                       }}
//                       onLoad={() => {
//                         console.log(
//                           'Question image loaded successfully:',
//                           questionData.question_image
//                         );
//                       }}
//                     />
//                   </div>
//                 )}
//                 <div className="grid gap-4">
//                   {questionData.options.map((opt, idx) => {
//                     const optionLetter = String.fromCharCode(97 + idx);
//                     const isCurrentOption = currentOption.toLowerCase() === optionLetter;

//                     return (
//                       <div key={idx} className="relative">
//                         <div
//                           className={`p-6 rounded-2xl border-2 transition-all duration-500 transform ${
//                             isCurrentOption
//                               ? 'bg-gradient-to-r from-green-100 via-emerald-100 to-teal-100 border-green-400 shadow-2xl scale-105 shadow-green-300/50'
//                               : 'bg-gradient-to-r from-white/80 to-gray-50/80 border-gray-300 hover:bg-gray-100/80 hover:scale-102 shadow-lg'
//                           } backdrop-blur-sm`}
//                         >
//                           <span className="font-bold text-gray-800 text-xl">
//                             {optionLetter.toUpperCase()}. {opt}
//                           </span>
//                         </div>
//                         {isCurrentOption && currentOptionTimer !== null && (
//                           <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-lg font-black animate-pulse shadow-xl">
//                             {currentOptionTimer}s
//                           </div>
//                         )}
//                       </div>
//                     );
//                   })}
//                 </div>
//               </div>
//             </div>
//           )}
//         </div>
//         {questionData && (
//           <div className="border-t border-gray-200/50 p-8 flex-shrink-0 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm">
//             <div className="flex flex-col sm:flex-row gap-6 justify-between items-center">
//               <div className="text-lg text-gray-700 flex items-center gap-4 font-medium">
//                 <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black">
//                   {questionCount}
//                 </div>
//                 Question {questionCount} • Quiz ID:{' '}
//                 <span className="text-blue-600 font-bold">{quizId}</span>
//               </div>
//               <div className="flex gap-4">
//                 {questionCount >= 5 && (
//                   <button
//                     onClick={() => setShowDashboard(true)}
//                     className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-600 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     <BarChart3 size={24} />
//                     View Dashboard
//                   </button>
//                 )}
//                 <button
//                   onClick={() => {
//                     initializeAudio();
//                     handleNextQuestion();
//                   }}
//                   className="px-10 py-4 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-black shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 disabled:transform-none text-lg"
//                   disabled={
//                     isFetchingNextQuestion ||
//                     quizStatus.includes('Capturing') ||
//                     quizStatus.includes('Processing') ||
//                     isReadingQuestion
//                   }
//                 >
//                   {isFetchingNextQuestion ? 'Processing...' : 'Start Capture & Next Question'}
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//       <audio ref={applauseAudioRef} preload="auto">
//         <source src={applauseAudio} type="audio/mpeg" />
//       </audio>
//       <audio ref={clockTickingAudioRef} preload="auto">
//         <source src={clockTickingAudio} type="audio/mpeg" />
//       </audio>
//       <style jsx>{`
//         @keyframes slideInRight {
//           from {
//             transform: translateX(100%);
//             opacity: 0;
//           }
//           to {
//             transform: translateX(0);
//             opacity: 1;
//           }
//         }
//         @keyframes float {
//           0%,
//           100% {
//             transform: translateY(0px) rotate(0deg);
//           }
//           50% {
//             transform: translateY(-20px) rotate(180deg);
//           }
//         }
//         @keyframes spin-slow {
//           from {
//             transform: rotate(0deg);
//           }
//           to {
//             transform: rotate(360deg);
//           }
//         }
//         @keyframes spin-reverse {
//           from {
//             transform: rotate(360deg);
//           }
//           to {
//             transform: rotate(0deg);
//           }
//         }
//         .animate-slideInRight {
//           animation: slideInRight 0.6s ease-out;
//         }
//         .animate-float {
//           animation: float 4s ease-in-out infinite;
//         }
//         .animate-spin-slow {
//           animation: spin-slow 8s linear infinite;
//         }
//         .animate-spin-reverse {
//           animation: spin-reverse 6s linear infinite;
//         }
//       `}</style>
//     </div>
//   );
// };

// export default LiveQuiz;


// import React, { useEffect, useRef, useState, useCallback } from 'react';
// import io from 'socket.io-client';
// import {
//   X,
//   BarChart3,
//   Video,
//   VideoOff,
//   Wifi,
//   WifiOff,
//   Clock,
//   Users,
//   Target,
//   Trophy,
//   Medal,
//   Award,
//   ChevronDown,
//   ChevronUp
// } from 'lucide-react';
// import { useStartQuizMutation, useNextQuestionMutation, useFinalizeSessionMutation } from './centerTraineeLive.slice';
// import applauseAudio from '../../../../assets/audio/applause-180037.mp3';
// import clockTickingAudio from '../../../../assets/audio/clock-ticking-down-376897.mp3';

// // API Configuration
// const CAPTURE_INTERVAL_MS = 200;
// const DURATION_PER_OPTION_S = 10;
// const QUESTION_READING_TIME_S = 15;

// // Enhanced Circular Option Display Component with Animated Countdown
// const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
//   const circumference = 2 * Math.PI * 120;
//   const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
//   const strokeDasharray = circumference;
//   const strokeDashoffset = circumference - progress;
//   const getTimerColor = () => {
//     const percentage = timeRemaining / totalTime;
//     if (percentage > 0.6) return '#10B981';
//     if (percentage > 0.3) return '#F59E0B';
//     return '#EF4444';
//   };
//   const getScale = () => {
//     const percentage = timeRemaining / totalTime;
//     return 1 + (1 - percentage) * 0.2;
//   };
//   if (!isActive) return null;
//   return (
//     <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
//       <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />
//       <div className="absolute inset-0">
//         {[...Array(12)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
//             style={{
//               top: `${10 + i * 7}%`,
//               left: `${5 + i * 8}%`,
//               animationDelay: `${i * 0.3}s`,
//               animationDuration: `${3 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <div
//         className="relative transition-all duration-300 ease-out"
//         style={{ transform: `scale(${getScale()})` }}
//       >
//         <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />
//         <div className="relative w-64 h-64 flex items-center justify-center">
//           <svg
//             className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
//             viewBox="0 0 260 260"
//           >
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.3)"
//               strokeWidth="12"
//               className="drop-shadow-lg"
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke={getTimerColor()}
//               strokeWidth="12"
//               strokeLinecap="round"
//               strokeDasharray={strokeDasharray}
//               strokeDashoffset={strokeDashoffset}
//               className="transition-all duration-1000 ease-out drop-shadow-xl"
//               style={{
//                 filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
//               }}
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="100"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.2)"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               className="animate-spin-reverse"
//             />
//             <defs>
//               <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
//                 <stop offset="0%" stopColor="#3B82F6" />
//                 <stop offset="50%" stopColor="#8B5CF6" />
//                 <stop offset="100%" stopColor="#EC4899" />
//               </linearGradient>
//               <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
//                 <stop offset="0%" stopColor="#FFFFFF" />
//                 <stop offset="100%" stopColor="#F8FAFC" />
//               </radialGradient>
//             </defs>
//           </svg>
//           <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
//             <div
//               className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
//                 textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
//               }}
//             >
//               {option}
//             </div>
//             <div
//               className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
//                 filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
//               }}
//             >
//               {timeRemaining}s
//             </div>
//             <div className="flex gap-1 mt-2">
//               {[...Array(totalTime)].map((_, i) => (
//                 <div
//                   key={i}
//                   className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
//                     i < totalTime - timeRemaining
//                       ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
//                       : 'bg-gray-300 scale-100'
//                   }`}
//                 />
//               ))}
//             </div>
//           </div>
//           <div className="absolute inset-0 pointer-events-none">
//             {[...Array(8)].map((_, i) => (
//               <div
//                 key={i}
//                 className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
//                 style={{
//                   top: `${15 + i * 10}%`,
//                   left: `${10 + i * 12}%`,
//                   animationDelay: `${i * 0.3}s`,
//                   animationDuration: `${2 + (i % 2)}s`
//                 }}
//               />
//             ))}
//           </div>
//           <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Reading Timer Component
// const ReadingTimer = ({ timeRemaining, isActive }) => {
//   if (!isActive) return null;
//   return (
//     <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
//       <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
//         <div className="text-center">
//           <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
//             <Clock className="animate-spin" size={32} />
//             Reading Time
//           </div>
//           <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
//           <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Hand Raise Animation Component
// const HandRaiseNotification = ({ logs }) => {
//   const [showNotification, setShowNotification] = useState(false);
//   const [latestLog, setLatestLog] = useState(null);
//   useEffect(() => {
//     if (logs.length > 0) {
//       const newest = logs[logs.length - 1];
//       setLatestLog(newest);
//       setShowNotification(true);
//       const timer = setTimeout(() => {
//         setShowNotification(false);
//       }, 4000);
//       return () => clearTimeout(timer);
//     }
//   }, [logs]);
//   if (!showNotification || !latestLog) return null;
//   return (
//     <div className="fixed top-24 right-8 z-50 transform animate-slideInRight">
//       <div className="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 text-white px-8 py-6 rounded-2xl shadow-2xl border-4 border-white/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-300">
//         <div className="flex items-center gap-4">
//           <div className="text-4xl animate-bounce">🙋‍♂️</div>
//           <div>
//             <div className="font-bold text-xl drop-shadow-sm">{latestLog.student_name}</div>
//             <div className="text-sm opacity-90 bg-white/20 px-3 py-1 rounded-full mt-1">
//               Option {latestLog.option ? latestLog.option.toUpperCase() : 'N/A'} • {latestLog.responseTime}s
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// const LiveQuiz = ({ quizId: inputQuizId, sessionId: inputSessionId, onClose }) => {
//   const [quizId, setQuizId] = useState(inputQuizId);
//   const [sessionId, setSessionId] = useState(inputSessionId || '');
//   const [clientId, setClientId] = useState('');
//   const [centerCode, setCenterCode] = useState(null);
//   const [questionData, setQuestionData] = useState(null);
//   const [quizStatus, setQuizStatus] = useState('');
//   const [summary, setSummary] = useState(null);
//   const [finalResults, setFinalResults] = useState(null);
//   const [liveFeedLogs, setLiveFeedLogs] = useState([]);
//   const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
//   const [socketStatus, setSocketStatus] = useState('Disconnected');
//   const [error, setError] = useState(null);
//   const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
//   const [currentOption, setCurrentOption] = useState('');
//   const [questionTimer, setQuestionTimer] = useState(null);
//   const [isReadingQuestion, setIsReadingQuestion] = useState(false);
//   const [showDashboard, setShowDashboard] = useState(false);
//   const [questionCount, setQuestionCount] = useState(0);
//   const [cameraStatus, setCameraStatus] = useState('Not initialized');
//   const [showManualInput, setShowManualInput] = useState(!inputQuizId);
//   const [manualQuizId, setManualQuizId] = useState('');
//   const [manualSessionId, setManualSessionId] = useState('');
//   const [startQuizMutation, { isLoading: isStartingQuiz }] = useStartQuizMutation();
//   const [nextQuestionMutation, { isLoading: isFetchingNextQuestion }] = useNextQuestionMutation();
//   const [finalizeSessionMutation] = useFinalizeSessionMutation();
//   const [dashboardData, setDashboardData] = useState(null);
//   const [isDashboardLoading, setIsDashboardLoading] = useState(false);
  
//   const videoRef = useRef(null);
//   const canvasRef = useRef(null);
//   const socketRef = useRef(null);
//   const videoStreamRef = useRef(null);
//   const timerIntervalRef = useRef(null);
//   const applauseAudioRef = useRef(null);
//   const clockTickingAudioRef = useRef(null);
  
//   // Track if socket has been initialized
//   const [isSocketInitialized, setIsSocketInitialized] = useState(false);
  
//   // Get center code from session storage
//   useEffect(() => {
//     const storedCenterCode = sessionStorage.getItem('centercode');
//     if (storedCenterCode && !centerCode) {
//       setCenterCode(storedCenterCode);
//       console.log(`🔒 Center code retrieved from session storage: ${storedCenterCode}`);
//     }
//   }, [centerCode]);
  
//   useEffect(() => {
//     if (quizId && videoRef.current && !isSocketInitialized) {
//       console.log('🎯 QuizId changed, initializing camera:', quizId);
//       initCamera();
//     }
//   }, [quizId, isSocketInitialized]);
  
//   // Cleanup function - now only disconnects socket when component unmounts
//   useEffect(() => {
//     return () => {
//       stopCamera();
//       if (socketRef.current) {
//         socketRef.current.disconnect();
//         socketRef.current = null;
//       }
//       if (timerIntervalRef.current) {
//         clearInterval(timerIntervalRef.current);
//       }
//       if (applauseAudioRef.current) {
//         applauseAudioRef.current.pause();
//         applauseAudioRef.current.currentTime = 0;
//       }
//       if (clockTickingAudioRef.current) {
//         clockTickingAudioRef.current.pause();
//         clockTickingAudioRef.current.currentTime = 0;
//       }
//     };
//   }, []);
  
//   // Initialize socket connection ONCE when component mounts
//   useEffect(() => {
//     const initSocket = () => {
//       if (socketRef.current || isSocketInitialized) return;
      
//       // Dynamically construct the Socket.IO URL based on the current environment
//       const socketUrl = `${window.location.protocol}//${window.location.hostname}:8036`;
//       console.log(`🔌 Initializing socket connection to: ${socketUrl}`);
      
//       socketRef.current = io(socketUrl, {
//         path: '/socketio2/socket.io',
//         transports: ['websocket', 'polling'],
//         upgrade: true,
//         rememberUpgrade: true,
//         timeout: 20000,
//         forceNew: false, // Critical change - don't force new connection
//         autoConnect: true,
//         reconnection: true,
//         reconnectionAttempts: 10, // Increased attempts
//         reconnectionDelay: 1000,
//         reconnectionDelayMax: 5000,
//         randomizationFactor: 0.5
//       });
      
//       // Set up socket event listeners
//       socketRef.current.on('connect', () => {
//         setSocketStatus('Connected');
//         console.log(`✅ Connected to Socket.IO server at ${socketUrl}`);
//       });
      
//       socketRef.current.on('disconnect', (reason) => {
//         setSocketStatus(`Disconnected: ${reason}`);
//         console.log(`❌ Socket disconnected: ${reason}`);
        
//         // Only try to reconnect if it wasn't intentional
//         if (reason !== 'io client disconnect') {
//           setTimeout(() => {
//             if (socketRef.current && !socketRef.current.connected) {
//               console.log('🔄 Attempting to reconnect...');
//               socketRef.current.connect();
//             }
//           }, 2000);
//         }
//       });
      
//       socketRef.current.on('connect_error', (error) => {
//         setSocketStatus(`Connection Error: ${error.message}`);
//         console.error('❌ Socket.IO connection error:', error);
//       });
      
//       socketRef.current.on('reconnect', (attemptNumber) => {
//         setSocketStatus(`Reconnected (attempt ${attemptNumber})`);
//         console.log(`🔄 Reconnected to Socket.IO server. Attempt: ${attemptNumber}`);
        
//         // If we have session info, rejoin the room
//         if (sessionId && clientId && centerCode) {
//           console.log(`🚪 Rejoining room with session=${sessionId}, client=${clientId}, center=${centerCode}`);
//           socketRef.current.emit('join_quiz_room', { 
//             session_id: sessionId, 
//             client_id: clientId,
//             center_code: centerCode
//           });
//         }
//       });
      
//       socketRef.current.on('reconnect_attempt', (attempt) => {
//         setSocketStatus(`Reconnecting... (attempt ${attempt})`);
//         console.log(`🔄 Reconnection attempt #${attempt}`);
//       });
      
//       socketRef.current.on('reconnect_error', (error) => {
//         console.error('❌ Socket.IO reconnection error:', error);
//       });
      
//       socketRef.current.on('reconnect_failed', () => {
//         setSocketStatus('Reconnection Failed');
//         console.error('❌ Socket.IO reconnection failed');
//       });
      
//       socketRef.current.on('hand_raised', (data) => {
//         console.log('🙋 Hand raised event received:', data);
//         setLiveFeedLogs((logs) => {
//           const newLog = {
//             ...data,
//             responseTime: currentQuestionStartTime
//               ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000)
//               : 0
//           };
//           return [...logs, newLog];
//         });
//       });
      
//       socketRef.current.on('center_identified', (data) => {
//         if (data.center_code && !centerCode) {
//           setCenterCode(data.center_code);
//           console.log(`🔒 This client is now locked to Center: ${data.center_code}`);
//         }
//       });
      
//       socketRef.current.onAny((eventName, ...args) => {
//         console.log(`📡 Socket event received: ${eventName}`, args);
//       });
      
//       setIsSocketInitialized(true);
//       return socketRef.current;
//     };
    
//     const socket = initSocket();
//     return () => {
//       if (socket) {
//         socket.disconnect();
//       }
//     };
//   }, []);
  
//   const generateUUID = () => crypto.randomUUID();
  
//   const startQuiz = async () => {
//     if (!quizId) {
//       setError('Missing Quiz ID');
//       return;
//     }
//     // Get center code from session storage
//     const storedCenterCode = sessionStorage.getItem('centercode');
//     if (!storedCenterCode) {
//       setError('Center code not found. Please log in again.');
//       return;
//     }
//     let currentSessionId = sessionId;
//     if (!currentSessionId) {
//       currentSessionId = generateUUID();
//       setSessionId(currentSessionId);
//     }
//     const currentClientId = generateUUID();
//     setClientId(currentClientId);
    
//     try {
//       setQuizStatus('Starting quiz...');
//       // Include center code in the request
//       const response = await startQuizMutation({ 
//         quiz_id: quizId, 
//         session_id: currentSessionId, 
//         client_id: currentClientId,
//         center_code: storedCenterCode
//       }).unwrap();
//       if (response.error) {
//         throw new Error(response.error);
//       }
      
//       // Only join the room after the API call succeeds
//       if (socketRef.current && isSocketInitialized) {
//         console.log(`🚪 Joining quiz room with session=${currentSessionId}, client=${currentClientId}, center=${storedCenterCode}`);
//         socketRef.current.emit('join_quiz_room', { 
//           session_id: currentSessionId, 
//           client_id: currentClientId,
//           center_code: storedCenterCode
//         });
//       }
      
//       setQuestionData(response);
//       setQuizStatus('Ready to start capture for this question.');
//       setQuestionCount(1);
//       console.log('🎯 Starting quiz, initializing camera immediately');
//       await initCamera();
//       initializeAudio();
//     } catch (error) {
//       setError(`Error starting quiz: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   const handleNextQuestion = async () => {
//     if (!sessionId || !clientId || !centerCode) return;
//     try {
//       await startCaptureCycle();
//       setQuizStatus('Processing results and fetching next question...');
//       // Include center code in the request
//       const response = await nextQuestionMutation({ 
//         session_id: sessionId, 
//         client_id: clientId,
//         center_code: centerCode
//       }).unwrap();
//       if (response.status === 'completed') {
//         setFinalResults(response);
//         stopCamera();
//         if (socketRef.current) {
//           socketRef.current.disconnect();
//           socketRef.current = null;
//         }
//         setQuizStatus('Quiz completed! View results for details.');
//         if (applauseAudioRef.current) {
//           console.log('🎉 Playing applause sound for quiz completion');
//           applauseAudioRef.current.currentTime = 0;
//           applauseAudioRef.current.volume = 0.8;
//           applauseAudioRef.current.play()
//             .then(() => {
//               console.log('✅ Applause audio started successfully');
//             })
//             .catch((error) => {
//               console.error('❌ Error playing applause audio:', error);
//             });
//           setTimeout(() => {
//             if (applauseAudioRef.current) {
//               console.log('⏹️ Stopping applause audio after 4 seconds');
//               applauseAudioRef.current.pause();
//               applauseAudioRef.current.currentTime = 0;
//             }
//           }, 4000);
//         }
//       } else if (response.question) {
//         setQuestionData(response);
//         setQuizStatus('Ready to start capture for this question.');
//         setQuestionCount((prev) => prev + 1);
//       }
//     } catch (error) {
//       setError(`Error during quiz progression: ${error.message || error.data?.error || 'Unknown error'}`);
//       setQuizStatus(`Error: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   const handleFinalize = async () => {
//     if (!sessionId) return;
//     if (!window.confirm('Are you sure you want to end this session for ALL centers?')) return;
//     try {
//       const response = await finalizeSessionMutation({ session_id: sessionId }).unwrap();
//       console.log('Finalize response:', response);
//       setShowDashboard(true);
//     } catch (error) {
//       setError(`Error finalizing session: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   const handleShowResults = async () => {
//     if (!sessionId || !centerCode) return;
//     setIsDashboardLoading(true);
//     try {
//       const response = await fetch(`http://localhost:8036/api/content/engagement_dashboard?session_id=${sessionId}&center_code=${centerCode}`);
//       if (!response.ok) {
//         throw new Error('Failed to fetch dashboard data');
//       }
//       const data = await response.json();
//       if (data.success === false) {
//         throw new Error(data.error || 'Unknown error');
//       }
//       setDashboardData(data);
//       setShowDashboard(true);
//     } catch (err) {
//       setError(`Error fetching results: ${err.message}`);
//     } finally {
//       setIsDashboardLoading(false);
//     }
//   };
  
//   const startCaptureCycle = async () => {
//     setLiveFeedLogs([]);
//     setIsReadingQuestion(true);
//     setQuestionTimer(QUESTION_READING_TIME_S);
//     setCurrentQuestionStartTime(Date.now());
//     timerIntervalRef.current = setInterval(() => {
//       setQuestionTimer((prev) => {
//         if (prev <= 1) {
//           clearInterval(timerIntervalRef.current);
//           return 0;
//         }
//         return prev - 1;
//       });
//     }, 1000);
//     await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));
//     setIsReadingQuestion(false);
//     setQuestionTimer(null);
//     const options = ['a', 'b', 'c', 'd'];
//     for (let i = 0; i < options.length; i++) {
//       setQuizStatus(
//         `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
//       );
//       await processSingleOption(options[i]);
//     }
//     setQuizStatus('Capture complete for this question. Results sent.');
//     setCurrentOptionTimer(null);
//     setCurrentOption('');
//   };
  
//   const processSingleOption = (optionChar) => {
//     return new Promise((resolve) => {
//       const startTime = Date.now();
//       let frameCount = 0;
//       let clockAudioTimeout = null;
//       setCurrentOption(optionChar.toUpperCase());
//       setCurrentOptionTimer(DURATION_PER_OPTION_S);
//       if (clockTickingAudioRef.current) {
//         console.log(`🔊 Playing clock ticking sound for option ${optionChar.toUpperCase()}`);
//         clockTickingAudioRef.current.currentTime = 0;
//         clockTickingAudioRef.current.volume = 0.7;
//         clockTickingAudioRef.current.play()
//           .then(() => {
//             console.log('✅ Clock ticking audio started successfully');
//           })
//           .catch((error) => {
//             console.error('❌ Error playing clock ticking audio:', error);
//           });
//         clockAudioTimeout = setTimeout(() => {
//           if (clockTickingAudioRef.current) {
//             console.log('⏹️ Stopping clock ticking audio after 10 seconds');
//             clockTickingAudioRef.current.pause();
//             clockTickingAudioRef.current.currentTime = 0;
//           }
//         }, 10000);
//       }
//       timerIntervalRef.current = setInterval(() => {
//         const elapsed = Math.floor((Date.now() - startTime) / 1000);
//         const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
//         setCurrentOptionTimer(remaining);
//       }, 1000);
//       const intervalId = setInterval(() => {
//         const responseTime = (Date.now() - startTime) / 1000;
//         captureAndSendFrame(optionChar, responseTime);
//         frameCount++;
//       }, CAPTURE_INTERVAL_MS);
//       setTimeout(() => {
//         clearInterval(intervalId);
//         if (timerIntervalRef.current) {
//           clearInterval(timerIntervalRef.current);
//         }
//         if (clockAudioTimeout) {
//           clearTimeout(clockAudioTimeout);
//         }
//         if (clockTickingAudioRef.current) {
//           console.log(`⏹️ Stopping clock ticking audio for option ${optionChar.toUpperCase()}`);
//           clockTickingAudioRef.current.pause();
//           clockTickingAudioRef.current.currentTime = 0;
//         }
//         resolve();
//       }, DURATION_PER_OPTION_S * 1000);
//     });
//   };
  
//   const captureAndSendFrame = useCallback((optionChar, responseTime) => {
//     const video = videoRef.current;
//     const canvas = canvasRef.current;
//     if (!video || !canvas) {
//       console.warn('⚠️ No video or canvas element available');
//       return;
//     }
//     if (!videoStreamRef.current) {
//       console.warn('⚠️ No video stream available');
//       return;
//     }
//     if (!socketRef.current?.connected) {
//       console.warn('⚠️ Socket not connected - cannot send frame');
//       return;
//     }
//     const ctx = canvas.getContext('2d');
//     canvas.width = video.videoWidth;
//     canvas.height = video.videoHeight;
//     ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
//     const frameData = canvas.toDataURL('image/jpeg', 0.6);
//     console.log(
//       `📸 Sending frame: session=${sessionId}, option=${optionChar}, time=${responseTime.toFixed(2)}s`
//     );
//     socketRef.current.emit('process_frame', {
//       session_id: sessionId,
//       client_id: clientId,
//       frame: frameData,
//       option_char: optionChar,
//       response_time_seconds: responseTime
//     });
//   }, [sessionId, clientId]);
  
//   const initCamera = async () => {
//     if (videoStreamRef.current) return;
//     try {
//       console.log('🎥 Requesting camera access...');
//       setCameraStatus('Requesting access...');
//       const stream = await navigator.mediaDevices.getUserMedia({
//         video: {
//           width: { ideal: 1280 },
//           height: { ideal: 720 }
//         }
//       });
//       if (videoRef.current) {
//         videoRef.current.srcObject = stream;
//         videoStreamRef.current = stream;
//         console.log('✅ Camera initialized successfully');
//         setCameraStatus('Active');
//       }
//     } catch (error) {
//       console.error('❌ Camera error:', error);
//       setCameraStatus('Error');
//       setError(`Camera error: ${error.message}`);
//     }
//   };
  
//   const stopCamera = () => {
//     if (videoStreamRef.current) {
//       videoStreamRef.current.getTracks().forEach((track) => track.stop());
//       videoStreamRef.current = null;
//     }
//     if (videoRef.current) {
//       videoRef.current.srcObject = null;
//     }
//     setCameraStatus('Not initialized');
//   };
  
//   const initializeAudio = () => {
//     if (applauseAudioRef.current) {
//       applauseAudioRef.current.load();
//       console.log('🔊 Applause audio initialized');
//     }
//     if (clockTickingAudioRef.current) {
//       clockTickingAudioRef.current.load();
//       console.log('🔊 Clock ticking audio initialized');
//     }
//   };
  
//   const testSocketConnection = () => {
//     if (socketRef.current) {
//       socketRef.current.emit('test_connection', { message: 'Hello from client' });
//     }
//   };
  
//   const handleCloseDashboard = () => {
//     setShowDashboard(false);
//   };
  
//   const handleManualQuizStart = async () => {
//     if (manualQuizId.trim()) {
//       setError(null);
//       setShowManualInput(false);
//       setQuizId(manualQuizId.trim());
//       // Get center code from session storage
//       const storedCenterCode = sessionStorage.getItem('centercode');
//       if (!storedCenterCode) {
//         setError('Center code not found. Please log in again.');
//         setShowManualInput(true);
//         return;
//       }
//       let currentSessionId = manualSessionId.trim();
//       if (!currentSessionId) {
//         currentSessionId = generateUUID();
//         setManualSessionId(currentSessionId);
//         setSessionId(currentSessionId);
//       } else {
//         setSessionId(currentSessionId);
//       }
//       await startQuiz();
//     } else {
//       setError('Please enter a valid Quiz ID');
//     }
//   };
  
//   useEffect(() => {
//     if (inputQuizId) {
//       startQuiz();
//     }
//   }, [inputQuizId]);
  
//   if (showDashboard && sessionId && centerCode) {
//     if (isDashboardLoading) {
//       return (
//         <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//           <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col items-center justify-center p-16">
//             <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-6"></div>
//             <h2 className="text-3xl font-bold text-gray-800">Loading Results...</h2>
//             <p className="text-lg text-gray-600 mt-2">Analyzing quiz performance data</p>
//           </div>
//         </div>
//       );
//     }
//     if (!dashboardData) {
//       return (
//         <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//           <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-md p-12 text-center">
//             <div className="text-6xl mb-6">⚠️</div>
//             <h2 className="text-3xl font-bold text-gray-800 mb-4">Failed to Load Results</h2>
//             <p className="text-gray-600 mb-8">There was an error retrieving the quiz results. Please try again later.</p>
//             <button
//               onClick={() => setShowDashboard(false)}
//               className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-lg font-bold"
//             >
//               Return to Quiz
//             </button>
//           </div>
//         </div>
//       );
//     }
//     return (
//       <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//         <div className="bg-white/95 backdrop-blur-xl rounded-3xl border-4 border-white/20 w-full max-w-6xl max-h-[95vh] flex flex-col">
//           <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-8 text-white relative overflow-hidden">
//             <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 animate-pulse" />
//             <div className="relative z-10 flex justify-between items-center">
//               <div className="flex items-center gap-6">
//                 <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
//                   <BarChart3 className="text-yellow-400" size={40} />
//                 </div>
//                 <div>
//                   <h2 className="text-4xl font-black mb-2">📊 Quiz Results</h2>
//                   <p className="text-xl opacity-90">Engagement Dashboard</p>
//                 </div>
//               </div>
//               <button
//                 onClick={handleCloseDashboard}
//                 className="text-white/80 hover:text-white transition-all duration-300 p-3 rounded-full hover:bg-white/20 hover:scale-110 transform"
//               >
//                 <X size={32} />
//               </button>
//             </div>
//           </div>
//           <div className="p-6 bg-gradient-to-r from-slate-800 to-slate-700 border-b border-white/10">
//             <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-white">
//               <div className="text-center">
//                 <div className="text-3xl font-black text-blue-400">
//                   {dashboardData?.total_questions_in_quiz || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Total Questions</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-green-400">
//                   {dashboardData?.engagement_summary_stats?.active_students || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Active Students</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-purple-400">
//                   {dashboardData?.engagement_summary_stats?.total_participations || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Total Participations</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-yellow-400">
//                   {dashboardData?.engagement_summary_stats?.average_frequency?.toFixed(1) || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Avg Frequency</div>
//               </div>
//             </div>
//           </div>
//           <div className="flex-1 overflow-y-auto p-8 space-y-4 bg-gradient-to-b from-slate-800 to-slate-900">
//             <h3 className="text-2xl font-black text-white mb-6 flex items-center gap-3">
//               <Trophy className="text-yellow-400" size={28} />
//               Top Performers
//             </h3>
//             <div className="space-y-3">
//               {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
//                 dashboardData.leaderboard.map((student, index) => (
//                   <div
//                     key={index}
//                     className="transform transition-all duration-300 hover:scale-[1.02] bg-gradient-to-r from-slate-700 to-slate-800 p-6 rounded-2xl shadow-xl border-2 border-white/20"
//                   >
//                     <div className="flex items-center justify-between text-white">
//                       <div className="flex items-center gap-6">
//                         <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center text-white font-black text-xl">
//                           {index + 1}
//                         </div>
//                         <div className="flex-1">
//                           <div className="text-2xl font-black mb-1">{student.first_name} {student.last_name}</div>
//                           <div className="text-sm opacity-90 flex gap-4">
//                             <span>Score: {student.score}</span>
//                             <span>Accuracy: {student.accuracy_percentage}%</span>
//                             <span>Avg Time: {student.avg_response_time_seconds}s</span>
//                           </div>
//                         </div>
//                         <div className="text-right">
//                           <div className="text-4xl font-black text-yellow-400">#{index + 1}</div>
//                           <div className="text-xs opacity-80 bg-white/20 px-2 py-1 rounded-full">
//                             {student.questions_attempted} Questions
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   </div>
//                 ))
//               ) : (
//                 <div className="text-center py-12 bg-slate-800 rounded-xl">
//                   <div className="text-5xl mb-4">📊</div>
//                   <h3 className="text-2xl font-bold text-white mb-2">No Results Available</h3>
//                   <p className="text-gray-400">The quiz results are still being processed or no students participated.</p>
//                 </div>
//               )}
//             </div>
            
//             <h3 className="text-2xl font-black text-white mt-10 mb-6 flex items-center gap-3">
//               <Users className="text-blue-400" size={28} />
//               Student Performance
//             </h3>
            
//             <div className="bg-slate-800 rounded-xl overflow-hidden">
//               <div className="overflow-x-auto">
//                 <table className="min-w-full">
//                   <thead>
//                     <tr className="bg-slate-700">
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Rank</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Student Name</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Score</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Accuracy</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Questions Attempted</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Avg. Response Time</th>
//                     </tr>
//                   </thead>
//                   <tbody>
//                     {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
//                       dashboardData.leaderboard.map((student, index) => (
//                         <tr 
//                           key={index}
//                           className={`border-b border-slate-700 hover:bg-slate-700/50 transition-colors ${
//                             index % 2 === 0 ? 'bg-slate-800' : 'bg-slate-850'
//                           }`}
//                         >
//                           <td className="py-4 px-6">
//                             <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold">
//                               {index + 1}
//                             </span>
//                           </td>
//                           <td className="py-4 px-6 text-white font-medium">
//                             {student.first_name} {student.last_name}
//                           </td>
//                           <td className="py-4 px-6 text-green-400 font-bold">
//                             {student.score}
//                           </td>
//                           <td className="py-4 px-6 text-blue-300">
//                             {student.accuracy_percentage}%
//                           </td>
//                           <td className="py-4 px-6 text-purple-300">
//                             {student.questions_attempted}
//                           </td>
//                           <td className="py-4 px-6 text-cyan-300">
//                             {student.avg_response_time_seconds}s
//                           </td>
//                         </tr>
//                       ))
//                     ) : (
//                       <tr>
//                         <td colSpan="6" className="py-8 text-center text-gray-400">
//                           No student performance data available
//                         </td>
//                       </tr>
//                     )}
//                   </tbody>
//                 </table>
//               </div>
//             </div>
            
//             <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Target className="text-blue-400" size={24} />
//                   Question Performance
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Most Challenging Question</span>
//                     <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
//                       #{dashboardData?.most_challenging_question || 'N/A'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Easiest Question</span>
//                     <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
//                       #{dashboardData?.easiest_question || 'N/A'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Average Accuracy</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.average_accuracy?.toFixed(1) || '0.0'}%
//                     </span>
//                   </div>
//                 </div>
//               </div>
              
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Clock className="text-purple-400" size={24} />
//                   Response Analysis
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Fastest Response</span>
//                     <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
//                       {dashboardData?.fastest_response?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Slowest Response</span>
//                     <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
//                       {dashboardData?.slowest_response?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Average Response Time</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.avg_response_time?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                 </div>
//               </div>
              
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Users className="text-green-400" size={24} />
//                   Engagement Metrics
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Peak Engagement</span>
//                     <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">
//                       {dashboardData?.peak_engagement?.toFixed(1) || '0.0'}%
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Engagement Trend</span>
//                     <span className={`px-3 py-1 rounded-full ${
//                       dashboardData?.engagement_trend === 'increasing' 
//                         ? 'bg-green-500/20 text-green-300' 
//                         : dashboardData?.engagement_trend === 'decreasing' 
//                           ? 'bg-red-500/20 text-red-300' 
//                           : 'bg-yellow-500/20 text-yellow-300'
//                     }`}>
//                       {dashboardData?.engagement_trend || 'stable'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Active Students</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.engagement_summary_stats?.active_students || 0}
//                     </span>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>
//           <div className="p-6 bg-slate-900 border-t border-white/10">
//             <button
//               onClick={onClose}
//               className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-xl"
//             >
//               <X size={24} />
//               Close Results & Exit Quiz
//             </button>
//           </div>
//         </div>
//       </div>
//     );
//   }
  
//   return (
//     <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//       <div className="absolute inset-0 overflow-hidden">
//         {[...Array(20)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-2 h-2 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-30 animate-float"
//             style={{
//               top: `${Math.random() * 100}%`,
//               left: `${Math.random() * 100}%`,
//               animationDelay: `${i * 0.5}s`,
//               animationDuration: `${4 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <CircularOptionDisplay
//         option={currentOption}
//         timeRemaining={currentOptionTimer}
//         totalTime={DURATION_PER_OPTION_S}
//         isActive={!!currentOption && currentOptionTimer !== null}
//       />
//       <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />
//       <HandRaiseNotification logs={liveFeedLogs} />
//       <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col shadow-2xl">
//         <div className="flex justify-between items-center p-8 border-b border-gray-200/50 flex-shrink-0 bg-gradient-to-r from-blue-100/50 via-purple-100/50 to-pink-100/50">
//           <div className="flex items-center gap-6">
//             <div className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-all duration-300">
//               <Target className="text-white" size={32} />
//             </div>
//             <div>
//               <h2 className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
//                 Live Quiz Experience
//               </h2>
//               <p className="text-gray-600 text-lg font-medium">Real-time engagement monitoring</p>
//             </div>
//           </div>
//           <button
//             onClick={onClose}
//             className="text-gray-500 hover:text-red-500 transition-all duration-300 p-3 rounded-full hover:bg-red-50 hover:scale-105 transform"
//           >
//             <X size={28} />
//           </button>
//         </div>
//         <div className="flex-1 overflow-y-auto p-8 space-y-8">
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 socketStatus.startsWith('Connected')
//                   ? 'bg-gradient-to-r from-green-100 to-emerald-100 border-green-300 text-green-800 shadow-green-200/50'
//                   : 'bg-gradient-to-r from-red-100 to-pink-100 border-red-300 text-red-800 shadow-red-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${socketStatus.startsWith('Connected') ? 'bg-green-200' : 'bg-red-200'}`}
//                 >
//                   {socketStatus.startsWith('Connected') ? <Wifi size={24} /> : <WifiOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Socket Status</div>
//                   <div className="text-sm opacity-80 font-medium">{socketStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 cameraStatus === 'Active'
//                   ? 'bg-gradient-to-r from-blue-100 to-cyan-100 border-blue-300 text-blue-800 shadow-blue-200/50'
//                   : 'bg-gradient-to-r from-yellow-100 to-orange-100 border-yellow-300 text-yellow-800 shadow-yellow-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${cameraStatus === 'Active' ? 'bg-blue-200' : 'bg-yellow-200'}`}
//                 >
//                   {cameraStatus === 'Active' ? <Video size={24} /> : <VideoOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Camera Status</div>
//                   <div className="text-sm opacity-80 font-medium">{cameraStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div className="p-6 rounded-2xl border-2 bg-gradient-to-r from-purple-100 to-pink-100 border-purple-300 text-purple-800 shadow-xl shadow-purple-200/50 transition-all duration-500 transform hover:scale-105">
//               <div className="flex items-center gap-4">
//                 <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black text-xl shadow-lg">
//                   {questionCount}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Question Progress</div>
//                   <div className="text-sm opacity-80 font-medium">Question {questionCount}</div>
//                 </div>
//               </div>
//             </div>
//           </div>
//           {socketRef.current && (
//             <button
//               onClick={testSocketConnection}
//               className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl transform hover:scale-105"
//             >
//               Test Socket Connection
//             </button>
//           )}
//           {error && (
//             <div className="text-center text-red-600 p-8 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 animate-pulse shadow-xl">
//               <div className="text-6xl mb-4">⚠️</div>
//               <p className="text-xl font-bold">{error}</p>
//             </div>
//           )}
//           {showManualInput && (
//             <div className="bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-8 border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//               <h3 className="text-3xl font-black text-gray-800 mb-6 flex items-center gap-4">
//                 <span className="text-4xl">🎯</span>
//                 Enter Quiz Details
//               </h3>
//               <div className="flex flex-col gap-6">
//                 <div>
//                   <label className="block text-lg font-bold text-gray-700 mb-3">Quiz ID</label>
//                   <input
//                     type="text"
//                     value={manualQuizId}
//                     onChange={(e) => setManualQuizId(e.target.value)}
//                     placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
//                     className="w-full px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-lg font-bold text-gray-700 mb-3">Session ID (Generate or paste)</label>
//                   <div className="flex gap-4">
//                     <input
//                       type="text"
//                       value={manualSessionId}
//                       onChange={(e) => setManualSessionId(e.target.value)}
//                       placeholder="Session ID"
//                       className="flex-1 px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
//                     />
//                     <button
//                       onClick={() => setManualSessionId(generateUUID())}
//                       className="px-6 py-4 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 transform hover:scale-105"
//                     >
//                       Generate
//                     </button>
//                   </div>
//                 </div>
//                 <button
//                   onClick={handleManualQuizStart}
//                   disabled={!manualQuizId.trim() || isStartingQuiz}
//                   className="px-10 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-teal-500 hover:from-purple-600 hover:via-blue-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 text-lg"
//                 >
//                   {isStartingQuiz ? 'Starting...' : 'Start Quiz'}
//                 </button>
//               </div>
//               <p className="text-gray-600 text-lg mt-4 font-medium">
//                 Enter the Quiz ID provided by your teacher to start the quiz.
//               </p>
//             </div>
//           )}
//           {!showManualInput && questionData && (
//             <div>
//               <button
//                 onClick={() => {
//                   setShowManualInput(true);
//                   setQuestionData(null);
//                   setQuizId(null);
//                   setError(null);
//                 }}
//                 className="px-6 py-3 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white rounded-xl text-lg font-medium transition-all duration-300 backdrop-blur-sm shadow-lg transform hover:scale-105"
//               >
//                 Enter Different Quiz ID
//               </button>
//             </div>
//           )}
//           {questionData && (
//             <div className="text-gray-800 space-y-8">
//               <div className="status-bar p-6 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-lg">
//                   <div className="font-bold">
//                     <strong>Session ID:</strong> <span className="text-blue-600">{sessionId}</span>
//                   </div>
//                   <div className="font-bold">
//                     <strong>Status:</strong> <span className="text-purple-600">{quizStatus}</span>
//                   </div>
//                 </div>
//               </div>
//               <div className="flex flex-col lg:flex-row gap-8">
//                 <div className="flex-1">
//                   <div className="relative rounded-2xl overflow-hidden border-4 border-white/50 bg-gradient-to-br from-gray-100 to-gray-200 backdrop-blur-sm shadow-2xl">
//                     <video
//                       ref={videoRef}
//                       autoPlay
//                       playsInline
//                       muted
//                       className="w-full max-w-2xl rounded-2xl"
//                     />
//                     <div
//                       className={`absolute top-6 left-6 px-4 py-3 rounded-xl text-lg font-bold backdrop-blur-sm shadow-lg ${
//                         cameraStatus === 'Active'
//                           ? 'bg-green-500/90 text-white'
//                           : cameraStatus === 'Error'
//                             ? 'bg-red-500/90 text-white'
//                             : 'bg-yellow-500/90 text-white'
//                       }`}
//                     >
//                       📹 Camera: {cameraStatus}
//                     </div>
//                   </div>
//                   <canvas ref={canvasRef} className="hidden" />
//                 </div>
//                 {liveFeedLogs.length > 0 && (
//                   <div className="w-full lg:w-80 bg-gradient-to-b from-white/90 to-blue-50/90 rounded-2xl p-6 border-2 border-blue-200 max-h-96 overflow-y-auto backdrop-blur-sm shadow-xl">
//                     <h3 className="text-xl font-black mb-4 text-gray-800 flex items-center gap-3">
//                       <Users className="text-blue-500" size={24} />
//                       Live Hand Raises ({liveFeedLogs.length})
//                     </h3>
//                     <div className="space-y-3">
//                       {liveFeedLogs
//                         .sort(
//                           (a, b) =>
//                             new Date(a.detection_timestamp) - new Date(b.detection_timestamp)
//                         )
//                         .map((log, idx) => (
//                           <div
//                             key={idx}
//                             className="bg-gradient-to-r from-white/80 to-blue-50/80 p-4 rounded-xl text-sm backdrop-blur-sm transform hover:scale-105 transition-all duration-300 shadow-lg border border-blue-200"
//                           >
//                             <div className="flex justify-between items-start mb-2">
//                               <span className="font-bold text-gray-800 text-lg">
//                                 #{idx + 1} {log.student_name}
//                               </span>
//                               <span className="text-sm text-gray-600 bg-blue-100 px-3 py-1 rounded-full font-medium">
//                                 {log.responseTime}s
//                               </span>
//                             </div>
//                             <div className="flex justify-between items-center">
//                               <span className="text-blue-600 font-bold text-lg">
//                                 Option {log.option ? log.option.toUpperCase() : 'N/A'}
//                               </span>
//                             </div>
//                           </div>
//                         ))}
//                     </div>
//                   </div>
//                 )}
//               </div>
//               <div className="space-y-6">
//                 <h2 className="text-3xl font-black bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent">
//                   {questionData.sub_topic_name}
//                 </h2>
//                 <div className="bg-gradient-to-r from-white/80 to-purple-50/80 p-8 rounded-2xl border-2 border-purple-200 backdrop-blur-sm shadow-xl">
//                   <p className="text-2xl text-gray-800 leading-relaxed font-medium">
//                     {questionData.question_number}. {questionData.question}
//                   </p>
//                 </div>
//                 {questionData.question_image && questionData.question_image.trim() && (
//                   <div className="bg-gradient-to-r from-white/80 to-blue-50/80 p-6 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                     <img
//                       src={questionData.question_image.trim()}
//                       alt="Question Image"
//                       className="max-w-full h-auto rounded-xl shadow-lg border-2 border-white/50"
//                       onError={(e) => {
//                         console.error('Failed to load question image:', e.target.src);
//                         if (e.target.src.includes('?X-Amz-')) {
//                           const baseUrl = e.target.src.split('?X-Amz-')[0];
//                           console.log('Trying base URL:', baseUrl);
//                           e.target.src = baseUrl;
//                         } else {
//                           e.target.style.display = 'none';
//                         }
//                       }}
//                       onLoad={() => {
//                         console.log(
//                           'Question image loaded successfully:',
//                           questionData.question_image
//                         );
//                       }}
//                     />
//                   </div>
//                 )}
//                 <div className="grid gap-4">
//                   {questionData.options.map((opt, idx) => {
//                     const optionLetter = String.fromCharCode(97 + idx);
//                     const isCurrentOption = currentOption.toLowerCase() === optionLetter;
//                     return (
//                       <div key={idx} className="relative">
//                         <div
//                           className={`p-6 rounded-2xl border-2 transition-all duration-500 transform ${
//                             isCurrentOption
//                               ? 'bg-gradient-to-r from-green-100 via-emerald-100 to-teal-100 border-green-400 shadow-2xl scale-105 shadow-green-300/50'
//                               : 'bg-gradient-to-r from-white/80 to-gray-50/80 border-gray-300 hover:bg-gray-100/80 hover:scale-102 shadow-lg'
//                           } backdrop-blur-sm`}
//                         >
//                           <span className="font-bold text-gray-800 text-xl">
//                             {optionLetter.toUpperCase()}. {opt}
//                           </span>
//                           {questionData.option_images && questionData.option_images[idx] && (
//                             <img
//                               src={questionData.option_images[idx]}
//                               alt={`Option ${optionLetter.toUpperCase()}`}
//                               className="max-w-90% max-h-150 mt-10 rounded-4"
//                             />
//                           )}
//                         </div>
//                         {isCurrentOption && currentOptionTimer !== null && (
//                           <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-lg font-black animate-pulse shadow-xl">
//                             {currentOptionTimer}s
//                           </div>
//                         )}
//                       </div>
//                     );
//                   })}
//                 </div>
//               </div>
//             </div>
//           )}
//         </div>
//         {questionData && (
//           <div className="border-t border-gray-200/50 p-8 flex-shrink-0 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm">
//             <div className="flex flex-col sm:flex-row gap-6 justify-between items-center">
//               <div className="text-lg text-gray-700 flex items-center gap-4 font-medium">
//                 <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black">
//                   {questionCount}
//                 </div>
//                 Question {questionCount} • Session ID:{' '}
//                 <span className="text-blue-600 font-bold">{sessionId}</span>
//               </div>
//               <div className="flex gap-4">
//                 {questionCount >= 5 && (
//                   <button
//                     onClick={() => setShowDashboard(true)}
//                     className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     <BarChart3 size={24} />
//                     View Results
//                   </button>
//                 )}
//                 {finalResults ? (
//                   <button
//                     onClick={handleShowResults}
//                     className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     <BarChart3 size={24} />
//                     Show Results
//                   </button>
//                 ) : (
//                   <button
//                     onClick={handleFinalize}
//                     className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     Finalize Session (Admin Only)
//                   </button>
//                 )}
//                 <button
//                   onClick={() => {
//                     initializeAudio();
//                     handleNextQuestion();
//                   }}
//                   className="px-10 py-4 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-black shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 disabled:transform-none text-lg"
//                   disabled={
//                     isFetchingNextQuestion ||
//                     quizStatus.includes('Capturing') ||
//                     quizStatus.includes('Processing') ||
//                     isReadingQuestion
//                   }
//                 >
//                   {isFetchingNextQuestion ? 'Processing...' : 'Start Capture & Next Question'}
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//       <audio ref={applauseAudioRef} preload="auto">
//         <source src={applauseAudio} type="audio/mpeg" />
//       </audio>
//       <audio ref={clockTickingAudioRef} preload="auto">
//         <source src={clockTickingAudio} type="audio/mpeg" />
//       </audio>
//       <style jsx>{`
//         @keyframes slideInRight {
//           from {
//             transform: translateX(100%);
//             opacity: 0;
//           }
//           to {
//             transform: translateX(0);
//             opacity: 1;
//           }
//         }
//         @keyframes float {
//           0%,
//           100% {
//             transform: translateY(0px) rotate(0deg);
//           }
//           50% {
//             transform: translateY(-20px) rotate(180deg);
//           }
//         }
//         @keyframes spin-slow {
//           from {
//             transform: rotate(0deg);
//           }
//           to {
//             transform: rotate(360deg);
//           }
//         }
//         @keyframes spin-reverse {
//           from {
//             transform: rotate(360deg);
//           }
//           to {
//             transform: rotate(0deg);
//           }
//         }
//         .animate-slideInRight {
//           animation: slideInRight 0.6s ease-out;
//         }
//         .animate-float {
//           animation: float 4s ease-in-out infinite;
//         }
//         .animate-spin-slow {
//           animation: spin-slow 8s linear infinite;
//         }
//         .animate-spin-reverse {
//           animation: spin-reverse 6s linear infinite;
//         }
//       `}</style>
//     </div>
//   );
// };

// export default LiveQuiz;
//working old office fronnt old code 
//=========================================================================================================//
// import React, { useEffect, useRef, useState, useCallback } from 'react';
// import io from 'socket.io-client';
// import {
//   X,
//   BarChart3,
//   Video,
//   VideoOff,
//   Wifi,
//   WifiOff,
//   Clock,
//   Users,
//   Target,
//   Trophy,
//   Medal,
//   Award,
//   ChevronDown,
//   ChevronUp
// } from 'lucide-react';
// import { useStartQuizMutation, useNextQuestionMutation, useFinalizeSessionMutation } from './centerTraineeLive.slice';
// import applauseAudio from '../../../../assets/audio/applause-180037.mp3';
// import clockTickingAudio from '../../../../assets/audio/clock-ticking-down-376897.mp3';

// // API Configuration
// const CAPTURE_INTERVAL_MS = 200;
// const DURATION_PER_OPTION_S = 10;
// const QUESTION_READING_TIME_S = 15;

// // Enhanced Circular Option Display Component with Animated Countdown
// const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
//   const circumference = 2 * Math.PI * 120;
//   const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
//   const strokeDasharray = circumference;
//   const strokeDashoffset = circumference - progress;
//   const getTimerColor = () => {
//     const percentage = timeRemaining / totalTime;
//     if (percentage > 0.6) return '#10B981';
//     if (percentage > 0.3) return '#F59E0B';
//     return '#EF4444';
//   };
//   const getScale = () => {
//     const percentage = timeRemaining / totalTime;
//     return 1 + (1 - percentage) * 0.2;
//   };
//   if (!isActive) return null;
//   return (
//     <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
//       <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />
//       <div className="absolute inset-0">
//         {[...Array(12)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
//             style={{
//               top: `${10 + i * 7}%`,
//               left: `${5 + i * 8}%`,
//               animationDelay: `${i * 0.3}s`,
//               animationDuration: `${3 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <div
//         className="relative transition-all duration-300 ease-out"
//         style={{ transform: `scale(${getScale()})` }}
//       >
//         <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />
//         <div className="relative w-64 h-64 flex items-center justify-center">
//           <svg
//             className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
//             viewBox="0 0 260 260"
//           >
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.3)"
//               strokeWidth="12"
//               className="drop-shadow-lg"
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke={getTimerColor()}
//               strokeWidth="12"
//               strokeLinecap="round"
//               strokeDasharray={strokeDasharray}
//               strokeDashoffset={strokeDashoffset}
//               className="transition-all duration-1000 ease-out drop-shadow-xl"
//               style={{
//                 filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
//               }}
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="100"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.2)"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               className="animate-spin-reverse"
//             />
//             <defs>
//               <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
//                 <stop offset="0%" stopColor="#3B82F6" />
//                 <stop offset="50%" stopColor="#8B5CF6" />
//                 <stop offset="100%" stopColor="#EC4899" />
//               </linearGradient>
//               <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
//                 <stop offset="0%" stopColor="#FFFFFF" />
//                 <stop offset="100%" stopColor="#F8FAFC" />
//               </radialGradient>
//             </defs>
//           </svg>
//           <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
//             <div
//               className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
//                 textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
//               }}
//             >
//               {option}
//             </div>
//             <div
//               className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
//                 filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
//               }}
//             >
//               {timeRemaining}s
//             </div>
//             <div className="flex gap-1 mt-2">
//               {[...Array(totalTime)].map((_, i) => (
//                 <div
//                   key={i}
//                   className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
//                     i < totalTime - timeRemaining
//                       ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
//                       : 'bg-gray-300 scale-100'
//                   }`}
//                 />
//               ))}
//             </div>
//           </div>
//           <div className="absolute inset-0 pointer-events-none">
//             {[...Array(8)].map((_, i) => (
//               <div
//                 key={i}
//                 className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
//                 style={{
//                   top: `${15 + i * 10}%`,
//                   left: `${10 + i * 12}%`,
//                   animationDelay: `${i * 0.3}s`,
//                   animationDuration: `${2 + (i % 2)}s`
//                 }}
//               />
//             ))}
//           </div>
//           <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Reading Timer Component
// const ReadingTimer = ({ timeRemaining, isActive }) => {
//   if (!isActive) return null;
//   return (
//     <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
//       <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
//         <div className="text-center">
//           <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
//             <Clock className="animate-spin" size={32} />
//             Reading Time
//           </div>
//           <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
//           <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Hand Raise Animation Component
// const HandRaiseNotification = ({ logs }) => {
//   const [showNotification, setShowNotification] = useState(false);
//   const [latestLog, setLatestLog] = useState(null);
//   useEffect(() => {
//     if (logs.length > 0) {
//       const newest = logs[logs.length - 1];
//       setLatestLog(newest);
//       setShowNotification(true);
//       const timer = setTimeout(() => {
//         setShowNotification(false);
//       }, 4000);
//       return () => clearTimeout(timer);
//     }
//   }, [logs]);
//   if (!showNotification || !latestLog) return null;
//   return (
//     <div className="fixed top-24 right-8 z-50 transform animate-slideInRight">
//       <div className="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 text-white px-8 py-6 rounded-2xl shadow-2xl border-4 border-white/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-300">
//         <div className="flex items-center gap-4">
//           <div className="text-4xl animate-bounce">🙋‍♂️</div>
//           <div>
//             <div className="font-bold text-xl drop-shadow-sm">{latestLog.student_name}</div>
//             <div className="text-sm opacity-90 bg-white/20 px-3 py-1 rounded-full mt-1">
//               Option {latestLog.option ? latestLog.option.toUpperCase() : 'N/A'} • {latestLog.responseTime}s
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// const LiveQuiz = ({ quizId: inputQuizId, sessionId: inputSessionId, onClose }) => {
//   const [quizId, setQuizId] = useState(inputQuizId);
//   const [sessionId, setSessionId] = useState(inputSessionId || '');
//   const [clientId, setClientId] = useState('');
//   const [centerCode, setCenterCode] = useState(null);
//   const [questionData, setQuestionData] = useState(null);
//   const [quizStatus, setQuizStatus] = useState('');
//   const [summary, setSummary] = useState(null);
//   const [finalResults, setFinalResults] = useState(null);
//   const [liveFeedLogs, setLiveFeedLogs] = useState([]);
//   const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
//   const [socketStatus, setSocketStatus] = useState('Disconnected');
//   const [error, setError] = useState(null);
//   const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
//   const [currentOption, setCurrentOption] = useState('');
//   const [questionTimer, setQuestionTimer] = useState(null);
//   const [isReadingQuestion, setIsReadingQuestion] = useState(false);
//   const [showDashboard, setShowDashboard] = useState(false);
//   const [questionCount, setQuestionCount] = useState(0);
//   const [cameraStatus, setCameraStatus] = useState('Not initialized');
//   const [showManualInput, setShowManualInput] = useState(!inputQuizId);
//   const [manualQuizId, setManualQuizId] = useState('');
//   const [manualSessionId, setManualSessionId] = useState('');
//   const [startQuizMutation, { isLoading: isStartingQuiz }] = useStartQuizMutation();
//   const [nextQuestionMutation, { isLoading: isFetchingNextQuestion }] = useNextQuestionMutation();
//   const [finalizeSessionMutation] = useFinalizeSessionMutation();
//   const [dashboardData, setDashboardData] = useState(null);
//   const [isDashboardLoading, setIsDashboardLoading] = useState(false);
  
//   const videoRef = useRef(null);
//   const canvasRef = useRef(null);
//   const socketRef = useRef(null);
//   const videoStreamRef = useRef(null);
//   const timerIntervalRef = useRef(null);
//   const applauseAudioRef = useRef(null);
//   const clockTickingAudioRef = useRef(null);
  
//   // Track if socket has been initialized
//   const [isSocketInitialized, setIsSocketInitialized] = useState(false);
  
//   // Get center code from session storage
//   useEffect(() => {
//     const storedCenterCode = sessionStorage.getItem('centercode');
//     if (storedCenterCode && !centerCode) {
//       setCenterCode(storedCenterCode);
//       console.log(`🔒 Center code retrieved from session storage: ${storedCenterCode}`);
//     }
//   }, [centerCode]);
  
//   useEffect(() => {
//     if (quizId && videoRef.current && !isSocketInitialized) {
//       console.log('🎯 QuizId changed, initializing camera:', quizId);
//       initCamera();
//     }
//   }, [quizId, isSocketInitialized]);
  
//   // Cleanup function - now only disconnects socket when component unmounts
//   useEffect(() => {
//     return () => {
//       stopCamera();
//       if (socketRef.current) {
//         socketRef.current.disconnect();
//         socketRef.current = null;
//       }
//       if (timerIntervalRef.current) {
//         clearInterval(timerIntervalRef.current);
//       }
//       if (applauseAudioRef.current) {
//         applauseAudioRef.current.pause();
//         applauseAudioRef.current.currentTime = 0;
//       }
//       if (clockTickingAudioRef.current) {
//         clockTickingAudioRef.current.pause();
//         clockTickingAudioRef.current.currentTime = 0;
//       }
//     };
//   }, []);
  
//   // Initialize socket connection ONCE when component mounts
//   useEffect(() => {
//     const initSocket = () => {
//       if (socketRef.current || isSocketInitialized) return;
      
//       // Dynamically construct the Socket.IO URL based on the current environment
//       const socketUrl = `${window.location.protocol}//${window.location.hostname}:8036`;
//       console.log(`🔌 Initializing socket connection to: ${socketUrl}`);
      
//       socketRef.current = io(socketUrl, {
//         path: '/socketio2/socket.io',
//         transports: ['websocket', 'polling'],
//         upgrade: true,
//         rememberUpgrade: true,
//         timeout: 20000,
//         forceNew: false, // Critical change - don't force new connection
//         autoConnect: true,
//         reconnection: true,
//         reconnectionAttempts: 10, // Increased attempts
//         reconnectionDelay: 1000,
//         reconnectionDelayMax: 5000,
//         randomizationFactor: 0.5
//       });
      
//       // Set up socket event listeners
//       socketRef.current.on('connect', () => {
//         setSocketStatus('Connected');
//         console.log(`✅ Connected to Socket.IO server at ${socketUrl}`);
//       });
      
//       socketRef.current.on('disconnect', (reason) => {
//         setSocketStatus(`Disconnected: ${reason}`);
//         console.log(`❌ Socket disconnected: ${reason}`);
        
//         // Only try to reconnect if it wasn't intentional
//         if (reason !== 'io client disconnect') {
//           setTimeout(() => {
//             if (socketRef.current && !socketRef.current.connected) {
//               console.log('🔄 Attempting to reconnect...');
//               socketRef.current.connect();
//             }
//           }, 2000);
//         }
//       });
      
//       socketRef.current.on('connect_error', (error) => {
//         setSocketStatus(`Connection Error: ${error.message}`);
//         console.error('❌ Socket.IO connection error:', error);
//       });
      
//       socketRef.current.on('reconnect', (attemptNumber) => {
//         setSocketStatus(`Reconnected (attempt ${attemptNumber})`);
//         console.log(`🔄 Reconnected to Socket.IO server. Attempt: ${attemptNumber}`);
        
//         // If we have session info, rejoin the room
//         if (sessionId && clientId && centerCode) {
//           console.log(`🚪 Rejoining room with session=${sessionId}, client=${clientId}, center=${centerCode}`);
//           socketRef.current.emit('join_quiz_room', { 
//             session_id: sessionId, 
//             client_id: clientId,
//             center_code: centerCode
//           });
//         }
//       });
      
//       socketRef.current.on('reconnect_attempt', (attempt) => {
//         setSocketStatus(`Reconnecting... (attempt ${attempt})`);
//         console.log(`🔄 Reconnection attempt #${attempt}`);
//       });
      
//       socketRef.current.on('reconnect_error', (error) => {
//         console.error('❌ Socket.IO reconnection error:', error);
//       });
      
//       socketRef.current.on('reconnect_failed', () => {
//         setSocketStatus('Reconnection Failed');
//         console.error('❌ Socket.IO reconnection failed');
//       });
      
//       socketRef.current.on('hand_raised', (data) => {
//         console.log('🙋 Hand raised event received:', data);
//         setLiveFeedLogs((logs) => {
//           const newLog = {
//             ...data,
//             responseTime: currentQuestionStartTime
//               ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000)
//               : 0
//           };
//           return [...logs, newLog];
//         });
//       });
      
//       socketRef.current.on('center_identified', (data) => {
//         if (data.center_code && !centerCode) {
//           setCenterCode(data.center_code);
//           console.log(`🔒 This client is now locked to Center: ${data.center_code}`);
//         }
//       });
      
//       socketRef.current.onAny((eventName, ...args) => {
//         console.log(`📡 Socket event received: ${eventName}`, args);
//       });
      
//       setIsSocketInitialized(true);
//       return socketRef.current;
//     };
    
//     const socket = initSocket();
//     return () => {
//       if (socket) {
//         socket.disconnect();
//       }
//     };
//   }, []);
  
//   const generateUUID = () => crypto.randomUUID();
  
//   const startQuiz = async () => {
//     if (!quizId) {
//       setError('Missing Quiz ID');
//       return;
//     }
//     // Get center code from session storage
//     const storedCenterCode = sessionStorage.getItem('centercode');
//     if (!storedCenterCode) {
//       setError('Center code not found. Please log in again.');
//       return;
//     }
//     let currentSessionId = sessionId;
//     if (!currentSessionId) {
//       currentSessionId = generateUUID();
//       setSessionId(currentSessionId);
//     }
//     const currentClientId = generateUUID();
//     setClientId(currentClientId);
    
//     try {
//       setQuizStatus('Starting quiz...');
      
//       // First, join the socket room
//       await new Promise((resolve, reject) => {
//         if (!socketRef.current) {
//           reject('Socket not initialized');
//           return;
//         }
//         socketRef.current.emit('join_quiz_room', { 
//           session_id: currentSessionId, 
//           client_id: currentClientId,
//           center_code: storedCenterCode
//         }, (ack) => {
//           if (ack && ack.success) {
//             console.log('✅ Successfully joined quiz room');
//             resolve();
//           } else {
//             console.error('❌ Failed to join quiz room:', ack ? ack.error : 'No acknowledgment');
//             reject(ack ? ack.error : 'No acknowledgment received');
//           }
//         });
//       });
      
//       // Then, start the quiz via API
//       const response = await startQuizMutation({ 
//         quiz_id: quizId, 
//         session_id: currentSessionId, 
//         client_id: currentClientId,
//         center_code: storedCenterCode
//       }).unwrap();
      
//       if (response.error) {
//         throw new Error(response.error);
//       }
      
//       setQuestionData(response);
//       setQuizStatus('Ready to start capture for this question.');
//       setQuestionCount(1);
//       console.log('🎯 Starting quiz, initializing camera immediately');
//       await initCamera();
//       initializeAudio();
//     } catch (error) {
//       setError(`Error starting quiz: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   const handleNextQuestion = async () => {
//     if (!sessionId || !clientId || !centerCode) return;
//     try {
//       await startCaptureCycle();
//       setQuizStatus('Processing results and fetching next question...');
//       // Include center code in the request
//       const response = await nextQuestionMutation({ 
//         session_id: sessionId, 
//         client_id: clientId,
//         center_code: centerCode
//       }).unwrap();
//       if (response.status === 'completed') {
//         setFinalResults(response);
//         stopCamera();
//         if (socketRef.current) {
//           socketRef.current.disconnect();
//           socketRef.current = null;
//         }
//         setQuizStatus('Quiz completed! View results for details.');
//         if (applauseAudioRef.current) {
//           console.log('🎉 Playing applause sound for quiz completion');
//           applauseAudioRef.current.currentTime = 0;
//           applauseAudioRef.current.volume = 0.8;
//           applauseAudioRef.current.play()
//             .then(() => {
//               console.log('✅ Applause audio started successfully');
//             })
//             .catch((error) => {
//               console.error('❌ Error playing applause audio:', error);
//             });
//           setTimeout(() => {
//             if (applauseAudioRef.current) {
//               console.log('⏹️ Stopping applause audio after 4 seconds');
//               applauseAudioRef.current.pause();
//               applauseAudioRef.current.currentTime = 0;
//             }
//           }, 4000);
//         }
//       } else if (response.question) {
//         setQuestionData(response);
//         setQuizStatus('Ready to start capture for this question.');
//         setQuestionCount((prev) => prev + 1);
//       }
//     } catch (error) {
//       setError(`Error during quiz progression: ${error.message || error.data?.error || 'Unknown error'}`);
//       setQuizStatus(`Error: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   const handleFinalize = async () => {
//     if (!sessionId) return;
//     if (!window.confirm('Are you sure you want to end this session for ALL centers?')) return;
//     try {
//       const response = await finalizeSessionMutation({ session_id: sessionId }).unwrap();
//       console.log('Finalize response:', response);
//       setShowDashboard(true);
//     } catch (error) {
//       setError(`Error finalizing session: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   const handleShowResults = async () => {
//     if (!sessionId || !centerCode) return;
//     setIsDashboardLoading(true);
//     try {
//       const response = await fetch(`http://localhost:8036/api/content/engagement_dashboard?session_id=${sessionId}&center_code=${centerCode}`);
//       if (!response.ok) {
//         throw new Error('Failed to fetch dashboard data');
//       }
//       const data = await response.json();
//       if (data.success === false) {
//         throw new Error(data.error || 'Unknown error');
//       }
//       setDashboardData(data);
//       setShowDashboard(true);
//     } catch (err) {
//       setError(`Error fetching results: ${err.message}`);
//     } finally {
//       setIsDashboardLoading(false);
//     }
//   };
  
//   const startCaptureCycle = async () => {
//     setLiveFeedLogs([]);
//     setIsReadingQuestion(true);
//     setQuestionTimer(QUESTION_READING_TIME_S);
//     setCurrentQuestionStartTime(Date.now());
//     timerIntervalRef.current = setInterval(() => {
//       setQuestionTimer((prev) => {
//         if (prev <= 1) {
//           clearInterval(timerIntervalRef.current);
//           return 0;
//         }
//         return prev - 1;
//       });
//     }, 1000);
//     await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));
//     setIsReadingQuestion(false);
//     setQuestionTimer(null);
//     const options = ['a', 'b', 'c', 'd'];
//     for (let i = 0; i < options.length; i++) {
//       setQuizStatus(
//         `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
//       );
//       await processSingleOption(options[i]);
//     }
//     setQuizStatus('Capture complete for this question. Results sent.');
//     setCurrentOptionTimer(null);
//     setCurrentOption('');
//   };
  
//   const processSingleOption = (optionChar) => {
//     return new Promise((resolve) => {
//       const startTime = Date.now();
//       let frameCount = 0;
//       let clockAudioTimeout = null;
//       setCurrentOption(optionChar.toUpperCase());
//       setCurrentOptionTimer(DURATION_PER_OPTION_S);
//       if (clockTickingAudioRef.current) {
//         console.log(`🔊 Playing clock ticking sound for option ${optionChar.toUpperCase()}`);
//         clockTickingAudioRef.current.currentTime = 0;
//         clockTickingAudioRef.current.volume = 0.7;
//         clockTickingAudioRef.current.play()
//           .then(() => {
//             console.log('✅ Clock ticking audio started successfully');
//           })
//           .catch((error) => {
//             console.error('❌ Error playing clock ticking audio:', error);
//           });
//         clockAudioTimeout = setTimeout(() => {
//           if (clockTickingAudioRef.current) {
//             console.log('⏹️ Stopping clock ticking audio after 10 seconds');
//             clockTickingAudioRef.current.pause();
//             clockTickingAudioRef.current.currentTime = 0;
//           }
//         }, 10000);
//       }
//       timerIntervalRef.current = setInterval(() => {
//         const elapsed = Math.floor((Date.now() - startTime) / 1000);
//         const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
//         setCurrentOptionTimer(remaining);
//       }, 1000);
//       const intervalId = setInterval(() => {
//         const responseTime = (Date.now() - startTime) / 1000;
//         captureAndSendFrame(optionChar, responseTime);
//         frameCount++;
//       }, CAPTURE_INTERVAL_MS);
//       setTimeout(() => {
//         clearInterval(intervalId);
//         if (timerIntervalRef.current) {
//           clearInterval(timerIntervalRef.current);
//         }
//         if (clockAudioTimeout) {
//           clearTimeout(clockAudioTimeout);
//         }
//         if (clockTickingAudioRef.current) {
//           console.log(`⏹️ Stopping clock ticking audio for option ${optionChar.toUpperCase()}`);
//           clockTickingAudioRef.current.pause();
//           clockTickingAudioRef.current.currentTime = 0;
//         }
//         resolve();
//       }, DURATION_PER_OPTION_S * 1000);
//     });
//   };
  
//   const captureAndSendFrame = useCallback((optionChar, responseTime) => {
//     const video = videoRef.current;
//     const canvas = canvasRef.current;
//     if (!video || !canvas) {
//       console.warn('⚠️ No video or canvas element available');
//       return;
//     }
//     if (!videoStreamRef.current) {
//       console.warn('⚠️ No video stream available');
//       return;
//     }
//     if (!socketRef.current?.connected) {
//       console.warn('⚠️ Socket not connected - cannot send frame');
//       return;
//     }
//     const ctx = canvas.getContext('2d');
//     canvas.width = video.videoWidth;
//     canvas.height = video.videoHeight;
//     ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
//     const frameData = canvas.toDataURL('image/jpeg', 0.6);
//     console.log(
//       `📸 Sending frame: session=${sessionId}, option=${optionChar}, time=${responseTime.toFixed(2)}s`
//     );
//     socketRef.current.emit('process_frame', {
//       session_id: sessionId,
//       client_id: clientId,
//       frame: frameData,
//       option_char: optionChar,
//       response_time_seconds: responseTime
//     });
//   }, [sessionId, clientId]);
  
//   const initCamera = async () => {
//     if (videoStreamRef.current) return;
//     try {
//       console.log('🎥 Requesting camera access...');
//       setCameraStatus('Requesting access...');
//       const stream = await navigator.mediaDevices.getUserMedia({
//         video: {
//           width: { ideal: 1280 },
//           height: { ideal: 720 }
//         }
//       });
//       if (videoRef.current) {
//         videoRef.current.srcObject = stream;
//         videoStreamRef.current = stream;
//         console.log('✅ Camera initialized successfully');
//         setCameraStatus('Active');
//       }
//     } catch (error) {
//       console.error('❌ Camera error:', error);
//       setCameraStatus('Error');
//       setError(`Camera error: ${error.message}`);
//     }
//   };
  
//   const stopCamera = () => {
//     if (videoStreamRef.current) {
//       videoStreamRef.current.getTracks().forEach((track) => track.stop());
//       videoStreamRef.current = null;
//     }
//     if (videoRef.current) {
//       videoRef.current.srcObject = null;
//     }
//     setCameraStatus('Not initialized');
//   };
  
//   const initializeAudio = () => {
//     if (applauseAudioRef.current) {
//       applauseAudioRef.current.load();
//       console.log('🔊 Applause audio initialized');
//     }
//     if (clockTickingAudioRef.current) {
//       clockTickingAudioRef.current.load();
//       console.log('🔊 Clock ticking audio initialized');
//     }
//   };
  
//   const testSocketConnection = () => {
//     if (socketRef.current) {
//       socketRef.current.emit('test_connection', { message: 'Hello from client' });
//     }
//   };
  
//   const handleCloseDashboard = () => {
//     setShowDashboard(false);
//   };
  
//   const handleManualQuizStart = async () => {
//     if (manualQuizId.trim()) {
//       setError(null);
//       setShowManualInput(false);
//       setQuizId(manualQuizId.trim());
//       // Get center code from session storage
//       const storedCenterCode = sessionStorage.getItem('centercode');
//       if (!storedCenterCode) {
//         setError('Center code not found. Please log in again.');
//         setShowManualInput(true);
//         return;
//       }
//       let currentSessionId = manualSessionId.trim();
//       if (!currentSessionId) {
//         currentSessionId = generateUUID();
//         setManualSessionId(currentSessionId);
//         setSessionId(currentSessionId);
//       } else {
//         setSessionId(currentSessionId);
//       }
//       await startQuiz();
//     } else {
//       setError('Please enter a valid Quiz ID');
//     }
//   };
  
//   useEffect(() => {
//     if (inputQuizId) {
//       startQuiz();
//     }
//   }, [inputQuizId]);
  
//   if (showDashboard && sessionId && centerCode) {
//     if (isDashboardLoading) {
//       return (
//         <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//           <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col items-center justify-center p-16">
//             <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-6"></div>
//             <h2 className="text-3xl font-bold text-gray-800">Loading Results...</h2>
//             <p className="text-lg text-gray-600 mt-2">Analyzing quiz performance data</p>
//           </div>
//         </div>
//       );
//     }
//     if (!dashboardData) {
//       return (
//         <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//           <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-md p-12 text-center">
//             <div className="text-6xl mb-6">⚠️</div>
//             <h2 className="text-3xl font-bold text-gray-800 mb-4">Failed to Load Results</h2>
//             <p className="text-gray-600 mb-8">There was an error retrieving the quiz results. Please try again later.</p>
//             <button
//               onClick={() => setShowDashboard(false)}
//               className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-lg font-bold"
//             >
//               Return to Quiz
//             </button>
//           </div>
//         </div>
//       );
//     }
//     return (
//       <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//         <div className="bg-white/95 backdrop-blur-xl rounded-3xl border-4 border-white/20 w-full max-w-6xl max-h-[95vh] flex flex-col">
//           <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-8 text-white relative overflow-hidden">
//             <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 animate-pulse" />
//             <div className="relative z-10 flex justify-between items-center">
//               <div className="flex items-center gap-6">
//                 <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
//                   <BarChart3 className="text-yellow-400" size={40} />
//                 </div>
//                 <div>
//                   <h2 className="text-4xl font-black mb-2">📊 Quiz Results</h2>
//                   <p className="text-xl opacity-90">Engagement Dashboard</p>
//                 </div>
//               </div>
//               <button
//                 onClick={handleCloseDashboard}
//                 className="text-white/80 hover:text-white transition-all duration-300 p-3 rounded-full hover:bg-white/20 hover:scale-110 transform"
//               >
//                 <X size={32} />
//               </button>
//             </div>
//           </div>
//           <div className="p-6 bg-gradient-to-r from-slate-800 to-slate-700 border-b border-white/10">
//             <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-white">
//               <div className="text-center">
//                 <div className="text-3xl font-black text-blue-400">
//                   {dashboardData?.total_questions_in_quiz || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Total Questions</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-green-400">
//                   {dashboardData?.engagement_summary_stats?.active_students || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Active Students</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-purple-400">
//                   {dashboardData?.engagement_summary_stats?.total_participations || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Total Participations</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-yellow-400">
//                   {dashboardData?.engagement_summary_stats?.average_frequency?.toFixed(1) || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Avg Frequency</div>
//               </div>
//             </div>
//           </div>
//           <div className="flex-1 overflow-y-auto p-8 space-y-4 bg-gradient-to-b from-slate-800 to-slate-900">
//             <h3 className="text-2xl font-black text-white mb-6 flex items-center gap-3">
//               <Trophy className="text-yellow-400" size={28} />
//               Top Performers
//             </h3>
//             <div className="space-y-3">
//               {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
//                 dashboardData.leaderboard.map((student, index) => (
//                   <div
//                     key={index}
//                     className="transform transition-all duration-300 hover:scale-[1.02] bg-gradient-to-r from-slate-700 to-slate-800 p-6 rounded-2xl shadow-xl border-2 border-white/20"
//                   >
//                     <div className="flex items-center justify-between text-white">
//                       <div className="flex items-center gap-6">
//                         <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center text-white font-black text-xl">
//                           {index + 1}
//                         </div>
//                         <div className="flex-1">
//                           <div className="text-2xl font-black mb-1">{student.first_name} {student.last_name}</div>
//                           <div className="text-sm opacity-90 flex gap-4">
//                             <span>Score: {student.score}</span>
//                             <span>Accuracy: {student.accuracy_percentage}%</span>
//                             <span>Avg Time: {student.avg_response_time_seconds}s</span>
//                           </div>
//                         </div>
//                         <div className="text-right">
//                           <div className="text-4xl font-black text-yellow-400">#{index + 1}</div>
//                           <div className="text-xs opacity-80 bg-white/20 px-2 py-1 rounded-full">
//                             {student.questions_attempted} Questions
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   </div>
//                 ))
//               ) : (
//                 <div className="text-center py-12 bg-slate-800 rounded-xl">
//                   <div className="text-5xl mb-4">📊</div>
//                   <h3 className="text-2xl font-bold text-white mb-2">No Results Available</h3>
//                   <p className="text-gray-400">The quiz results are still being processed or no students participated.</p>
//                 </div>
//               )}
//             </div>
            
//             <h3 className="text-2xl font-black text-white mt-10 mb-6 flex items-center gap-3">
//               <Users className="text-blue-400" size={28} />
//               Student Performance
//             </h3>
            
//             <div className="bg-slate-800 rounded-xl overflow-hidden">
//               <div className="overflow-x-auto">
//                 <table className="min-w-full">
//                   <thead>
//                     <tr className="bg-slate-700">
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Rank</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Student Name</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Score</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Accuracy</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Questions Attempted</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Avg. Response Time</th>
//                     </tr>
//                   </thead>
//                   <tbody>
//                     {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
//                       dashboardData.leaderboard.map((student, index) => (
//                         <tr 
//                           key={index}
//                           className={`border-b border-slate-700 hover:bg-slate-700/50 transition-colors ${
//                             index % 2 === 0 ? 'bg-slate-800' : 'bg-slate-850'
//                           }`}
//                         >
//                           <td className="py-4 px-6">
//                             <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold">
//                               {index + 1}
//                             </span>
//                           </td>
//                           <td className="py-4 px-6 text-white font-medium">
//                             {student.first_name} {student.last_name}
//                           </td>
//                           <td className="py-4 px-6 text-green-400 font-bold">
//                             {student.score}
//                           </td>
//                           <td className="py-4 px-6 text-blue-300">
//                             {student.accuracy_percentage}%
//                           </td>
//                           <td className="py-4 px-6 text-purple-300">
//                             {student.questions_attempted}
//                           </td>
//                           <td className="py-4 px-6 text-cyan-300">
//                             {student.avg_response_time_seconds}s
//                           </td>
//                         </tr>
//                       ))
//                     ) : (
//                       <tr>
//                         <td colSpan="6" className="py-8 text-center text-gray-400">
//                           No student performance data available
//                         </td>
//                       </tr>
//                     )}
//                   </tbody>
//                 </table>
//               </div>
//             </div>
            
//             <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Target className="text-blue-400" size={24} />
//                   Question Performance
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Most Challenging Question</span>
//                     <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
//                       #{dashboardData?.most_challenging_question || 'N/A'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Easiest Question</span>
//                     <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
//                       #{dashboardData?.easiest_question || 'N/A'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Average Accuracy</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.average_accuracy?.toFixed(1) || '0.0'}%
//                     </span>
//                   </div>
//                 </div>
//               </div>
              
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Clock className="text-purple-400" size={24} />
//                   Response Analysis
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Fastest Response</span>
//                     <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
//                       {dashboardData?.fastest_response?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Slowest Response</span>
//                     <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
//                       {dashboardData?.slowest_response?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Average Response Time</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.avg_response_time?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                 </div>
//               </div>
              
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Users className="text-green-400" size={24} />
//                   Engagement Metrics
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Peak Engagement</span>
//                     <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">
//                       {dashboardData?.peak_engagement?.toFixed(1) || '0.0'}%
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Engagement Trend</span>
//                     <span className={`px-3 py-1 rounded-full ${
//                       dashboardData?.engagement_trend === 'increasing' 
//                         ? 'bg-green-500/20 text-green-300' 
//                         : dashboardData?.engagement_trend === 'decreasing' 
//                           ? 'bg-red-500/20 text-red-300' 
//                           : 'bg-yellow-500/20 text-yellow-300'
//                     }`}>
//                       {dashboardData?.engagement_trend || 'stable'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Active Students</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.engagement_summary_stats?.active_students || 0}
//                     </span>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>
//           <div className="p-6 bg-slate-900 border-t border-white/10">
//             <button
//               onClick={onClose}
//               className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-xl"
//             >
//               <X size={24} />
//               Close Results & Exit Quiz
//             </button>
//           </div>
//         </div>
//       </div>
//     );
//   }
  
//   return (
//     <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//       <div className="absolute inset-0 overflow-hidden">
//         {[...Array(20)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-2 h-2 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-30 animate-float"
//             style={{
//               top: `${Math.random() * 100}%`,
//               left: `${Math.random() * 100}%`,
//               animationDelay: `${i * 0.5}s`,
//               animationDuration: `${4 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <CircularOptionDisplay
//         option={currentOption}
//         timeRemaining={currentOptionTimer}
//         totalTime={DURATION_PER_OPTION_S}
//         isActive={!!currentOption && currentOptionTimer !== null}
//       />
//       <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />
//       <HandRaiseNotification logs={liveFeedLogs} />
//       <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col shadow-2xl">
//         <div className="flex justify-between items-center p-8 border-b border-gray-200/50 flex-shrink-0 bg-gradient-to-r from-blue-100/50 via-purple-100/50 to-pink-100/50">
//           <div className="flex items-center gap-6">
//             <div className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-all duration-300">
//               <Target className="text-white" size={32} />
//             </div>
//             <div>
//               <h2 className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
//                 Live Quiz Experience
//               </h2>
//               <p className="text-gray-600 text-lg font-medium">Real-time engagement monitoring</p>
//             </div>
//           </div>
//           <button
//             onClick={onClose}
//             className="text-gray-500 hover:text-red-500 transition-all duration-300 p-3 rounded-full hover:bg-red-50 hover:scale-105 transform"
//           >
//             <X size={28} />
//           </button>
//         </div>
//         <div className="flex-1 overflow-y-auto p-8 space-y-8">
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 socketStatus.startsWith('Connected')
//                   ? 'bg-gradient-to-r from-green-100 to-emerald-100 border-green-300 text-green-800 shadow-green-200/50'
//                   : 'bg-gradient-to-r from-red-100 to-pink-100 border-red-300 text-red-800 shadow-red-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${socketStatus.startsWith('Connected') ? 'bg-green-200' : 'bg-red-200'}`}
//                 >
//                   {socketStatus.startsWith('Connected') ? <Wifi size={24} /> : <WifiOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Socket Status</div>
//                   <div className="text-sm opacity-80 font-medium">{socketStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 cameraStatus === 'Active'
//                   ? 'bg-gradient-to-r from-blue-100 to-cyan-100 border-blue-300 text-blue-800 shadow-blue-200/50'
//                   : 'bg-gradient-to-r from-yellow-100 to-orange-100 border-yellow-300 text-yellow-800 shadow-yellow-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${cameraStatus === 'Active' ? 'bg-blue-200' : 'bg-yellow-200'}`}
//                 >
//                   {cameraStatus === 'Active' ? <Video size={24} /> : <VideoOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Camera Status</div>
//                   <div className="text-sm opacity-80 font-medium">{cameraStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div className="p-6 rounded-2xl border-2 bg-gradient-to-r from-purple-100 to-pink-100 border-purple-300 text-purple-800 shadow-xl shadow-purple-200/50 transition-all duration-500 transform hover:scale-105">
//               <div className="flex items-center gap-4">
//                 <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black text-xl shadow-lg">
//                   {questionCount}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Question Progress</div>
//                   <div className="text-sm opacity-80 font-medium">Question {questionCount}</div>
//                 </div>
//               </div>
//             </div>
//           </div>
//           {socketRef.current && (
//             <button
//               onClick={testSocketConnection}
//               className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl transform hover:scale-105"
//             >
//               Test Socket Connection
//             </button>
//           )}
//           {error && (
//             <div className="text-center text-red-600 p-8 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 animate-pulse shadow-xl">
//               <div className="text-6xl mb-4">⚠️</div>
//               <p className="text-xl font-bold">{error}</p>
//             </div>
//           )}
//           {showManualInput && (
//             <div className="bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-8 border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//               <h3 className="text-3xl font-black text-gray-800 mb-6 flex items-center gap-4">
//                 <span className="text-4xl">🎯</span>
//                 Enter Quiz Details
//               </h3>
//               <div className="flex flex-col gap-6">
//                 <div>
//                   <label className="block text-lg font-bold text-gray-700 mb-3">Quiz ID</label>
//                   <input
//                     type="text"
//                     value={manualQuizId}
//                     onChange={(e) => setManualQuizId(e.target.value)}
//                     placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
//                     className="w-full px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-lg font-bold text-gray-700 mb-3">Session ID (Generate or paste)</label>
//                   <div className="flex gap-4">
//                     <input
//                       type="text"
//                       value={manualSessionId}
//                       onChange={(e) => setManualSessionId(e.target.value)}
//                       placeholder="Session ID"
//                       className="flex-1 px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
//                     />
//                     <button
//                       onClick={() => setManualSessionId(generateUUID())}
//                       className="px-6 py-4 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 transform hover:scale-105"
//                     >
//                       Generate
//                     </button>
//                   </div>
//                 </div>
//                 <button
//                   onClick={handleManualQuizStart}
//                   disabled={!manualQuizId.trim() || isStartingQuiz}
//                   className="px-10 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-teal-500 hover:from-purple-600 hover:via-blue-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 text-lg"
//                 >
//                   {isStartingQuiz ? 'Starting...' : 'Start Quiz'}
//                 </button>
//               </div>
//               <p className="text-gray-600 text-lg mt-4 font-medium">
//                 Enter the Quiz ID provided by your teacher to start the quiz.
//               </p>
//             </div>
//           )}
//           {!showManualInput && questionData && (
//             <div>
//               <button
//                 onClick={() => {
//                   setShowManualInput(true);
//                   setQuestionData(null);
//                   setQuizId(null);
//                   setError(null);
//                 }}
//                 className="px-6 py-3 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white rounded-xl text-lg font-medium transition-all duration-300 backdrop-blur-sm shadow-lg transform hover:scale-105"
//               >
//                 Enter Different Quiz ID
//               </button>
//             </div>
//           )}
//           {questionData && (
//             <div className="text-gray-800 space-y-8">
//               <div className="status-bar p-6 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-lg">
//                   <div className="font-bold">
//                     <strong>Session ID:</strong> <span className="text-blue-600">{sessionId}</span>
//                   </div>
//                   <div className="font-bold">
//                     <strong>Status:</strong> <span className="text-purple-600">{quizStatus}</span>
//                   </div>
//                 </div>
//               </div>
//               <div className="flex flex-col lg:flex-row gap-8">
//                 <div className="flex-1">
//                   <div className="relative rounded-2xl overflow-hidden border-4 border-white/50 bg-gradient-to-br from-gray-100 to-gray-200 backdrop-blur-sm shadow-2xl">
//                     <video
//                       ref={videoRef}
//                       autoPlay
//                       playsInline
//                       muted
//                       className="w-full max-w-2xl rounded-2xl"
//                     />
//                     <div
//                       className={`absolute top-6 left-6 px-4 py-3 rounded-xl text-lg font-bold backdrop-blur-sm shadow-lg ${
//                         cameraStatus === 'Active'
//                           ? 'bg-green-500/90 text-white'
//                           : cameraStatus === 'Error'
//                             ? 'bg-red-500/90 text-white'
//                             : 'bg-yellow-500/90 text-white'
//                       }`}
//                     >
//                       📹 Camera: {cameraStatus}
//                     </div>
//                   </div>
//                   <canvas ref={canvasRef} className="hidden" />
//                 </div>
//                 {liveFeedLogs.length > 0 && (
//                   <div className="w-full lg:w-80 bg-gradient-to-b from-white/90 to-blue-50/90 rounded-2xl p-6 border-2 border-blue-200 max-h-96 overflow-y-auto backdrop-blur-sm shadow-xl">
//                     <h3 className="text-xl font-black mb-4 text-gray-800 flex items-center gap-3">
//                       <Users className="text-blue-500" size={24} />
//                       Live Hand Raises ({liveFeedLogs.length})
//                     </h3>
//                     <div className="space-y-3">
//                       {liveFeedLogs
//                         .sort(
//                           (a, b) =>
//                             new Date(a.detection_timestamp) - new Date(b.detection_timestamp)
//                         )
//                         .map((log, idx) => (
//                           <div
//                             key={idx}
//                             className="bg-gradient-to-r from-white/80 to-blue-50/80 p-4 rounded-xl text-sm backdrop-blur-sm transform hover:scale-105 transition-all duration-300 shadow-lg border border-blue-200"
//                           >
//                             <div className="flex justify-between items-start mb-2">
//                               <span className="font-bold text-gray-800 text-lg">
//                                 #{idx + 1} {log.student_name}
//                               </span>
//                               <span className="text-sm text-gray-600 bg-blue-100 px-3 py-1 rounded-full font-medium">
//                                 {log.responseTime}s
//                               </span>
//                             </div>
//                             <div className="flex justify-between items-center">
//                               <span className="text-blue-600 font-bold text-lg">
//                                 Option {log.option ? log.option.toUpperCase() : 'N/A'}
//                               </span>
//                             </div>
//                           </div>
//                         ))}
//                     </div>
//                   </div>
//                 )}
//               </div>
//               <div className="space-y-6">
//                 <h2 className="text-3xl font-black bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent">
//                   {questionData.sub_topic_name}
//                 </h2>
//                 <div className="bg-gradient-to-r from-white/80 to-purple-50/80 p-8 rounded-2xl border-2 border-purple-200 backdrop-blur-sm shadow-xl">
//                   <p className="text-2xl text-gray-800 leading-relaxed font-medium">
//                     {questionData.question_number}. {questionData.question}
//                   </p>
//                 </div>
//                 {questionData.question_image && questionData.question_image.trim() && (
//                   <div className="bg-gradient-to-r from-white/80 to-blue-50/80 p-6 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                     <img
//                       src={questionData.question_image.trim()}
//                       alt="Question Image"
//                       className="max-w-full h-auto rounded-xl shadow-lg border-2 border-white/50"
//                       onError={(e) => {
//                         console.error('Failed to load question image:', e.target.src);
//                         if (e.target.src.includes('?X-Amz-')) {
//                           const baseUrl = e.target.src.split('?X-Amz-')[0];
//                           console.log('Trying base URL:', baseUrl);
//                           e.target.src = baseUrl;
//                         } else {
//                           e.target.style.display = 'none';
//                         }
//                       }}
//                       onLoad={() => {
//                         console.log(
//                           'Question image loaded successfully:',
//                           questionData.question_image
//                         );
//                       }}
//                     />
//                   </div>
//                 )}
//                 <div className="grid gap-4">
//                   {questionData.options.map((opt, idx) => {
//                     const optionLetter = String.fromCharCode(97 + idx);
//                     const isCurrentOption = currentOption.toLowerCase() === optionLetter;
//                     return (
//                       <div key={idx} className="relative">
//                         <div
//                           className={`p-6 rounded-2xl border-2 transition-all duration-500 transform ${
//                             isCurrentOption
//                               ? 'bg-gradient-to-r from-green-100 via-emerald-100 to-teal-100 border-green-400 shadow-2xl scale-105 shadow-green-300/50'
//                               : 'bg-gradient-to-r from-white/80 to-gray-50/80 border-gray-300 hover:bg-gray-100/80 hover:scale-102 shadow-lg'
//                           } backdrop-blur-sm`}
//                         >
//                           <span className="font-bold text-gray-800 text-xl">
//                             {optionLetter.toUpperCase()}. {opt}
//                           </span>
//                           {questionData.option_images && questionData.option_images[idx] && (
//                             <img
//                               src={questionData.option_images[idx]}
//                               alt={`Option ${optionLetter.toUpperCase()}`}
//                               className="max-w-90% max-h-150 mt-10 rounded-4"
//                             />
//                           )}
//                         </div>
//                         {isCurrentOption && currentOptionTimer !== null && (
//                           <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-lg font-black animate-pulse shadow-xl">
//                             {currentOptionTimer}s
//                           </div>
//                         )}
//                       </div>
//                     );
//                   })}
//                 </div>
//               </div>
//             </div>
//           )}
//         </div>
//         {questionData && (
//           <div className="border-t border-gray-200/50 p-8 flex-shrink-0 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm">
//             <div className="flex flex-col sm:flex-row gap-6 justify-between items-center">
//               <div className="text-lg text-gray-700 flex items-center gap-4 font-medium">
//                 <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black">
//                   {questionCount}
//                 </div>
//                 Question {questionCount} • Session ID:{' '}
//                 <span className="text-blue-600 font-bold">{sessionId}</span>
//               </div>
//               <div className="flex gap-4">
//                 {questionCount >= 5 && (
//                   <button
//                     onClick={() => setShowDashboard(true)}
//                     className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     <BarChart3 size={24} />
//                     View Results
//                   </button>
//                 )}
//                 {finalResults ? (
//                   <button
//                     onClick={handleShowResults}
//                     className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     <BarChart3 size={24} />
//                     Show Results
//                   </button>
//                 ) : (
//                   <button
//                     onClick={handleFinalize}
//                     className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     Finalize Session (Admin Only)
//                   </button>
//                 )}
//                 <button
//                   onClick={() => {
//                     initializeAudio();
//                     handleNextQuestion();
//                   }}
//                   className="px-10 py-4 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-black shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 disabled:transform-none text-lg"
//                   disabled={
//                     isFetchingNextQuestion ||
//                     quizStatus.includes('Capturing') ||
//                     quizStatus.includes('Processing') ||
//                     isReadingQuestion
//                   }
//                 >
//                   {isFetchingNextQuestion ? 'Processing...' : 'Start Capture & Next Question'}
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//       <audio ref={applauseAudioRef} preload="auto">
//         <source src={applauseAudio} type="audio/mpeg" />
//       </audio>
//       <audio ref={clockTickingAudioRef} preload="auto">
//         <source src={clockTickingAudio} type="audio/mpeg" />
//       </audio>
//       <style jsx>{`
//         @keyframes slideInRight {
//           from {
//             transform: translateX(100%);
//             opacity: 0;
//           }
//           to {
//             transform: translateX(0);
//             opacity: 1;
//           }
//         }
//         @keyframes float {
//           0%,
//           100% {
//             transform: translateY(0px) rotate(0deg);
//           }
//           50% {
//             transform: translateY(-20px) rotate(180deg);
//           }
//         }
//         @keyframes spin-slow {
//           from {
//             transform: rotate(0deg);
//           }
//           to {
//             transform: rotate(360deg);
//           }
//         }
//         @keyframes spin-reverse {
//           from {
//             transform: rotate(360deg);
//           }
//           to {
//             transform: rotate(0deg);
//           }
//         }
//         .animate-slideInRight {
//           animation: slideInRight 0.6s ease-out;
//         }
//         .animate-float {
//           animation: float 4s ease-in-out infinite;
//         }
//         .animate-spin-slow {
//           animation: spin-slow 8s linear infinite;
//         }
//         .animate-spin-reverse {
//           animation: spin-reverse 6s linear infinite;
//         }
//       `}</style>
//     </div>
//   );
// };

// export default LiveQuiz;

// import React, { useEffect, useRef, useState, useCallback } from 'react';
// import io from 'socket.io-client';
// import {
//   X,
//   BarChart3,
//   Video,
//   VideoOff,
//   Wifi,
//   WifiOff,
//   Clock,
//   Users,
//   Target,
//   Trophy,
//   Medal,
//   Award,
//   ChevronDown,
//   ChevronUp
// } from 'lucide-react';
// import { useStartQuizMutation, useNextQuestionMutation } from './centerTraineeLive.slice';
// import applauseAudio from '../../../../assets/audio/applause-180037.mp3';
// import clockTickingAudio from '../../../../assets/audio/clock-ticking-down-376897.mp3';

// // API Configuration
// const CAPTURE_INTERVAL_MS = 200;
// const DURATION_PER_OPTION_S = 10;
// const QUESTION_READING_TIME_S = 15;

// // Enhanced Circular Option Display Component with Animated Countdown
// const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
//   const circumference = 2 * Math.PI * 120;
//   const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
//   const strokeDasharray = circumference;
//   const strokeDashoffset = circumference - progress;
//   const getTimerColor = () => {
//     const percentage = timeRemaining / totalTime;
//     if (percentage > 0.6) return '#10B981';
//     if (percentage > 0.3) return '#F59E0B';
//     return '#EF4444';
//   };
//   const getScale = () => {
//     const percentage = timeRemaining / totalTime;
//     return 1 + (1 - percentage) * 0.2;
//   };
//   if (!isActive) return null;
//   return (
//     <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
//       <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />
//       <div className="absolute inset-0">
//         {[...Array(12)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
//             style={{
//               top: `${10 + i * 7}%`,
//               left: `${5 + i * 8}%`,
//               animationDelay: `${i * 0.3}s`,
//               animationDuration: `${3 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <div
//         className="relative transition-all duration-300 ease-out"
//         style={{ transform: `scale(${getScale()})` }}
//       >
//         <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />
//         <div className="relative w-64 h-64 flex items-center justify-center">
//           <svg
//             className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
//             viewBox="0 0 260 260"
//           >
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.3)"
//               strokeWidth="12"
//               className="drop-shadow-lg"
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke={getTimerColor()}
//               strokeWidth="12"
//               strokeLinecap="round"
//               strokeDasharray={strokeDasharray}
//               strokeDashoffset={strokeDashoffset}
//               className="transition-all duration-1000 ease-out drop-shadow-xl"
//               style={{
//                 filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
//               }}
//             />
//             <circle
//               cx="130"
//               cy="130"
//               r="100"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.2)"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               className="animate-spin-reverse"
//             />
//             <defs>
//               <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
//                 <stop offset="0%" stopColor="#3B82F6" />
//                 <stop offset="50%" stopColor="#8B5CF6" />
//                 <stop offset="100%" stopColor="#EC4899" />
//               </linearGradient>
//               <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
//                 <stop offset="0%" stopColor="#FFFFFF" />
//                 <stop offset="100%" stopColor="#F8FAFC" />
//               </radialGradient>
//             </defs>
//           </svg>
//           <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
//             <div
//               className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
//                 textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
//               }}
//             >
//               {option}
//             </div>
//             <div
//               className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
//                 filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
//               }}
//             >
//               {timeRemaining}s
//             </div>
//             <div className="flex gap-1 mt-2">
//               {[...Array(totalTime)].map((_, i) => (
//                 <div
//                   key={i}
//                   className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
//                     i < totalTime - timeRemaining
//                       ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
//                       : 'bg-gray-300 scale-100'
//                   }`}
//                 />
//               ))}
//             </div>
//           </div>
//           <div className="absolute inset-0 pointer-events-none">
//             {[...Array(8)].map((_, i) => (
//               <div
//                 key={i}
//                 className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
//                 style={{
//                   top: `${15 + i * 10}%`,
//                   left: `${10 + i * 12}%`,
//                   animationDelay: `${i * 0.3}s`,
//                   animationDuration: `${2 + (i % 2)}s`
//                 }}
//               />
//             ))}
//           </div>
//           <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Reading Timer Component
// const ReadingTimer = ({ timeRemaining, isActive }) => {
//   if (!isActive) return null;
//   return (
//     <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
//       <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
//         <div className="text-center">
//           <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
//             <Clock className="animate-spin" size={32} />
//             Reading Time
//           </div>
//           <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
//           <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
//         </div>
//       </div>
//     </div>
//   );
// };

// // Enhanced Hand Raise Animation Component
// const HandRaiseNotification = ({ logs }) => {
//   const [showNotification, setShowNotification] = useState(false);
//   const [latestLog, setLatestLog] = useState(null);
//   useEffect(() => {
//     if (logs.length > 0) {
//       const newest = logs[logs.length - 1];
//       setLatestLog(newest);
//       setShowNotification(true);
//       const timer = setTimeout(() => {
//         setShowNotification(false);
//       }, 4000);
//       return () => clearTimeout(timer);
//     }
//   }, [logs]);
//   if (!showNotification || !latestLog) return null;
//   return (
//     <div className="fixed top-24 right-8 z-50 transform animate-slideInRight">
//       <div className="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 text-white px-8 py-6 rounded-2xl shadow-2xl border-4 border-white/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-300">
//         <div className="flex items-center gap-4">
//           <div className="text-4xl animate-bounce">🙋‍♂️</div>
//           <div>
//             <div className="font-bold text-xl drop-shadow-sm">{latestLog.student_name}</div>
//             <div className="text-sm opacity-90 bg-white/20 px-3 py-1 rounded-full mt-1">
//               Option {latestLog.option ? latestLog.option.toUpperCase() : 'N/A'} • {latestLog.responseTime}s
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// const LiveQuiz = ({ quizId: inputQuizId, sessionId: inputSessionId, onClose }) => {
//   const [quizId, setQuizId] = useState(inputQuizId);
//   const [sessionId, setSessionId] = useState(inputSessionId || '');
//   const [clientId, setClientId] = useState('');
//   const [centerCode, setCenterCode] = useState(null);
//   const [questionData, setQuestionData] = useState(null);
//   const [quizStatus, setQuizStatus] = useState('');
//   const [summary, setSummary] = useState(null);
//   const [finalResults, setFinalResults] = useState(null);
//   const [liveFeedLogs, setLiveFeedLogs] = useState([]);
//   const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
//   const [socketStatus, setSocketStatus] = useState('Disconnected');
//   const [error, setError] = useState(null);
//   const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
//   const [currentOption, setCurrentOption] = useState('');
//   const [questionTimer, setQuestionTimer] = useState(null);
//   const [isReadingQuestion, setIsReadingQuestion] = useState(false);
//   const [showDashboard, setShowDashboard] = useState(false);
//   const [questionCount, setQuestionCount] = useState(0);
//   const [cameraStatus, setCameraStatus] = useState('Not initialized');
//   const [showManualInput, setShowManualInput] = useState(!inputQuizId);
//   const [manualQuizId, setManualQuizId] = useState('');
//   const [manualSessionId, setManualSessionId] = useState('');
//   const [startQuizMutation, { isLoading: isStartingQuiz }] = useStartQuizMutation();
//   const [nextQuestionMutation, { isLoading: isFetchingNextQuestion }] = useNextQuestionMutation();
//   const [dashboardData, setDashboardData] = useState(null);
//   const [isDashboardLoading, setIsDashboardLoading] = useState(false);
  
//   const videoRef = useRef(null);
//   const canvasRef = useRef(null);
//   const socketRef = useRef(null);
//   const videoStreamRef = useRef(null);
//   const timerIntervalRef = useRef(null);
//   const applauseAudioRef = useRef(null);
//   const clockTickingAudioRef = useRef(null);
  
//   // Track if socket has been initialized
//   const [isSocketInitialized, setIsSocketInitialized] = useState(false);
  
//   // Get center code from session storage
//   useEffect(() => {
//     const storedCenterCode = sessionStorage.getItem('centercode');
//     if (storedCenterCode && !centerCode) {
//       setCenterCode(storedCenterCode);
//       console.log(`🔒 Center code retrieved from session storage: ${storedCenterCode}`);
//     }
//   }, [centerCode]);
  
//   useEffect(() => {
//     if (quizId && videoRef.current && !isSocketInitialized) {
//       console.log('🎯 QuizId changed, initializing camera:', quizId);
//       initCamera();
//     }
//   }, [quizId, isSocketInitialized]);
  
//   // Cleanup function - now only disconnects socket when component unmounts
//   useEffect(() => {
//     return () => {
//       stopCamera();
//       if (socketRef.current) {
//         socketRef.current.disconnect();
//         socketRef.current = null;
//       }
//       if (timerIntervalRef.current) {
//         clearInterval(timerIntervalRef.current);
//       }
//       if (applauseAudioRef.current) {
//         applauseAudioRef.current.pause();
//         applauseAudioRef.current.currentTime = 0;
//       }
//       if (clockTickingAudioRef.current) {
//         clockTickingAudioRef.current.pause();
//         clockTickingAudioRef.current.currentTime = 0;
//       }
//     };
//   }, []);
  
//   // Initialize socket connection ONCE when component mounts
//   useEffect(() => {
//     const initSocket = () => {
//       if (socketRef.current || isSocketInitialized) return;
      
//       // Dynamically construct the Socket.IO URL based on the current environment
//       const socketUrl = `${window.location.protocol}//${window.location.hostname}:8036`;
//       console.log(`🔌 Initializing socket connection to: ${socketUrl}`);
      
//       socketRef.current = io(socketUrl, {
//         path: '/socketio2/socket.io',
//         transports: ['websocket', 'polling'],
//         upgrade: true,
//         rememberUpgrade: true,
//         timeout: 20000,
//         forceNew: false, // Critical change - don't force new connection
//         autoConnect: true,
//         reconnection: true,
//         reconnectionAttempts: 10, // Increased attempts
//         reconnectionDelay: 1000,
//         reconnectionDelayMax: 5000,
//         randomizationFactor: 0.5
//       });
      
//       // Set up socket event listeners
//       socketRef.current.on('connect', () => {
//         setSocketStatus('Connected');
//         console.log(`✅ Connected to Socket.IO server at ${socketUrl}`);
//       });
      
//       socketRef.current.on('disconnect', (reason) => {
//         setSocketStatus(`Disconnected: ${reason}`);
//         console.log(`❌ Socket disconnected: ${reason}`);
        
//         // Only try to reconnect if it wasn't intentional
//         if (reason !== 'io client disconnect') {
//           setTimeout(() => {
//             if (socketRef.current && !socketRef.current.connected) {
//               console.log('🔄 Attempting to reconnect...');
//               socketRef.current.connect();
//             }
//           }, 2000);
//         }
//       });
      
//       socketRef.current.on('connect_error', (error) => {
//         setSocketStatus(`Connection Error: ${error.message}`);
//         console.error('❌ Socket.IO connection error:', error);
//       });
      
//       socketRef.current.on('reconnect', (attemptNumber) => {
//         setSocketStatus(`Reconnected (attempt ${attemptNumber})`);
//         console.log(`🔄 Reconnected to Socket.IO server. Attempt: ${attemptNumber}`);
        
//         // If we have session info, rejoin the room
//         if (sessionId && clientId && centerCode) {
//           console.log(`🚪 Rejoining room with session=${sessionId}, client=${clientId}, center=${centerCode}`);
//           socketRef.current.emit('join_quiz_room', { 
//             session_id: sessionId, 
//             client_id: clientId,
//             center_code: centerCode
//           });
//         }
//       });
      
//       socketRef.current.on('reconnect_attempt', (attempt) => {
//         setSocketStatus(`Reconnecting... (attempt ${attempt})`);
//         console.log(`🔄 Reconnection attempt #${attempt}`);
//       });
      
//       socketRef.current.on('reconnect_error', (error) => {
//         console.error('❌ Socket.IO reconnection error:', error);
//       });
      
//       socketRef.current.on('reconnect_failed', () => {
//         setSocketStatus('Reconnection Failed');
//         console.error('❌ Socket.IO reconnection failed');
//       });
      
//       socketRef.current.on('hand_raised', (data) => {
//         console.log('🙋 Hand raised event received:', data);
//         setLiveFeedLogs((logs) => {
//           const newLog = {
//             ...data,
//             responseTime: currentQuestionStartTime
//               ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000)
//               : 0
//           };
//           return [...logs, newLog];
//         });
//       });
      
//       socketRef.current.on('center_identified', (data) => {
//         if (data.center_code && !centerCode) {
//           setCenterCode(data.center_code);
//           console.log(`🔒 This client is now locked to Center: ${data.center_code}`);
//         }
//       });
      
//       socketRef.current.onAny((eventName, ...args) => {
//         console.log(`📡 Socket event received: ${eventName}`, args);
//       });
      
//       setIsSocketInitialized(true);
//       return socketRef.current;
//     };
    
//     const socket = initSocket();
//     return () => {
//       if (socket) {
//         socket.disconnect();
//       }
//     };
//   }, []);
  
//   const generateUUID = () => crypto.randomUUID();
  
//   const startQuiz = async () => {
//     if (!quizId) {
//       setError('Missing Quiz ID');
//       return;
//     }
//     // Get center code from session storage
//     const storedCenterCode = sessionStorage.getItem('centercode');
//     if (!storedCenterCode) {
//       setError('Center code not found. Please log in again.');
//       return;
//     }
//     let currentSessionId = sessionId;
//     if (!currentSessionId) {
//       currentSessionId = generateUUID();
//       setSessionId(currentSessionId);
//     }
//     const currentClientId = generateUUID();
//     setClientId(currentClientId);
    
//     try {
//       setQuizStatus('Starting quiz...');
      
//       // First, join the socket room
//       await new Promise((resolve, reject) => {
//         if (!socketRef.current) {
//           reject('Socket not initialized');
//           return;
//         }
//         socketRef.current.emit('join_quiz_room', { 
//           session_id: currentSessionId, 
//           client_id: currentClientId,
//           center_code: storedCenterCode
//         }, (ack) => {
//           if (ack && ack.success) {
//             console.log('✅ Successfully joined quiz room');
//             resolve();
//           } else {
//             console.error('❌ Failed to join quiz room:', ack ? ack.error : 'No acknowledgment');
//             reject(ack ? ack.error : 'No acknowledgment received');
//           }
//         });
//       });
      
//       // Then, start the quiz via API
//       const response = await startQuizMutation({ 
//         quiz_id: quizId, 
//         session_id: currentSessionId, 
//         client_id: currentClientId,
//         center_code: storedCenterCode
//       }).unwrap();
      
//       if (response.error) {
//         throw new Error(response.error);
//       }
      
//       setQuestionData(response);
//       setQuizStatus('Ready to start capture for this question.');
//       setQuestionCount(1);
//       console.log('🎯 Starting quiz, initializing camera immediately');
//       await initCamera();
//       initializeAudio();
//     } catch (error) {
//       setError(`Error starting quiz: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   const handleNextQuestion = async () => {
//     if (!sessionId || !clientId || !centerCode) return;
//     try {
//       await startCaptureCycle();
//       setQuizStatus('Processing results and fetching next question...');
//       // Include center code in the request
//       const response = await nextQuestionMutation({ 
//         session_id: sessionId, 
//         client_id: clientId,
//         center_code: centerCode
//       }).unwrap();
//       if (response.status === 'completed') {
//         setFinalResults(response);
//         stopCamera();
//         if (socketRef.current) {
//           socketRef.current.disconnect();
//           socketRef.current = null;
//         }
//         setQuizStatus('Quiz completed! View results for details.');
//         if (applauseAudioRef.current) {
//           console.log('🎉 Playing applause sound for quiz completion');
//           applauseAudioRef.current.currentTime = 0;
//           applauseAudioRef.current.volume = 0.8;
//           applauseAudioRef.current.play()
//             .then(() => {
//               console.log('✅ Applause audio started successfully');
//             })
//             .catch((error) => {
//               console.error('❌ Error playing applause audio:', error);
//             });
//           setTimeout(() => {
//             if (applauseAudioRef.current) {
//               console.log('⏹️ Stopping applause audio after 4 seconds');
//               applauseAudioRef.current.pause();
//               applauseAudioRef.current.currentTime = 0;
//             }
//           }, 4000);
//         }
//       } else if (response.question) {
//         setQuestionData(response);
//         setQuizStatus('Ready to start capture for this question.');
//         setQuestionCount((prev) => prev + 1);
//       }
//     } catch (error) {
//       setError(`Error during quiz progression: ${error.message || error.data?.error || 'Unknown error'}`);
//       setQuizStatus(`Error: ${error.message || error.data?.error || 'Unknown error'}`);
//     }
//   };
  
//   // const handleFinalize = async () => {
//   //   if (!sessionId) return;
//   //   if (!window.confirm('Are you sure you want to end this session for ALL centers?')) return;
//   //   try {
//   //     const response = await finalizeSessionMutation({ session_id: sessionId }).unwrap();
//   //     console.log('Finalize response:', response);
//   //     setShowDashboard(true);
//   //   } catch (error) {
//   //     setError(`Error finalizing session: ${error.message || error.data?.error || 'Unknown error'}`);
//   //   }
//   // };
  
//   const handleShowResults = async () => {
//     if (!sessionId || !centerCode) return;
//     setIsDashboardLoading(true);
//     try {
//       const response = await fetch(`https://testing.sasthra.in/api/content/engagement_dashboard?session_id=${sessionId}&center_code=${centerCode}`);
//       if (!response.ok) {
//         throw new Error('Failed to fetch dashboard data');
//       }
//       const data = await response.json();
//       if (data.success === false) {
//         throw new Error(data.error || 'Unknown error');
//       }
//       setDashboardData(data);
//       setShowDashboard(true);
//     } catch (err) {
//       setError(`Error fetching results: ${err.message}`);
//     } finally {
//       setIsDashboardLoading(false);
//     }
//   };
  
//   const startCaptureCycle = async () => {
//     setLiveFeedLogs([]);
//     setIsReadingQuestion(true);
//     setQuestionTimer(QUESTION_READING_TIME_S);
//     setCurrentQuestionStartTime(Date.now());
//     timerIntervalRef.current = setInterval(() => {
//       setQuestionTimer((prev) => {
//         if (prev <= 1) {
//           clearInterval(timerIntervalRef.current);
//           return 0;
//         }
//         return prev - 1;
//       });
//     }, 1000);
//     await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));
//     setIsReadingQuestion(false);
//     setQuestionTimer(null);
//     const options = ['a', 'b', 'c', 'd'];
//     for (let i = 0; i < options.length; i++) {
//       setQuizStatus(
//         `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
//       );
//       await processSingleOption(options[i]);
//     }
//     setQuizStatus('Capture complete for this question. Results sent.');
//     setCurrentOptionTimer(null);
//     setCurrentOption('');
//   };
  
//   const processSingleOption = (optionChar) => {
//     return new Promise((resolve) => {
//       const startTime = Date.now();
//       let frameCount = 0;
//       let clockAudioTimeout = null;
//       setCurrentOption(optionChar.toUpperCase());
//       setCurrentOptionTimer(DURATION_PER_OPTION_S);
//       if (clockTickingAudioRef.current) {
//         console.log(`🔊 Playing clock ticking sound for option ${optionChar.toUpperCase()}`);
//         clockTickingAudioRef.current.currentTime = 0;
//         clockTickingAudioRef.current.volume = 0.7;
//         clockTickingAudioRef.current.play()
//           .then(() => {
//             console.log('✅ Clock ticking audio started successfully');
//           })
//           .catch((error) => {
//             console.error('❌ Error playing clock ticking audio:', error);
//           });
//         clockAudioTimeout = setTimeout(() => {
//           if (clockTickingAudioRef.current) {
//             console.log('⏹️ Stopping clock ticking audio after 10 seconds');
//             clockTickingAudioRef.current.pause();
//             clockTickingAudioRef.current.currentTime = 0;
//           }
//         }, 10000);
//       }
//       timerIntervalRef.current = setInterval(() => {
//         const elapsed = Math.floor((Date.now() - startTime) / 1000);
//         const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
//         setCurrentOptionTimer(remaining);
//       }, 1000);
//       const intervalId = setInterval(() => {
//         const responseTime = (Date.now() - startTime) / 1000;
//         captureAndSendFrame(optionChar, responseTime);
//         frameCount++;
//       }, CAPTURE_INTERVAL_MS);
//       setTimeout(() => {
//         clearInterval(intervalId);
//         if (timerIntervalRef.current) {
//           clearInterval(timerIntervalRef.current);
//         }
//         if (clockAudioTimeout) {
//           clearTimeout(clockAudioTimeout);
//         }
//         if (clockTickingAudioRef.current) {
//           console.log(`⏹️ Stopping clock ticking audio for option ${optionChar.toUpperCase()}`);
//           clockTickingAudioRef.current.pause();
//           clockTickingAudioRef.current.currentTime = 0;
//         }
//         resolve();
//       }, DURATION_PER_OPTION_S * 1000);
//     });
//   };
  
//   const captureAndSendFrame = useCallback((optionChar, responseTime) => {
//     const video = videoRef.current;
//     const canvas = canvasRef.current;
//     if (!video || !canvas) {
//       console.warn('⚠️ No video or canvas element available');
//       return;
//     }
//     if (!videoStreamRef.current) {
//       console.warn('⚠️ No video stream available');
//       return;
//     }
//     if (!socketRef.current?.connected) {
//       console.warn('⚠️ Socket not connected - cannot send frame');
//       return;
//     }
//     const ctx = canvas.getContext('2d');
//     canvas.width = video.videoWidth;
//     canvas.height = video.videoHeight;
//     ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
//     const frameData = canvas.toDataURL('image/jpeg', 0.6);
//     console.log(
//       `📸 Sending frame: session=${sessionId}, option=${optionChar}, time=${responseTime.toFixed(2)}s`
//     );
//     socketRef.current.emit('process_frame', {
//       session_id: sessionId,
//       client_id: clientId,
//       frame: frameData,
//       option_char: optionChar,
//       response_time_seconds: responseTime
//     });
//   }, [sessionId, clientId]);
  
//   const initCamera = async () => {
//     if (videoStreamRef.current) return;
//     try {
//       console.log('🎥 Requesting camera access...');
//       setCameraStatus('Requesting access...');
//       const stream = await navigator.mediaDevices.getUserMedia({
//         video: {
//           width: { ideal: 1280 },
//           height: { ideal: 720 }
//         }
//       });
//       if (videoRef.current) {
//         videoRef.current.srcObject = stream;
//         videoStreamRef.current = stream;
//         console.log('✅ Camera initialized successfully');
//         setCameraStatus('Active');
//       }
//     } catch (error) {
//       console.error('❌ Camera error:', error);
//       setCameraStatus('Error');
//       setError(`Camera error: ${error.message}`);
//     }
//   };
  
//   const stopCamera = () => {
//     if (videoStreamRef.current) {
//       videoStreamRef.current.getTracks().forEach((track) => track.stop());
//       videoStreamRef.current = null;
//     }
//     if (videoRef.current) {
//       videoRef.current.srcObject = null;
//     }
//     setCameraStatus('Not initialized');
//   };
  
//   const initializeAudio = () => {
//     if (applauseAudioRef.current) {
//       applauseAudioRef.current.load();
//       console.log('🔊 Applause audio initialized');
//     }
//     if (clockTickingAudioRef.current) {
//       clockTickingAudioRef.current.load();
//       console.log('🔊 Clock ticking audio initialized');
//     }
//   };
  
//   const testSocketConnection = () => {
//     if (socketRef.current) {
//       socketRef.current.emit('test_connection', { message: 'Hello from client' });
//     }
//   };
  
//   const handleCloseDashboard = () => {
//     setShowDashboard(false);
//   };
  
//   const handleManualQuizStart = async () => {
//     if (manualQuizId.trim()) {
//       setError(null);
//       setShowManualInput(false);
//       setQuizId(manualQuizId.trim());
//       // Get center code from session storage
//       const storedCenterCode = sessionStorage.getItem('centercode');
//       if (!storedCenterCode) {
//         setError('Center code not found. Please log in again.');
//         setShowManualInput(true);
//         return;
//       }
//       let currentSessionId = manualSessionId.trim();
//       if (!currentSessionId) {
//         currentSessionId = generateUUID();
//         setManualSessionId(currentSessionId);
//         setSessionId(currentSessionId);
//       } else {
//         setSessionId(currentSessionId);
//       }
//       await startQuiz();
//     } else {
//       setError('Please enter a valid Quiz ID');
//     }
//   };
  
//   useEffect(() => {
//     if (inputQuizId) {
//       startQuiz();
//     }
//   }, [inputQuizId]);
  
//   if (showDashboard && sessionId && centerCode) {
//     if (isDashboardLoading) {
//       return (
//         <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//           <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col items-center justify-center p-16">
//             <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-6"></div>
//             <h2 className="text-3xl font-bold text-gray-800">Loading Results...</h2>
//             <p className="text-lg text-gray-600 mt-2">Analyzing quiz performance data</p>
//           </div>
//         </div>
//       );
//     }
//     if (!dashboardData) {
//       return (
//         <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//           <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-md p-12 text-center">
//             <div className="text-6xl mb-6">⚠️</div>
//             <h2 className="text-3xl font-bold text-gray-800 mb-4">Failed to Load Results</h2>
//             <p className="text-gray-600 mb-8">There was an error retrieving the quiz results. Please try again later.</p>
//             <button
//               onClick={() => setShowDashboard(false)}
//               className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-lg font-bold"
//             >
//               Return to Quiz
//             </button>
//           </div>
//         </div>
//       );
//     }
//     return (
//       <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//         <div className="bg-white/95 backdrop-blur-xl rounded-3xl border-4 border-white/20 w-full max-w-6xl max-h-[95vh] flex flex-col">
//           <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-8 text-white relative overflow-hidden">
//             <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 animate-pulse" />
//             <div className="relative z-10 flex justify-between items-center">
//               <div className="flex items-center gap-6">
//                 <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
//                   <BarChart3 className="text-yellow-400" size={40} />
//                 </div>
//                 <div>
//                   <h2 className="text-4xl font-black mb-2">📊 Quiz Results</h2>
//                   <p className="text-xl opacity-90">Engagement Dashboard</p>
//                 </div>
//               </div>
//               <button
//                 onClick={handleCloseDashboard}
//                 className="text-white/80 hover:text-white transition-all duration-300 p-3 rounded-full hover:bg-white/20 hover:scale-110 transform"
//               >
//                 <X size={32} />
//               </button>
//             </div>
//           </div>
//           <div className="p-6 bg-gradient-to-r from-slate-800 to-slate-700 border-b border-white/10">
//             <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-white">
//               <div className="text-center">
//                 <div className="text-3xl font-black text-blue-400">
//                   {dashboardData?.total_questions_in_quiz || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Total Questions</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-green-400">
//                   {dashboardData?.engagement_summary_stats?.active_students || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Active Students</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-purple-400">
//                   {dashboardData?.engagement_summary_stats?.total_participations || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Total Participations</div>
//               </div>
//               <div className="text-center">
//                 <div className="text-3xl font-black text-yellow-400">
//                   {dashboardData?.engagement_summary_stats?.average_frequency?.toFixed(1) || 0}
//                 </div>
//                 <div className="text-sm opacity-80">Avg Frequency</div>
//               </div>
//             </div>
//           </div>
//           <div className="flex-1 overflow-y-auto p-8 space-y-4 bg-gradient-to-b from-slate-800 to-slate-900">
//             <h3 className="text-2xl font-black text-white mb-6 flex items-center gap-3">
//               <Trophy className="text-yellow-400" size={28} />
//               Top Performers
//             </h3>
//             <div className="space-y-3">
//               {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
//                 dashboardData.leaderboard.map((student, index) => (
//                   <div
//                     key={index}
//                     className="transform transition-all duration-300 hover:scale-[1.02] bg-gradient-to-r from-slate-700 to-slate-800 p-6 rounded-2xl shadow-xl border-2 border-white/20"
//                   >
//                     <div className="flex items-center justify-between text-white">
//                       <div className="flex items-center gap-6">
//                         <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center text-white font-black text-xl">
//                           {index + 1}
//                         </div>
//                         <div className="flex-1">
//                           <div className="text-2xl font-black mb-1">{student.first_name} {student.last_name}</div>
//                           <div className="text-sm opacity-90 flex gap-4">
//                             <span>Score: {student.score}</span>
//                             <span>Accuracy: {student.accuracy_percentage}%</span>
//                             <span>Avg Time: {student.avg_response_time_seconds}s</span>
//                           </div>
//                         </div>
//                         <div className="text-right">
//                           <div className="text-4xl font-black text-yellow-400">#{index + 1}</div>
//                           <div className="text-xs opacity-80 bg-white/20 px-2 py-1 rounded-full">
//                             {student.questions_attempted} Questions
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   </div>
//                 ))
//               ) : (
//                 <div className="text-center py-12 bg-slate-800 rounded-xl">
//                   <div className="text-5xl mb-4">📊</div>
//                   <h3 className="text-2xl font-bold text-white mb-2">No Results Available</h3>
//                   <p className="text-gray-400">The quiz results are still being processed or no students participated.</p>
//                 </div>
//               )}
//             </div>
            
//             <h3 className="text-2xl font-black text-white mt-10 mb-6 flex items-center gap-3">
//               <Users className="text-blue-400" size={28} />
//               Student Performance
//             </h3>
            
//             <div className="bg-slate-800 rounded-xl overflow-hidden">
//               <div className="overflow-x-auto">
//                 <table className="min-w-full">
//                   <thead>
//                     <tr className="bg-slate-700">
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Rank</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Student Name</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Score</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Accuracy</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Questions Attempted</th>
//                       <th className="py-4 px-6 text-left text-sm font-bold text-blue-300">Avg. Response Time</th>
//                     </tr>
//                   </thead>
//                   <tbody>
//                     {dashboardData?.leaderboard && dashboardData.leaderboard.length > 0 ? (
//                       dashboardData.leaderboard.map((student, index) => (
//                         <tr 
//                           key={index}
//                           className={`border-b border-slate-700 hover:bg-slate-700/50 transition-colors ${
//                             index % 2 === 0 ? 'bg-slate-800' : 'bg-slate-850'
//                           }`}
//                         >
//                           <td className="py-4 px-6">
//                             <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-bold">
//                               {index + 1}
//                             </span>
//                           </td>
//                           <td className="py-4 px-6 text-white font-medium">
//                             {student.first_name} {student.last_name}
//                           </td>
//                           <td className="py-4 px-6 text-green-400 font-bold">
//                             {student.score}
//                           </td>
//                           <td className="py-4 px-6 text-blue-300">
//                             {student.accuracy_percentage}%
//                           </td>
//                           <td className="py-4 px-6 text-purple-300">
//                             {student.questions_attempted}
//                           </td>
//                           <td className="py-4 px-6 text-cyan-300">
//                             {student.avg_response_time_seconds}s
//                           </td>
//                         </tr>
//                       ))
//                     ) : (
//                       <tr>
//                         <td colSpan="6" className="py-8 text-center text-gray-400">
//                           No student performance data available
//                         </td>
//                       </tr>
//                     )}
//                   </tbody>
//                 </table>
//               </div>
//             </div>
            
//             <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Target className="text-blue-400" size={24} />
//                   Question Performance
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Most Challenging Question</span>
//                     <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
//                       #{dashboardData?.most_challenging_question || 'N/A'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Easiest Question</span>
//                     <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
//                       #{dashboardData?.easiest_question || 'N/A'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Average Accuracy</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.average_accuracy?.toFixed(1) || '0.0'}%
//                     </span>
//                   </div>
//                 </div>
//               </div>
              
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Clock className="text-purple-400" size={24} />
//                   Response Analysis
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Fastest Response</span>
//                     <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
//                       {dashboardData?.fastest_response?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Slowest Response</span>
//                     <span className="bg-red-500/20 text-red-300 px-3 py-1 rounded-full">
//                       {dashboardData?.slowest_response?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Average Response Time</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.avg_response_time?.toFixed(1) || '0.0'}s
//                     </span>
//                   </div>
//                 </div>
//               </div>
              
//               <div className="bg-slate-800 rounded-xl p-6">
//                 <h4 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
//                   <Users className="text-green-400" size={24} />
//                   Engagement Metrics
//                 </h4>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Peak Engagement</span>
//                     <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">
//                       {dashboardData?.peak_engagement?.toFixed(1) || '0.0'}%
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Engagement Trend</span>
//                     <span className={`px-3 py-1 rounded-full ${
//                       dashboardData?.engagement_trend === 'increasing' 
//                         ? 'bg-green-500/20 text-green-300' 
//                         : dashboardData?.engagement_trend === 'decreasing' 
//                           ? 'bg-red-500/20 text-red-300' 
//                           : 'bg-yellow-500/20 text-yellow-300'
//                     }`}>
//                       {dashboardData?.engagement_trend || 'stable'}
//                     </span>
//                   </div>
//                   <div className="flex justify-between items-center">
//                     <span className="text-gray-300">Active Students</span>
//                     <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
//                       {dashboardData?.engagement_summary_stats?.active_students || 0}
//                     </span>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>
//           <div className="p-6 bg-slate-900 border-t border-white/10">
//             <button
//               onClick={onClose}
//               className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-xl"
//             >
//               <X size={24} />
//               Close Results & Exit Quiz
//             </button>
//           </div>
//         </div>
//       </div>
//     );
//   }
  
//   return (
//     <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
//       <div className="absolute inset-0 overflow-hidden">
//         {[...Array(20)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-2 h-2 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-30 animate-float"
//             style={{
//               top: `${Math.random() * 100}%`,
//               left: `${Math.random() * 100}%`,
//               animationDelay: `${i * 0.5}s`,
//               animationDuration: `${4 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>
//       <CircularOptionDisplay
//         option={currentOption}
//         timeRemaining={currentOptionTimer}
//         totalTime={DURATION_PER_OPTION_S}
//         isActive={!!currentOption && currentOptionTimer !== null}
//       />
//       <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />
//       <HandRaiseNotification logs={liveFeedLogs} />
//       <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col shadow-2xl">
//         <div className="flex justify-between items-center p-8 border-b border-gray-200/50 flex-shrink-0 bg-gradient-to-r from-blue-100/50 via-purple-100/50 to-pink-100/50">
//           <div className="flex items-center gap-6">
//             <div className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-all duration-300">
//               <Target className="text-white" size={32} />
//             </div>
//             <div>
//               <h2 className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
//                 Live Quiz Experience
//               </h2>
//               <p className="text-gray-600 text-lg font-medium">Real-time engagement monitoring</p>
//             </div>
//           </div>
//           <button
//             onClick={onClose}
//             className="text-gray-500 hover:text-red-500 transition-all duration-300 p-3 rounded-full hover:bg-red-50 hover:scale-105 transform"
//           >
//             <X size={28} />
//           </button>
//         </div>
//         <div className="flex-1 overflow-y-auto p-8 space-y-8">
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 socketStatus.startsWith('Connected')
//                   ? 'bg-gradient-to-r from-green-100 to-emerald-100 border-green-300 text-green-800 shadow-green-200/50'
//                   : 'bg-gradient-to-r from-red-100 to-pink-100 border-red-300 text-red-800 shadow-red-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${socketStatus.startsWith('Connected') ? 'bg-green-200' : 'bg-red-200'}`}
//                 >
//                   {socketStatus.startsWith('Connected') ? <Wifi size={24} /> : <WifiOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Socket Status</div>
//                   <div className="text-sm opacity-80 font-medium">{socketStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div
//               className={`p-6 rounded-2xl border-2 transition-all duration-500 transform hover:scale-105 ${
//                 cameraStatus === 'Active'
//                   ? 'bg-gradient-to-r from-blue-100 to-cyan-100 border-blue-300 text-blue-800 shadow-blue-200/50'
//                   : 'bg-gradient-to-r from-yellow-100 to-orange-100 border-yellow-300 text-yellow-800 shadow-yellow-200/50'
//               } shadow-xl`}
//             >
//               <div className="flex items-center gap-4">
//                 <div
//                   className={`p-3 rounded-xl ${cameraStatus === 'Active' ? 'bg-blue-200' : 'bg-yellow-200'}`}
//                 >
//                   {cameraStatus === 'Active' ? <Video size={24} /> : <VideoOff size={24} />}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Camera Status</div>
//                   <div className="text-sm opacity-80 font-medium">{cameraStatus}</div>
//                 </div>
//               </div>
//             </div>
//             <div className="p-6 rounded-2xl border-2 bg-gradient-to-r from-purple-100 to-pink-100 border-purple-300 text-purple-800 shadow-xl shadow-purple-200/50 transition-all duration-500 transform hover:scale-105">
//               <div className="flex items-center gap-4">
//                 <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black text-xl shadow-lg">
//                   {questionCount}
//                 </div>
//                 <div>
//                   <div className="font-bold text-lg">Question Progress</div>
//                   <div className="text-sm opacity-80 font-medium">Question {questionCount}</div>
//                 </div>
//               </div>
//             </div>
//           </div>
//           {socketRef.current && (
//             <button
//               onClick={testSocketConnection}
//               className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl transform hover:scale-105"
//             >
//               Test Socket Connection
//             </button>
//           )}
//           {error && (
//             <div className="text-center text-red-600 p-8 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 animate-pulse shadow-xl">
//               <div className="text-6xl mb-4">⚠️</div>
//               <p className="text-xl font-bold">{error}</p>
//             </div>
//           )}
//           {showManualInput && (
//             <div className="bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-8 border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//               <h3 className="text-3xl font-black text-gray-800 mb-6 flex items-center gap-4">
//                 <span className="text-4xl">🎯</span>
//                 Enter Quiz Details
//               </h3>
//               <div className="flex flex-col gap-6">
//                 <div>
//                   <label className="block text-lg font-bold text-gray-700 mb-3">Quiz ID</label>
//                   <input
//                     type="text"
//                     value={manualQuizId}
//                     onChange={(e) => setManualQuizId(e.target.value)}
//                     placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
//                     className="w-full px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-lg font-bold text-gray-700 mb-3">Session ID (Generate or paste)</label>
//                   <div className="flex gap-4">
//                     <input
//                       type="text"
//                       value={manualSessionId}
//                       onChange={(e) => setManualSessionId(e.target.value)}
//                       placeholder="Session ID"
//                       className="flex-1 px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
//                     />
//                     <button
//                       onClick={() => setManualSessionId(generateUUID())}
//                       className="px-6 py-4 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 transform hover:scale-105"
//                     >
//                       Generate
//                     </button>
//                   </div>
//                 </div>
//                 <button
//                   onClick={handleManualQuizStart}
//                   disabled={!manualQuizId.trim() || isStartingQuiz}
//                   className="px-10 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-teal-500 hover:from-purple-600 hover:via-blue-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 text-lg"
//                 >
//                   {isStartingQuiz ? 'Starting...' : 'Start Quiz'}
//                 </button>
//               </div>
//               <p className="text-gray-600 text-lg mt-4 font-medium">
//                 Enter the Quiz ID provided by your teacher to start the quiz.
//               </p>
//             </div>
//           )}
//           {!showManualInput && questionData && (
//             <div>
//               <button
//                 onClick={() => {
//                   setShowManualInput(true);
//                   setQuestionData(null);
//                   setQuizId(null);
//                   setError(null);
//                 }}
//                 className="px-6 py-3 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white rounded-xl text-lg font-medium transition-all duration-300 backdrop-blur-sm shadow-lg transform hover:scale-105"
//               >
//                 Enter Different Quiz ID
//               </button>
//             </div>
//           )}
//           {questionData && (
//             <div className="text-gray-800 space-y-8">
//               <div className="status-bar p-6 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-lg">
//                   <div className="font-bold">
//                     <strong>Session ID:</strong> <span className="text-blue-600">{sessionId}</span>
//                   </div>
//                   <div className="font-bold">
//                     <strong>Status:</strong> <span className="text-purple-600">{quizStatus}</span>
//                   </div>
//                 </div>
//               </div>
//               <div className="flex flex-col lg:flex-row gap-8">
//                 <div className="flex-1">
//                   <div className="relative rounded-2xl overflow-hidden border-4 border-white/50 bg-gradient-to-br from-gray-100 to-gray-200 backdrop-blur-sm shadow-2xl">
//                     <video
//                       ref={videoRef}
//                       autoPlay
//                       playsInline
//                       muted
//                       className="w-full max-w-2xl rounded-2xl"
//                     />
//                     <div
//                       className={`absolute top-6 left-6 px-4 py-3 rounded-xl text-lg font-bold backdrop-blur-sm shadow-lg ${
//                         cameraStatus === 'Active'
//                           ? 'bg-green-500/90 text-white'
//                           : cameraStatus === 'Error'
//                             ? 'bg-red-500/90 text-white'
//                             : 'bg-yellow-500/90 text-white'
//                       }`}
//                     >
//                       📹 Camera: {cameraStatus}
//                     </div>
//                   </div>
//                   <canvas ref={canvasRef} className="hidden" />
//                 </div>
//                 {liveFeedLogs.length > 0 && (
//                   <div className="w-full lg:w-80 bg-gradient-to-b from-white/90 to-blue-50/90 rounded-2xl p-6 border-2 border-blue-200 max-h-96 overflow-y-auto backdrop-blur-sm shadow-xl">
//                     <h3 className="text-xl font-black mb-4 text-gray-800 flex items-center gap-3">
//                       <Users className="text-blue-500" size={24} />
//                       Live Hand Raises ({liveFeedLogs.length})
//                     </h3>
//                     <div className="space-y-3">
//                       {liveFeedLogs
//                         .sort(
//                           (a, b) =>
//                             new Date(a.detection_timestamp) - new Date(b.detection_timestamp)
//                         )
//                         .map((log, idx) => (
//                           <div
//                             key={idx}
//                             className="bg-gradient-to-r from-white/80 to-blue-50/80 p-4 rounded-xl text-sm backdrop-blur-sm transform hover:scale-105 transition-all duration-300 shadow-lg border border-blue-200"
//                           >
//                             <div className="flex justify-between items-start mb-2">
//                               <span className="font-bold text-gray-800 text-lg">
//                                 #{idx + 1} {log.student_name}
//                               </span>
//                               <span className="text-sm text-gray-600 bg-blue-100 px-3 py-1 rounded-full font-medium">
//                                 {log.responseTime}s
//                               </span>
//                             </div>
//                             <div className="flex justify-between items-center">
//                               <span className="text-blue-600 font-bold text-lg">
//                                 Option {log.option ? log.option.toUpperCase() : 'N/A'}
//                               </span>
//                             </div>
//                           </div>
//                         ))}
//                     </div>
//                   </div>
//                 )}
//               </div>
//               <div className="space-y-6">
//                 <h2 className="text-3xl font-black bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent">
//                   {questionData.sub_topic_name}
//                 </h2>
//                 <div className="bg-gradient-to-r from-white/80 to-purple-50/80 p-8 rounded-2xl border-2 border-purple-200 backdrop-blur-sm shadow-xl">
//                   <p className="text-2xl text-gray-800 leading-relaxed font-medium">
//                     {questionData.question_number}. {questionData.question}
//                   </p>
//                 </div>
//                 {questionData.question_image && questionData.question_image.trim() && (
//                   <div className="bg-gradient-to-r from-white/80 to-blue-50/80 p-6 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
//                     <img
//                       src={questionData.question_image.trim()}
//                       alt="Question Image"
//                       className="max-w-full h-auto rounded-xl shadow-lg border-2 border-white/50"
//                       onError={(e) => {
//                         console.error('Failed to load question image:', e.target.src);
//                         if (e.target.src.includes('?X-Amz-')) {
//                           const baseUrl = e.target.src.split('?X-Amz-')[0];
//                           console.log('Trying base URL:', baseUrl);
//                           e.target.src = baseUrl;
//                         } else {
//                           e.target.style.display = 'none';
//                         }
//                       }}
//                       onLoad={() => {
//                         console.log(
//                           'Question image loaded successfully:',
//                           questionData.question_image
//                         );
//                       }}
//                     />
//                   </div>
//                 )}
//                 <div className="grid gap-4">
//                   {questionData.options.map((opt, idx) => {
//                     const optionLetter = String.fromCharCode(97 + idx);
//                     const isCurrentOption = currentOption.toLowerCase() === optionLetter;
//                     return (
//                       <div key={idx} className="relative">
//                         <div
//                           className={`p-6 rounded-2xl border-2 transition-all duration-500 transform ${
//                             isCurrentOption
//                               ? 'bg-gradient-to-r from-green-100 via-emerald-100 to-teal-100 border-green-400 shadow-2xl scale-105 shadow-green-300/50'
//                               : 'bg-gradient-to-r from-white/80 to-gray-50/80 border-gray-300 hover:bg-gray-100/80 hover:scale-102 shadow-lg'
//                           } backdrop-blur-sm`}
//                         >
//                           <span className="font-bold text-gray-800 text-xl">
//                             {optionLetter.toUpperCase()}. {opt}
//                           </span>
//                           {questionData.option_images && questionData.option_images[idx] && (
//                             <img
//                               src={questionData.option_images[idx]}
//                               alt={`Option ${optionLetter.toUpperCase()}`}
//                               className="max-w-90% max-h-150 mt-10 rounded-4"
//                             />
//                           )}
//                         </div>
//                         {isCurrentOption && currentOptionTimer !== null && (
//                           <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-lg font-black animate-pulse shadow-xl">
//                             {currentOptionTimer}s
//                           </div>
//                         )}
//                       </div>
//                     );
//                   })}
//                 </div>
//               </div>
//             </div>
//           )}
//         </div>
//         {questionData && (
//           <div className="border-t border-gray-200/50 p-8 flex-shrink-0 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm">
//             <div className="flex flex-col sm:flex-row gap-6 justify-between items-center">
//               <div className="text-lg text-gray-700 flex items-center gap-4 font-medium">
//                 <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black">
//                   {questionCount}
//                 </div>
//                 Question {questionCount} • Session ID:{' '}
//                 <span className="text-blue-600 font-bold">{sessionId}</span>
//               </div>
//               <div className="flex gap-4">
//                 {questionCount >= 5 && (
//                   <button
//                     onClick={() => setShowDashboard(true)}
//                     className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     <BarChart3 size={24} />
//                     View Results
//                   </button>
//                 )}
//                 {finalResults ? (
//                   <button
//                     onClick={handleShowResults}
//                     className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-500 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
//                   >
//                     <BarChart3 size={24} />
//                     Show Results
//                   </button>
//                 ) : null}
//                 <button
//                   onClick={() => {
//                     initializeAudio();
//                     handleNextQuestion();
//                   }}
//                   className="px-10 py-4 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-black shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 disabled:transform-none text-lg"
//                   disabled={
//                     isFetchingNextQuestion ||
//                     quizStatus.includes('Capturing') ||
//                     quizStatus.includes('Processing') ||
//                     isReadingQuestion
//                   }
//                 >
//                   {isFetchingNextQuestion ? 'Processing...' : 'Start Capture & Next Question'}
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//       <audio ref={applauseAudioRef} preload="auto">
//         <source src={applauseAudio} type="audio/mpeg" />
//       </audio>
//       <audio ref={clockTickingAudioRef} preload="auto">
//         <source src={clockTickingAudio} type="audio/mpeg" />
//       </audio>
//       <style jsx>{`
//         @keyframes slideInRight {
//           from {
//             transform: translateX(100%);
//             opacity: 0;
//           }
//           to {
//             transform: translateX(0);
//             opacity: 1;
//           }
//         }
//         @keyframes float {
//           0%,
//           100% {
//             transform: translateY(0px) rotate(0deg);
//           }
//           50% {
//             transform: translateY(-20px) rotate(180deg);
//           }
//         }
//         @keyframes spin-slow {
//           from {
//             transform: rotate(0deg);
//           }
//           to {
//             transform: rotate(360deg);
//           }
//         }
//         @keyframes spin-reverse {
//           from {
//             transform: rotate(360deg);
//           }
//           to {
//             transform: rotate(0deg);
//           }
//         }
//         .animate-slideInRight {
//           animation: slideInRight 0.6s ease-out;
//         }
//         .animate-float {
//           animation: float 4s ease-in-out infinite;
//         }
//         .animate-spin-slow {
//           animation: spin-slow 8s linear infinite;
//         }
//         .animate-spin-reverse {
//           animation: spin-reverse 6s linear infinite;
//         }
//       `}</style>
//     </div>
//   );
// };

// export default LiveQuiz;