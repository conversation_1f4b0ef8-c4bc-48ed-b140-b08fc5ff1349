import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { setLiveQuizData, useLazyGetLiveQuizServiceQuery } from '../dashboard.slice';
import { useDispatch, useSelector } from 'react-redux';
import Toastify from '../../../../../components/PopUp/Toastify';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import {
  ClipboardList,
  Check,
  Bar<PERSON>hart,
  Award,
  Timer,
  Info,
  RefreshCw,
  X,
  Pie<PERSON>hart
} from 'lucide-react';

// --- Constants ---
const PRIMARY_GRADIENT = 'bg-gradient-to-br from-blue-500 to-indigo-600';

// --- Helper & State Components (Reused from CBT for consistency) ---
const GlassCard = ({ children, className = '' }) => (
  <div
    className={`bg-white/60 backdrop-blur-xl border border-white/30 rounded-2xl shadow-lg shadow-blue-500/10 ring-1 ring-black/5 ${className}`}>
    {children}
  </div>
);
const QuizSkeleton = () => (
  <div className="animate-pulse space-y-6">
    <div className="h-10 w-48 bg-slate-200 rounded-lg"></div>
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)]?.map((_, i) => (
        <div key={i} className="h-28 bg-slate-200 rounded-xl"></div>
      ))}
    </div>
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="h-80 bg-slate-200 rounded-xl"></div>
      <div className="h-80 bg-slate-200 rounded-xl"></div>
    </div>
    <div className="h-64 bg-slate-200 rounded-xl"></div>
  </div>
);
const ErrorDisplay = ({ error, onRetry }) => (
  <div className="flex flex-col items-center justify-center text-center p-8 bg-red-50/50 border border-red-200 rounded-xl">
    <div className="w-14 h-14 flex items-center justify-center bg-red-100 rounded-full mb-4">
      <X className="w-8 h-8 text-red-600" />
    </div>
    <h3 className="text-xl font-semibold text-red-800">Failed to Load Data</h3>
    <p className="text-red-600 mt-2 mb-4 max-w-md">
      {error?.data?.message || 'An unexpected error occurred.'}
    </p>
    <button
      onClick={onRetry}
      className="flex items-center px-4 py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 transition-colors shadow-sm">
      <RefreshCw className="w-4 h-4 mr-2" /> Try Again
    </button>
  </div>
);
const EmptyState = ({ message }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    className="text-center p-12 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/30 shadow-lg shadow-blue-500/10">
    <div className="w-14 h-14 flex items-center justify-center bg-slate-100 rounded-full mb-4 mx-auto">
      <Info className="w-8 h-8 text-slate-400" />
    </div>
    <h3 className="text-xl font-semibold text-slate-700">No Quizzes Found</h3>
    <p className="text-slate-500 mt-2">{message}</p>
  </motion.div>
);

// --- UI Components ---
const LiveQuizHeader = () => (
  <div className="mb-8">
    <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
      Live Quiz Analytics
    </h1>
    <p className="text-slate-500 mt-1">Review your performance in real-time quizzes.</p>
  </div>
);
const StatCard = ({ icon, title, value, unit = '' }) => (
  <motion.div
    variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}
    className="relative p-6 bg-white rounded-2xl shadow-lg shadow-blue-500/10 border border-slate-200/50 overflow-hidden">
    <div className="flex items-center gap-4">
      <div className={`p-3 rounded-full text-white ${PRIMARY_GRADIENT}`}>{icon}</div>
      <div>
        <p className="text-sm font-medium text-slate-500">{title}</p>
        <p className="text-2xl font-bold text-slate-800">
          {value} <span className="text-lg font-medium text-slate-400">{unit}</span>
        </p>
      </div>
    </div>
  </motion.div>
);
const AccuracyDonutChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { type: 'pie' },
      title: {
        text: `${data.overallAccuracy.toFixed(1)}%`,
        align: 'center',
        verticalAlign: 'middle',
        y: 10,
        style: { fontSize: '24px', fontWeight: 'bold', color: '#334155' }
      },
      subtitle: {
        text: 'Overall Accuracy',
        align: 'center',
        verticalAlign: 'middle',
        y: 30,
        style: { fontSize: '14px', color: '#64748b' }
      },
      tooltip: { pointFormat: '<b>{point.y}</b> questions ({point.percentage:.1f}%)' },
      plotOptions: {
        pie: {
          innerSize: '70%',
          dataLabels: { enabled: false },
          showInLegend: true,
          borderWidth: 3
        }
      },
      series: [
        {
          name: 'Questions',
          data: [
            { name: 'Correct', y: data.correct, color: '#10b981' },
            { name: 'Incorrect', y: data.incorrect, color: '#ef4444' },
            { name: 'Unattempted', y: data.unattempted, color: '#94a3b8' }
          ]
        }
      ]
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <PieChart className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Response Accuracy</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const SubjectPerformanceChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { type: 'bar' },
      xAxis: { categories: data?.map((d) => d.name), labels: { style: { color: '#64748b' } } },
      yAxis: { title: { text: 'Average Accuracy (%)' }, max: 100 },
      legend: { enabled: false },
      tooltip: { pointFormat: 'Avg. Accuracy: <b>{point.y:.1f}%</b>' },
      series: [
        { name: 'Average Accuracy', data: data?.map((d) => ({ y: d.avg, color: d.color })) }
      ],
      plotOptions: {
        bar: {
          borderRadius: 5,
          dataLabels: {
            enabled: true,
            format: '{y:.1f}%',
            style: { color: '#334155', textOutline: 'none' }
          }
        }
      }
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <BarChart className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Performance by Subject</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const RecentQuizzesTable = ({ data }) => (
  <GlassCard className="overflow-hidden">
    <h3 className="p-4 text-lg font-bold text-slate-700 border-b border-slate-900/10">
      Recent Quizzes
    </h3>
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead className="bg-slate-50/50">
          <tr>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Subject</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Score</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Accuracy</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Avg. Time</th>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Date</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Status</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-slate-900/5">
          {data.slice(0, 10)?.map((quiz, index) => (
            <motion.tr
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              className="hover:bg-blue-500/10 transition-colors">
              <td className="px-6 py-4 font-medium text-slate-800">{quiz.subject}</td>
              <td className="px-6 py-4 text-center font-bold text-indigo-600">{quiz.score}</td>
              <td className="px-6 py-4 text-center text-slate-600 font-semibold">
                {quiz.accuracy_percentage.toFixed(1)}%
              </td>
              <td className="px-6 py-4 text-center text-slate-500">
                {quiz.avg_response_time_seconds}s
              </td>
              <td className="px-6 py-4 text-slate-500">{quiz.created_at}</td>
              <td className="px-6 py-4 text-center">
                <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800 capitalize">
                  {quiz.status}
                </span>
              </td>
            </motion.tr>
          ))}
        </tbody>
      </table>
    </div>
  </GlassCard>
);

// --- Main Component ---
const LiveQuiz = () => {
  const [toastRes, setToastRes] = useState(null);
  const [trigger, { isLoading, isError, error }] = useLazyGetLiveQuizServiceQuery();
  const dispatch = useDispatch();
  const liveQuizData = useSelector((state) => state.studentDashboard.liveQuizData);

  const handleGetLiveQuizServiceApi = useCallback(async () => {
    try {
      const res = await trigger({
        center_code: sessionStorage.centercode,
        user_id: sessionStorage.userId
      }).unwrap();
      dispatch(setLiveQuizData(res));
    } catch (err) {
      setToastRes(err);
    }
  }, [dispatch, trigger]);

  useEffect(() => {
    handleGetLiveQuizServiceApi();
  }, [handleGetLiveQuizServiceApi]);

  // --- Memoized Data Processing for Analytics ---
  const performanceStats = useMemo(() => {
    if (liveQuizData?.length === 0)
      return { total: 0, avgAccuracy: 'N/A', bestScore: 'N/A', avgTime: 'N/A' };

    const totalQuizzes = liveQuizData?.length;
    const totalAccuracy = liveQuizData?.reduce((sum, quiz) => sum + quiz.accuracy_percentage, 0);
    const bestScore = Math.max(...(liveQuizData?.map((quiz) => quiz.score) ?? [0]));
    const totalResponseTime = liveQuizData?.reduce(
      (sum, quiz) => sum + quiz.avg_response_time_seconds,
      0
    );

    return {
      total: totalQuizzes,
      avgAccuracy: (totalAccuracy / totalQuizzes).toFixed(1),
      bestScore: bestScore,
      avgTime: (totalResponseTime / totalQuizzes).toFixed(1)
    };
  }, [liveQuizData]);

  const accuracyData = useMemo(() => {
    const totals = liveQuizData?.reduce(
      (acc, quiz) => {
        acc.correct += quiz.correct_responses || 0;
        acc.incorrect += quiz.incorrect_responses || 0;
        acc.unattempted += quiz.unattempted_questions || 0;
        return acc;
      },
      { correct: 0, incorrect: 0, unattempted: 0 }
    );

    const totalQuestions = totals?.correct + totals?.incorrect + totals?.unattempted;
    const overallAccuracy = totalQuestions > 0 ? (totals?.correct / totalQuestions) * 100 : 0;

    return { ...totals, overallAccuracy };
  }, [liveQuizData]);

  const subjectPerformanceData = useMemo(() => {
    const subjectMap = new Map();
    liveQuizData?.forEach((quiz) => {
      if (!subjectMap.has(quiz.subject)) {
        subjectMap.set(quiz.subject, { totalAccuracy: 0, count: 0 });
      }
      const current = subjectMap.get(quiz.subject);
      current.totalAccuracy += quiz.accuracy_percentage;
      current.count += 1;
    });

    const colors = ['#4f46e5', '#10b981', '#f97316', '#8b5cf6', '#ec4899'];
    return Array.from(subjectMap.entries())?.map(([name, data], i) => ({
      name,
      avg: data.totalAccuracy / data.count,
      color: colors[i % colors?.length]
    }));
  }, [liveQuizData]);

  // --- Render Logic ---
  const renderContent = () => {
    if (isLoading) return <QuizSkeleton />;
    if (isError) return <ErrorDisplay error={error} onRetry={handleGetLiveQuizServiceApi} />;

    return (
      <>
        <LiveQuizHeader />
        {liveQuizData?.length > 0 ? (
          <div className="space-y-6">
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
              initial="hidden"
              animate="visible"
              variants={{ visible: { transition: { staggerChildren: 0.1 } } }}>
              <StatCard
                icon={<ClipboardList />}
                title="Total Quizzes"
                value={performanceStats.total}
              />
              <StatCard
                icon={<Check />}
                title="Overall Accuracy"
                value={performanceStats.avgAccuracy}
                unit="%"
              />
              <StatCard icon={<Award />} title="Best Score" value={performanceStats.bestScore} />
              <StatCard
                icon={<Timer />}
                title="Avg. Response"
                value={performanceStats.avgTime}
                unit="s"
              />
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}>
                <AccuracyDonutChart data={accuracyData} />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}>
                <SubjectPerformanceChart data={subjectPerformanceData} />
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}>
              <RecentQuizzesTable data={liveQuizData} />
            </motion.div>
          </div>
        ) : (
          <EmptyState message="You haven't completed any live quizzes yet." />
        )}
      </>
    );
  };

  return (
    <div className="p-0">
      <Toastify res={toastRes} resClear={() => setToastRes(null)} />
      {renderContent()}
    </div>
  );
};

export default LiveQuiz;
