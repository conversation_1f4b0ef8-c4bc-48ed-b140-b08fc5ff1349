import { questionPaperGenerationApi } from '../../../../redux/api/api';

export const uploadQuestionPaperSlice = questionPaperGenerationApi.injectEndpoints({
  endpoints: (builder) => ({
    uploadQuestionPaper: builder.mutation({
      query: (formData) => ({
        url: '/upload_pyq_paper',
        method: 'POST',
        body: formData,
        formData: true
      }),
      transformResponse: (response) => {
        console.log('Raw uploadQuestionPaper response:', response);
        if (response.message && response.questions) {
          const result = {
            ...response,
            questions: response.questions || []
          };
          console.log('Transformed response:', result);
          return result;
        }
        return response;
      },
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.error || 'Error uploading question paper'
      })
    })
  })
});

export const { useUploadQuestionPaperMutation } = uploadQuestionPaperSlice;
