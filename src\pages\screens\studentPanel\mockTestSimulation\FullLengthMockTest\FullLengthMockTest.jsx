import { useState, useEffect, useRef } from "react"
import {
  Clock,
  ChevronRight,
  ChevronLeft,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Award,
  RotateCcw,
  EyeOff,
  Eye,
} from "lucide-react"
import { MathJax, MathJaxContext } from "better-react-mathjax"
import { useGetMockTestQuestionsQuery, useSubmitTestResultsMutation } from "./fullLengthMockTest.slice.js"

// Component to render text with LaTeX formatting
const MathText = ({ children }) => {
  if (!children) return null

  const text = children.toString()

  // Split text by math delimiters
  const parts = []
  let currentIndex = 0

  // Find all math expressions (both inline and display)
  const mathRegex = /(\$\$([^$]+)\$\$|\$([^$]+)\$)/g
  let match

  while ((match = mathRegex.exec(text)) !== null) {
    // Add text before math expression
    if (match.index > currentIndex) {
      parts.push({
        type: "text",
        content: text.substring(currentIndex, match.index),
      })
    }

    // Add math expression
    if (match[2]) {
      // Display math ($$...$$)
      parts.push({
        type: "display",
        content: `\\[${match[2].trim()}\\]`,
      })
    } else if (match[3]) {
      // Inline math ($...$)
      parts.push({
        type: "inline",
        content: `\$$${match[3].trim()}\$$`,
      })
    }

    currentIndex = match.index + match[0].length
  }

  // Add remaining text
  if (currentIndex < text.length) {
    parts.push({
      type: "text",
      content: text.substring(currentIndex),
    })
  }

  // If no math found, return original text
  if (parts.length === 0) {
    return <span>{text}</span>
  }

  return (
    <span>
      {parts.map((part, index) => {
        switch (part.type) {
          case "display":
          case "inline":
            return <MathJax key={index}>{part.content}</MathJax>
          case "text":
            return <span key={index}>{part.content}</span>
          default:
            return <span key={index}>{part.content}</span>
        }
      })}
    </span>
  )
}

const FullLengthMockTest = () => {
  const [testStarted, setTestStarted] = useState(false)
  const [questions, setQuestions] = useState([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState({})
  const [timeRemaining, setTimeRemaining] = useState(180 * 60) // 180 minutes in seconds
  const [showResults, setShowResults] = useState(false)
  const [testCompleted, setTestCompleted] = useState(false)
  const [selectedExam, setSelectedExam] = useState("neet")
  const [showImage, setShowImage] = useState(false)
  const [tabSwitchCount, setTabSwitchCount] = useState(0)
  const [showWarning, setShowWarning] = useState(false)
  const [numericalAnswer, setNumericalAnswer] = useState("")
  const timerRef = useRef(null)

  // Exam configurations
  const EXAM_PATTERNS = {
    neet: { physics: 45, chemistry: 45, biology: 90 },
    jee_mains: { physics: 25, chemistry: 25, mathematics: 25 },
  }

  const MARKING_SCHEME = {
    neet: { correct: 4, incorrect: -1, unattempted: 0 },
    jee_mains: { correct: 4, incorrect: -1, unattempted: 0 },
  }

  const EXAM_DURATIONS = {
    neet: 180, // 180 minutes
    jee_mains: 180, // 180 minutes
  }

  // MathJax configuration
  const mathJaxConfig = {
    loader: { load: ["[tex]/html"] },
    tex: {
      packages: { "[+]": ["html"] },
      inlineMath: [["$$", "$$"]],
      displayMath: [["\\[", "\\]"]],
      processEscapes: true,
      processEnvironments: true,
    },
  }

  // Fetch questions using Redux Toolkit Query
  const { data, error, isLoading } = useGetMockTestQuestionsQuery(
    { examName: selectedExam },
    { skip: !testStarted }
  )

  const [submitTestResults] = useSubmitTestResultsMutation()

  // Process questions when data is fetched
  useEffect(() => {
    if (data) {
      let allQuestions = []
      let questionIndex = 0
      Object.keys(data.subjects).forEach((subject) => {
        const subjectQuestions = data.subjects[subject].map((q) => ({
          ...q,
          questionIndex: questionIndex++,
          subject: subject.charAt(0).toUpperCase() + subject.slice(1),
        }))
        allQuestions = [...allQuestions, ...subjectQuestions]
      })
      const shuffledQuestions = allQuestions.sort(() => Math.random() - 0.5)
      setQuestions(shuffledQuestions)
    }
    if (error) {
      console.error("Error fetching questions:", error)
      alert("Failed to load questions. Please check your connection and ensure the backend is running.")
    }
  }, [data, error])

  // Timer effect
  useEffect(() => {
    if (testStarted && !testCompleted && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            submitTest()
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [testStarted, testCompleted, timeRemaining])

  // Tab switch detection
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (testStarted && !testCompleted && document.hidden) {
        setTabSwitchCount((prev) => {
          const newCount = prev + 1
          if (newCount <= 3) {
            setShowWarning(true)
          }
          if (newCount === 3) {
            submitTest()
          }
          return Math.min(newCount, 3)
        })
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [testStarted, testCompleted])

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const startTest = () => {
    setTestStarted(true)
    setShowResults(false)
    setAnswers({})
    setCurrentQuestionIndex(0)
    setTestCompleted(false)
    setTimeRemaining(EXAM_DURATIONS[selectedExam] * 60)
    setTabSwitchCount(0)
    setNumericalAnswer("")
    // Enter full-screen mode
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error("Failed to enter full-screen mode:", err)
      })
    }
  }

  const selectAnswer = (optionIndex) => {
    setAnswers((prev) => ({
      ...prev,
      [currentQuestionIndex]: optionIndex + 1, // Convert to 1-based indexing for backend
    }))
  }

  const handleNumericalAnswer = (value) => {
    setNumericalAnswer(value)
    setAnswers((prev) => ({
      ...prev,
      [currentQuestionIndex]: value, // Store numerical answer as string
    }))
  }

  const navigateQuestion = (direction) => {
    if (direction === "next" && currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1)
      setNumericalAnswer("") // Reset numerical answer for next question
    } else if (direction === "prev" && currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1)
      setNumericalAnswer(answers[currentQuestionIndex - 1] || "") // Restore previous answer
    }
  }

  const jumpToQuestion = (index) => {
    setCurrentQuestionIndex(index)
    setNumericalAnswer(answers[index] || "") // Restore answer for selected question
  }

  const submitTest = async () => {
    setTestStarted(false)
    setTestCompleted(true)
    setShowResults(true)
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }

    const results = calculateResults()
    const studentId = sessionStorage.getItem("userId") || "defaultStudentId"

    const testData = {
      studentId,
      examType: selectedExam,
      totalQuestions: questions.length,
      answered: Object.keys(answers).length,
      correct: results.correct,
      incorrect: results.incorrect,
      unanswered: results.unanswered,
      totalMarks: results.totalMarks,
      maxMarks: results.maxMarks,
      percentage: results.percentage,
      timeTaken: EXAM_DURATIONS[selectedExam] * 60 - timeRemaining,
      timestamp: new Date().toISOString(),
      answers: answers,
    }

    try {
      await submitTestResults(testData).unwrap()
    } catch (error) {
      console.error("Error saving results to backend:", error)
      alert("Failed to save test results. Please try again.")
    }
  }

  const calculateResults = () => {
    let correct = 0
    let incorrect = 0
    let unanswered = 0
    const markingScheme = MARKING_SCHEME[selectedExam]

    questions.forEach((question, index) => {
      const userAnswer = answers[index]
      if (userAnswer === undefined) {
        unanswered++
      } else if (question.question_type === "numerical") {
        // For numerical questions, assume exact match with correct answer (string comparison)
        if (userAnswer === question.answer) {
          correct++
        } else {
          incorrect++
        }
      } else if (userAnswer === Number.parseInt(question.answer)) {
        correct++
      } else {
        incorrect++
      }
    })

    const totalMarks =
      correct * markingScheme.correct + incorrect * markingScheme.incorrect + unanswered * markingScheme.unattempted

    return {
      correct,
      incorrect,
      unanswered,
      totalMarks,
      percentage: ((correct / questions.length) * 100).toFixed(1),
      maxMarks: questions.length * markingScheme.correct,
    }
  }

  const resetTest = () => {
    setTestStarted(false)
    setShowResults(false)
    setQuestions([])
    setAnswers({})
    setCurrentQuestionIndex(0)
    setTimeRemaining(EXAM_DURATIONS[selectedExam] * 60)
    setTestCompleted(false)
    setTabSwitchCount(0)
    setNumericalAnswer("")
  }

  const getQuestionStatus = (index) => {
    if (answers[index] !== undefined) return "answered"
    return "unanswered"
  }

  const getTotalQuestions = () => {
    const pattern = EXAM_PATTERNS[selectedExam]
    return Object.values(pattern).reduce((sum, count) => sum + count, 0)
  }

  const getMaxMarks = () => {
    const totalQuestions = getTotalQuestions()
    return totalQuestions * MARKING_SCHEME[selectedExam].correct
  }

  const getSubjects = () => {
    const pattern = EXAM_PATTERNS[selectedExam]
    return Object.keys(pattern)
  }

  const renderQuestion = () => {
    if (isLoading) {
      return (
        <div className="flex h-96 flex-col items-center justify-center gap-3 rounded-xl border border-gray-200 bg-white">
          <div className="h-10 w-10 animate-spin rounded-full border-2 border-blue-600 border-b-transparent" />
          <span className="text-sm text-gray-600">Loading questions...</span>
        </div>
      )
    }

    if (questions.length === 0) return null

    const question = questions[currentQuestionIndex]
    const userAnswer = answers[currentQuestionIndex]

    return (
      <div className="flex h-full flex-col rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="rounded-full bg-blue-50 px-3 py-1 text-sm font-medium text-blue-700">
              {question.subject}
            </span>
            <span className="text-sm text-gray-600">
              Question {currentQuestionIndex + 1} of {questions.length}
            </span>
          </div>
          <div className="flex items-center gap-2 rounded-md bg-gray-50 px-3 py-1.5 text-blue-700" aria-live="polite">
            <Clock className="h-4 w-4" />
            <span className="text-lg font-bold">{formatTime(timeRemaining)}</span>
          </div>
        </div>

        <div className="mb-6 flex-1">
          <h3 className="mb-4 text-lg font-semibold leading-relaxed text-gray-900">
            <MathText>{question.question_text}</MathText>
          </h3>

          {question.actual_question_image && (
            <button
              onClick={() => setShowImage(!showImage)}
              className="mb-3 inline-flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {showImage ? <EyeOff size={18} /> : <Eye size={18} />}
              <span>{showImage ? "Hide image" : "Show image"}</span>
            </button>
          )}

          {showImage && question.actual_question_image && (
            <div className="rounded-lg border border-gray-200 bg-white p-3">
              <img
                src={question.actual_question_image || "/placeholder.svg"}
                alt="Question"
                className="mx-auto h-60 w-80 rounded-md object-contain"
              />
            </div>
          )}
        </div>

        <div className="mb-6 grid gap-3">
          {question.question_type === "numerical" && question.options.length === 0 ? (
            <div className="flex flex-col gap-2">
              <input
                type="text"
                value={numericalAnswer}
                onChange={(e) => handleNumericalAnswer(e.target.value)}
                placeholder="Enter numerical answer"
                className="w-full rounded-lg border-2 border-gray-200 p-4 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          ) : (
            question.options.map((option, index) => {
              const selected = userAnswer === index + 1
              return (
                <button
                  key={index}
                  onClick={() => selectAnswer(index)}
                  className={`group w-full rounded-lg border-2 p-4 text-left transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    selected
                      ? "border-blue-500 bg-blue-50 shadow-sm"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <span className="mr-3 inline-flex h-6 w-6 items-center justify-center rounded-full border text-sm font-semibold text-gray-700 group-hover:bg-white">
                    {String.fromCharCode(65 + index)}
                  </span>
                  <span className="align-middle text-gray-900">
                    <MathText>{option}</MathText>
                  </span>
                </button>
              )
            })
          )}
        </div>

        <div className="mt-auto flex items-center justify-between">
          <button
            onClick={() => navigateQuestion("prev")}
            disabled={currentQuestionIndex === 0}
            className="inline-flex items-center rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <ChevronLeft className="mr-1 h-4 w-4" />
            Previous
          </button>

          <div className="flex items-center gap-2">
            <button
              onClick={submitTest}
              className="rounded-lg bg-red-600 px-5 py-2 text-sm font-semibold text-white transition-colors hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              Submit Test
            </button>
            <button
              onClick={() => navigateQuestion("next")}
              disabled={currentQuestionIndex === questions.length - 1}
              className="inline-flex items-center rounded-lg bg-blue-600 px-5 py-2 text-sm font-semibold text-white transition-colors hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Next
              <ChevronRight className="ml-1 h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    )
  }

  const renderQuestionPalette = () => {
    if (!testStarted || questions.length === 0) return null

    return (
      <div className="flex h-full flex-col rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-semibold text-gray-900">Question Palette</h4>

        {/* Summary Stats */}
        <div className="mb-6 grid grid-cols-3 gap-2">
          <div className="rounded bg-green-50 p-2 text-center">
            <div className="text-lg font-bold text-green-700">{Object.keys(answers).length}</div>
            <div className="text-xs text-gray-600">Answered</div>
          </div>
          <div className="rounded bg-gray-50 p-2 text-center">
            <div className="text-lg font-bold text-gray-700">{questions.length - Object.keys(answers).length}</div>
            <div className="text-xs text-gray-600">Remaining</div>
          </div>
          <div className="rounded bg-blue-50 p-2 text-center">
            <div className="text-lg font-bold text-blue-700">{questions.length}</div>
            <div className="text-xs text-gray-600">Total</div>
          </div>
        </div>

        {/* Subject-wise breakdown */}
        <div className="mb-6">
          <h5 className="mb-3 text-sm font-medium text-gray-900">Subject Progress</h5>
          <div className="space-y-2">
            {getSubjects().map((subject) => {
              const subjectQuestions = questions.filter((q) => q.subject.toLowerCase() === subject.toLowerCase())
              const subjectIndices = []
              questions.forEach((q, index) => {
                if (q.subject.toLowerCase() === subject.toLowerCase()) {
                  subjectIndices.push(index)
                }
              })
              const answeredCount = subjectIndices.filter((index) => answers[index] !== undefined).length
              const totalCount = subjectQuestions.length
              const percentage = totalCount > 0 ? (answeredCount / totalCount) * 100 : 0

              // Fixed color mapping: keep to red/blue/green for consistency
              const barColor =
                subject.toLowerCase() === "physics"
                  ? "bg-red-500"
                  : subject.toLowerCase() === "chemistry"
                    ? "bg-blue-500"
                    : "bg-green-500"

              return (
                <div key={subject} className="text-xs">
                  <div className="mb-1 flex justify-between">
                    <span className="font-medium capitalize text-gray-900">{subject}</span>
                    <span className="text-gray-700">
                      {answeredCount}/{totalCount}
                    </span>
                  </div>
                  <div className="h-2 w-full rounded-full bg-gray-200">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${barColor}`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Question grid */}
        <div className="flex-1 overflow-hidden">
          <div className="grid max-h-full grid-cols-6 gap-2 overflow-y-auto pr-1">
            {questions.map((question, index) => (
              <button
                key={index}
                onClick={() => jumpToQuestion(index)}
                className={`flex h-10 w-10 items-center justify-center rounded text-xs font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  currentQuestionIndex === index
                    ? "scale-110 bg-blue-600 text-white ring-2 ring-blue-300"
                    : getQuestionStatus(index) === "answered"
                      ? "bg-green-500 text-white hover:bg-green-600"
                      : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                }`}
                title={`Question ${index + 1} - ${question.subject}`}
              >
                {index + 1}
              </button>
            ))}
          </div>
        </div>

        <div className="mt-4 border-t pt-4">
          <div className="flex items-center justify-between text-xs text-gray-700">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 rounded bg-green-500" />
              <span>Answered</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 rounded bg-gray-300" />
              <span>Not Answered</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 rounded bg-blue-600" />
              <span>Current</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderResults = () => {
    const results = calculateResults()

    return (
      <div className="mx-auto max-w-6xl p-4 md:p-6">
        <div className="mb-6 rounded-xl border border-gray-200 bg-white p-6 shadow-sm md:p-8">
          <div className="mb-8 text-center">
            <Award className="mx-auto mb-4 h-14 w-14 text-yellow-500" />
            <h2 className="mb-2 text-2xl font-bold text-gray-900 md:text-3xl">Test Completed!</h2>
            <p className="text-gray-600">Here's your performance summary for {selectedExam.toUpperCase()}</p>
          </div>

          <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-4 md:gap-6">
            <div className="rounded-lg bg-green-50 p-4 text-center">
              <CheckCircle className="mx-auto mb-2 h-7 w-7 text-green-600" />
              <div className="text-2xl font-bold text-green-700">{results.correct}</div>
              <div className="text-sm text-gray-600">Correct (+{MARKING_SCHEME[selectedExam].correct} each)</div>
            </div>
            <div className="rounded-lg bg-red-50 p-4 text-center">
              <XCircle className="mx-auto mb-2 h-7 w-7 text-red-600" />
              <div className="text-2xl font-bold text-red-700">{results.incorrect}</div>
              <div className="text-sm text-gray-600">Incorrect ({MARKING_SCHEME[selectedExam].incorrect} each)</div>
            </div>
            <div className="rounded-lg bg-gray-50 p-4 text-center">
              <AlertTriangle className="mx-auto mb-2 h-7 w-7 text-gray-600" />
              <div className="text-2xl font-bold text-gray-700">{results.unanswered}</div>
              <div className="text-sm text-gray-600">Unanswered (0 each)</div>
            </div>
            <div className="rounded-lg bg-blue-50 p-4 text-center">
              <Award className="mx-auto mb-2 h-7 w-7 text-blue-600" />
              <div className="text-2xl font-bold text-blue-700">{results.totalMarks}</div>
              <div className="text-sm text-gray-600">Total Marks / {results.maxMarks}</div>
            </div>
          </div>

          <div className="mb-6 text-center">
            <div className="mb-1 text-4xl font-bold text-blue-700">{results.percentage}%</div>
            <div className="text-gray-600">Overall Percentage</div>
          </div>

          <button
            onClick={resetTest}
            className="mx-auto flex w-full items-center justify-center rounded-lg bg-blue-600 px-4 py-3 font-semibold text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 md:w-1/2"
          >
            <RotateCcw className="mr-2 h-5 w-5" />
            Take Another Test
          </button>
        </div>

        {/* Detailed Results */}
        <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-sm md:p-6">
          <h3 className="mb-4 text-xl font-bold text-gray-900">Detailed Analysis</h3>
          <div className="max-h-96 space-y-4 overflow-y-auto">
            {questions.map((question, index) => {
              const userAnswer = answers[index]
              const correctAnswer = question.question_type === "numerical" ? question.answer : Number.parseInt(question.answer)
              const isCorrect = userAnswer === correctAnswer
              const wasAnswered = userAnswer !== undefined

              return (
                <div key={index} className="rounded-lg border border-gray-200 p-4">
                  <div className="mb-2 flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900">Q{index + 1}.</span>
                      <span className="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700">{question.subject}</span>
                    </div>
                    <div
                      className={`rounded px-2 py-1 text-xs font-medium ${
                        !wasAnswered
                          ? "bg-gray-100 text-gray-700"
                          : isCorrect
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                      }`}
                    >
                      {!wasAnswered
                        ? "Not Answered (0)"
                        : isCorrect
                          ? `Correct (+${MARKING_SCHEME[selectedExam].correct})`
                          : `Incorrect (${MARKING_SCHEME[selectedExam].incorrect})`}
                    </div>
                  </div>
                  <p className="mb-3 text-sm text-gray-800">
                    <MathText>{question.question_text}</MathText>
                  </p>
                  <div className="grid grid-cols-1 gap-2 text-sm md:grid-cols-2">
                    <div>
                      <span className="font-medium text-gray-900">Your Answer: </span>
                      {wasAnswered ? (
                        <span className={isCorrect ? "text-green-700" : "text-red-700"}>
                          {question.question_type === "numerical" ? userAnswer : String.fromCharCode(65 + userAnswer - 1)}
                        </span>
                      ) : (
                        <span className="text-gray-600">Not answered</span>
                      )}
                    </div>
                    <div>
                      <span className="font-medium text-gray-900">Correct Answer: </span>
                      <span className="text-green-700">
                        {question.question_type === "numerical" ? correctAnswer : String.fromCharCode(65 + correctAnswer - 1)}
                      </span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    )
  }

  if (showResults) {
    return <MathJaxContext config={mathJaxConfig}>{renderResults()}</MathJaxContext>
  }

  const answeredCount = Object.keys(answers).length

  return (
    <MathJaxContext config={mathJaxConfig}>
      {testStarted && (
        <header className="fixed inset-x-0 top-0 z-40 border-b border-gray-200 bg-white/90 backdrop-blur">
          <div className="mx-auto flex max-w-7xl items-center justify-between px-4 py-3">
            <div className="flex items-center gap-3">
              <span className="rounded bg-blue-50 px-2.5 py-1 text-xs font-semibold text-blue-700">
                {selectedExam.toUpperCase()}
              </span>
              <div className="text-sm text-gray-700">
                Q{currentQuestionIndex + 1}/{questions.length || 0}
              </div>
              <div className="hidden h-4 w-px bg-gray-300 md:block" />
              <div className="text-sm text-gray-700">
                Answered: <span className="font-semibold">{answeredCount}</span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div
                className="inline-flex items-center gap-2 rounded-md bg-gray-50 px-3 py-1.5 text-blue-700"
                aria-live="polite"
              >
                <Clock className="h-4 w-4" />
                <span className="font-bold">{formatTime(timeRemaining)}</span>
              </div>
              <button
                onClick={submitTest}
                className="rounded-lg bg-red-600 px-4 py-2 text-sm font-semibold text-white transition-colors hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Submit
              </button>
            </div>
          </div>
        </header>
      )}

      <div className={`min-h-screen bg-gray-50 ${testStarted ? "pt-20" : ""}`}>
        {/* Warning Modal */}
        {showWarning && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
            <div className="w-full max-w-md rounded-lg border border-gray-200 bg-white p-6 shadow-xl">
              <div className="mb-4 flex items-center justify-center">
                <AlertTriangle className="h-12 w-12 text-red-600" />
              </div>
              <h3 className="mb-2 text-center text-xl font-bold text-gray-900">Warning {tabSwitchCount} of 3</h3>
              <p className="mb-6 text-center text-gray-600">
                Switching tabs is not allowed. Your test will be auto-submitted after 3 warnings.
              </p>
              <button
                onClick={() => setShowWarning(false)}
                className="w-full rounded-lg bg-blue-600 py-2 font-semibold text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                OK
              </button>
            </div>
          </div>
        )}

        {testStarted ? (
          <div className="mx-auto flex h-[calc(100vh-5rem)] max-w-7xl flex-col px-4 pb-4">
            <div className="grid min-h-0 flex-1 grid-cols-1 gap-4 lg:grid-cols-4">
              <div className="flex min-h-0 flex-col lg:col-span-3">{renderQuestion()}</div>
              <div className="flex min-h-0 flex-col lg:col-span-1">{renderQuestionPalette()}</div>
            </div>
          </div>
        ) : (
          <div className="px-4 py-10 text-center">
            <Clock className="mx-auto mb-4 h-16 w-16 text-blue-600" />
            <h2 className="mb-2 text-2xl font-bold text-gray-900">Full Length Mock Test</h2>

            {/* Exam Selection */}
            <div className="mx-auto mb-8 max-w-2xl">
              <h3 className="mb-4 text-lg font-semibold text-gray-900">Select Exam</h3>
              <div className="flex justify-center gap-3">
                <div className="w-48">
                  <button
                    onClick={() => setSelectedExam("neet")}
                    className={`w-full rounded-lg border-2 px-5 py-3 text-sm font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      selectedExam === "neet"
                        ? "border-blue-600 bg-blue-50 text-blue-700"
                        : "border-gray-300 text-gray-800 hover:border-gray-400 hover:bg-gray-50"
                    }`}
                  >
                    NEET
                  </button>
                </div>
                <div className="w-48">
                  <button
                    onClick={() => setSelectedExam("jee_mains")}
                    className={`w-full rounded-lg border-2 px-5 py-3 text-sm font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      selectedExam === "jee_mains"
                        ? "border-blue-600 bg-blue-50 text-blue-700"
                        : "border-gray-300 text-gray-800 hover:border-gray-400 hover:bg-gray-50"
                    }`}
                  >
                    JEE MAINS
                  </button>
                </div>
              </div>
            </div>

            <div className="mx-auto mb-8 grid max-w-3xl grid-cols-1 gap-4 md:grid-cols-3">
              <div className="rounded-lg border border-blue-100 bg-blue-50 p-4">
                <div className="text-2xl font-bold text-blue-700">{getTotalQuestions()}</div>
                <div className="text-sm text-gray-600">Total Questions</div>
              </div>
              <div className="rounded-lg border border-green-100 bg-green-50 p-4">
                <div className="text-2xl font-bold text-green-700">{EXAM_DURATIONS[selectedExam]}</div>
                <div className="text-sm text-gray-600">Minutes</div>
              </div>
              <div className="rounded-lg border border-blue-100 bg-blue-50 p-4">
                <div className="text-2xl font-bold text-blue-700">{getMaxMarks()}</div>
                <div className="text-sm text-gray-600">Total Marks</div>
              </div>
            </div>

            <div className="mb-6 text-sm text-gray-700">
              <p>
                <strong>Marking Scheme:</strong> Correct: +{MARKING_SCHEME[selectedExam].correct}, Incorrect:{" "}
                {MARKING_SCHEME[selectedExam].incorrect}, Unattempted: {MARKING_SCHEME[selectedExam].unattempted}
              </p>
            </div>

            <button
              onClick={startTest}
              disabled={isLoading}
              className="mx-auto inline-flex items-center rounded-lg bg-blue-600 px-8 py-4 text-lg font-semibold text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isLoading ? "Loading..." : "Start Test"}
            </button>
          </div>
        )}
      </div>
    </MathJaxContext>
  )
}

export default FullLengthMockTest
