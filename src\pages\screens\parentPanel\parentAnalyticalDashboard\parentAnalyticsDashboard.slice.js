import { createSlice } from '@reduxjs/toolkit';
import { AnalyticsApi } from '../../../../redux/api/api';

const initialState = {
  studentsAnalyticalData: null,
  liveQuizData: null,
  aiTutorQuizData: null,
  studentOcrData: null,
  studentOmrData: null
};

export const parentAnalyticsDashboardApiSlice = AnalyticsApi.injectEndpoints({
  endpoints: (builder) => ({
    getStudentAnalyticalData: builder.query({
      query: (query) => {
        return `/analytics/parent/${query.studentId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentLiveQuizData: builder.query({
      query: (query) => {
        return `/analytics/parent/live-quiz/${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentAiTutorQuizData: builder.query({
      query: (query) => {
        return `/analytics/students-quiz/${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentOcrData: builder.query({
      query: (query) => {
        return `/analytics/ocr/student/${query.studentId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentOmrData: builder.query({
      query: (query) => {
        return `/analytics/omr/student/${query.studentId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    })
  })
});

const parentAnalyticsDashboardSlice = createSlice({
  name: 'parentAnalyticsDashboard',
  initialState,
  reducers: {
    setStudentsAnalyticalData: (state, action) => {
      state.studentsAnalyticalData = action.payload;
    },
    clearStudentsAnalyticalData: (state) => {
      state.studentsAnalyticalData = null;
    },
    setLiveQuizData: (state, action) => {
      state.liveQuizData = action.payload;
    },
    clearLiveQuizData: (state) => {
      state.liveQuizData = null;
    },
    setAiTutorQuizData: (state, action) => {
      state.aiTutorQuizData = action.payload;
    },
    clearAiTutorQuizData: (state) => {
      state.aiTutorQuizData = null;
    },
    setOcrData: (state, action) => {
      state.studentOcrData = action.payload;
    },
    clearOcrData: (state) => {
      state.studentOcrData = null;
    },
    setStudentOmrData: (state, action) => {
      state.studentOmrData = action.payload;
    },
    clearStudentOmrData: (state) => {
      state.studentOmrData = null;
    }
  }
});

export const {
  useLazyGetStudentAnalyticalDataQuery,
  useLazyGetStudentLiveQuizDataQuery,
  useLazyGetStudentAiTutorQuizDataQuery,
  useLazyGetStudentOcrDataQuery,
  useLazyGetStudentOmrDataQuery
} = parentAnalyticsDashboardApiSlice;
export const {
  setStudentsAnalyticalData,
  clearStudentsAnalyticalData,
  setLiveQuizData,
  clearLiveQuizData,
  setAiTutorQuizData,
  clearAiTutorQuizData,
  setOcrData,
  clearOcrData,
  setStudentOmrData,
  clearStudentOmrData
} = parentAnalyticsDashboardSlice.actions;
export const selectParentAnalytics = (state) => state.parentDashboard.studentsAnalyticalData;
export default parentAnalyticsDashboardSlice.reducer;
