import { mentorApi } from '../../../../redux/api/api';

export const addMentorSlice = mentorApi.injectEndpoints({
  endpoints: (builder) => ({
    directorMentorDashboardService: builder.query({
      query: (body) => ({
        url: '/mentor-dashboard',
        method: 'GET',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log(' Mentor Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['mentorApi']
    }),
    mentorSession: builder.mutation({
      query: ({ action, sessionId }) => ({
        url: '/mentor-session',
        method: 'POST',
        body: { action, session_id: sessionId }
      }),
      transformResponse: (response) => {
        console.log('Mentor Session Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['mentorApi']
    }),
    mentorsByStatus: builder.query({
      query: () => ({
        url: '/mentors-by-status',
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Mentors By Status Response:', response);
        return response;
      },
      transformErrorResponse: ({ status, data }) => ({
        status,
        data
      }),
      providesTags: ['mentorApi']
    }),
    onlineMentors: builder.query({
      query: () => ({
        url: '/mentors-online',
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Online Mentors Response:', response);
        return response;
      },
      transformErrorResponse: ({ status, data }) => ({
        status,
        data
      }),
      providesTags: ['mentorApi']
    }),
    awayMentors: builder.query({
      query: () => ({
        url: '/mentors-away',
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Away Mentors Response:', response);
        return response;
      },
      transformErrorResponse: ({ status, data }) => ({
        status,
        data
      }),
      providesTags: ['mentorApi']
    }),
    offlineMentors: builder.query({
      query: () => ({
        url: '/mentors-offline',
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Offline Mentors Response:', response);
        return response;
      },
      transformErrorResponse: ({ status, data }) => ({
        status,
        data
      }),
      providesTags: ['mentorApi']
    })
  })
});

export const { useDirectorMentorDashboardServiceQuery, useMentorSessionMutation, useMentorsByStatusQuery, useOnlineMentorsQuery, useAwayMentorsQuery, useOfflineMentorsQuery } = addMentorSlice;

