import { useState } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import ParentAnalyticsDashboard from './ParentAnalyticsDashboard';
import ParentLiveQuiz from './ParentLiveQuiz/ParentLiveQuiz';
import ParentStudentQuiz from './ParentStudentQuiz/ParentStudentQuiz';
import ParentOcr from './ParentOcr/ParentOcr';
import ParentOmr from './ParentOmr/ParentOmr';

const ParentAnalyticalTabs = () => {
  const [activeTab, setActiveTab] = useState('computer-based-test');

  const Tabs = [
    {
      id: 'computer-based-test',
      label: 'Computer Based Test',
      element: <ParentAnalyticsDashboard />
    },
    {
      id: 'live-quiz',
      label: 'Live Quiz',
      element: <ParentLiveQuiz />
    },
    {
      id: 'student-quiz',
      label: 'AI Tutor Quiz',
      element: <ParentStudentQuiz />
    },
    {
      id: 'ocr',
      label: 'OCR',
      element: <ParentOcr />
    },
    {
      id: 'omr',
      label: 'OMR',
      element: <ParentOmr />
    },
  ];

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-900 to-gray-900 text-slate-100 isolate overflow-x-hidden">
      <div
        className="absolute inset-x-0 -z-10 transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true">
        <div
          className="relative left-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-20 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
          }}
        />
      </div>

      <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 space-y-10">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, ease: 'easeOut' }}
          className="text-center">
          <h1 className="text-4xl sm:text-5xl font-extrabold text-white text-balance tracking-tight">
            Student Performance
            <span className="ml-3 text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 via-sky-400 to-emerald-400">
              Analytics Hub
            </span>
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-base text-slate-400">
            A unified dashboard for tracking Computer Based Tests, Live Quizzes, and AI Tutor Quiz
            performance.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.2, ease: 'easeOut' }}
          className="flex justify-center">
          <div className="flex space-x-2 bg-slate-800/40 backdrop-blur-md p-2 rounded-2xl border border-slate-700 shadow-lg shadow-indigo-500/10">
            {Tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`relative px-4 sm:px-6 py-3 text-sm sm:text-base font-semibold rounded-xl transition-colors duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900 focus-visible:ring-sky-400 ${
                  activeTab === tab.id ? 'text-white' : 'text-slate-300 hover:text-white'
                }`}>
                {activeTab === tab.id && (
                  <motion.div
                    layoutId="activeTabBackground"
                    className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-xl shadow-md"
                    transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                  />
                )}
                <span className="relative z-10 tracking-wide">{tab.label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        <div className="mt-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}>
              {Tabs.find((tab) => tab.id === activeTab)?.element}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default ParentAnalyticalTabs;
