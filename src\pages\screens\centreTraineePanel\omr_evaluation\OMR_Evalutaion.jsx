import { useState, useRef, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useListSubjectsQuery } from '../../directorPanel/subjects/subjects.Slice';
import { useLazyGetCenterStudentsFacultyQuery } from '../overView/centerTraineedOverview.slice';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FaClipboardList,
  FaPen,
  FaUserGraduate,
  FaIdBadge,
  FaUsers,
  FaGraduationCap,
  FaBookOpen,
  FaBuilding,
  FaFileAlt,
  FaCloudUploadAlt,
  FaCheckCircle,
  FaWeightHanging,
  FaFileImage,
  FaPlus,
  FaRedo,
  FaExclamationTriangle,
  FaChevronDown,
  FaTimes
} from 'react-icons/fa';

import { useOmrEvaluationServiceMutation, setOmrEvaluation } from './omr_evaluation.Slice';

const CreateOMRSheetEvaluation = () => {
  const dispatch = useDispatch();
  const { EvaluationData } = useSelector((state) => state.evaluation || { EvaluationData: null });

  const [createEvaluation, { isLoading: isCreatingEvaluation }] = useOmrEvaluationServiceMutation();
  const {
    data: subjectsData,
    error: subjectsError,
    isLoading: isLoadingSubjects
  } = useListSubjectsQuery();
  const [
    triggerGetCenterData,
    { data: centerData, error: centerError, isLoading: isLoadingCenterData }
  ] = useLazyGetCenterStudentsFacultyQuery();

  const [res, setRes] = useState(null);
  const [omrSheetImage, setOmrSheetImage] = useState(null);
  const [answerKeyFile, setAnswerKeyFile] = useState(null);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const omrSheetInputRef = useRef(null);
  const answerKeyInputRef = useRef(null);

  const [evaluationForm, setEvaluationForm] = useState({
    student_id: '',
    student_name: '',
    admission_no: '',
    section: '',
    student_email: '',
    subject: 'General',
    center_code: ''
  });

  useEffect(() => {
    triggerGetCenterData();
  }, [triggerGetCenterData]);

  useEffect(() => {
    if (centerData?.faculty?.center_code) {
      setEvaluationForm((prev) => ({
        ...prev,
        center_code: centerData.faculty.center_code
      }));
    }
  }, [centerData]);

  const containerVariants = {
    hidden: { opacity: 0, y: 16 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, staggerChildren: 0.1 } }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const buttonVariants = {
    hover: { scale: 1.05, transition: { duration: 0.2 } },
    tap: { scale: 0.98, transition: { duration: 0.1 } }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'student_id') {
      const selectedStudent = centerData?.students?.find((student) => student.id === value);
      setEvaluationForm((prev) => ({
        ...prev,
        student_id: value,
        student_name: selectedStudent
          ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
          : '',
        admission_no: selectedStudent?.admission_no || '',
        section: selectedStudent?.grade || '',
        student_email: selectedStudent?.student_email || ''
      }));
    } else {
      setEvaluationForm((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleOmrSheetChange = (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (!['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'].includes(file.type)) {
      setRes({
        status: 400,
        data: { message: 'Only PNG, JPEG, or PDF files are allowed for OMR sheet' }
      });
      return;
    }
    if (file.size > 5 * 1024 * 1024) { // Reduced to 5MB
      setRes({ status: 400, data: { message: 'OMR sheet file size exceeds 5MB limit' } });
      return;
    }
    setOmrSheetImage(file);
  };

  const handleAnswerKeyChange = (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (file.type !== 'text/csv') {
      setRes({ status: 400, data: { message: 'Only CSV files are allowed for answer key' } });
      return;
    }
    if (file.size > 5 * 1024 * 1024) { // Reduced to 5MB
      setRes({ status: 400, data: { message: 'Answer key file size exceeds 5MB limit' } });
      return;
    }
    setAnswerKeyFile(file);
  };

  const clearOmrSheet = () => {
    setOmrSheetImage(null);
    if (omrSheetInputRef.current) omrSheetInputRef.current.value = '';
  };

  const clearAnswerKey = () => {
    setAnswerKeyFile(null);
    if (answerKeyInputRef.current) answerKeyInputRef.current.value = '';
  };

  const resetForm = () => {
    setEvaluationForm({
      student_id: '',
      student_name: '',
      admission_no: '',
      section: '',
      student_email: '',
      subject: 'General',
      center_code: centerData?.faculty?.center_code || ''
    });
    setOmrSheetImage(null);
    setAnswerKeyFile(null);
    setSubmitAttempted(false);
    if (omrSheetInputRef.current) omrSheetInputRef.current.value = '';
    if (answerKeyInputRef.current) answerKeyInputRef.current.value = '';
  };

  const resClear = () => setRes(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitAttempted(true);

    if (!omrSheetImage) {
      setRes({ status: 400, data: { message: 'Please upload an OMR sheet image' } });
      return;
    }
    if (!answerKeyFile) {
      setRes({ status: 400, data: { message: 'Please upload an answer key file' } });
      return;
    }
    if (!evaluationForm.student_id) {
      setRes({ status: 400, data: { message: 'Please select a student' } });
      return;
    }
    if (!evaluationForm.subject) {
      setRes({ status: 400, data: { message: 'Please select a subject' } });
      return;
    }
    if (!evaluationForm.student_email) {
      setRes({ status: 400, data: { message: 'Student email is missing' } });
      return;
    }

    const formData = new FormData();
    formData.append('student_sheet_image', omrSheetImage);
    formData.append('answer_key_file', answerKeyFile);
    formData.append('student_id', evaluationForm.student_id);
    formData.append('student_name', evaluationForm.student_name);
    formData.append('admission_no', evaluationForm.admission_no);
    formData.append('section', evaluationForm.section);
    formData.append('student_email', evaluationForm.student_email);
    formData.append('subject', evaluationForm.subject);
    formData.append('center_code', evaluationForm.center_code);

    for (let [key, value] of formData.entries()) {
      console.log(`${key}:`, value instanceof File ? value.name : value);
    }
    try {
      const response = await createEvaluation(formData).unwrap();
      const evaluationData = response.data || response;
      dispatch(setOmrEvaluation(evaluationData));
      setRes({ status: 200, data: { message: 'OMR evaluation created successfully!' } });
      resetForm();
    } catch (error) {
      setRes({
        status: error.status || 500,
        data: { message: error.data?.message || 'Failed to create OMR evaluation' }
      });
    }
  };

  const isStep1Done = !!evaluationForm.student_id;
  const isStep2Done = !!evaluationForm.subject;
  const isStep3Done = !!(omrSheetImage && answerKeyFile);

  const showError = useMemo(() => {
    if (!submitAttempted) return {};
    return {
      student_id: !evaluationForm.student_id,
      subject: !evaluationForm.subject,
      student_sheet_image: !omrSheetImage,
      answer_key_file: !answerKeyFile
    };
  }, [submitAttempted, evaluationForm.student_id, evaluationForm.subject, omrSheetImage, answerKeyFile]);

  return (
    <div className="min-h-screen p-6 bg-gradient-to-br from-white to-gray-50 font-poppins relative">
      <Toastify res={res} resClear={resClear} />

      <div className="max-w-6xl mx-auto relative z-10">
        <motion.header
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-10">
          <h1 className="text-4xl md:text-5xl font-extrabold mb-4 text-[#F59E0B] drop-shadow-md">
            <FaClipboardList className="inline-block mr-4" />
            OMR Sheet Evaluation
          </h1>
          <p className="text-gray-700 text-lg md:text-xl leading-relaxed max-w-2xl mx-auto">
            Streamline OMR evaluations with a modern and efficient interface
          </p>
        </motion.header>

        <motion.div initial={{ opacity: 0, y: 12 }} animate={{ opacity: 1, y: 0 }} className="mb-6">
          <div className="bg-white border border-[#F59E0B]/20 rounded-xl p-4 shadow-lg">
            <ol className="flex justify-between items-center space-x-4">
              {[
                { label: 'Select Student', done: isStep1Done },
                { label: 'Choose Details', done: isStep2Done },
                { label: 'Upload Files', done: isStep3Done }
              ].map((step, idx) => (
                <li key={idx} className="flex-1 text-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2 ${
                      step.done
                        ? 'bg-[#F59E0B] border-[#F59E0B] text-white'
                        : 'border-2 border-[#F59E0B] text-[#F59E0B]'
                    } transition-all duration-300`}
                    aria-hidden="true">
                    {step.done ? (
                      <FaCheckCircle className="text-white text-xs" />
                    ) : (
                      <span className="text-sm font-bold">{idx + 1}</span>
                    )}
                  </div>
                  <span
                    className={`text-sm ${step.done ? 'text-gray-800 font-medium' : 'text-gray-600'}`}>
                    {step.label}
                  </span>
                  {idx < 2 && (
                    <div
                      className={`h-1 bg-[#F59E0B]/20 mx-auto ${
                        step.done ? 'w-full' : 'w-0'
                      } transition-all duration-300`}
                    />
                  )}
                </li>
              ))}
            </ol>
          </div>
        </motion.div>

        <div className="mb-6">
          <div className="flex items-center gap-3 bg-[#F59E0B]/10 border border-[#F59E0B]/20 text-gray-800 rounded-xl p-3 shadow-sm">
            <FaExclamationTriangle className="text-[#F59E0B]" />
            <p className="text-sm">All fields required. Files (PNG/JPEG/PDF, CSV) up to 5MB each.</p>
          </div>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="bg-white rounded-2xl shadow-xl border border-[#F59E0B]/20 overflow-hidden">
          <div className="p-6 md:p-8">
            <motion.div
              variants={itemVariants}
              className="flex items-center gap-5 mb-8 pb-4 border-b border-[#F59E0B]/20">
              <div className="w-14 h-14 rounded-xl bg-[#F59E0B] flex items-center justify-center shadow-md">
                <FaPen className="text-white text-xl" />
              </div>
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-800">
                  Create New OMR Evaluation
                </h2>
                <p className="text-gray-600 text-sm md:text-base">
                  Upload and evaluate OMR sheets seamlessly
                </p>
              </div>
            </motion.div>

            <form onSubmit={handleSubmit} className="space-y-8">
              <section aria-labelledby="student-details">
                <h3 id="student-details" className="text-lg font-semibold text-gray-800 mb-5">
                  Student Details
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaUserGraduate className="text-[#F59E0B] mr-2" />
                      Select Student
                    </label>
                    <div className="relative">
                      <select
                        name="student_id"
                        value={evaluationForm.student_id}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 border rounded-lg text-gray-700 bg-white shadow-sm focus:ring-2 focus:ring-[#F59E0B]/20 focus:border-[#F59E0B] transition ${
                          showError.student_id ? 'border-red-400' : 'border-[#F59E0B]/40'
                        }`}
                        disabled={isLoadingCenterData}
                        aria-invalid={showError.student_id ? 'true' : 'false'}
                        aria-describedby="student_id_help">
                        <option value="">Choose a student...</option>
                        {centerData?.students?.length > 0 ? (
                          centerData.students.map((student) => (
                            <option key={student.id} value={student.id}>
                              {student.first_name} {student.last_name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {isLoadingCenterData ? 'Loading...' : 'No students'}
                          </option>
                        )}
                      </select>
                      <FaChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 text-[#F59E0B]" />
                    </div>
                    <p id="student_id_help" className="text-xs text-gray-500">
                      Auto-fills details on selection.
                    </p>
                    {centerError && (
                      <div className="text-red-600 bg-red-50 p-2 rounded text-xs">
                        <FaExclamationTriangle className="mr-1 inline" />
                        {centerError.data?.message || 'Error loading students'}
                      </div>
                    )}
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaIdBadge className="text-[#F59E0B] mr-2" />
                      Student Name
                    </label>
                    <input
                      type="text"
                      name="student_name"
                      value={evaluationForm.student_name}
                      readOnly
                      placeholder="Auto-filled"
                      className="w-full px-4 py-2 border border-[#F59E0B]/40 rounded-lg text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaUsers className="text-[#F59E0B] mr-2" />
                      Admission No.
                    </label>
                    <input
                      type="text"
                      name="admission_number"
                      value={evaluationForm.admission_no}
                      readOnly
                      placeholder="Auto-filled"
                      className="w-full px-4 py-2 border border-[#F59E0B]/40 rounded-lg text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaGraduationCap className="text-[#F59E0B] mr-2" />
                      Section
                    </label>
                    <input
                      type="text"
                      name="section"
                      value={evaluationForm.section}
                      readOnly
                      placeholder="Auto-filled"
                      className="w-full px-4 py-2 border border-[#F59E0B]/40 rounded-lg text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>
                </div>
              </section>

              <section aria-labelledby="evaluation-details" className="pt-4">
                <h3 id="evaluation-details" className="text-lg font-semibold text-gray-800 mb-5">
                  Evaluation Details
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaBookOpen className="text-[#F59E0B] mr-2" />
                      Subject
                    </label>
                    <div className="relative">
                      <select
                        name="subject"
                        value={evaluationForm.subject}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 border rounded-lg text-gray-700 bg-white shadow-sm focus:ring-2 focus:ring-[#F59E0B]/20 focus:border-[#F59E0B] transition ${
                          showError.subject ? 'border-red-400' : 'border-[#F59E0B]/40'
                        }`}
                        disabled={isLoadingSubjects}
                        aria-invalid={showError.subject ? 'true' : 'false'}
                        aria-describedby="subject_help">
                        <option value="">Choose a subject...</option>
                        {subjectsData && subjectsData.length > 0 ? (
                          subjectsData.map((subject) => (
                            <option key={subject.subject_id} value={subject.subject_name}>
                              {subject.subject_name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {isLoadingSubjects ? 'Loading...' : 'No subjects'}
                          </option>
                        )}
                      </select>
                      <FaChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 text-[#F59E0B]" />
                    </div>
                    <p id="subject_help" className="text-xs text-gray-500">Select the subject.</p>
                    {subjectsError && (
                      <div className="text-red-600 bg-red-50 p-2 rounded text-xs">
                        <FaExclamationTriangle className="mr-1 inline" />
                        {subjectsError.data?.message || 'Error loading subjects'}
                      </div>
                    )}
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaBuilding className="text-[#F59E0B] mr-2" />
                      Center Code
                    </label>
                    <input
                      type="text"
                      name="center_code"
                      value={evaluationForm.center_code}
                      readOnly
                      placeholder="Auto-filled"
                      className="w-full px-4 py-2 border border-[#F59E0B]/40 rounded-lg text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaBuilding className="text-[#F59E0B] mr-2" />
                      Email
                    </label>
                    <input
                      type="text"
                      name="student_email"
                      value={evaluationForm.student_email}
                      readOnly
                      placeholder="Auto-filled"
                      className="w-full px-4 py-2 border border-[#F59E0B]/40 rounded-lg text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>
                </div>
              </section>

              <section aria-labelledby="omr-upload-section">
                <h3 id="omr-upload-section" className="text-lg font-semibold text-gray-800 mb-5">
                  Upload Files
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaFileImage className="text-[#F59E0B] mr-2" />
                      OMR Sheet (PNG/JPEG/PDF)
                    </label>
                    <div
                      className={`relative border-2 border-dashed rounded-lg p-4 text-center ${
                        showError.student_sheet_image ? 'border-red-400' : 'border-[#F59E0B]/40'
                      } hover:border-[#F59E0B] hover:bg-[#F59E0B]/10 transition`}
                      role="button"
                      tabIndex={0}
                      aria-label="Upload OMR sheet"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          omrSheetInputRef.current?.click();
                        }
                      }}>
                      <input
                        type="file"
                        name="student_sheet_image"
                        accept="image/png,image/jpeg,image/jpg,application/pdf"
                        onChange={handleOmrSheetChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        ref={omrSheetInputRef}
                        aria-required="true"
                      />
                      <FaCloudUploadAlt className="text-2xl text-[#F59E0B] mb-2 mx-auto" />
                      <p className="text-sm text-gray-700">Click to upload</p>
                      <p className="text-xs text-gray-500">Max 5MB</p>
                    </div>
                    {omrSheetImage && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <FaFileImage className="text-green-600" />
                          <span>{omrSheetImage.name}</span>
                        </div>
                        <button
                          type="button"
                          onClick={clearOmrSheet}
                          className="text-red-600 hover:text-red-800"
                          aria-label="Remove OMR sheet">
                          <FaTimes />
                        </button>
                      </div>
                    )}
                  </motion.div>

                  <motion.div variants={itemVariants} className="space-y-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <FaFileAlt className="text-[#F59E0B] mr-2" />
                      Answer Key (CSV)
                    </label>
                    <div
                      className={`relative border-2 border-dashed rounded-lg p-4 text-center ${
                        showError.answer_key_file ? 'border-red-400' : 'border-[#F59E0B]/40'
                      } hover:border-[#F59E0B] hover:bg-[#F59E0B]/10 transition`}
                      role="button"
                      tabIndex={0}
                      aria-label="Upload answer key"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          answerKeyInputRef.current?.click();
                        }
                      }}>
                      <input
                        type="file"
                        name="answer_key_file"
                        accept="text/csv"
                        onChange={handleAnswerKeyChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        ref={answerKeyInputRef}
                        aria-required="true"
                      />
                      <FaCloudUploadAlt className="text-2xl text-[#F59E0B] mb-2 mx-auto" />
                      <p className="text-sm text-gray-700">Click to upload</p>
                      <p className="text-xs text-gray-500">Max 5MB</p>
                    </div>
                    {answerKeyFile && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <FaFileAlt className="text-green-600" />
                          <span>{answerKeyFile.name}</span>
                        </div>
                        <button
                          type="button"
                          onClick={clearAnswerKey}
                          className="text-red-600 hover:text-red-800"
                          aria-label="Remove answer key">
                          <FaTimes />
                        </button>
                      </div>
                    )}
                  </motion.div>
                </div>
              </section>

              <section aria-labelledby="review-summary" className="pt-4">
                <h3 id="review-summary" className="text-lg font-semibold text-gray-800 mb-5">
                  Review Summary
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    { label: 'Student', value: evaluationForm.student_name || '—' },
                    { label: 'Email', value: evaluationForm.student_email || '—' },
                    { label: 'Subject', value: evaluationForm.subject || '—' },
                    { label: 'Admission No.', value: evaluationForm.admission_no || '—' },
                    { label: 'Section', value: evaluationForm.section || '—' }
                  ].map((item, idx) => (
                    <motion.div
                      key={idx}
                      variants={itemVariants}
                      className="rounded-lg border border-[#F59E0B]/20 bg-white p-4 shadow hover:bg-gray-50 transition">
                      <p className="text-xs text-gray-500">{item.label}</p>
                      <p className="text-sm font-medium text-gray-800">{item.value}</p>
                    </motion.div>
                  ))}
                </div>
              </section>

              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <motion.button
                  type="submit"
                  disabled={isCreatingEvaluation || isLoadingSubjects || isLoadingCenterData}
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                  className="bg-[#F59E0B] text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed">
                  <span className="flex items-center">
                    {isCreatingEvaluation ? (
                      <>
                        <motion.span
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                        />
                        Processing...
                      </>
                    ) : (
                      <>
                        <FaPlus className="mr-2" />
                        Create Evaluation
                      </>
                    )}
                  </span>
                </motion.button>

                <motion.button
                  type="button"
                  onClick={resetForm}
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                  className="px-6 py-3 border border-[#F59E0B]/50 text-[#F59E0B] rounded-lg font-semibold hover:bg-[#F59E0B]/10">
                  <span className="flex items-center">
                    <FaRedo className="mr-2" />
                    Reset
                  </span>
                </motion.button>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CreateOMRSheetEvaluation;