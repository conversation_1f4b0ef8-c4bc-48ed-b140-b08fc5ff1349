export const StudentQuizTableHeader = [
  {
    header: 'S.No',
    data: 'sno',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Subject',
    data: 'subject',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Topic',
    data: 'topic',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Sub Topic',
    data: 'sub_topic',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Total Questions',
    data: 'totalQuestions',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Total Answered',
    data: 'totalAnswered',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Correct Answers',
    data: 'correctCount',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Incorrect Answers',
    data: 'wrongCount',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Score',
    data: 'score',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Start At',
    data: 'created_at',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'End At',
    data: 'completed_at',
    className: 'overflow-text-wrap ',
    show: true
  }
];
