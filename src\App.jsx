import React, { useState } from 'react';
import { Navigate, useRoutes } from 'react-router';
// eslint-disable-next-line
import { motion } from 'framer-motion';
import LandPage from './pages/LandPages/LandPage';
import Login from './pages/auth/Login';
import DirectorDashboard from './pages/screens/directorPanel/directorPanelDashboard/DirectorPanelDashboard';
import CentreCounselorDashboard from './pages/screens/centreCounselorPanel/centreCounselorDashboard/CentreCounselorDashboard';
import StudentsDashboard from './pages/screens/studentPanel/studentsDashboard/StudentsDashboard';
import NeetPrepHome from './pages/LandPages/NeetPrepHome';
import PageNotFound from './pages/auth/PageNotFound';
import Home from './pages/Home';
import Inbox from './pages/screens/directorPanel/inbox/Inbox';
import AddCenter from './pages/screens/directorPanel/addCenter/AddCenter';
import AddKotaTeachers from './pages/screens/directorPanel/addKotaTeachers/AddKotaTeachers';
import ListCenters from './pages/screens/directorPanel/listCenters/ListCenters';
import ListKotaTeachers from './pages/screens/directorPanel/listKotaTeachers/ListKotaTeachers';
import RecordedVideos from './pages/screens/studentPanel/recordedVideos/RecordedVideos';
import LiveStreaming from './pages/screens/studentPanel/liveStreaming/LiveStreaming';
import EBookCentre from './pages/screens/studentPanel/eBookCentre/EBookCentre';
import CreateYourOwnTest from './pages/screens/studentPanel/createYourOwnTest/CreateYourOwnTest';
import MockTestSimulation from './pages/screens/studentPanel/mockTestSimulation/MockTestSimulation';
import BoosterModule from './pages/screens/studentPanel/boosterModule/BoosterModule';
import StudentCommunity from './pages/screens/studentPanel/studentCommunity/StudentCommunity';
import AddStudents from './pages/screens/centreCounselorPanel/addStudents/AddStudents';
import AddFaculty from './pages/screens/centreCounselorPanel/addFaculty/AddFaculty';
import ListStudents from './pages/screens/centreCounselorPanel/listStudents/ListStudents';
import ListFaculty from './pages/screens/centreCounselorPanel/listFaculty/ListFaculty';
import KotaTeachers from './pages/screens/centreCounselorPanel/kotaTeachers/KotaTeachers';
import ProtectedRoute from './pages/auth/ProtectedRoute';
import Dashboard from './pages/screens/Dashboard/Dashboard';
import ProcessSelector from './pages/screens/directorPanel/processSelector/ProcessSelector';
import TeacherLiveStreaming from './pages/screens/teacherPanel/teacherLiveStreaming/TeacherLiveStreaming';
import MappedCenters from './pages/screens/teacherPanel/mappedCenters/MappedCenters';
import TeacherDashboard from './pages/screens/teacherPanel/teacherDashboard/TeacherDashboard';
import ScheduleEvents from './pages/screens/teacherPanel/scheduleEvents/ScheduleEvents';
import CenterTraineeDashboard from './pages/screens/centreTraineePanel/centerTraineeDashboard/CenterTraineeDashboard';
import CenterStudentsFaculty from './pages/screens/centreTraineePanel/centerStudentsFacultyPanel/CenterStudentsFaculty';
import Courses from './pages/screens/directorPanel/courses/CreateCourses';
import Batches from './pages/screens/directorPanel/batches/CreateBatches';
import CenterTraineeLiveViewer from './pages/screens/centreTraineePanel/centerTraineeLiveViewer/CenterTraineeLiveViewer';
import CenterTraineeOverview from './pages/screens/centreTraineePanel/overView/CennterTraineeOverview';
import StudentInfo from './pages/screens/parentPanel/studentsInfo/StudentInfo';
import CenterInfo from './pages/screens/parentPanel/centerInfo/CenterInfo';
import ParentDashboard from './pages/screens/parentPanel/parentDashboard/ParentDashboard';
import AiTutor from './pages/screens/studentPanel/aiTutor/AiTutor';
import CreateTestForStudents from './pages/screens/centreTraineePanel/createTestPanel/CreateTestForStudents';
import Events from './pages/screens/directorPanel/events/Events';
import Subjects from './pages/screens/directorPanel/subjects/Subjects';
import JeePrepHome from './pages/LandPages/JeePrepHome';
import AboutUs from './pages/LandPages/AboutUs';
import UpcomingEvents from './pages/LandPages/UpcomingEvents';
import ListBatchStudents from './pages/screens/centreTraineePanel/evaluator/ListBatchStudents';
import ProblemSolver from './pages/screens/studentPanel/problemSolver/ProblemSolver';
import PaperBasedTest from './pages/screens/centreTraineePanel/paperBasedTest/PaperBasedTest';
import NewDashboard from './pages/screens/studentPanel/Dashboard/NewDashboard';
import StudentAttendance from './pages/screens/parentPanel/studentAttendance/StudentAttendance';
import OverallPerformance from './pages/screens/parentPanel/overallPerformance/OverallPerformance';
import NotificationHandler from './notifications/NotificationHandler';
import AddMentor from './pages/screens/directorPanel/addMentor/AddMentor';
import ListMentor from './pages/screens/directorPanel/listMentor/ListMentor';
import PrivacyPolicy from './pages/FooterPage/PrivacyPolicy';
import TermsAndConditions from './pages/FooterPage/TermsAndConditions';
import SasthraTools from './pages/screens/studentPanel/sasthraTools/SasthraTools';
import ClassroomFeedback from './pages/screens/studentPanel/feedback/AI-Tutor_Feedback/ClassroomFeedback.jsx';
import MainFeedback from './pages/screens/studentPanel/feedback/MainFeedback.jsx';
import PdfQuestionGenrator from './pages/screens/studentPanel/pdfQuestionGenrator/PdfQuestionGenrator';
import FaceVerification from './pages/screens/studentPanel/faceVerification/FaceVerification';
import FaceVerificationGuard from './pages/screens/studentPanel/faceVerification/FaceVerificationGuard';
import Recommendations from './pages/screens/studentPanel/recommendation/Recommendations';
import UnifiedLabs from './pages/screens/studentPanel/learnPractically/UnifiedLabs';
import AssessmentGuard from './pages/screens/studentPanel/onBroadingAssesment/AssessmentGuard';
import ContactForm from './pages/contact/ContactForm';
import MentorDashboard from './pages/screens/mentorPanel/mentorDashboard/MentorDashboard';
import TeacherMaterialGallery from './pages/screens/teacherPanel/materialGallery/TeacherMaterialGallery';
import StudentMaterialGallery from './pages/screens/studentPanel/studentMaterialGallery/StudentMaterialGallery';
import CenterTraineeMaterialGallery from './pages/screens/centreTraineePanel/centerTraineeMaterialGallery/CenterTraineeMaterialGallery';
import DirectorAnalyticalDashboard from './pages/screens/directorPanel/directorAnalyticalDashboard/DirectorAnalyticalDashboard.jsx';
import DirectorAnalyticalByCenter from './pages/screens/directorPanel/directorAnalyticalDashboard/DirectorAnalyticalByCenter.jsx';
import DirectorAnalyticalByStudent from './pages/screens/directorPanel/directorAnalyticalDashboard/DirectorAnalyticalByStudent.jsx';
import UploadQuestionPaper from './pages/screens/directorPanel/uploadQuestionPaper/UploadQuestionPaper.jsx';
import ParentAnalyticalTabs from './pages/screens/parentPanel/parentAnalyticalDashboard/ParentAnalyticalTabs.jsx';
import MentorCommunity from './pages/screens/mentorPanel/mentorCommunity/MentorCommunity.jsx';
import AnalyticalDetails from './pages/screens/centreCounselorPanel/AnalyticalDashboard/ComputerBasedTest/AnalyticalDetails.jsx';
import Analyticals from './pages/screens/centreCounselorPanel/AnalyticalDashboard/Analyticals.jsx';
import OMR_Evalutaion from './pages/screens/centreTraineePanel/omr_evaluation/OMR_Evalutaion.jsx';
import OMRResultsDisplay from './pages/screens/centreTraineePanel/omr_evaluation/OMRResultsDisplay.jsx';

const App = () => {
  const isAuthenticated = !!sessionStorage.token;

  const [showPopup, setShowPopup] = useState(false);
  const elements = useRoutes([
    { path: '/', element: <LandPage openPopup={() => setShowPopup(true)} /> },
    { path: 'neet', element: <NeetPrepHome /> },
    { path: 'jee', element: <JeePrepHome /> },
    { path: 'aboutus', element: <AboutUs /> },
    { path: 'contact', element: <ContactForm /> },
    { path: 'face-verification', element: <FaceVerification /> },
    { path: 'upcomingevents', element: <UpcomingEvents /> },
    { path: 'auth', element: isAuthenticated ? <Navigate to={'/sasthra'} replace /> : <Login /> },
    { path: 'labs', element: <UnifiedLabs /> },
    {
      path: 'privacy-policy',
      element: isAuthenticated ? <Navigate to={'/sasthra'} replace /> : <PrivacyPolicy />
    },
    {
      path: 'tnc',
      element: isAuthenticated ? <Navigate to={'/sasthra'} replace /> : <TermsAndConditions />
    },

    {
      path: 'sasthra',
      element: isAuthenticated ? <Home /> : <Navigate to={'/auth'} replace />,
      children: [
        { index: true, element: <Dashboard /> },
        {
          path: 'director',
          element: <ProtectedRoute allowedRoles={['director']} />,
          children: [
            { index: true, element: <DirectorDashboard /> },
            { path: 'director-inbox', element: <Inbox /> },
            { path: 'add-center', element: <AddCenter /> },
            { path: 'add-kota-teachers', element: <AddKotaTeachers /> },
            { path: 'list-center', element: <ListCenters /> },
            { path: 'list-kota', element: <ListKotaTeachers /> },
            { path: 'process-selector', element: <ProcessSelector /> },
            { path: 'course', element: <Courses /> },
            { path: 'batch', element: <Batches /> },
            { path: 'events', element: <Events /> },
            { path: 'subjects', element: <Subjects /> },
            { path: 'dashboard', element: <DirectorAnalyticalDashboard /> },
            { path: 'center/:id', element: <DirectorAnalyticalByCenter /> },
            { path: 'center/:id/student/:id', element: <DirectorAnalyticalByStudent /> },
            { path: 'add-mentor', element: <AddMentor /> },
            { path: 'list-mentor', element: <ListMentor /> },
            { path: 'upload-question-paper', element: <UploadQuestionPaper /> }
          ]
        },
        {
          path: 'student',
          element: <ProtectedRoute allowedRoles={['student']} />,
          children: [
            { index: true, path: 'student-dashboard', element: <StudentsDashboard /> },
            { path: 'recorded-videos', element: <RecordedVideos /> },
            { path: 'live-streaming', element: <LiveStreaming /> },
            { path: 'ebook-centre', element: <EBookCentre /> },
            {
              path: 'create-your-own-test',
              element: (
                <AssessmentGuard>
                  <FaceVerificationGuard>
                    <CreateYourOwnTest />
                  </FaceVerificationGuard>
                </AssessmentGuard>
              )
            },
            { path: 'mock-test-simulation', element: <MockTestSimulation /> },
            { path: 'booster-module', element: <BoosterModule /> },
            { path: 'student-community', element: <StudentCommunity /> },
            { path: 'ai-tutor', element: <AiTutor /> },
            { path: 'problem-solver', element: <ProblemSolver /> },
            { path: 'virtual-labs', element: <UnifiedLabs /> },
            { path: 'virtual-labss', element: <UnifiedLabs /> },
            { path: 'dashboard', element: <NewDashboard /> },
            { path: 'recommendation', element: <Recommendations /> },
            { path: 'question-generator', element: <PdfQuestionGenrator /> },
            { path: 'sasthra-tools', element: <SasthraTools /> },
            { path: 'feedback-submit', element: <MainFeedback /> },
            { path: 'material-upload', element: <StudentMaterialGallery /> }
          ]
        },
        {
          path: 'center-counselor',
          element: <ProtectedRoute allowedRoles={['center_counselor']} />,
          children: [
            {
              index: true,
              path: 'center-counselor-dashboard',
              element: <CentreCounselorDashboard />
            },
            { path: 'add-students', element: <AddStudents /> },
            { path: 'add-faculty', element: <AddFaculty /> },
            { path: 'list-students', element: <ListStudents /> },
            { path: 'list-faculty', element: <ListFaculty /> },
            { path: 'kota-teachers', element: <KotaTeachers /> },
            { path: 'dashboard', element: <Analyticals /> },
            { path: 'analytical-details/:id', element: <AnalyticalDetails /> }
          ]
        },
        {
          path: 'teacher',
          element: <ProtectedRoute allowedRoles={['kota_teacher']} />,
          children: [
            {
              index: true,
              path: 'teacher-dashboard',
              element: <TeacherDashboard />
            },
            { path: 'mapping-centers', element: <MappedCenters /> },
            { path: 'live-streaming', element: <TeacherLiveStreaming /> },
            { path: 'schedule-events', element: <ScheduleEvents /> },
            { path: 'material-upload', element: <TeacherMaterialGallery /> }
          ]
        },
        {
          path: 'faculty',
          element: <ProtectedRoute allowedRoles={['faculty']} />,
          children: [
            {
              index: true,
              path: 'centre-trainee-dashboard',
              element: <CenterTraineeDashboard />
            },
            { path: 'students', element: <CenterStudentsFaculty /> },
            { path: 'other-faculty', element: <CenterTraineeLiveViewer /> },
            { path: 'overview', element: <CenterTraineeOverview /> },
            { path: 'live-viewer', element: <CenterTraineeLiveViewer /> },
            { path: 'paper-based-evaluator', element: <CreateTestForStudents /> },
            { path: 'create-test', element: <CreateTestForStudents /> },
            { path: 'paper-based-test', element: <PaperBasedTest /> },
            { path: 'evaluator-result', element: <ListBatchStudents /> },
            { path: 'ai-tutor', element: <AiTutor /> },
            { path: 'material-upload', element: <CenterTraineeMaterialGallery /> },
            { path: 'omr-sheet-evaluation', element: <OMR_Evalutaion /> },
            { path: 'omr-evaluation-result', element: <OMRResultsDisplay /> }

          ]
        },
        {
          path: 'parent',
          element: <ProtectedRoute allowedRoles={['parent']} />,
          children: [
            { index: true, element: <ParentDashboard /> },
            { path: 'student-info', element: <StudentInfo /> },
            { path: 'center-info', element: <CenterInfo /> },
            { path: 'student-attendance', element: <StudentAttendance /> },
            { path: 'overall-performance', element: <OverallPerformance /> },
            { path: 'dashboard', element: <ParentAnalyticalTabs /> }
          ]
        },
        {
          path: 'mendor',
          element: <ProtectedRoute allowedRoles={['mendor']} />,
          children: [
            { index: true, element: <MentorDashboard /> },
            { path: 'mentor-community', element: <MentorCommunity /> }
          ]
        }
      ]
    },
    { path: '*', element: <PageNotFound /> }
  ]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gray-100">
      <NotificationHandler />
      {elements}
    </motion.div>
  );
};

export default App;
