import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  ChevronLeftIcon,
  CircleArrowOutUpRightIcon,
  FileSearch2,
  PlayCircleIcon,
  SearchX,
  X,
  BeakerIcon,
  CalculatorIcon,
  Earth,
  Globe,
  SparklesIcon,
  BookOpen,
  FlaskConical
} from 'lucide-react';
import useSound from 'use-sound';
import clsx from 'clsx';

import {
  clearSimulationContent,
  setLearnPracticallyData,
  setSimulationContent,
  useLazyGetLearnPracticallyServiceByIdQuery,
  useLazyGetLearnPracticallyServiceQuery
} from './learnPractically.slice';

// --- NEW ---
// A dedicated component for the animated background for better separation of concerns.
const AnimatedBackground = () => (
  <>
    {/* 
      INSTRUCTIONS FOR THE ANIMATION:
      To make the background animation work, you need to add the following
      CSS keyframes rule to your global stylesheet (e.g., index.css or globals.css).
      This animation smoothly pans the background gradient.

      @keyframes gradient-pan {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
    */}
    <div
      className="fixed inset-0 z-[-2] "
      style={{
        backgroundSize: '400% 400%',
        animation: 'gradient-pan 20s ease infinite'
      }}
    />
    {/* This overlay adds a slight darkening and a frosted glass effect for better readability and a premium feel */}
    <div className="fixed inset-0 z-[-1] bg-black/40 backdrop-blur-sm" />
  </>
);

const Spinner = () => (
  <div className="w-12 h-12 border-4 border-t-transparent border-sky-400 rounded-full animate-spin"></div>
);

const Placeholder = ({ icon, title, message }) => (
  <div className="text-center text-gray-300 mt-16 flex flex-col items-center gap-4 p-4">
    <div className="w-20 h-20 text-gray-500">{icon}</div>
    <h3 className="text-2xl font-bold text-gray-100">{title}</h3>
    <p className="max-w-md text-gray-400">{message}</p>
  </div>
);

const LabHeader = ({ subject, simName, simLink, onBack }) => (
  <header className="flex-shrink-0 z-10 flex items-center justify-between p-4 text-white bg-black/30 backdrop-blur-sm border-b border-white/10 shadow-lg">
    <motion.button
      onClick={onBack}
      whileHover={{ scale: 1.05, backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
      whileTap={{ scale: 0.95 }}
      className="flex items-center gap-2 px-4 py-2 font-semibold bg-white/10 rounded-lg transition-colors">
      <ChevronLeftIcon className="w-5 h-5" />
      Back to {subject.name}
    </motion.button>
    <h2 className="text-xl font-bold text-center truncate px-4">{simName}</h2>
    {simLink ? (
      <motion.a
        href={simLink}
        target="_blank"
        rel="noopener noreferrer"
        whileHover={{ scale: 1.05, backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
        whileTap={{ scale: 0.95 }}
        className="flex items-center gap-2 px-4 py-2 font-semibold bg-white/10 rounded-lg transition-colors">
        New Tab <CircleArrowOutUpRightIcon className="w-5 h-5" />
      </motion.a>
    ) : (
      <div className="w-[125px]"></div>
    )}
  </header>
);

const unifiedSubjects = [
  {
    id: '1',
    name: 'Physics',
    subName: 'physics',
    Icon: SparklesIcon,
    gradient: 'from-sky-500 to-indigo-600',
    glowClass: 'hover:shadow-sky-400/50',
    accentClass: 'bg-sky-500',
    webSimulations: [
      { name: 'Astro Simulations', link: 'https://ccnmtl.github.io/astro-simulations/' },
      { name: 'MyPhysicsLab', link: 'https://www.myphysicslab.com/' },
      {
        name: 'Physics Aviary',
        link: 'https://www.thephysicsaviary.com/Physics/Programs/Labs/find.php'
      },
      { name: 'oPhysics', link: 'https://ophysics.com/' }
    ]
  },
  {
    id: '2',
    name: 'Chemistry',
    subName: 'chemistry',
    Icon: FlaskConical,
    gradient: 'from-amber-500 to-orange-600',
    glowClass: 'hover:shadow-amber-400/50',
    accentClass: 'bg-amber-500',
    webSimulations: [
      { name: 'ChemCollective V-Labs', link: 'https://chemcollective.org/vlabs' },
      { name: 'Chemagic Molecules', link: 'https://chemagic.org/molecules/amini.html' },
      { name: 'Chemix Lab Editor', link: 'https://chemix.org/' }
    ]
  },
  {
    id: '5',
    name: 'Biology',
    subName: 'biology',
    Icon: Globe,
    gradient: 'from-emerald-500 to-green-600',
    glowClass: 'hover:shadow-emerald-400/50',
    accentClass: 'bg-emerald-500',
    webSimulations: []
  },
  {
    id: '3',
    name: 'Mathematics',
    subName: 'math& statics',
    Icon: CalculatorIcon,
    gradient: 'from-rose-500 to-pink-600',
    glowClass: 'hover:shadow-rose-400/50',
    accentClass: 'bg-rose-500',
    webSimulations: []
  },
  {
    id: '4',
    name: 'Earth & Space',
    subName: 'earth& space',
    Icon: Earth,
    gradient: 'from-blue-600 to-slate-800',
    glowClass: 'hover:shadow-blue-400/50',
    accentClass: 'bg-blue-500',
    webSimulations: []
  }
];

const springyTransition = { type: 'spring', stiffness: 300, damping: 30 };
const gentleSpring = { type: 'spring', stiffness: 200, damping: 25 };

const listContainerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.07, delayChildren: 0.1 } }
};
const listItemVariants = {
  hidden: { y: 20, opacity: 0, scale: 0.95 },
  visible: { y: 0, opacity: 1, scale: 1, transition: gentleSpring }
};

const viewVariants = {
  initial: { opacity: 0, scale: 0.97 },
  animate: { opacity: 1, scale: 1, transition: { duration: 0.4, ease: [0.4, 0, 0.2, 1] } },
  exit: { opacity: 0, scale: 0.97, transition: { duration: 0.2, ease: [0.4, 0, 1, 1] } }
};

const UnifiedLabs = () => {
  const dispatch = useDispatch();

  const [selectedSubject, setSelectedSubject] = useState(null);
  const [activeTab, setActiveTab] = useState('school');
  const [selectedWebSim, setSelectedWebSim] = useState(null);
  const [selectedSchoolPractical, setSelectedSchoolPractical] = useState(null);
  const [isIframeLoading, setIsIframeLoading] = useState(true);

  const schoolPracticals = useSelector((state) => state.learnPractically.learnPracticallyData);
  const simulationContent = useSelector((state) => state.learnPractically.simulationContent);

  const [fetchPracticals, { isLoading: arePracticalsLoading }] =
    useLazyGetLearnPracticallyServiceQuery();
  const [fetchPracticalById, { isLoading: isPracticalContentLoading }] =
    useLazyGetLearnPracticallyServiceByIdQuery();

  const [playClick] = useSound('/click.wav', { volume: 0.5 });
  const [playHover] = useSound('/hover.mp3', { volume: 0.15, playbackRate: 1.2 });

  const handleSubjectSelect = useCallback(
    async (subject) => {
      playClick();
      setSelectedSubject(subject);
      setActiveTab('school');
      try {
        const response = await fetchPracticals(subject.subName).unwrap();
        dispatch(setLearnPracticallyData(response));
      } catch (error) {
        console.error('Error fetching school practicals:', error);
        dispatch(setLearnPracticallyData([]));
      }
    },
    [dispatch, fetchPracticals, playClick]
  );

  const handleSelectWebSim = (sim) => {
    playClick();
    setIsIframeLoading(true);
    setSelectedWebSim(sim);
  };

  const handleSelectSchoolPractical = useCallback(
    async (practical) => {
      if (!practical || !selectedSubject) return;
      playClick();
      setIsIframeLoading(true);
      setSelectedSchoolPractical(practical);
      dispatch(clearSimulationContent());
      try {
        const res = await fetchPracticalById({
          subjectName: selectedSubject.subName,
          simulationId: practical._id
        }).unwrap();
        dispatch(setSimulationContent(res.content));
      } catch (error) {
        console.error('Error fetching practical content:', error);
      }
    },
    [dispatch, fetchPracticalById, selectedSubject, playClick]
  );

  const handleBackToSubjectView = () => {
    playClick();
    setSelectedWebSim(null);
    setSelectedSchoolPractical(null);
    dispatch(clearSimulationContent());
  };

  const handleBackToHub = () => {
    playClick();
    setSelectedSubject(null);
    dispatch(setLearnPracticallyData(null));
  };

  return (
    <div className="min-h-screen font-sans text-stone-200 relative overflow-hidden">
      {/* --- REPLACED a static div with our new animated background component --- */}
      {/* <AnimatedBackground /> */}

      <div className="relative z-10 p-4 sm:p-6 lg:p-8">
        <AnimatePresence mode="wait">
          {(selectedWebSim || selectedSchoolPractical) && selectedSubject && (
            <motion.div
              key="simulation-view"
              variants={viewVariants}
              initial="initial"
              animate="animate"
              exit="exit">
              <LabHeader
                subject={selectedSubject}
                simName={selectedWebSim?.name || selectedSchoolPractical?.filename}
                simLink={selectedWebSim?.link}
                onBack={handleBackToSubjectView}
              />
              {selectedWebSim && (
                <div className="relative w-full bg-black/50 mt-4 rounded-xl shadow-2xl p-2 ring-1 ring-white/10">
                  {isIframeLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-stone-900 rounded-lg">
                      <Spinner />
                    </div>
                  )}
                  <iframe
                    src={selectedWebSim.link}
                    title={selectedWebSim.name}
                    className="w-full h-[calc(100vh-120px)] border-0 rounded-lg"
                    onLoad={() => setIsIframeLoading(false)}
                    style={{ visibility: isIframeLoading ? 'hidden' : 'visible' }}
                  />
                </div>
              )}
              {selectedSchoolPractical && (
                <div className="flex flex-col lg:flex-row gap-6 mt-4 h-[calc(100vh-120px)]">
                  <motion.div
                    initial={{ x: -30, opacity: 0 }}
                    animate={{ x: 0, opacity: 1, transition: { ...gentleSpring, delay: 0.2 } }}
                    className="lg:w-1/3 bg-stone-900/50 backdrop-blur-xl rounded-xl p-6 border border-white/10 shadow-lg flex flex-col">
                    <h3 className="flex items-center gap-3 text-xl font-bold mb-4 pb-3 border-b border-white/10 text-stone-100">
                      <BookOpen className="text-sky-400" />
                      Instructions
                    </h3>
                    <div className="overflow-y-auto pr-2">
                      <p className="text-stone-300 leading-relaxed">
                        {selectedSchoolPractical.description || 'No instructions provided.'}
                      </p>
                    </div>
                  </motion.div>
                  <motion.div
                    initial={{ x: 30, opacity: 0 }}
                    animate={{ x: 0, opacity: 1, transition: { ...gentleSpring, delay: 0.2 } }}
                    className="lg:w-2/3 bg-black/50 rounded-xl shadow-inner overflow-hidden relative flex items-center justify-center p-2 ring-1 ring-white/10">
                    {(isIframeLoading || isPracticalContentLoading) && (
                      <div className="absolute inset-0 flex items-center justify-center bg-stone-900 rounded-lg">
                        <Spinner />
                      </div>
                    )}
                    {!isPracticalContentLoading && !simulationContent && (
                      <Placeholder
                        icon={<SearchX size={80} />}
                        title="Content Not Found"
                        message="There was an issue loading this practical."
                      />
                    )}
                    {simulationContent && (
                      <iframe
                        key={selectedSchoolPractical._id}
                        srcDoc={simulationContent}
                        title={selectedSchoolPractical.filename}
                        onLoad={() => setIsIframeLoading(false)}
                        className="w-full h-full bg-white rounded-lg"
                        style={{
                          opacity: isIframeLoading || isPracticalContentLoading ? 0 : 1,
                          transition: 'opacity 0.3s ease-in-out'
                        }}
                      />
                    )}
                  </motion.div>
                </div>
              )}
            </motion.div>
          )}

          {selectedSubject && !selectedWebSim && !selectedSchoolPractical && (
            <motion.div
              key="subject-detail-view"
              className="max-w-7xl mx-auto"
              variants={viewVariants}
              initial="initial"
              animate="animate"
              exit="exit">
              <motion.div
                layoutId={`card-container-${selectedSubject.id}`}
                transition={springyTransition}
                className={`relative p-6 rounded-2xl text-white bg-gradient-to-br ${selectedSubject.gradient} shadow-2xl`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <motion.div layoutId={`card-icon-${selectedSubject.id}`}>
                      <selectedSubject.Icon className="w-16 h-16" />
                    </motion.div>
                    <div>
                      <motion.h1
                        layoutId={`card-title-${selectedSubject.id}`}
                        className="text-4xl lg:text-5xl font-bold">
                        {selectedSubject.name}
                      </motion.h1>
                      <motion.p className="text-white/80 mt-1">
                        Explore simulations and practicals
                      </motion.p>
                    </div>
                  </div>
                  <motion.button
                    onClick={handleBackToHub}
                    className="p-2 bg-black/20 rounded-full hover:bg-black/40 transition-colors"
                    aria-label="Close"
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}>
                    <X className="w-6 h-6 text-white" />
                  </motion.button>
                </div>
              </motion.div>

              <div className="flex border-b border-white/10 mt-8 mb-6">
                {['school', 'web'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className="px-6 py-3 text-lg font-semibold relative text-stone-400 hover:text-white transition-colors outline-none focus-visible:ring-2 ring-sky-400">
                    {tab === 'school' ? 'School Practicals' : 'Web Simulations'}
                    {activeTab === tab && (
                      <motion.div
                        className={clsx(
                          'absolute bottom-[-1px] left-0 right-0 h-1',
                          selectedSubject.accentClass
                        )}
                        layoutId="underline"
                        transition={springyTransition}
                      />
                    )}
                  </button>
                ))}
              </div>

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ y: 10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: -10, opacity: 0 }}
                  transition={{ duration: 0.2 }}>
                  <motion.div
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    variants={listContainerVariants}
                    initial="hidden"
                    animate="visible">
                    {(activeTab === 'web' ? selectedSubject.webSimulations : schoolPracticals)?.map(
                      (item) => (
                        <motion.div
                          key={item.name || item._id}
                          variants={listItemVariants}
                          onClick={() =>
                            activeTab === 'web'
                              ? handleSelectWebSim(item)
                              : handleSelectSchoolPractical(item)
                          }
                          onHoverStart={playHover}
                          whileHover={{ y: -6, scale: 1.03, transition: gentleSpring }}
                          whileTap={{ scale: 0.97, y: 0 }}
                          className="p-5 bg-stone-900/50 backdrop-blur-lg rounded-xl border border-white/10 shadow-lg cursor-pointer group transition-all duration-300 hover:shadow-2xl hover:border-sky-400/50 hover:bg-stone-800/60">
                          <div className="flex items-center gap-4">
                            <PlayCircleIcon className="w-10 h-10 text-stone-500 group-hover:text-sky-400 transition-colors duration-300" />
                            <h3 className="font-semibold text-lg text-stone-200 flex-1">
                              {item.name || item.filename}
                            </h3>
                          </div>
                        </motion.div>
                      )
                    )}
                  </motion.div>

                  {activeTab === 'web' && selectedSubject.webSimulations.length === 0 && (
                    <Placeholder
                      icon={<FileSearch2 size={80} />}
                      title="No Web Sims Found"
                      message="We're still searching for the best web simulations for this subject."
                    />
                  )}
                  {activeTab === 'school' && arePracticalsLoading && (
                    <div className="flex justify-center mt-16">
                      <Spinner />
                    </div>
                  )}
                  {activeTab === 'school' &&
                    !arePracticalsLoading &&
                    (!schoolPracticals || schoolPracticals.length === 0) && (
                      <Placeholder
                        icon={<FileSearch2 size={80} />}
                        title="No School Practicals"
                        message={`No curriculum-specific practicals were found for ${selectedSubject.name}.`}
                      />
                    )}
                </motion.div>
              </AnimatePresence>
            </motion.div>
          )}

          {!selectedSubject && (
            <motion.div
              key="hub-view"
              className="max-w-6xl mx-auto text-center px-4"
              variants={viewVariants}
              initial="initial"
              animate="animate"
              exit="exit">
              <motion.h1
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="text-5xl md:text-7xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-stone-100 to-stone-400 mb-4">
                Virtual Labs
              </motion.h1>
              <motion.p
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="text-lg text-stone-400 mb-16 max-w-2xl mx-auto">
                Dive into a universe of discovery. Select a subject to begin your exploration.
              </motion.p>
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                variants={listContainerVariants}
                initial="hidden"
                animate="visible">
                {unifiedSubjects.map((sub) => (
                  <motion.div
                    key={sub.id}
                    layoutId={`card-container-${sub.id}`}
                    onClick={() => handleSubjectSelect(sub)}
                    onHoverStart={playHover}
                    className={clsx(
                      'relative p-8 rounded-2xl text-white cursor-pointer overflow-hidden bg-gradient-to-br',
                      sub.gradient,
                      sub.glowClass,
                      'shadow-lg transition-shadow duration-300'
                    )}
                    variants={listItemVariants}
                    whileHover={{ y: -8, scale: 1.03, transition: gentleSpring }}
                    whileTap={{
                      scale: 0.98,
                      y: 0,
                      transition: { type: 'spring', stiffness: 400, damping: 20 }
                    }}>
                    <motion.div className="absolute -bottom-4 -right-4 w-32 h-32 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                      <sub.Icon className="w-full h-full" />
                    </motion.div>
                    <div className="relative z-10 text-left">
                      <motion.div layoutId={`card-icon-${sub.id}`}>
                        <sub.Icon className="w-12 h-12 mb-4" />
                      </motion.div>
                      <motion.h2 layoutId={`card-title-${sub.id}`} className="text-3xl font-bold">
                        {sub.name}
                      </motion.h2>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default UnifiedLabs;
