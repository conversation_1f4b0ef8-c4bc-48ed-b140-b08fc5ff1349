import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BarChart,
  X,
  ChevronRight,
  BookOpen,
  GitCompare,
  FileText,
  GraduationCap,
  Microscope,
  Atom,
  Dna,
  Calculator
} from 'lucide-react';
import Logo from '../../../assets/sas_png2.png';
import { useHoverDelay } from '../../../Hooks/useHoverDelay';

import { DropdownPortal } from './DropdownPortal';

const LandingNavbar = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeExam, setActiveExam] = useState(null);

  const navigation = [
    { name: 'Know Sasthra', href: '/aboutus' },
    { name: 'Explore Sasthra', href: '/aboutus' },
    {
      name: 'NEET',
      submenus: [
        { name: 'Sasthra NEET', href: '/neet' },
        { name: 'NEET Intro', href: '/neet/intro' },
        { name: 'Exam Info', href: '/neet/exam-info' },
        {
          name: 'NEET Syllabus',
          icon: BookOpen,
          submenus: [
            { name: 'Physics Syllabus', href: '/neet/syllabus/physics', icon: Atom },
            { name: 'Chemistry Syllabus', href: '/neet/syllabus/chemistry', icon: Atom },
            { name: 'Biology Syllabus', href: '/neet/syllabus/biology', icon: Dna }
          ]
        },
        {
          name: 'Difference Between',
          icon: GitCompare,
          submenus: [
            { name: 'Physics Concepts', href: '/neet/difference/physics', icon: Atom },
            { name: 'Chemistry Concepts', href: '/neet/difference/chemistry', icon: Atom },
            { name: 'Biology Concepts', href: '/neet/difference/biology', icon: Dna }
          ]
        },
        {
          name: 'Previous Year Papers',
          icon: FileText,
          submenus: [
            { name: 'NEET 2025', href: '/neet/papers/2025' },
            { name: 'NEET 2024', href: '/neet/papers/2024' },
            { name: 'NEET 2023', href: '/neet/papers/2023' },
            { name: 'NEET 2022', href: '/neet/papers/2022' },
            { name: 'NEET 2021', href: '/neet/papers/2021' }
          ]
        }
      ]
    },
    {
      name: 'JEE',
      submenus: [
        {
          name: 'Main',
          submenus: [
            { name: 'SASTHRA JEE Main', href: '/jee' },
            { name: 'JEE Main Intro', href: '/jee/main/intro' },
            { name: 'Exam Info', href: '/jee/main/exam-info' },
            {
              name: 'JEE Main Syllabus',
              icon: BookOpen,
              submenus: [
                { name: 'Physics Syllabus', href: '/jee/main/syllabus/physics', icon: Atom },
                { name: 'Chemistry Syllabus', href: '/jee/main/syllabus/chemistry', icon: Atom },
                { name: 'Maths Syllabus', href: '/jee/main/syllabus/maths', icon: Calculator }
              ]
            },
            {
              name: 'Difference Between',
              icon: GitCompare,
              submenus: [
                { name: 'Physics Concepts', href: '/jee/main/difference/physics', icon: Atom },
                { name: 'Chemistry Concepts', href: '/jee/main/difference/chemistry', icon: Atom },
                { name: 'Maths Concepts', href: '/jee/main/difference/maths', icon: Calculator }
              ]
            },
            {
              name: 'Previous Year Papers',
              icon: FileText,
              submenus: [
                { name: 'JEE Main 2025', href: '/jee/main/papers/2025' },
                { name: 'JEE Main 2024', href: '/jee/main/papers/2024' },
                { name: 'JEE Main 2023', href: '/jee/main/papers/2023' },
                { name: 'JEE Main 2022', href: '/jee/main/papers/2022' },
                { name: 'JEE Main 2021', href: '/jee/main/papers/2021' }
              ]
            }
          ]
        },
        {
          name: 'Advanced',
          submenus: [
            { name: 'SASTHRA JEE Advanced', href: '/jee' },
            { name: 'JEE Advanced Intro', href: '/jee/advanced/intro' },
            { name: 'Exam Info', href: '/jee/advanced/exam-info' },
            {
              name: 'JEE Advanced Syllabus',
              icon: BookOpen,
              submenus: [
                { name: 'Physics Syllabus', href: '/jee/advanced/syllabus/physics', icon: Atom },
                {
                  name: 'Chemistry Syllabus',
                  href: '/jee/advanced/syllabus/chemistry',
                  icon: Atom
                },
                { name: 'Maths Syllabus', href: '/jee/advanced/syllabus/maths', icon: Calculator }
              ]
            },
            {
              name: 'Difference Between',
              icon: GitCompare,
              submenus: [
                { name: 'Physics Concepts', href: '/jee/advanced/difference/physics', icon: Atom },
                {
                  name: 'Chemistry Concepts',
                  href: '/jee/advanced/difference/chemistry',
                  icon: Atom
                },
                { name: 'Maths Concepts', href: '/jee/advanced/difference/maths', icon: Calculator }
              ]
            },
            {
              name: 'Previous Year Papers',
              icon: FileText,
              submenus: [
                { name: 'JEE Advanced 2025', href: '/jee/advanced/papers/2025' },
                { name: 'JEE Advanced 2024', href: '/jee/advanced/papers/2024' },
                { name: 'JEE Advanced 2023', href: '/jee/advanced/papers/2023' },
                { name: 'JEE Advanced 2022', href: '/jee/advanced/papers/2022' },
                { name: 'JEE Advanced 2021', href: '/jee/advanced/papers/2021' }
              ]
            }
          ]
        }
      ]
    },
    { name: 'Events', href: '/upcomingevents' },
    { name: 'Contact Us', href: '/contact' }
  ];

  const sidebarVariants = {
    open: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: '-100%',
      opacity: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  // Desktop Dropdown Component
  // ✅ Adjust path

  // Inside your component, replace DropdownMenu with:

  const DropdownMenu = ({ item }) => {
    const { isHovered, handleMouseEnter, handleMouseLeave } = useHoverDelay(150);
    const buttonRef = useRef(null);
    const [position, setPosition] = useState({ top: 0, left: 0 });

    useEffect(() => {
      if (isHovered && buttonRef.current) {
        const rect = buttonRef.current.getBoundingClientRect();
        setPosition({
          top: rect.bottom + window.scrollY,
          left: rect.left + window.scrollX
        });
      }
    }, [isHovered]);

    if (!item.submenus) {
      return (
        <a
          href={item.href}
          className="flex items-center gap-1 py-2 px-3  text-lg  text-[var(--color-teacher)] hover:text-black transition-colors rounded-md hover:bg-white/10 relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-0.5 after:bg-amber-400 after:transition-all hover:after:w-full"
          onClick={() => setActiveExam(null)}>
          {item.icon && <item.icon className="w-4 h-4" />}
          {item.name}
        </a>
      );
    }

    return (
      <div
        className="relative group"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}>
        <button
          ref={buttonRef}
          className="flex items-center gap-1 py-2 px-3 text-lg text-[var(--color-teacher)] hover:text-black transition-colors rounded-md hover:bg-white/10 relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-0 after:h-0.5 after:bg-amber-400 after:transition-all group-hover:after:w-full">
          {item.icon && <item.icon className="w-4 h-4" />}
          {item.name} <ChevronRight className="w-4 h-4 rotate-90" />
        </button>

        {isHovered && (
          <DropdownPortal>
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="fixed w-60 bg-gradient-to-b from-gray-900 to-gray-800 rounded-lg shadow-2xl py-2 z-50 border border-amber-500/20"
              style={{
                top: position.top,
                left: position.left,
                pointerEvents: 'auto'
              }}>
              <div className="px-3 py-2 border-b border-amber-500/20 mb-2">
                <h3 className="text-amber-400 font-semibold text-sm flex items-center gap-2">
                  {item.icon && <item.icon className="w-4 h-4" />}
                  {item.name} Resources
                </h3>
              </div>
              <div className="max-h-80 overflow-y-auto">
                {item.submenus.map((subItem, idx) => (
                  <div key={idx} className="px-2">
                    {subItem.submenus ? (
                      <NestedDropdownPortal
                        item={subItem}
                        examType={item.name}
                        triggerRect={position}
                      />
                    ) : (
                      <a
                        href={subItem.href}
                        className="flex items-center justify-between py-2.5 px-4 text-sm text-white hover:text-amber-400 hover:bg-gray-800 rounded-md transition-colors">
                        <div className="flex items-center">
                          {subItem.icon && <subItem.icon className="w-4 h-4 mr-2 text-amber-300" />}
                          {subItem.name}
                        </div>
                        <ChevronRight className="w-4 h-4 text-amber-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </a>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>
          </DropdownPortal>
        )}
      </div>
    );
  };

  // Nested Dropdown for 3rd level — Smart Right/Left Flip
  const NestedDropdownPortal = ({ item, examType, triggerRect }) => {
    const { isHovered, handleMouseEnter, handleMouseLeave } = useHoverDelay(150);
    const parentRef = useRef(null);
    const [position, setPosition] = useState({ top: 0, left: 0 });
    const [isOverflowingRight, setIsOverflowingRight] = useState(false);

    useEffect(() => {
      if (isHovered && parentRef.current) {
        const rect = parentRef.current.getBoundingClientRect();
        const spaceRight = window.innerWidth - rect.right;
        const menuWidth = 224; // w-56

        setIsOverflowingRight(spaceRight < menuWidth + 16);

        setPosition({
          top: rect.top + window.scrollY,
          left: isOverflowingRight ? rect.left - menuWidth - 16 : rect.right + window.scrollX + 8
        });
      }
    }, [isHovered, parentRef.current]);

    return (
      <div
        className="group relative"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        ref={parentRef}>
        <div className="flex items-center justify-between w-full py-2.5 px-4 text-sm text-white hover:text-amber-400 hover:bg-gray-800 rounded-md transition-colors cursor-pointer">
          <div className="flex items-center">
            {item.icon && <item.icon className="w-4 h-4 mr-2 text-amber-300" />}
            {item.name}
          </div>
          <ChevronRight
            className={`w-4 h-4 text-amber-400 ml-2 transform transition-transform ${
              isOverflowingRight ? '-rotate-180' : 'rotate-0'
            }`}
          />
        </div>

        {isHovered && (
          <DropdownPortal>
            <motion.div
              initial={{ opacity: 0, x: isOverflowingRight ? 10 : -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: isOverflowingRight ? 10 : -10 }}
              transition={{ duration: 0.15 }}
              className="fixed w-56 bg-gradient-to-b from-gray-900 to-gray-800 rounded-lg shadow-2xl py-2 z-50 border border-amber-500/20"
              style={{
                top: position.top,
                left: position.left,
                pointerEvents: 'auto'
              }}>
              <div className="px-3 py-2 border-b border-amber-500/20 mb-2">
                <h3 className="text-amber-400 font-semibold text-xs">
                  {examType} {item.name}
                </h3>
              </div>
              <div className="max-h-60 overflow-y-auto">
                {item.submenus.map((subItem, idx) => (
                  <a
                    key={idx}
                    href={subItem.href}
                    className="flex items-center py-2.5 px-4 text-sm text-white hover:text-amber-400 hover:bg-gray-800 rounded-md transition-colors">
                    {subItem.icon && <subItem.icon className="w-4 h-4 mr-2 text-amber-300" />}
                    {subItem.name}
                  </a>
                ))}
              </div>
            </motion.div>
          </DropdownPortal>
        )}
      </div>
    );
  };

  // Mobile Menu Item (Recursive)
  const MobileMenuItem = ({ item, closeMenu, level = 0 }) => {
    const [isOpen, setIsOpen] = useState(false);

    if (!item.submenus) {
      return (
        <a
          href={item.href}
          className="text-white text-lg font-medium py-3 px-4 rounded-lg hover:bg-gray-800 hover:text-amber-400 transition-all flex items-center"
          onClick={closeMenu}
          style={{ paddingLeft: `${level * 20 + 16}px` }}>
          <ChevronRight className="w-5 h-5 mr-2 text-amber-500" />
          {item.icon && <item.icon className="w-5 h-5 mr-2 text-amber-500" />}
          {item.name}
        </a>
      );
    }

    return (
      <div>
        <button
          className="w-full text-left text-white text-lg font-medium py-3 px-4 rounded-lg hover:bg-gray-800 hover:text-amber-400 transition-all flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
          style={{ paddingLeft: `${level * 20 + 16}px` }}>
          <div className="flex items-center">
            <ChevronRight className="w-5 h-5 mr-2 text-amber-500" />
            {item.icon && <item.icon className="w-5 h-5 mr-2 text-amber-500" />}
            {item.name}
          </div>
          <ChevronRight
            className={`w-5 h-5 transform transition-transform ${isOpen ? 'rotate-90' : ''}`}
          />
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="ml-6 border-l-2 border-amber-500/60 pl-4 space-y-2 mt-1">
              {item.submenus.map((subItem, idx) => (
                <MobileMenuItem key={idx} item={subItem} closeMenu={closeMenu} level={level + 1} />
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <header className="fixed inset-x-0 top-0 z-50 bg-white/90 backdrop-blur-md border-b border-amber-500/30">
      <nav aria-label="Global" className="flex items-center justify-between p-4 lg:px-8">
        <div className="flex lg:flex-1">
          <a href="/" className="-m-1.5 p-1.5 flex items-center">
            <span className="sr-only">Sasthra</span>
            <img alt="Sasthra Logo" src={Logo} className="h-full w-40" />
          </a>
        </div>
        <div className="flex lg:hidden">
          <button
            type="button"
            onClick={() => setMobileMenuOpen(true)}
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-black hover:bg-white/10 transition">
            <span className="sr-only">Open main menu</span>
            <BarChart aria-hidden="true" className="size-6" />
          </button>
        </div>
        <div className="hidden lg:flex lg:gap-x-6">
          {navigation.map((item, idx) => (
            <DropdownMenu key={idx} item={item} />
          ))}
        </div>
        <div className="hidden lg:flex lg:flex-1 lg:justify-end">
          <a
            href="/auth"
            className="text-sm font-semibold py-2 px-4 bg-[var(--color-counselor)] text-gray-900 hover:bg-amber-400 transition-colors rounded-md flex items-center gap-1 group shadow-lg shadow-amber-500/20">
            Sign In{' '}
            <span aria-hidden="true" className="group-hover:translate-x-1 transition">
              →
            </span>
          </a>
        </div>
      </nav>

      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            className="fixed inset-0 z-50 lg:hidden"
            initial="closed"
            animate="open"
            exit="closed"
            variants={sidebarVariants}>
            <div
              className="absolute inset-0 bg-[var(--color-teacher)] backdrop-blur-sm"
              onClick={() => setMobileMenuOpen(false)}
            />
            <motion.div
              className="absolute top-0 left-0 w-4/5 max-w-sm h-screen flex flex-col bg-[var(--color-teacher)] shadow-xl border-r border-amber-500/20 overflow-y-auto pb-8"
              style={{ overscrollBehavior: 'contain' }}>
              <div className="flex items-center justify-between p-6 border-b border-amber-500/20 bg-white">
                <div className="flex items-center">
                  <img alt="Sasthra Logo" src={Logo} className="h-full w-32" />
                </div>
                <button
                  type="button"
                  onClick={() => setMobileMenuOpen(false)}
                  className="p-2 rounded-md text-black hover:bg-gray-800 transition">
                  <span className="sr-only">Close menu</span>
                  <X className="size-6" aria-hidden="true" />
                </button>
              </div>
              <nav className="flex flex-col p-6 space-y-4">
                {navigation.map((item, idx) => (
                  <MobileMenuItem
                    key={idx}
                    item={item}
                    closeMenu={() => setMobileMenuOpen(false)}
                  />
                ))}
                <a
                  href="/auth"
                  className="text-white text-lg font-medium py-3 px-4 rounded-lg bg-[var(--color-counselor)]  hover:bg-amber-400 transition-all flex items-center justify-center mt-8 border-t border-amber-500/20 pt-4"
                  onClick={() => setMobileMenuOpen(false)}>
                  Sign In
                </a>
              </nav>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active exam indicator (desktop only) */}
      {activeExam && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="hidden lg:block absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-amber-400 to-amber-600"
        />
      )}
    </header>
  );
};

export default LandingNavbar;
