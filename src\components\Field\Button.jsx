import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router';
import { Tooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import { useId } from 'react';

const Button = ({
  name,
  type = 'button',
  title, // This is a separate prop for the native HTML title attribute
  onClick,
  disabled = false,
  className,
  icon,
  link,
  loading = false,
  tooltip, // This is for the react-tooltip content
  tooltipId: customTooltipId, // Prop received as 'tooltipId', used internally as 'customTooltipId'
  tooltipPlace = 'top',
  children,
  ...rest
}) => {
  const generatedId = useId();
  const T_ID = customTooltipId || (tooltip ? `tooltip-${generatedId}` : undefined);
  const elementId = `button-${generatedId}`;

  const buttonContent = (
    <>
      {icon && <span className={name || children ? 'mr-2' : ''}>{icon}</span>}
      {name || children}
      {loading && (
        <div className="ml-2 animate-spin rounded-full h-4 w-4 border-b-2 border-t-2 border-current opacity-75"></div>
      )}
    </>
  );

  const baseStyling =
    'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2';
  const defaultThemeClasses = `px-4 py-2.5 text-sm text-white ${
    disabled || loading
      ? 'bg-gray-400 cursor-not-allowed'
      : 'bg-[#e79686] hover:bg-[#E2725B] active:bg-[#D0604D] focus:ring-[#E2725B]'
  }`;
  const finalClassName = className ? className : `${baseStyling} ${defaultThemeClasses}`;

  const commonProps = {
    title: title || (typeof tooltip === 'string' && !customTooltipId ? tooltip : undefined),
    className: finalClassName,
    disabled: disabled || loading,
    'aria-disabled': disabled || loading,
    'aria-busy': loading ? true : undefined,
    id: T_ID ? elementId : undefined,
    ...(T_ID && { 'data-tooltip-id': T_ID }),
    ...rest
  };

  // Style for the tooltip to ensure it's above other elements
  const tooltipStyle = { zIndex: 9999 }; // You can adjust this value as needed

  if (link) {
    return (
      <>
        <Link
          to={link}
          role="button"
          {...commonProps}
          onClick={disabled || loading ? (e) => e.preventDefault() : onClick}
        >
          {buttonContent}
        </Link>
        {tooltip && T_ID && (
          <Tooltip
            id={T_ID}
            place={tooltipPlace}
            style={tooltipStyle} // Apply z-index style
          >
            {tooltip}
          </Tooltip>
        )}
      </>
    );
  }

  return (
    <>
      <button type={type} onClick={onClick} {...commonProps}>
        {buttonContent}
      </button>
      {tooltip && T_ID && (
        <Tooltip
          id={T_ID}
          place={tooltipPlace}
          style={tooltipStyle} // Apply z-index style
        >
          {tooltip}
        </Tooltip>
      )}
    </>
  );
};

Button.propTypes = {
  name: PropTypes.node,
  children: PropTypes.node,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  title: PropTypes.string,
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  icon: PropTypes.node,
  link: PropTypes.string,
  loading: PropTypes.bool,
  tooltip: PropTypes.node,
  tooltipId: PropTypes.string,
  tooltipPlace: PropTypes.oneOf([
    'top',
    'top-start',
    'top-end',
    'right',
    'right-start',
    'right-end',
    'bottom',
    'bottom-start',
    'bottom-end',
    'left',
    'left-start',
    'left-end'
  ])
};

export default Button;
