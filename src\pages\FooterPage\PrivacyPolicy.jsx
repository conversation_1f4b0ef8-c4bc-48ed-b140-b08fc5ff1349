import React from 'react';

// Data for the privacy policy sections. This structure remains the same.
const policyData = [
    {
        title: 'Information We Collect',
        content: 'We collect information to provide better services to our users. This includes personal information such as your name, email address, phone number, and educational details. We also collect usage data to improve our services.'
    },
    {
        title: 'How We Use Your Information',
        content: 'We use the information we collect to provide, maintain, and improve our services, to develop new ones, and to protect our users. We also use this information to offer you tailored content – like giving you more relevant search results and personalized learning recommendations.'
    },
    {
        title: 'Information Sharing',
        // Using an array to easily create a list in the renderer
        content: [
            'We do not share your personal information with companies, organizations, or individuals outside of our organization except in the following cases:',
            'With your consent',
            'For legal reasons',
            'With our trusted educational partners'
        ]
    },
    {
        title: 'Information Security',
        content: 'We work hard to protect our users from unauthorized access to or unauthorized alteration, disclosure, or destruction of information we hold. We review our information collection, storage, and processing practices, including physical security measures, to prevent unauthorized access to our systems.'
    },
    {
        title: 'Your Rights',
        content: 'You have the right to access, update, or delete your personal information at any time. You can also choose to opt out of certain data collection practices. Please contact our support team for assistance with managing your data preferences.'
    },
    {
        title: 'Changes to This Policy',
        content: 'Our Privacy Policy may change from time to time. We will post any privacy policy changes on this page and, if the changes are significant, we will provide a more prominent notice.'
    },
    {
        title: 'Contact Us',
        content: 'If you have any questions about our Privacy Policy, please contact <NAME_EMAIL>'
    }
];

// Helper function to render content, now using Tailwind classes.
const renderContent = (content) => {
    const paragraphClasses = "text-slate-700 leading-relaxed";

    // Handle lists
    if (Array.isArray(content)) {
        const [intro, ...listItems] = content;
        return (
            <div>
                <p className={paragraphClasses}>{intro}</p>
                <ul className="list-disc list-inside mt-4 space-y-2">
                    {listItems.map((item, index) => (
                        <li key={index} className={paragraphClasses}>{item}</li>
                    ))}
                </ul>
            </div>
        );
    }
    
    // Handle the contact email link
    if (content.includes('<EMAIL>')) {
        const parts = content.split('<EMAIL>');
        return (
            <p className={paragraphClasses}>
                {parts[0]}
                <a 
                    href="mailto:<EMAIL>" 
                    className="font-semibold text-blue-600 hover:text-blue-700 hover:underline"
                >
                    <EMAIL>
                </a>
                {parts[1]}
            </p>
        );
    }
    
    // Handle regular paragraphs
    return <p className={paragraphClasses}>{content}</p>;
};


const PrivacyPolicy = () => {
    return (
        <div className="bg-slate-50 min-h-screen font-sans">
            <div className="container mx-auto p-6 md:px-10">
                <main className="bg-white p-8 md:p-12 rounded-lg shadow-lg">
                    <h1 className="text-3xl md:text-4xl font-bold text-blue-600 text-center mb-8 pb-4 border-b-2 border-slate-200">
                        Privacy Policy
                    </h1>
                    
                    <div className="space-y-8">
                        {policyData.map((section, index) => (
                            <section key={index}>
                                <h2 className="text-2xl font-semibold text-blue-600 mb-3">
                                    {section.title}
                                </h2>
                                {renderContent(section.content)}
                            </section>
                        ))}
                    </div>
                </main>
            </div>
        </div>
    );
};

export default PrivacyPolicy;