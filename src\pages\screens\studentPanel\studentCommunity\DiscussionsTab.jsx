// src/components/student-community/DiscussionsTab.jsx
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';

const DiscussionsTab = ({
  threads = [],
  loadingThreads,
  selectedThread,
  setSelectedThread,
  newMessage,
  setNewMessage,
  handleCreateThread,
  newReply,
  setNewReply,
  handleAddReply,
  rolee,
  formatRelativeTime
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-4 h-[calc(100vh-80px)] overflow-y-auto">
      {/* Thread List – Creative Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white/70 backdrop-blur-md rounded-2xl shadow-lg border border-gray-100 p-5 lg:sticky lg:top-24 h-auto lg:h-[calc(100vh-120px)] overflow-y-auto">
          <h2 className="text-2xl font-bold mb-5 flex items-center gap-3">
            <span className="text-3xl animate-pulse">💬</span>
            <span className="bg-gradient-to-r from-indigo-600 to-blue-500 bg-clip-text text-transparent">
              Community
            </span>
          </h2>
          {loadingThreads && !threads.length ? (
            <div className="flex items-center justify-center py-8">
              <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : !threads.length ? (
            <div className="text-center py-10">
              <p className="text-gray-500 text-sm">Be the first to start a discussion!</p>
            </div>
          ) : (
            <div className="space-y-3">
              {threads.map((thread) => {
                const isSelected = selectedThread?.id === thread.id;
                return (
                  <motion.div
                    key={thread.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    whileHover={{ scale: 1.02, x: 4 }}
                    transition={{ duration: 0.2 }}
                    className={`p-4 rounded-2xl cursor-pointer transition-all duration-300 relative overflow-hidden group ${
                      isSelected
                        ? 'bg-indigo-50 border-l-4 border-indigo-500 shadow-md'
                        : 'hover:bg-white hover:shadow-md'
                    }`}
                    onClick={() => setSelectedThread(thread)}>
                    {isSelected && (
                      <motion.div
                        className="absolute inset-0 bg-indigo-100/60 rounded-2xl pointer-events-none z-0"
                        layoutId="selected-thread-bg"
                        initial={false}
                        transition={{ duration: 0.3 }}
                      />
                    )}
                    <div className="font-semibold text-gray-800 z-10 relative">
                      {thread.first_name || 'Anonymous'} {thread.sender_type}
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2 mt-1">{thread.content}</p>
                    <div className="flex justify-between items-center mt-3 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        <span className="bg-indigo-100 px-2 py-0.5 rounded-full font-medium">
                          💬 {thread.subthreads?.length || 0}
                        </span>
                      </span>
                      <span>{new Date(thread.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="absolute bottom-0 left-0 w-0 bg-gradient-to-r from-indigo-400 to-blue-400 h-0.5 transition-all duration-300 group-hover:w-full"></div>
                  </motion.div>
                );
              })}
            </div>
          )}

          {/* New Thread Composer */}
          <div className="mt-6 pt-5 border-t border-gray-200">
            <motion.label
              initial={{ y: 4, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1.5">
              <motion.span
                animate={{ rotate: [0, -10, 10, -10, 0] }}
                transition={{ repeat: Infinity, repeatDelay: 3, duration: 2 }}
                className="text-lg">
                ✨
              </motion.span>
              <span>Start a Discussion</span>
            </motion.label>
            <div className="relative">
              <motion.div whileFocus={{ scale: 1.02 }} className="relative group">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="What's on your mind?"
                  className="w-full p-3 pl-4 pr-12 text-sm text-gray-700 placeholder-gray-400 bg-white border-2 border-gray-200 rounded-2xl focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-300 z-10 relative"
                  onKeyPress={(e) => e.key === 'Enter' && handleCreateThread()}
                />
                <AnimatePresence>
                  {newMessage && (
                    <motion.span
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-lg pointer-events-none select-none text-indigo-300 group-hover:text-indigo-500 transition-colors duration-200">
                      💬
                    </motion.span>
                  )}
                </AnimatePresence>
                <motion.div
                  className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-50 to-blue-50 opacity-0 group-hover:opacity-70 transition-opacity duration-300 z-0"
                  initial={false}
                  whileHover={{ opacity: 0.7 }}
                />
              </motion.div>
              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <span>{newMessage.length}/200</span>
              </div>
            </div>
            <motion.button
              onClick={handleCreateThread}
              disabled={!newMessage.trim()}
              className="w-full mt-3 text-white font-medium py-3 rounded-2xl flex items-center justify-center gap-2 overflow-hidden relative group"
              whileHover={!newMessage.trim() ? {} : { y: -1 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 300, damping: 25 }}>
              <div
                className={`absolute inset-0 transition-all duration-500 ${
                  !newMessage.trim()
                    ? 'bg-gradient-to-r from-gray-400 to-gray-500'
                    : 'bg-gradient-to-r from-indigo-500 to-blue-600 group-hover:from-indigo-600 group-hover:to-blue-700'
                }`}
              />
              <motion.div
                className="absolute inset-0 -skew-x-12 w-1/2 h-full bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-80 transition-opacity duration-300"
                animate={{ x: ['100%', '-100%'] }}
                transition={{ repeat: Infinity, duration: 2, ease: 'linear' }}
              />
              <motion.span
                className="relative z-10 flex items-center gap-1.5"
                animate={{ x: [0, 2, 0] }}
                transition={{ repeat: Infinity, repeatType: 'reverse', duration: 2, delay: 0.5 }}>
                <span>🚀</span>
                Post Thread
              </motion.span>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Thread Detail – Immersive View */}
      {selectedThread ? (
        <div className="lg:col-span-2">
          <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-gray-100 p-6 h-auto lg:h-[calc(100vh-120px)] flex flex-col">
            {/* Header */}
            <div className="mb-6 relative">
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="text-3xl font-bold bg-gradient-to-r from-gray-800 via-indigo-700 to-gray-900 bg-clip-text text-transparent leading-tight">
                {selectedThread.content}
              </motion.h2>
              <div className="flex items-center gap-3 mt-3 text-sm text-gray-600 group">
                <div className="relative flex items-center gap-2">
                  <span
                    className="w-2.5 h-2.5 bg-green-500 rounded-full animate-pulse shadow-inner"
                    title="User is active"></span>
                  <span className="font-semibold text-gray-800 transition-transform duration-200 group-hover:scale-105">
                    {selectedThread.first_name || 'Anonymous'} {selectedThread.sender_type}
                  </span>
                  <motion.span
                    className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 whitespace-nowrap z-10"
                    initial={{ y: -5 }}
                    animate={{ y: 0 }}>
                    🌐 Active now
                  </motion.span>
                </div>
                <div className="flex items-center text-gray-300 select-none">
                  <motion.div
                    className="w-1 h-1 bg-gray-400 rounded-full mx-1"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
                  />
                </div>
                <div className="flex items-center gap-1 text-gray-500 group/time">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3.5 w-3.5 opacity-70"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>
                    {new Date(selectedThread.created_at).toLocaleString([], {
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                  <span className="absolute opacity-0 group-hover/time:opacity-90 bg-black text-white text-xs px-2 py-1 rounded transition-opacity duration-300 text-nowrap ml-1 whitespace-nowrap z-10">
                    {formatRelativeTime(new Date(selectedThread.created_at))}
                  </span>
                </div>
                <div className="ml-auto hidden sm:flex items-center gap-1 text-xs text-indigo-600 font-medium bg-indigo-50 px-2.5 py-1 rounded-full">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path
                      fillRule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>{selectedThread.subthreads?.length + 1 || 1} msg</span>
                </div>
              </div>
              <div className="mt-4 h-0.5 bg-gradient-to-r from-transparent via-indigo-200 to-transparent"></div>
            </div>

            {/* Replies */}
            <div className="flex-1 overflow-y-auto pr-2 space-y-4">
              <div className="space-y-3">
                <h3 className="font-bold text-gray-700 flex items-center gap-2">
                  <span>💬</span>
                  Replies ({selectedThread.subthreads?.length || 0})
                </h3>
                <AnimatePresence initial={false}>
                  {selectedThread.subthreads?.map((reply) => (
                    <motion.div
                      key={reply.id}
                      initial={{ opacity: 0, y: 15 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      className="ml-4 pl-6 border-l-2 border-dashed border-indigo-200 group hover:border-solid hover:border-indigo-400 transition-all duration-300 relative">
                      <div className="absolute top-2.5 -left-3 w-4 h-4 bg-indigo-100 border border-indigo-300 rounded-full flex items-center justify-center text-xs text-indigo-600 group-hover:scale-125 transition-transform">
                        💬
                      </div>
                      <div className="bg-white p-3 rounded-xl shadow-sm border border-gray-200 hover:shadow transition-shadow">
                        <div className="flex justify-between items-start">
                          <div className="font-semibold text-sm text-gray-800">
                            {reply.first_name || 'User'} {reply.sender_type}
                          </div>
                          <span className="text-xs text-gray-400 whitespace-nowrap ml-2">
                            {new Date(reply.created_at).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1 leading-relaxed">
                          {reply.content}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </div>
            <div style={{ height: 0 }} />

            {/* Reply Input */}
            <div className="mt-5 pt-5 border-t border-gray-200">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newReply}
                  onChange={(e) => setNewReply(e.target.value)}
                  placeholder="Join the conversation..."
                  className="flex-1 p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-300 focus:border-indigo-500 text-sm transition-all"
                  onKeyPress={(e) => e.key === 'Enter' && handleAddReply(selectedThread.id)}
                />
                <button
                  onClick={() => handleAddReply(selectedThread.id)}
                  disabled={!newReply.trim()}
                  className="bg-indigo-600 disabled:bg-gray-400 text-white px-6 py-3 rounded-full hover:bg-indigo-700 transition-all flex-shrink-0 shadow-md hover:shadow-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="lg:col-span-2 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center px-6 py-10 rounded-2xl bg-gradient-to-br from-gray-50 to-indigo-50 border-2 border-dashed border-gray-200 max-w-md">
            <div className="text-6xl mb-4 animate-bounce">👈</div>
            <h3 className="text-xl font-bold text-gray-700 mb-2">Pick a Message</h3>
            <p className="text-gray-500 text-sm">
              Explore questions, share ideas, and grow together with your peers.
            </p>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default DiscussionsTab;
