// StudentPersonalDetailsForm.jsx (updated with requested features)
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Input from '../../../../components/Field/Input';
import Button from '../../../../components/Field/Button';
import SearchableDropdown from '../../../../components/Field/SearchableDropdown';
import {
  useAddStudentPersonalDetailsMutation,
  useLazyGetStudentParentDetailsQuery
} from './studentPersonalDetailsForm.slice';
import {
  useCompleteStudentPersonalDetailsMutation,
  useCheckStudentPersonalDetailsMutation
} from '../onBroadingAssesment/onBroadingAssessment.slice';
import Toastify from '../../../../components/PopUp/Toastify';

const StudentPersonalDetailsForm = ({ onComplete }) => {
  const [isOpen, setIsOpen] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    weekday_study_hours: '',
    weekend_study_hours: '',
    school_working_hours: '',
    tuition_status: '',
    tuition_details: '',
    plans_to_quit_tuition: '',
    study_method_preference: '',
    parent_education: '',
    parent_social_media_use: '',
    has_relative_neet_jee_success: '',
    relative_neet_jee_details: '',
    has_neighbor_friend_neet_jee_success: '',
    neighbor_friend_neet_jee_details: ''
  });
  const [loading, setLoading] = useState(false);
  const [res, setRes] = useState(null);

  const [addStudentPersonalDetails] = useAddStudentPersonalDetailsMutation();
  const [completeStudentPersonalDetails] = useCompleteStudentPersonalDetailsMutation();
  const [checkStudentPersonalDetails] = useCheckStudentPersonalDetailsMutation();
  const [getParentDetails, { parentData }] = useLazyGetStudentParentDetailsQuery();
  const token = sessionStorage.getItem('token');
  const studentId = sessionStorage.getItem('userId');

  const steps = [
    { title: 'Study Hours', icon: 'clock', color: 'bg-amber-500' },
    { title: 'Tuition Info', icon: 'book', color: 'bg-blue-500' },
    // { title: 'Study Methods', icon: 'graduation-cap', color: 'bg-emerald-500' },
    { title: 'Parent Info', icon: 'users', color: 'bg-rose-500' },
    { title: 'Social Env', icon: 'mobile-screen-button', color: 'bg-yellow-500' },
    { title: 'Success Stories', icon: 'users', color: 'bg-purple-500' }
  ];

  useEffect(() => {
    if (token) {
      getParentDetails({ token });
    }
  }, [token, getParentDetails]);

  useEffect(() => {
    if (parentData?.parent_name) {
      setFormData((prev) => ({
        ...prev,
        parent_education: `Education Details: `
      }));
    }
  }, [parentData]);

  // Auto-set study method based on quit plans
  useEffect(() => {
    if (formData.plans_to_quit_tuition === 'YES') {
      setFormData((prev) => ({ ...prev, study_method_preference: 'SASTHRA' }));
    } else if (formData.plans_to_quit_tuition === 'NO') {
      setFormData((prev) => ({ ...prev, study_method_preference: 'BOTH' }));
    }
  }, [formData.plans_to_quit_tuition]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await addStudentPersonalDetails({ body: formData, token }).unwrap();
      if (response && studentId) {
        await completeStudentPersonalDetails({ student_id: studentId }).unwrap();
        await checkStudentPersonalDetails({ student_id: studentId }).unwrap();
      }
      setRes(response);

      setIsOpen(false);
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Error:', error);
      setRes(error);
    } finally {
      setLoading(false);
    }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
    exit: { opacity: 0, y: 50, transition: { duration: 0.3 } }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.4 } }
  };

  const tuitionDetailsOptions = [
    { id: 'ONLINE', name: 'Online Classes' },
    { id: 'OFFLINE', name: 'Offline Tuition' }
  ];

  const customDropdownStyles = {
    control: (provided) => ({
      ...provided,
      background: '#f8fafc',
      border: '2px solid #e2e8f0',
      borderRadius: '12px',
      boxShadow: 'none',
      color: '#1e293b',
      padding: '0.5rem',
      '&:hover': { borderColor: 'var(--color-student)' }
    }),
    singleValue: (provided) => ({
      ...provided,
      color: '#1e293b'
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#94a3b8'
    }),
    menu: (provided) => ({
      ...provided,
      background: '#ffffff',
      border: '1px solid #e2e8f0',
      borderRadius: '12px',
      boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
    }),
    option: (provided, state) => ({
      ...provided,
      background: state.isSelected ? 'var(--color-student)' : 'transparent',
      color: '#1e293b',
      '&:hover': { background: 'var(--color-counselor)' }
    }),
    input: (provided) => ({
      ...provided,
      color: '#1e293b'
    })
  };

  if (!studentId) {
    console.error('Student ID not found in session storage');
    return null;
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-8">
            <Toastify res={res} resClear={() => setRes(null)} />
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-3">Study Schedule</h3>
              <p className="text-slate-600 max-w-md mx-auto">
                Tell us about your daily study routine and time management
              </p>
            </div>

            <div className="space-y-6">
              <motion.div
                className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
                whileHover={{
                  y: -5,
                  boxShadow:
                    '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                }}
                transition={{ duration: 0.2 }}>
                <div className="flex items-center gap-4 mb-5">
                  <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-amber-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-slate-800">Weekday Study Hours</h4>
                    <p className="text-slate-500 text-sm">During school days</p>
                  </div>
                </div>
                <Input
                  type="number"
                  name="weekday_study_hours"
                  value={formData.weekday_study_hours}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.5"
                  placeholder="e.g., 4.5 hours"
                  className="w-full bg-slate-50 border border-slate-200 rounded-xl text-slate-800 focus:border-[var(--color-student)] focus:ring-4 focus:ring-[var(--color-student)]/20 px-5 py-4 placeholder:text-slate-400 text-lg"
                />
              </motion.div>

              <motion.div
                className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
                whileHover={{
                  y: -5,
                  boxShadow:
                    '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                }}
                transition={{ duration: 0.2 }}>
                <div className="flex items-center gap-4 mb-5">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-blue-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-slate-800">
                      Weekend- Day Based Study Hours
                    </h4>
                    <p className="text-slate-500 text-sm">During weekends and holidays</p>
                  </div>
                </div>
                <Input
                  type="number"
                  name="weekend_study_hours"
                  value={formData.weekend_study_hours}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.5"
                  placeholder="e.g., 6.0 hours"
                  className="w-full bg-slate-50 border border-slate-200 rounded-xl text-slate-800 focus:border-[var(--color-student)] focus:ring-4 focus:ring-[var(--color-student)]/20 px-5 py-4 placeholder:text-slate-400 text-lg"
                />
              </motion.div>

              <motion.div
                className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
                whileHover={{
                  y: -5,
                  boxShadow:
                    '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                }}
                transition={{ duration: 0.2 }}>
                <div className="flex items-center gap-4 mb-5">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-green-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-slate-800">School Working Hours</h4>
                    <p className="text-slate-500 text-sm">Time spent in school</p>
                  </div>
                </div>
                <Input
                  type="number"
                  name="school_working_hours"
                  value={formData.school_working_hours}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.5"
                  placeholder="e.g., 7.5 hours"
                  className="w-full bg-slate-50 border border-slate-200 rounded-xl text-slate-800 focus:border-[var(--color-student)] focus:ring-4 focus:ring-[var(--color-student)]/20 px-5 py-4 placeholder:text-slate-400 text-lg"
                />
              </motion.div>
            </div>
          </motion.div>
        );

      case 1:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-3">Tuition Information</h3>
              <p className="text-slate-600 max-w-md mx-auto">
                Share details about your additional learning support
              </p>
            </div>

            <motion.div
              className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}>
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-blue-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-slate-800">
                    Do you attend tuition classes?
                  </h4>
                  <p className="text-slate-500 text-sm">Additional coaching support</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <motion.button
                  type="button"
                  onClick={() => handleSelectChange('tuition_status', 'YES')}
                  className={`p-6 rounded-2xl border-2 transition-all duration-300 flex flex-col items-center justify-center ${
                    formData.tuition_status === 'YES'
                      ? 'border-[var(--color-counselor)] bg-[var(--color-counselor)]/10'
                      : 'border-slate-200 hover:border-slate-300'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}>
                  <div className="text-3xl mb-3">✅</div>
                  <div className="text-slate-800 font-medium text-lg">Yes</div>
                </motion.button>
                <motion.button
                  type="button"
                  onClick={() => handleSelectChange('tuition_status', 'NO')}
                  className={`p-6 rounded-2xl border-2 transition-all duration-300 flex flex-col items-center justify-center ${
                    formData.tuition_status === 'NO'
                      ? 'border-red-500 bg-red-50'
                      : 'border-slate-200 hover:border-slate-300'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}>
                  <div className="text-3xl mb-3">❌</div>
                  <div className="text-slate-800 font-medium text-lg">No</div>
                </motion.button>
              </div>
            </motion.div>

            {formData.tuition_status === 'YES' && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="space-y-6">
                <motion.div
                  className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
                  whileHover={{ y: -5 }}
                  transition={{ duration: 0.2 }}>
                  <div className="flex items-center gap-4 mb-5">
                    <div className="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 text-indigo-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800">Tuition Type</h4>
                      <p className="text-slate-500 text-sm">Select your tuition format</p>
                    </div>
                  </div>
                  <SearchableDropdown
                    options={tuitionDetailsOptions}
                    placeholder="Select tuition type"
                    value={formData.tuition_details}
                    onChange={(selected) =>
                      handleSelectChange('tuition_details', selected ? selected.id : '')
                    }
                    required
                    customStyles={customDropdownStyles}
                  />
                </motion.div>

                <motion.div
                  className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
                  whileHover={{ y: -5 }}
                  transition={{ duration: 0.2 }}>
                  <div className="flex items-center gap-4 mb-5">
                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 text-purple-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800">
                        Plans to Quit Tuition?
                      </h4>
                      <p className="text-slate-500 text-sm">Future intentions</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <motion.button
                      type="button"
                      onClick={() => handleSelectChange('plans_to_quit_tuition', 'YES')}
                      className={`p-6 rounded-2xl border-2 transition-all duration-300 flex flex-col items-center justify-center ${
                        formData.plans_to_quit_tuition === 'YES'
                          ? 'border-green-500 bg-green-50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}>
                      <div className="text-3xl mb-3">✅</div>
                      <div className="text-slate-800 font-medium text-lg">Yes</div>
                    </motion.button>
                    <motion.button
                      type="button"
                      onClick={() => handleSelectChange('plans_to_quit_tuition', 'NO')}
                      className={`p-6 rounded-2xl border-2 transition-all duration-300 flex flex-col items-center justify-center ${
                        formData.plans_to_quit_tuition === 'NO'
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}>
                      <div className="text-3xl mb-3">❌</div>
                      <div className="text-slate-800 font-medium text-lg">No</div>
                    </motion.button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        );

      // case 2:
      //   return (
      //     // <motion.div
      //     //   initial={{ opacity: 0, x: 20 }}
      //     //   animate={{ opacity: 1, x: 0 }}
      //     //   exit={{ opacity: 0, x: -20 }}
      //     //   className="space-y-8">
      //     //   <div className="text-center mb-8">
      //     //     <div className="w-20 h-20 bg-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
      //     //       <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      //     //         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
      //     //         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
      //     //         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
      //     //       </svg>
      //     //     </div>
      //     //     <h3 className="text-2xl font-bold text-slate-800 mb-3">Study Methods</h3>
      //     //     <p className="text-slate-600 max-w-md mx-auto">Your preferred learning approach</p>
      //     //   </div>

      //     //   {formData.tuition_status === 'YES' && (
      //     //     <motion.div
      //     //       className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
      //     //       whileHover={{ y: -5 }}
      //     //       transition={{ duration: 0.2 }}>
      //     //       <div className="flex items-center gap-4 mb-5">
      //     //         <div className="w-12 h-12 bg-teal-100 rounded-xl flex items-center justify-center">
      //     //           <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      //     //             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      //     //           </svg>
      //     //         </div>
      //     //         <div>
      //     //           <h4 className="text-lg font-semibold text-slate-800">Study Method Preference</h4>
      //     //           <p className="text-slate-500 text-sm">
      //     //             {formData.plans_to_quit_tuition === 'YES'
      //     //               ? 'Automatically set to Sasthra Resources'
      //     //               : formData.plans_to_quit_tuition === 'NO'
      //     //                 ? 'Automatically set to Both Combined'
      //     //                 : 'Select your preference'}
      //     //           </p>
      //     //         </div>
      //     //       </div>
      //     //       <div className="bg-slate-50 border border-slate-200 rounded-xl px-5 py-4 text-lg text-slate-800">
      //     //         {formData.study_method_preference === 'SASTHRA'
      //     //           ? 'Sasthra Resources (Auto-selected)'
      //     //           : formData.study_method_preference === 'BOTH'
      //     //             ? 'Both Combined (Auto-selected)'
      //     //             : 'Please select tuition quit plans first'}
      //     //       </div>
      //     //     </motion.div>
      //     //   )}

      //     //   <motion.div
      //     //     className="bg-gradient-to-r from-amber-50 to-orange-50 p-6 rounded-2xl border border-amber-100"
      //     //     whileHover={{ y: -3 }}
      //     //     transition={{ duration: 0.2 }}>
      //     //     <div className="flex items-start gap-4">
      //     //       <div className="text-2xl mt-1">💡</div>
      //     //       <div>
      //     //         <h4 className="text-lg font-semibold text-slate-800 mb-2">Study Tips</h4>
      //     //         <p className="text-slate-600">
      //     //           Combining multiple study methods often leads to better results.
      //     //           Consider mixing current materials with additional resources for comprehensive preparation.
      //     //         </p>
      //     //       </div>
      //     //     </div>
      //     //   </motion.div>
      //     // </motion.div>
      //   );

      case 2:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-3">Parent Information</h3>
              <p className="text-slate-600 max-w-md mx-auto">
                Share information about your family's educational background
              </p>
            </div>

            {parentData?.parent_name && (
              <motion.div
                className="bg-blue-50 p-6 rounded-2xl border border-blue-100"
                whileHover={{ y: -3 }}
                transition={{ duration: 0.2 }}>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-blue-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    />
                  </div>
                  <div>
                    <p className="text-slate-600 text-sm">Parent/Guardian Name</p>
                    <p className="text-slate-800 font-medium text-lg">{parentData.parent_name}</p>
                  </div>
                </div>
              </motion.div>
            )}

            <motion.div
              className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}>
              {/* 👇 Display parent name here if available */}
              {parentData?.parent_name && (
                <div className="mb-4">
                  <span className="text-sm font-medium text-black text-slate-600">
                    For:{' '}
                    <span className="font-semibold text-slate-800">{parentData.parent_name}</span>
                  </span>
                </div>
              )}

              {/* 👇 Original heading */}
              <div className="flex items-center gap-4 mb-5">
                <div className="w-12 h-12 bg-rose-100 rounded-xl flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-rose-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-slate-800">Parent Education Details</h4>
                  <p className="text-slate-500 text-sm">
                    Educational qualifications and profession
                  </p>
                </div>
              </div>

              <textarea
                id="parent_education"
                name="parent_education"
                value={formData.parent_education}
                onChange={handleChange}
                required
                placeholder={`Describe ${parentData?.parent_name ? parentData.parent_name + "'s" : "your parent/guardian's"} educational background and profession... Example: 'My father is a software engineer with a B.Tech degree. My mother is a teacher with an M.Ed degree.'`}
                className="w-full bg-slate-50 text-slate-800 placeholder:text-slate-400 border border-slate-200 focus:border-rose-500 focus:ring-4 focus:ring-rose-100 rounded-xl px-5 py-4 min-h-[180px] text-lg resize-none"
              />
            </motion.div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-3">Social Environment</h3>
              <p className="text-slate-600 max-w-md mx-auto">
                Tell us about your family's digital habits
              </p>
            </div>

            <motion.div
              className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}>
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-amber-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-slate-800">
                    Parent Social Media Usage
                  </h4>
                  <p className="text-slate-500 text-sm">
                    How often does your family use social media?
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <motion.button
                  type="button"
                  onClick={() => handleSelectChange('parent_social_media_use', 'YES')}
                  className={`p-6 rounded-2xl border-2 transition-all duration-300 flex flex-col items-center justify-center ${
                    formData.parent_social_media_use === 'YES'
                      ? 'border-yellow-500 bg-yellow-50'
                      : 'border-slate-200 hover:border-slate-300'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}>
                  <div className="text-3xl mb-3">📱</div>
                  <div className="text-slate-800 font-medium text-lg">Yes</div>
                  <div className="text-slate-500 text-sm mt-1">Regular usage</div>
                </motion.button>
                <motion.button
                  type="button"
                  onClick={() => handleSelectChange('parent_social_media_use', 'NO')}
                  className={`p-6 rounded-2xl border-2 transition-all duration-300 flex flex-col items-center justify-center ${
                    formData.parent_social_media_use === 'NO'
                      ? 'border-gray-500 bg-gray-50'
                      : 'border-slate-200 hover:border-slate-300'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}>
                  <div className="text-3xl mb-3">📵</div>
                  <div className="text-slate-800 font-medium text-lg">No</div>
                  <div className="text-slate-500 text-sm mt-1">
                    Dont know about social media usage
                  </div>
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-3">Success Stories</h3>
              <p className="text-slate-600 max-w-md mx-auto">
                Share inspiring stories from your circle
              </p>
            </div>

            <motion.div
              className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}>
              <div className="flex items-center gap-4 mb-5">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-purple-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-slate-800">
                    Relative Members with NEET/JEE Success
                  </h4>
                  <p className="text-slate-500 text-sm">Academic achievements in your family</p>
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-slate-800 font-medium mb-3">
                  Do you have any relative members who have successfully completed NEET/JEE exams?
                </label>
                <div className="flex space-x-6">
                  <label className="flex items-center text-slate-800 cursor-pointer">
                    <input
                      type="radio"
                      name="has_relative_neet_jee_success"
                      value="YES"
                      checked={formData.has_relative_neet_jee_success === 'YES'}
                      onChange={(e) =>
                        handleSelectChange('has_relative_neet_jee_success', e.target.value)
                      }
                      required
                      className="h-5 w-5 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="ml-3 text-lg">Yes</span>
                  </label>
                  <label className="flex items-center text-slate-800 cursor-pointer">
                    <input
                      type="radio"
                      name="has_relative_neet_jee_success"
                      value="NO"
                      checked={formData.has_relative_neet_jee_success === 'NO'}
                      onChange={(e) =>
                        handleSelectChange('has_relative_neet_jee_success', e.target.value)
                      }
                      required
                      className="h-5 w-5 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="ml-3 text-lg">No</span>
                  </label>
                </div>
              </div>

              {formData.has_relative_neet_jee_success === 'YES' && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="mt-4">
                  <label
                    htmlFor="relative_neet_jee_details"
                    className="block text-slate-800 font-medium mb-3">
                    Please provide details about your relative members who succeeded in NEET/JEE:
                  </label>
                  <textarea
                    id="relative_neet_jee_details"
                    name="relative_neet_jee_details"
                    value={formData.relative_neet_jee_details}
                    onChange={handleChange}
                    required
                    placeholder="Example: 'My cousin scored 680 in NEET 2022 and is now studying at AIIMS.' 'My uncle's son got into IIT Bombay through JEE Advanced.'"
                    className="w-full bg-slate-50 text-slate-800 placeholder:text-slate-400 border border-slate-200 focus:border-rose-500 focus:ring-4 focus:ring-rose-100 rounded-xl px-5 py-4 min-h-[180px] text-lg resize-none"
                  />
                </motion.div>
              )}
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-2xl shadow-lg border border-slate-100"
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}>
              <div className="flex items-center gap-4 mb-5">
                <div className="w-12 h-12 bg-fuchsia-100 rounded-xl flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-fuchsia-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-slate-800">
                    Neighbors/Friends with NEET/JEE Success
                  </h4>
                  <p className="text-slate-500 text-sm">Success stories from your community</p>
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-slate-800 font-medium mb-3">
                  Do you have any neighbors or friends who have successfully completed NEET/JEE
                  exams?
                </label>
                <div className="flex space-x-6">
                  <label className="flex items-center text-slate-800 cursor-pointer">
                    <input
                      type="radio"
                      name="has_neighbor_friend_neet_jee_success"
                      value="YES"
                      checked={formData.has_neighbor_friend_neet_jee_success === 'YES'}
                      onChange={(e) =>
                        handleSelectChange('has_neighbor_friend_neet_jee_success', e.target.value)
                      }
                      required
                      className="h-5 w-5 text-fuchsia-600 focus:ring-fuchsia-500"
                    />
                    <span className="ml-3 text-lg">Yes</span>
                  </label>
                  <label className="flex items-center text-slate-800 cursor-pointer">
                    <input
                      type="radio"
                      name="has_neighbor_friend_neet_jee_success"
                      value="NO"
                      checked={formData.has_neighbor_friend_neet_jee_success === 'NO'}
                      onChange={(e) =>
                        handleSelectChange('has_neighbor_friend_neet_jee_success', e.target.value)
                      }
                      required
                      className="h-5 w-5 text-fuchsia-600 focus:ring-fuchsia-500"
                    />
                    <span className="ml-3 text-lg">No</span>
                  </label>
                </div>
              </div>

              {formData.has_neighbor_friend_neet_jee_success === 'YES' && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="mt-4">
                  <label
                    htmlFor="neighbor_friend_neet_jee_details"
                    className="block text-slate-800 font-medium mb-3">
                    Please provide details about your neighbors/friends who succeeded in NEET/JEE:
                  </label>
                  <textarea
                    id="neighbor_friend_neet_jee_details"
                    name="neighbor_friend_neet_jee_details"
                    value={formData.neighbor_friend_neet_jee_details}
                    onChange={handleChange}
                    required
                    placeholder="Example: 'My neighbor's son got into IIT Bombay through JEE Advanced.' 'My friend scored 720 in NEET and is now studying at CMC Vellore.'"
                    className="w-full bg-slate-50 text-slate-800 placeholder:text-slate-400 border border-slate-200 focus:border-rose-500 focus:ring-4 focus:ring-rose-100 rounded-xl px-5 py-4 min-h-[180px] text-lg resize-none"
                  />
                </motion.div>
              )}
            </motion.div>

            <motion.div
              className="bg-gradient-to-r from-amber-50 to-orange-50 p-6 rounded-2xl border border-amber-100"
              whileHover={{ y: -3 }}
              transition={{ duration: 0.2 }}>
              <div className="flex items-start gap-4">
                <div className="text-2xl mt-1">✨</div>
                <div>
                  <h4 className="text-lg font-semibold text-slate-800 mb-2">Motivation</h4>
                  <p className="text-slate-600">
                    Having role models in your circle can be a great source of inspiration and
                    motivation for your own journey.
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-slate-900/90"
          variants={backdropVariants}
          initial="hidden"
          animate="visible"
          exit="hidden">
          <motion.div
            className="relative bg-white rounded-3xl shadow-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}>
            {/* Creative Header */}
            <div className="text-center mb-10 relative">
              <h2 className="text-3xl font-bold text-[var(--color-student)] pt-16 mb-2">
                Student Journey Setup
              </h2>
              <p className="text-slate-600">Crafting your personalized learning path</p>
              <div className="flex items-center justify-center gap-2 mt-3">
                <span className="text-slate-600 font-medium">Step {currentStep + 1}</span>
                <span className="text-slate-600">of</span>
                <span className="text-slate-600 font-medium">{steps.length}</span>
              </div>
            </div>

            {/* Progress tracker with creative design */}
            <div className="mb-12">
              <div className="flex justify-between items-center mb-8 relative">
                {/* Connecting line */}
                <div className="absolute top-1/2 left-0 right-0 h-1 bg-slate-200 -z-10 transform -translate-y-1/2"></div>

                {steps.map((step, index) => (
                  <motion.div
                    key={index}
                    className="flex flex-col items-center relative z-10"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.2 }}>
                    <div
                      className={`w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-bold transition-all duration-300 shadow-lg ${
                        index === currentStep
                          ? `${step.color} text-white ring-4 ring-white border-4 border-${step.color.replace('bg-', '')}`
                          : index < currentStep
                            ? 'bg-green-500 text-white'
                            : 'bg-white text-slate-400 border-2 border-slate-200'
                      }`}>
                      {index < currentStep ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={`M${step.icon === 'clock' ? '12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' : step.icon === 'book' ? '12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253' : step.icon === 'graduation-cap' ? '12 14l9-5-9-5-9 5 9 5z' : step.icon === 'users' ? '17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' : step.icon === 'mobile-screen-button' ? '12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z' : 'M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'}`}
                          />
                        </svg>
                      )}
                    </div>
                    <span
                      className={`text-xs mt-3 text-center max-w-[100px] leading-tight font-medium ${
                        index === currentStep ? 'text-slate-800 font-bold' : 'text-slate-500'
                      }`}>
                      {step.title}
                    </span>
                  </motion.div>
                ))}
              </div>
              <div className="w-full bg-slate-200 rounded-full h-3">
                <motion.div
                  className="bg-[var(--color-counselor)] h-3 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                  transition={{ duration: 0.5, ease: 'easeOut' }}></motion.div>
              </div>
            </div>

            {/* Form content */}
            <form
              onSubmit={
                currentStep === steps.length - 1 ? handleSubmit : (e) => e.preventDefault()
              }>
              {renderStepContent()}

              {/* Navigation */}
              <div className="flex justify-between mt-12">
                <motion.button
                  type="button"
                  onClick={prevStep}
                  disabled={currentStep === 0}
                  className={`px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 flex items-center gap-2 ${
                    currentStep === 0
                      ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                      : 'bg-slate-200 text-slate-700 hover:bg-slate-300'
                  }`}
                  whileHover={currentStep === 0 ? {} : { scale: 1.05 }}
                  whileTap={currentStep === 0 ? {} : { scale: 0.95 }}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                  Previous
                </motion.button>

                {currentStep === steps.length - 1 ? (
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      type="submit"
                      name={loading ? 'Submitting...' : 'Complete Setup'}
                      disabled={loading}
                      className="px-8 py-4 bg-[var(--color-student)] text-white rounded-2xl font-semibold text-lg hover:opacity-90 transition-all duration-300 shadow-lg"
                    />
                  </motion.div>
                ) : (
                  <motion.button
                    type="button"
                    onClick={nextStep}
                    className="px-8 py-4 bg-[var(--color-student)] text-white rounded-2xl font-semibold text-lg hover:opacity-90 transition-all duration-300 shadow-lg flex items-center gap-2"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}>
                    Next
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </motion.button>
                )}
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default StudentPersonalDetailsForm;
