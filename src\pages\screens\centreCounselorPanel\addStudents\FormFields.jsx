import {
  faUser,
  faEnvelope,
  faPhone,
  faCalendar,
  faGraduationCap,
  faPercentage,
  faPray,
  faUsers,
  faIdCard,
  faMapMarkerAlt,
  faFlag,
  faBriefcase,
  faMoneyBillWave,
  faFileUpload,
  faBook
} from '@fortawesome/free-solid-svg-icons';

export const fields = (courses = [], batches = []) => [
  {
    section: 'Student Info',
    icon: faUser,
    inputs: [
      {
        name: 'first_name',
        placeholder: 'Enter First Name',
        type: 'text',
        required: true,
        leftIcon: faUser,
        maxLength: 100
      },
      {
        name: 'last_name',
        placeholder: 'Enter Last Name',
        type: 'text',
        required: true,
        leftIcon: faUser,
        maxLength: 100
      },
      {
        name: 'email',
        placeholder: 'Enter Email',
        type: 'email',
        required: true,
        leftIcon: faEnvelope
      },
      {
        name: 'phone',
        placeholder: 'Enter Phone (+91xxxxxxxxxx)',
        type: 'tel',
        required: true,
        maxLength: 13,
        leftIcon: faPhone
      },
      {
        name: 'dob',
        placeholder: 'Select Date of Birth',
        type: 'date',
        required: true,
        leftIcon: faCalendar
      },
      {
        name: 'grade',

        type: 'number',
        required: true,
        maxLength: 12,
        leftIcon: faBook
      },
      {
        name: 'course_id',
        placeholder: 'Select Course',
        type: 'select',
        required: true,
        leftIcon: faGraduationCap,
        options: courses.map((course) => ({ value: course.id, label: course.course_name }))
      },
      {
        name: 'batch_id',
        placeholder: 'Select Batch',
        type: 'select',
        required: true,
        leftIcon: faGraduationCap,
        options: batches.map((batch) => ({ value: batch.id, label: batch.batch_name }))
      },
      {
        name: 'marks_10th',
        placeholder: 'Enter 10th Marks (%) or NULL',
        type: 'text', // Changed to text to allow "NA"
        required: true,
        leftIcon: faPercentage
      },
      {
        name: 'marks_12th',
        placeholder: 'Enter 12th Marks (%) or NULL',
        type: 'text', // Changed to text to allow "NA"
        required: true,
        leftIcon: faPercentage
      },
      {
        name: 'religion',
        placeholder: 'Enter Religion (Optional)',
        type: 'text',
        leftIcon: faPray,
        maxLength: 100
      },
      {
        name: 'gender',
        placeholder: 'Select Gender',
        type: 'select',
        required: true,
        leftIcon: faUser,
        options: [
          { value: 'MALE', label: 'Male' },
          { value: 'FEMALE', label: 'Female' },
          { value: 'OTHER', label: 'Other' }
        ]
      },
      {
        name: 'age',
        placeholder: 'Age (Auto-calculated)',
        type: 'number',
        required: true,
        leftIcon: faCalendar,
        min: 1,
        max: 100,
        readOnly: true
      },
      {
        name: 'aadhar_number',
        placeholder: 'Aadhar Number (1234-5678-9012)',
        type: 'text',
        leftIcon: faIdCard,
        pattern: '[0-9]{4}-[0-9]{4}-[0-9]{4}'
      },
      {
        name: 'nationality',
        placeholder: 'Enter Nationality',
        type: 'text',
        required: true,
        leftIcon: faFlag,
        maxLength: 100
      },
      {
        name: 'full_address',
        placeholder: 'Enter Full Address',
        type: 'text',
        required: true,
        leftIcon: faMapMarkerAlt
      }
    ]
  },
  {
    section: 'Parent Info',
    icon: faUsers,
    inputs: [
      {
        name: 'parent_relationship',
        placeholder: 'Select Relationship',
        type: 'select',
        required: true,
        leftIcon: faUsers,
        options: [
          { value: 'father', label: 'Father' },
          { value: 'mother', label: 'Mother' },
          { value: 'guardian', label: 'Guardian' }
        ]
      },
      {
        name: 'parent_first_name',
        placeholder: 'Enter Parent First Name',
        type: 'text',
        required: true,
        leftIcon: faUser,
        maxLength: 100
      },
      {
        name: 'parent_last_name',
        placeholder: 'Enter Parent Last Name',
        type: 'text',
        leftIcon: faUser,
        maxLength: 100
      },
      {
        name: 'parent_email',
        placeholder: 'Enter Parent Email',
        type: 'email',
        leftIcon: faEnvelope
      },
      {
        name: 'parent_phone',
        placeholder: 'Enter Parent Phone (+91xxxxxxxxxx)',
        type: 'tel',
        required: true,
        maxLength: 13,
        leftIcon: faPhone
      },
      {
        name: 'parent_occupation',
        placeholder: 'Enter Parent Occupation',
        type: 'text',
        leftIcon: faBriefcase,
        maxLength: 100
      },
      {
        name: 'parent_annual_income_inr',
        placeholder: 'Enter Annual Income (INR)',
        type: 'number',
        leftIcon: faMoneyBillWave,
        min: 0,
        step: 1000
      }
    ]
  },
  {
    section: 'Document Uploads',
    icon: faFileUpload,
    inputs: [
      {
        name: 'aadhar_card',
        placeholder: 'Aadhar Card (PDF/Image)',
        type: 'file',
        accept: '.pdf,.jpg,.jpeg,.png',
        leftIcon: faIdCard
      },
      {
        name: 'pan_card',
        placeholder: 'PAN Card (PDF/Image)',
        type: 'file',
        accept: '.pdf,.jpg,.jpeg,.png',
        leftIcon: faIdCard
      },
      {
        name: 'photo',
        placeholder: 'Student Photo (Image)',
        type: 'file',
        accept: '.jpg,.jpeg,.png',
        leftIcon: faUser
      },
      {
        name: 'other_document',
        placeholder: 'Other Document (PDF/Image)',
        type: 'file',
        accept: '.pdf,.jpg,.jpeg,.png',
        leftIcon: faFileUpload
      }
    ]
  }
];
