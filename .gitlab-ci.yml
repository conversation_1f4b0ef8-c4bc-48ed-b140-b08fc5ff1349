# stages:
#   - build
#   - test
#   - deploy
 
# variables:
#   CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:sas
#   ENV_FILE: config/env.production
 
# default:
#   image: docker:latest
#   services:
#     - docker:dind
 
# before_script:
#   - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
 
# build:
#   stage: build
#   tags:
#     - sas
#   script:
#     - docker build -t $CONTAINER_RELEASE_IMAGE -f Dockerfile .
#     - docker push $CONTAINER_RELEASE_IMAGE
#   rules:
#     - if: '$CI_COMMIT_BRANCH == "main"'
 
# test:
#   stage: test
#   tags:
#     - sas
#   script:
#     - echo "Running tests..."
#   rules:
#     - if: '$CI_COMMIT_BRANCH == "main"'
 
# deploy:
#   stage: deploy
#   tags:
#     - sas
#   script:
#     - echo "Triggering deployment pipeline..."
#     - curl --request POST --form "token=${DEPLOY_REPO_TRIGGER_TOKEN}" --form "ref=main" "https://srv861308.hstgr.cloud/api/v4/projects/${DEPLOY_REPO_ID}/trigger/pipeline"
#   rules:
#     - if: '$CI_COMMIT_BRANCH == "main"'


stages:
  - build
  - test
  - release
  - deploy

variables:
  CONTAINER_BASE_IMAGE: $CI_REGISTRY_IMAGE
  ENV_FILE: config/env.production

default:
  image: docker:latest
  services:
    - docker:dind

before_script:
  - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"

# --- Build Production Image ---
build_prod:
  stage: build
  tags:
    - sas
  script:
    - docker build --build-arg MODE=production -t ${CONTAINER_BASE_IMAGE}:prod -f Dockerfile .
    - docker push ${CONTAINER_BASE_IMAGE}:prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'

# --- Build Test Image ---
build_test:
  stage: build
  tags:
    - sas
  script:
    - docker build --build-arg MODE=testing -t ${CONTAINER_BASE_IMAGE}:test -f Dockerfile .
    - docker push ${CONTAINER_BASE_IMAGE}:test
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'

# --- Test Stage ---
test:
  stage: test
  tags:
    - sas
  script:
    - echo "Running tests..."
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "test"'

# --- Release Stage ---
release:
  stage: release
  tags:
    - sas
  script:
    - echo "Creating release..."
    # Example: tag the production image as latest and push
    - docker pull ${CONTAINER_BASE_IMAGE}:prod
    - docker tag ${CONTAINER_BASE_IMAGE}:prod ${CONTAINER_BASE_IMAGE}:latest
    - docker push ${CONTAINER_BASE_IMAGE}:latest
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'

# --- Deploy Stage ---
deploy:
  stage: deploy
  tags:
    - sas
  script:
    - echo "Triggering deployment pipeline..."
    - curl --request POST --form "token=${DEPLOY_REPO_TRIGGER_TOKEN}" --form "ref=main" "https://srv861308.hstgr.cloud/api/v4/projects/${DEPLOY_REPO_ID}/trigger/pipeline"
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
