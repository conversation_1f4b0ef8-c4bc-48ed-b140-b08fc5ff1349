import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Clock,
  Target,
  Brain,
  Award,
  BarChart3,
  Play,
  ChevronRight,
  Zap
} from 'lucide-react';
import FullLengthMockTest from './FullLengthMockTest/FullLengthMockTest';
import SubjectWiseMockTest from './SubjectWiseMockTest/SubjectWiseMockTest';
import { motion, AnimatePresence } from 'framer-motion';

const MockTestSimulation = () => {
  const [activeTab, setActiveTab] = useState('fullLength');
  const [currentTest, setCurrentTest] = useState(null);
  const [hoveredCard, setHoveredCard] = useState(null);

  const handleStartTest = (testType) => {
    setCurrentTest(testType);
  };

  const handleBack = () => {
    setCurrentTest(null);
  };

  // 3D Card Component with advanced Framer Motion effects
  const ThreeDCard = ({ feature, index, onClick }) => {
    return (
      <motion.div
        className="relative cursor-pointer"
        initial={{ opacity: 0, y: 50, rotateX: -15 }}
        animate={{ opacity: 1, y: 0, rotateX: 0 }}
        transition={{
          duration: 0.6,
          delay: index * 0.15,
          type: 'spring',
          stiffness: 100,
          damping: 15
        }}
        whileHover={{
          y: -15,
          rotateY: 5,
          transition: { type: 'spring', stiffness: 300 }
        }}
        onHoverStart={() => setHoveredCard(index)}
        onHoverEnd={() => setHoveredCard(null)}
        onClick={onClick}
        style={{
          perspective: 1000,
          transformStyle: 'preserve-3d'
        }}>
        <motion.div
          className="relative rounded-2xl p-6 h-52 overflow-hidden bg-gradient-to-br from-white to-gray-50 shadow-2xl"
          whileHover={{
            boxShadow: '0 25px 50px -12px rgba(59, 130, 246, 0.25)',
            transition: { duration: 0.3 }
          }}
          style={{
            transformStyle: 'preserve-3d'
          }}>
          {/* 3D edge effect */}
          <motion.div
            className="absolute inset-0 rounded-2xl border-2 border-blue-100 opacity-0"
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          />

          {/* Floating particles that emerge on hover */}
          <AnimatePresence>
            {hoveredCard === index && (
              <>
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-blue-400 rounded-full"
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      y: [-10, -30, -50],
                      x: Math.random() * 40 - 20
                    }}
                    transition={{
                      duration: 1.5,
                      delay: i * 0.3,
                      repeat: Infinity
                    }}
                    style={{
                      left: `${20 + i * 30}%`,
                      bottom: '10%'
                    }}
                  />
                ))}
              </>
            )}
          </AnimatePresence>

          <div
            className="relative z-10 h-full flex flex-col justify-between"
            style={{ transformStyle: 'preserve-3d' }}>
            {/* Icon with 3D float effect */}
            <motion.div
              className="flex items-start justify-between"
              whileHover={{ rotateZ: 5 }}
              transition={{ type: 'spring', stiffness: 300 }}>
              <motion.div
                className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg"
                whileHover={{
                  scale: 1.1,
                  rotateY: 180,
                  transition: { duration: 0.4 }
                }}
                style={{ transformStyle: 'preserve-3d' }}>
                <motion.div
                  animate={{ rotateY: hoveredCard === index ? 180 : 0 }}
                  transition={{ duration: 0.4 }}
                  style={{ transformStyle: 'preserve-3d' }}>
                  {feature.icon}
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Content with 3D depth */}
            <motion.div style={{ transform: 'translateZ(20px)' }}>
              <h3 className="text-lg font-bold text-gray-800 mb-1">{feature.title}</h3>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </motion.div>

            {/* CTA with depth effect */}
            <motion.div
              className={`flex items-center justify-between mt-3 ${
                hoveredCard === index ? 'text-blue-600' : 'text-gray-400'
              }`}
              style={{ transform: 'translateZ(10px)' }}>
              <span className="text-xs font-medium">Start now</span>
              <motion.div
                animate={{ x: hoveredCard === index ? 8 : 0 }}
                transition={{ type: 'spring', stiffness: 500 }}>
                <ChevronRight size={16} />
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    );
  };

  if (currentTest === 'fullLength') {
    return <FullLengthMockTest onBack={handleBack} />;
  }

  if (currentTest === 'subjectWise') {
    return <SubjectWiseMockTest onBack={handleBack} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4 md:p-6 relative overflow-hidden">
      {/* 3D Animated background elements */}
      <motion.div
        className="absolute top-10% right-10% w-64 h-64 bg-blue-300/20 rounded-full blur-xl"
        animate={{
          y: [0, 20, 0],
          scale: [1, 1.1, 1]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      />
      <motion.div
        className="absolute bottom-10% left-10% w-72 h-72 bg-indigo-300/20 rounded-full blur-xl"
        animate={{
          y: [0, -15, 0],
          scale: [1, 1.05, 1]
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 1
        }}
      />

      <div className="max-w-5xl mx-auto relative z-10">
        {/* 3D Animated Header */}
        <motion.header
          className="text-center mb-12 mt-6"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, type: 'spring', stiffness: 100 }}>
      

          <motion.h1
            className="text-4xl md:text-5xl font-bold text-gray-800 mb-3"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}>
            Mock Test{' '}
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Simulation
            </span>
          </motion.h1>

          <motion.p
            className="text-gray-600 max-w-xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}>
            Practice with real  questions and improve your performance with our  test
            simulation
          </motion.p>
        </motion.header>

        {/* 3D Tab selector */}
        <motion.div
          className="flex justify-center mb-12"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}>
          <div
            className="bg-white/90 backdrop-blur-md rounded-2xl p-2 flex border border-gray-200 shadow-lg"
            style={{ transformStyle: 'preserve-3d' }}>
            {['fullLength', 'subjectWise'].map((tab, index) => (
              <motion.button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-3 rounded-xl transition-all duration-300 flex items-center text-sm relative overflow-hidden ${
                  activeTab === tab ? 'text-white' : 'text-gray-600 hover:text-blue-600'
                }`}
                whileHover={{ y: -3 }}
                whileTap={{ y: 0 }}
                style={{ transformStyle: 'preserve-3d' }}>
                {activeTab === tab && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl"
                    layoutId="activeTabBackground"
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    style={{ borderRadius: 12 }}
                  />
                )}
                <span className="relative z-10 flex items-center">
                  {tab === 'fullLength' ? (
                    <Clock className="w-4 h-4 mr-2" />
                  ) : (
                    <BookOpen className="w-4 h-4 mr-2" />
                  )}
                  {tab === 'fullLength' ? 'Full Test' : 'By Subject'}
                </span>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* 3D Content area */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white/90 backdrop-blur-md rounded-3xl border border-gray-200 p-8 md:p-10 mb-10 shadow-2xl"
          style={{
            transformStyle: 'preserve-3d',
            perspective: 1000
          }}>
          <motion.div
            className="text-center mb-10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {activeTab === 'fullLength' ? 'Full Length Test' : 'Subject Wise Test'}
            </h2>
            <p className="text-gray-600">
              {activeTab === 'fullLength'
                ? 'Simulate the real NEET exam with a complete 3-hour test.'
                : 'Practice specific subjects and topics.'}
            </p>
          </motion.div>

          {/* 3D Feature cards grid */}
          <motion.div
            className="grid md:grid-cols-3 gap-6 mb-10"
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.15
                }
              }
            }}>
            {(activeTab === 'fullLength'
              ? [
                  {
                    icon: <Clock className="w-6 h-6 text-white" />,
                    title: '3 Hours',
                    description: 'Real exam duration simulation'
                  },
                  {
                    icon: <Brain className="w-6 h-6 text-white" />,
                    title: '180 Questions',
                    description: 'Physics, Chemistry, Biology'
                  },
                  {
                    icon: <BarChart3 className="w-6 h-6 text-white" />,
                    title: 'Detailed Analysis',
                    description: 'Performance insights'
                  }
                ]
              : [
                  {
                    icon: <BookOpen className="w-6 h-6 text-white" />,
                    title: 'Per Subject',
                    description: 'Choose your subject'
                  },
                  {
                    icon: <Clock className="w-6 h-6 text-white" />,
                    title: 'Timed Sessions',
                    description: '25-45 minute quizzes'
                  },
                  {
                    icon: <Award className="w-6 h-6 text-white" />,
                    title: 'Progress Tracking',
                    description: 'Track weak areas'
                  }
                ]
            ).map((feature, index) => (
              <ThreeDCard
                key={index}
                feature={feature}
                index={index}
                onClick={() => handleStartTest(activeTab)}
              />
            ))}
          </motion.div>

          {/* 3D Start test button */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.8 }}>
            <motion.button
              onClick={() => handleStartTest(activeTab)}
              className="relative bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-10 py-4 rounded-2xl text-lg font-semibold shadow-2xl overflow-hidden group"
              whileHover={{
                y: -5,
                scale: 1.05,
                transition: { type: 'spring', stiffness: 400 }
              }}
              whileTap={{ scale: 0.98 }}
              style={{ transformStyle: 'preserve-3d' }}>
              {/* 3D shine effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12"
                initial={{ x: '-100%' }}
                whileHover={{ x: '200%' }}
                transition={{ duration: 0.8 }}
              />

              <span className="relative z-10 flex items-center justify-center gap-2">
                Start {activeTab === 'fullLength' ? 'Full Test' : 'Subject Test'}
                <motion.div
                  animate={{ rotateZ: hoveredCard !== null ? 90 : 0 }}
                  transition={{ duration: 0.3 }}>
                  <Play className="w-5 h-5" />
                </motion.div>
              </span>
            </motion.button>

            <motion.p
              className="text-gray-500 text-sm mt-4 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1 }}>
              <Clock className="w-4 h-4 mr-1" />
              Ensure you have enough time to complete
            </motion.p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default MockTestSimulation;
