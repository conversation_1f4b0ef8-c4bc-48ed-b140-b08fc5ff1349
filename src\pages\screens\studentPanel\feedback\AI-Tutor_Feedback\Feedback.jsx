import React, { useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageSquare, User, BookOpen, Target, CheckCircle, X, AlertCircle } from 'lucide-react';

// Memoize InputField to prevent unnecessary re-renders
const InputField = React.memo(({ name, value, onChange, label, placeholder, type = "text", rows = null }) => {
  const Component = rows ? 'textarea' : 'input';
  return (
    <div className="mb-6">
      <label className="block text-sm font-semibold text-gray-700 mb-2">{label}</label>
      <Component
        type={rows ? undefined : type}
        name={name}
        value={value || ''} // Ensure value is never undefined
        onChange={onChange}
        rows={rows}
        placeholder={placeholder}
        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white resize-none"
        required
      />
    </div>
  );
});

// Radio button component
const RadioField = React.memo(({ name, value, onChange, label, options }) => {
  return (
    <div className="mb-6">
      <label className="block text-sm font-semibold text-gray-700 mb-3">{label}</label>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {options.map((option) => (
          <motion.label
            key={option.value}
            whileHover={{ scale: 1.02 }}
            className={`flex items-center justify-center p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
              value === option.value
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-300 hover:border-blue-300 hover:bg-gray-50'
            }`}
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={onChange}
              className="sr-only"
            />
            <span className="font-medium text-center">{option.label}</span>
          </motion.label>
        ))}
      </div>
    </div>
  );
});

const Feedback = ({ processId, onClose }) => {
  const [lessonData, setLessonData] = useState({
    course_name: '',
    sub_topic_name: '',
    topic_name: '',
    subject_name: ''
  });

  const [feedback, setFeedback] = useState({
    overall_rating: '',
    teacher_clarity: '',
    teacher_engagement: '',
    lesson_pace: '',
    lesson_difficulty: '',
    material_quality: '',
    would_recommend: '',
    teacher_preparation: '',
    lesson_objectives_met: '',
    interaction_level: '',
    liked_most: '',
    suggestions: '',
    tutor_name: ''
  });

  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define options for different types of questions
  const ratingOptions = [
    { value: '1-3', label: 'Poor' },
    { value: '4-6', label: 'Good' },
    { value: '7-8', label: 'Very Good' },
    { value: '9-10', label: 'Excellent' }
  ];

  const qualityOptions = [
    { value: 'poor', label: 'Poor' },
    { value: 'fair', label: 'Fair' },
    { value: 'good', label: 'Good' },
    { value: 'excellent', label: 'Excellent' }
  ];

  const clarityOptions = [
    { value: 'unclear', label: 'Unclear' },
    { value: 'somewhat_clear', label: 'Somewhat Clear' },
    { value: 'clear', label: 'Clear' },
    { value: 'very_clear', label: 'Very Clear' }
  ];

  const engagementOptions = [
    { value: 'low', label: 'Low' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'high', label: 'High' },
    { value: 'excellent', label: 'Excellent' }
  ];

  const paceOptions = [
    { value: 'too_slow', label: 'Too Slow' },
    { value: 'just_right', label: 'Just Right' },
    { value: 'slightly_fast', label: 'Slightly Fast' },
    { value: 'too_fast', label: 'Too Fast' }
  ];

  const difficultyOptions = [
    { value: 'too_easy', label: 'Too Easy' },
    { value: 'appropriate', label: 'Appropriate' },
    { value: 'challenging', label: 'Challenging' },
    { value: 'too_difficult', label: 'Too Difficult' }
  ];

  const yesNoOptions = [
    { value: 'no', label: 'No' },
    { value: 'partially', label: 'Partially' },
    { value: 'mostly', label: 'Mostly' },
    { value: 'yes', label: 'Yes' }
  ];

  const recommendOptions = [
    { value: 'no', label: 'No' },
    { value: 'maybe', label: 'Maybe' },
    { value: 'likely', label: 'Likely' },
    { value: 'definitely', label: 'Definitely' }
  ];

  const responsivenessOptions = [
    { value: 'slow', label: 'Slow' },
    { value: 'adequate', label: 'Adequate' },
    { value: 'good', label: 'Good' },
    { value: 'excellent', label: 'Excellent' }
  ];

  useEffect(() => {
    if (processId) {
      // Simulate API call
      setTimeout(() => {
        setLessonData({
          course_name: 'Mathematics Course',
          subject_name: 'Algebra',
          topic_name: 'Linear Equations',
          sub_topic_name: 'Solving Linear Equations'
        });
        setLoading(false);
      }, 1000);
    }
  }, [processId]);

  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    console.log(`Updating ${name} with value:`, value); // Debug log
    setFeedback((prev) => ({ 
      ...prev, 
      [name]: value || '' // Ensure value is never undefined
    }));
  }, []);

  const handleSubmit = async () => {
    setIsSubmitting(true);

    const student_id = sessionStorage.getItem('userId'); // Simulate student ID
    console.log('Student ID:', student_id);
    console.log('Current feedback state:', feedback); // Debug log

    // Clean the feedback object to ensure no undefined values
    const cleanedFeedback = Object.keys(feedback).reduce((acc, key) => {
      acc[key] = feedback[key] || '';
      return acc;
    }, {});

    const payload = {
      student_id,
      process_selector_id: processId,
      ...lessonData,
      ...cleanedFeedback
    };

    console.log('Final payload being sent:', payload); // Debug log

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Response: Success');
      setStatus('success');
      setFeedback({
        overall_rating: '',
        teacher_clarity: '',
        teacher_engagement: '',
        lesson_pace: '',
        lesson_difficulty: '',
        material_quality: '',
        would_recommend: '',
        teacher_preparation: '',
        lesson_objectives_met: '',
        interaction_level: '',
        liked_most: '',
        suggestions: '',
        tutor_name: ''
      });
    } catch (err) {
      console.error('Submission error:', err);
      setStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.4 }
    }
  };

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="fixed inset-0 bg-gray-100 bg-opacity-95 flex items-center justify-center p-4 z-50"
      >
        <div className="bg-white rounded-2xl p-8 flex items-center space-x-4">
          <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
          <p className="text-gray-700 font-medium">Give Your Valuable Feedback...</p>
        </div>
      </motion.div>
    );
  }

  if (status === 'success') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="fixed inset-0 bg-gray-100 bg-opacity-95 flex items-center justify-center p-4 z-50"
      >
        <motion.div
          initial={{ y: 50 }}
          animate={{ y: 0 }}
          className="bg-white rounded-2xl p-8 max-w-md w-full text-center shadow-2xl"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring" }}
          >
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          </motion.div>
          <h3 className="text-2xl font-bold text-gray-800 mb-2">Thank You!</h3>
          <p className="text-gray-600 mb-6">Your feedback has been submitted successfully.</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onClose}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Close
          </motion.button>
        </motion.div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="fixed inset-0 bg-gray-100 bg-opacity-95 flex items-center justify-center p-4 z-50"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 p-6 rounded-t-2xl text-white relative">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
          >
            <X className="w-6 h-6" />
          </motion.button>
          <div className="flex items-center space-x-3">
            <MessageSquare className="w-8 h-8" />
            <h2 className="text-2xl font-bold">Lesson Feedback</h2>
          </div>
          <p className="text-blue-100 mt-2">Help us improve your tutoring experience</p>
        </div>

        <div className="p-8">
          {/* Lesson Information */}
          <motion.div variants={itemVariants} className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <BookOpen className="w-5 h-5 mr-2 text-blue-600" />
              Lesson Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { label: 'Course', value: lessonData.course_name },
                { label: 'Subject', value: lessonData.subject_name },
                { label: 'Topic', value: lessonData.topic_name },
                { label: 'Sub-topic', value: lessonData.sub_topic_name }
              ].map((field, index) => (
                <motion.div
                  key={field.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500"
                >
                  <label className="block text-sm font-semibold text-gray-600 mb-1">{field.label}</label>
                  <p className="text-gray-800 font-medium">{field.value}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <div className="space-y-8">
            {/* Overall Experience */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <Target className="w-5 h-5 mr-2 text-purple-600" />
                Overall Experience
              </h3>
              
              <RadioField
                name="overall_rating"
                value={feedback.overall_rating}
                onChange={handleChange}
                label="How would you rate your session overall?"
                options={ratingOptions}
              />
            </motion.div>

            {/* Tutor Evaluation */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <User className="w-5 h-5 mr-2 text-green-600" />
                Tutor Evaluation
              </h3>
              
              <div className="space-y-6">
                <InputField
                  name="tutor_name"
                  value={feedback.tutor_name}
                  onChange={handleChange}
                  label="What is the name of your tutor?"
                  placeholder="e.g., John Smith - Enter the name of your tutor"
                  type="text"
                />
                
                <RadioField
                  name="teacher_clarity"
                  value={feedback.teacher_clarity}
                  onChange={handleChange}
                  label="How clear and understandable were the explanations?"
                  options={clarityOptions}
                />
                
                <RadioField
                  name="teacher_engagement"
                  value={feedback.teacher_engagement}
                  onChange={handleChange}
                  label="How engaging and interactive was the session?"
                  options={engagementOptions}
                />
                
                <RadioField
                  name="teacher_preparation"
                  value={feedback.teacher_preparation}
                  onChange={handleChange}
                  label="How well did the tutor adapt to your learning needs and pace?"
                  options={qualityOptions}
                />
                
                <RadioField
                  name="interaction_level"
                  value={feedback.interaction_level}
                  onChange={handleChange}
                  label="How responsive was the tutor to your questions and feedback?"
                  options={responsivenessOptions}
                />
              </div>
            </motion.div>

            {/* Session Content */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <BookOpen className="w-5 h-5 mr-2 text-blue-600" />
                Session Content
              </h3>
              
              <div className="space-y-6">
                <RadioField
                  name="lesson_pace"
                  value={feedback.lesson_pace}
                  onChange={handleChange}
                  label="How was the pace of the session?"
                  options={paceOptions}
                />
                
                <RadioField
                  name="lesson_difficulty"
                  value={feedback.lesson_difficulty}
                  onChange={handleChange}
                  label="How appropriate was the difficulty level set by the tutor?"
                  options={difficultyOptions}
                />
                
                <RadioField
                  name="material_quality"
                  value={feedback.material_quality}
                  onChange={handleChange}
                  label="How would you rate the quality of content and examples provided?"
                  options={qualityOptions}
                />
                
                <RadioField
                  name="lesson_objectives_met"
                  value={feedback.lesson_objectives_met}
                  onChange={handleChange}
                  label="Do you feel the tutor helped you achieve your learning objectives?"
                  options={yesNoOptions}
                />
              </div>
            </motion.div>

            {/* Additional Feedback */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                <MessageSquare className="w-5 h-5 mr-2 text-purple-600" />
                Additional Feedback
              </h3>
              
              <div className="space-y-6">
                <RadioField
                  name="would_recommend"
                  value={feedback.would_recommend}
                  onChange={handleChange}
                  label="Would you recommend this session to other students?"
                  options={recommendOptions}
                />
                
                <InputField
                  name="liked_most"
                  value={feedback.liked_most}
                  onChange={handleChange}
                  label="What did you like most about the session?"
                  placeholder="e.g., I loved how the tutor could instantly provide multiple explanations until I understood the concept"
                  rows={3}
                />
                
                <InputField
                  name="suggestions"
                  value={feedback.suggestions}
                  onChange={handleChange}
                  label="Any suggestions to improve the session?"
                  placeholder="e.g., Maybe add more visual diagrams or include practice quizzes after each topic"
                  rows={3}
                />
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div 
              variants={itemVariants}
              className="flex justify-between pt-6 border-t border-gray-200"
            >
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
              >
                Cancel
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="button"
                onClick={handleSubmit}
                disabled={isSubmitting || !feedback.overall_rating}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4" />
                    <span>Submit Feedback</span>
                  </>
                )}
              </motion.button>
            </motion.div>
          </div>

          {status === 'error' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2"
            >
              <AlertCircle className="w-5 h-5 text-red-500" />
              <p className="text-red-700 font-medium">Failed to submit feedback. Please try again.</p>
            </motion.div>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default Feedback;