import React, { useEffect, useState, useMemo } from 'react';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  setStudentsAnalyticalData,
  useLazyGetStudentAnalyticalDataQuery
} from './parentAnalyticsDashboard.slice';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { motion } from 'framer-motion';
import {
  BookOpen,
  TrendingUp,
  Award,
  Clock,
  Hash,
  CircleCheck,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

const getSubjectColor = (subject) => {
  switch ((subject || '').toLowerCase()) {
    case 'physics':
      return { bg: 'bg-sky-500/10', text: 'text-sky-400', bar: 'bg-sky-500' };
    case 'maths':
      return { bg: 'bg-indigo-500/10', text: 'text-indigo-400', bar: 'bg-indigo-500' };
    case 'biology':
      return { bg: 'bg-teal-500/10', text: 'text-teal-400', bar: 'bg-teal-500' };
    case 'chemistry':
      return { bg: 'bg-emerald-500/10', text: 'text-emerald-400', bar: 'bg-emerald-500' };
    default:
      return { bg: 'bg-slate-500/10', text: 'text-slate-400', bar: 'bg-slate-500' };
  }
};

const ParentAnalyticsDashboard = () => {
  const [getStudentAnalyticsData, { isLoading }] = useLazyGetStudentAnalyticalDataQuery();
  const [res, setRes] = useState(null);
  const studentAnalyticsData = useSelector(
    (state) => state.parentAnalyticsDashboard.studentsAnalyticalData
  );
  const dispatch = useDispatch();

  useEffect(() => {
    handleGetStudentAnalyticsData();
  }, []);

  const handleGetStudentAnalyticsData = async () => {
    try {
      const res = await getStudentAnalyticsData({
        studentId: sessionStorage.studentIdByParent
      }).unwrap();
      dispatch(setStudentsAnalyticalData(res?.data || {}));
    } catch (error) {
      setRes(error);
    }
  };

  const subjectStats = useMemo(() => {
    const subjects = ['physics', 'chemistry', 'maths', 'biology'];
    return subjects
      .map((subject) => {
        const tests = studentAnalyticsData?.[subject] || [];
        const totalQuestions = tests.reduce(
          (sum, test) => sum + (test.evaluation_results?.total_questions || 0),
          0
        );
        const totalCorrect = tests.reduce(
          (sum, test) => sum + (test.evaluation_results?.num_correct || 0),
          0
        );
        const accuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
        return {
          subject: subject.charAt(0).toUpperCase() + subject.slice(1),
          tests: tests.length,
          accuracy,
          totalQuestions,
          totalCorrect
        };
      })
      .filter((stat) => stat.tests > 0);
  }, [studentAnalyticsData]);

  const recentActivity = useMemo(() => {
    const allTests = [];
    Object.keys(studentAnalyticsData || {}).forEach((subject) => {
      (studentAnalyticsData?.[subject] || []).forEach((test) => {
        allTests.push({ ...test, subject: subject.charAt(0).toUpperCase() + subject.slice(1) });
      });
    });
    return allTests
      .sort(
        (a, b) =>
          new Date(b.evaluation_time?.split(' & ')[0]) -
          new Date(a.evaluation_time?.split(' & ')[0])
      )
      .slice(0, 5);
  }, [studentAnalyticsData]);

  const totalTests = subjectStats.reduce((sum, stat) => sum + stat.tests, 0);
  const avgAccuracy =
    subjectStats.length > 0
      ? Math.round(subjectStats.reduce((sum, stat) => sum + stat.accuracy, 0) / subjectStats.length)
      : 0;
  const bestSubject = useMemo(
    () =>
      subjectStats.length > 0 ? [...subjectStats].sort((a, b) => b.accuracy - a.accuracy)[0] : null,
    [subjectStats]
  );

  if (isLoading) {
    return <LoadingState />;
  }

  if (totalTests === 0) {
    return <EmptyState onRefresh={handleGetStudentAnalyticsData} />;
  }

  return (
    <div className="relative min-h-screen text-slate-100 p-4 md:p-8 isolate">
      <div
        className="absolute inset-x-0 -z-10 transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true">
        <div
          className="relative left-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#6366f1] to-[#06b6d4] opacity-20 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
          }}
        />
      </div>
      <Toastify res={res} resClear={() => setRes(null)} />

      <main className="max-w-7xl mx-auto space-y-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          <div>
            <h1 className="text-3xl md:text-4xl font-extrabold tracking-tight text-white text-balance">
              Student CBT Analytics
            </h1>
            <p className="mt-1 text-pretty text-sm text-slate-400">
              Track academic progress and performance at a glance.
            </p>
          </div>
          <motion.button
            type="button"
            onClick={handleGetStudentAnalyticsData}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center justify-center gap-2 rounded-lg bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-lg shadow-indigo-600/20 hover:bg-indigo-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900"
            aria-label="Refresh analytics"
            title="Refresh analytics">
            <RefreshCw size={16} /> Refresh
          </motion.button>
        </motion.div>

        <motion.div
          variants={{ show: { transition: { staggerChildren: 0.08 } } }}
          initial="hidden"
          animate="show"
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Tests Taken"
            value={totalTests}
            icon={<BookOpen />}
            color="indigo"
          />
          <StatCard
            title="Average Accuracy"
            value={`${avgAccuracy}%`}
            icon={<TrendingUp />}
            color="sky"
          />
          <StatCard
            title="Best Subject"
            value={bestSubject?.subject || 'N/A'}
            subtitle={`${bestSubject?.accuracy || 0}% accuracy`}
            icon={<Award />}
            color="emerald"
          />
          <StatCard
            title="Recent Tests"
            value={recentActivity.length}
            subtitle="Latest 5 activities"
            icon={<Clock />}
            color="amber"
          />
        </motion.div>

        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-white">Subject Breakdown</h2>
          <motion.div
            variants={{ show: { transition: { staggerChildren: 0.1 } } }}
            initial="hidden"
            animate="show"
            className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {subjectStats.map((stat) => (
              <SubjectCard key={stat.subject} stat={stat} />
            ))}
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="rounded-2xl border border-slate-700 bg-slate-800/50 shadow-lg overflow-hidden">
          <div className="p-6">
            <h3 className="text-xl font-semibold text-white">Recent Test Activity</h3>
            <p className="text-sm text-slate-400 mt-1">A log of the latest test performances.</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead className="bg-slate-900/50">
                <tr>
                  <th className="py-3 px-6 text-left font-semibold text-slate-300 tracking-wider">
                    Date
                  </th>
                  <th className="py-3 px-6 text-left font-semibold text-slate-300 tracking-wider">
                    Subject
                  </th>
                  <th className="py-3 px-6 text-left font-semibold text-slate-300 tracking-wider">
                    Unit & Topic
                  </th>
                  <th className="py-3 px-6 text-left font-semibold text-slate-300 tracking-wider">
                    Score
                  </th>
                  <th className="py-3 px-6 text-left font-semibold text-slate-300 tracking-wider">
                    Accuracy
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-700">
                {recentActivity.map((activity, index) => (
                  <ActivityRow key={index} activity={activity} delay={0.6 + index * 0.05} />
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>
      </main>
    </div>
  );
};

const itemVariant = {
  hidden: { opacity: 0, scale: 0.95, y: 20 },
  show: { opacity: 1, scale: 1, y: 0, transition: { type: 'spring', stiffness: 100, damping: 15 } }
};

const StatCard = ({ title, value, subtitle, icon, color }) => {
  const palettes = {
    indigo: { icon: 'text-indigo-400', bg: 'bg-indigo-500/10' },
    sky: { icon: 'text-sky-400', bg: 'bg-sky-500/10' },
    emerald: { icon: 'text-emerald-400', bg: 'bg-emerald-500/10' },
    amber: { icon: 'text-amber-400', bg: 'bg-amber-500/10' }
  };
  const c = palettes[color] || palettes.indigo;

  return (
    <motion.div
      variants={itemVariant}
      className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-md shadow-lg">
      <div className="flex items-center justify-between">
        <p className="text-sm font-medium text-slate-400">{title}</p>
        <div className={`rounded-full p-2.5 ${c.bg}`}>
          {React.cloneElement(icon, { className: `h-5 w-5 ${c.icon}` })}
        </div>
      </div>
      <p className="mt-2 text-3xl font-bold text-white">{value}</p>
      {subtitle && <p className="mt-1 text-xs text-slate-400">{subtitle}</p>}
    </motion.div>
  );
};

const SubjectCard = ({ stat }) => {
  const { bg, text, bar } = getSubjectColor(stat.subject);
  return (
    <motion.div
      variants={itemVariant}
      className="rounded-2xl border border-slate-700 bg-slate-800/50 p-6 shadow-lg">
      <div className="mb-4 flex items-center justify-between">
        <h3 className={`text-lg font-bold ${text}`}>{stat.subject}</h3>
        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${bg} ${text}`}>
          {stat.tests} tests
        </span>
      </div>
      <div className="space-y-5">
        <div>
          <div className="mb-1 flex justify-between text-sm">
            <span className="text-slate-300">Accuracy</span>
            <span className={`font-medium ${text}`}>{stat.accuracy}%</span>
          </div>
          <div className="h-2 w-full rounded-full bg-slate-700">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${stat.accuracy}%` }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className={`h-2 rounded-full ${bar}`}
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 pt-2 border-t border-slate-700">
          <Detail
            label="Total Questions"
            value={stat.totalQuestions}
            icon={<Hash className="text-slate-400" size={16} />}
          />
          <Detail
            label="Correct Answers"
            value={stat.totalCorrect}
            icon={<CircleCheck className="text-emerald-400" size={16} />}
          />
        </div>
      </div>
    </motion.div>
  );
};

const ActivityRow = ({ activity, delay }) => {
  const accuracy = Math.round(
    ((activity.evaluation_results?.num_correct || 0) /
      (activity.evaluation_results?.total_questions || 1)) *
      100
  );
  const { bg, text } = getSubjectColor(activity.subject);
  const accuracyColor =
    accuracy >= 80 ? 'text-emerald-400' : accuracy >= 50 ? 'text-amber-400' : 'text-rose-400';

  return (
    <motion.tr
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.4, delay }}
      className="hover:bg-slate-700/50 transition-colors">
      <td className="py-4 px-6 text-slate-400 whitespace-nowrap">
        {activity.evaluation_time?.split(' & ')[0]}
      </td>
      <td className="py-4 px-6">
        <span
          className={`inline-flex items-center rounded-md px-2.5 py-1 text-xs font-semibold ${bg} ${text}`}>
          {activity.subject}
        </span>
      </td>
      <td className="py-4 px-6">
        <div className="font-medium text-slate-200">{activity.unit_name}</div>
        <div className="text-xs text-slate-500 max-w-xs truncate">{activity.sub_topics}</div>
      </td>
      <td className="py-4 px-6 font-medium text-slate-300">
        {activity.evaluation_results?.score || 0}/
        {activity.evaluation_results?.total_questions || 0}
      </td>
      <td className={`py-4 px-6 font-bold ${accuracyColor}`}>{accuracy}%</td>
    </motion.tr>
  );
};

const Detail = ({ label, value, icon }) => (
  <div className="flex items-start space-x-2">
    <div className="mt-0.5 flex-shrink-0">{icon}</div>
    <div>
      <p className="text-sm text-slate-400">{label}</p>
      <p className="font-semibold text-slate-200">{value}</p>
    </div>
  </div>
);

const LoadingState = () => (
  <div className="min-h-screen p-8">
    <div className="mx-auto max-w-7xl animate-pulse space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <div className="h-10 w-72 rounded-lg bg-slate-700" />
          <div className="h-4 w-96 rounded mt-2 bg-slate-800" />
        </div>
        <div className="h-10 w-28 rounded-lg bg-slate-700" />
      </div>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="h-32 rounded-2xl bg-slate-800" />
        <div className="h-32 rounded-2xl bg-slate-800" />
        <div className="h-32 rounded-2xl bg-slate-800" />
        <div className="h-32 rounded-2xl bg-slate-800" />
      </div>
      <div className="h-8 w-64 rounded bg-slate-700" />
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="h-52 rounded-2xl bg-slate-800" />
        <div className="h-52 rounded-2xl bg-slate-800" />
      </div>
      <div className="h-72 rounded-2xl bg-slate-800" />
    </div>
  </div>
);

const EmptyState = ({ onRefresh }) => (
  <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 to-gray-900 text-center p-6">
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'spring' }}
      className="rounded-2xl border-2 border-dashed border-slate-700 bg-white/5 p-12">
      <AlertTriangle className="mb-4 h-16 w-16 text-amber-500 mx-auto" />
      <h2 className="mb-2 text-2xl font-bold text-white">No Analytics Data Yet</h2>
      <p className="max-w-md text-slate-400">
        It looks like no tests have been completed. Once tests are taken, this dashboard will be
        populated with performance analytics.
      </p>
      <motion.button
        type="button"
        onClick={onRefresh}
        whileTap={{ scale: 0.95 }}
        className="mt-6 inline-flex items-center gap-2 justify-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-lg shadow-indigo-600/20 hover:bg-indigo-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900">
        <RefreshCw size={16} /> Check for Data
      </motion.button>
    </motion.div>
  </div>
);

export default ParentAnalyticsDashboard;
