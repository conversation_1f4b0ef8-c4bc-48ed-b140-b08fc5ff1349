    // RecordedVideos.jsx
    import React, { useState, useMemo, useEffect } from 'react';
    import { motion, AnimatePresence } from 'framer-motion';
    import {
      Play,
      Clock,
      BookOpen,
      ChevronRight,
      Atom,
      FlaskConical,
      Leaf,
      BrainCircuit,
      GraduationCap,
      X,
      Maximize2,
      Users
    } from 'lucide-react';
    import { useLazyGetClassEventDetailsQuery } from '../liveStreaming/studentLiveStreaming.slice';
    import { useGetStudentsQuery } from '../studentsDashboard/students.Slice';

    // 🔧 Fallbacks
    const FALLBACK_VIDEO_URL = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
    const FALLBACK_THUMBNAIL =
      'https://img.freepik.com/free-vector/video-player-with-film-reel_23-2148557445.jpg';

    const RecordedVideos = () => {
      const [hoveredVideo, setHoveredVideo] = useState(null);
      const [selectedVideo, setSelectedVideo] = useState(null);
      const [isFullscreen, setIsFullscreen] = useState(false);

      // ✅ Get userId from sessionStorage
      const userId = sessionStorage.getItem('userId');

      // 🔄 Fetch student data using RTK Query
      const {
        data: studentsData,
        isLoading: isStudentLoading,
        isError: isStudentError
      } = useGetStudentsQuery(undefined, {
        skip: !userId
      });

      // 📝 Extract student details
      const user = studentsData?.student || {
        course: sessionStorage.getItem('course') || 'JEE',
        batch_id: sessionStorage.getItem('batchId'),
        batch_name: sessionStorage.getItem('batchName'),
        center_code: sessionStorage.getItem('centercode')
      };

      // Store student details in session storage when available
      useEffect(() => {
        if (studentsData?.student) {
          sessionStorage.setItem('course', studentsData.student.course);
          sessionStorage.setItem('batchId', studentsData.student.batch_id);
          sessionStorage.setItem('batchName', studentsData.student.batch_name);
          sessionStorage.setItem('centercode', studentsData.student.center_code);
        }
      }, [studentsData]);

      // Default to JEE if not NEET
      const selectedExam = user.course === 'NEET' ? 'NEET' : 'JEE';

      // RTK Query for video data
      const [trigger, { data: apiResponse, isLoading: isVideoLoading, isError, error }] =
        useLazyGetClassEventDetailsQuery();

      // Fetch video data when student details are available
      useEffect(() => {
        if (!userId || isStudentLoading) return;

        trigger({ userId })
          .unwrap()
          .catch((err) => {
            console.error('Failed to load videos:', err);
          });
      }, [userId, trigger, isStudentLoading]);

      // Capitalize subject (e.g., "physics" → "Physics")
      const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

      // Transform video URL to embeddable format
      const getEmbedUrl = (url) => {
        if (!url) return '';

        // Handle YouTube URLs
        if (url.includes('youtube.com') || url.includes('youtu.be')) {
          // Convert youtu.be URLs to youtube.com format
          let videoId = url.includes('youtu.be') ? url.split('youtu.be/')[1] : url.split('v=')[1];

          // Remove any additional parameters
          if (videoId?.includes('&')) {
            videoId = videoId.split('&')[0];
          }

          return `https://www.youtube.com/embed/${videoId}`;
        }

        // Handle Vimeo URLs
        if (url.includes('vimeo.com')) {
          const vimeoId = url.split('vimeo.com/')[1];
          return `https://player.vimeo.com/video/${vimeoId}`;
        }

        // For other video platforms or direct video URLs
        return url;
      };

      // Map API response to video list
      const recordedVideos = useMemo(() => {
        if (!apiResponse?.events) return [];

        return apiResponse.events
          .filter((event) => {
            const matchesCourse = event.course_name?.toLowerCase() === user.course?.toLowerCase();
            const matchesBatchId = event.batch_id === user.batch_id;
            const hasValidUrl =
              event.recording_url &&
              event.recording_url.trim() !== '' &&
              event.recording_url !== 'null';

            return matchesCourse && matchesBatchId && hasValidUrl;
          })
          .map((event) => ({
            id: event.id,
            title: event.topic || `Class on ${capitalize(event.subject)}`,
            subject: capitalize(event.subject),
            duration: '1:20:00',
            thumbnail: getSubjectThumbnail(capitalize(event.subject)),
            link: getEmbedUrl(event.recording_url.trim()),
            date: event.event_date
          }));
      }, [apiResponse, user.course, user.batch_id]);

      // Add console logs for debugging
      useEffect(() => {
        console.log('Student Data:', {
          course: user.course,
          batch_id: user.batch_id,
          center_code: user.center_code
        });
        console.log('API Response:', apiResponse?.events);
        console.log('Filtered Videos:', recordedVideos);
      }, [user, apiResponse, recordedVideos]);

      // Handle fullscreen
      const toggleFullscreen = () => {
        setIsFullscreen(!isFullscreen);
      };

      // Handle video selection
      const handleVideoClick = (video) => {
        setSelectedVideo(video);
      };

      // Handle close video
      const handleCloseVideo = () => {
        setSelectedVideo(null);
        setIsFullscreen(false);
      };

      // Subject thumbnails
      function getSubjectThumbnail(subject) {
        const thumbnails = {
          Physics:
            'https://t4.ftcdn.net/jpg/04/38/69/77/360_F_438697752_bIJMNtw4TvQGq9T3k7JnhmOei91WHuGj.jpg',
          Chemistry:
            'https://img.freepik.com/free-vector/chalkboard-background-with-chemistry-information_23-2148159091.jpg',
          Mathematics:
            'https://img.freepik.com/free-vector/isometric-maths-material-background_23-2146146102.jpg',
          Biology:
            'https://t3.ftcdn.net/jpg/00/85/94/80/360_F_85948050_pnCz9BxRzGqFbp5e2mR7RmaJG8e2kKFP.jpg'
        };
        return thumbnails[subject] || FALLBACK_THUMBNAIL;
      }

      // Subjects in exam-specific order
      const subjectsInCourse = useMemo(() => {
        const uniqueSubjects = [...new Set(recordedVideos.map((v) => v.subject))];

        // Define complete subject lists for each course
        const orderNEET = ['Physics', 'Chemistry', 'Biology'];
        const orderJEE = ['Physics', 'Chemistry', 'Mathematics'];

        // Select order based on course
        const courseOrder = selectedExam === 'NEET' ? orderNEET : orderJEE;

        // Filter subjects that exist in the videos
        return courseOrder.filter((subject) => uniqueSubjects.includes(subject));
      }, [recordedVideos, selectedExam]);

      const [selectedSubject, setSelectedSubject] = useState(subjectsInCourse[0] || 'Physics');

      // Sync selectedSubject when subjects change
      useEffect(() => {
        if (subjectsInCourse.length > 0 && !subjectsInCourse.includes(selectedSubject)) {
          setSelectedSubject(subjectsInCourse[0]);
        }
      }, [subjectsInCourse, selectedSubject]);

      // Filter videos by subject
      const filteredVideos = useMemo(() => {
        return selectedSubject
          ? recordedVideos.filter((v) => v.subject === selectedSubject)
          : recordedVideos;
      }, [recordedVideos, selectedSubject]);

      // Icons for subjects
      const subjectIcons = {
        Physics: <Atom className="text-student" />,
        Chemistry: <FlaskConical className="text-student" />,
        Mathematics: <BrainCircuit className="text-student" />,
        Biology: <Leaf className="text-student" />
      };

      // Animations
      const container = {
        hidden: { opacity: 0 },
        show: { opacity: 1, transition: { staggerChildren: 0.1 } }
      };

      const item = {
        hidden: { opacity: 0, y: 20 },
        show: { opacity: 1, y: 0 }
      };

      // Loading/Error States
      if (!userId) {
        return (
          <div className="p-6 text-center text-red-500">
            <p>Missing user data. Please log in again.</p>
          </div>
        );
      }

      if (isStudentError || isError) {
        return (
          <div className="p-6 text-center text-red-500">
            <p>Error: {error?.data?.message || 'Failed to load data'}</p>
          </div>
        );
      }

      if (isStudentLoading || isVideoLoading) {
        return (
          <div className="p-6 text-center">
            <p className="text-gray-600">Loading...</p>
          </div>
        );
      }

      return (
        <div className="p-4 sm:p-6 md:p-8 lg:p-10 max-w-7xl mx-auto bg-white min-h-screen">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ staggerChildren: 0.1 }}
            className="mb-6 sm:mb-8">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6">
              <div className="flex items-center gap-2 sm:gap-3">
                <div className="p-2 sm:p-3 bg-student/10 rounded-xl">
                  <GraduationCap className="text-student w-5 h-5 sm:w-6 sm:h-6" />
                </div>
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Recorded Videos</h1>
                  <p className="text-sm sm:text-base text-gray-600">
                    Access your recorded classes anytime
                  </p>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-2 mt-4 sm:mt-0">
                {/* Course Badge */}
                <div className="flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-student/5 rounded-lg border border-student/10">
                  <BookOpen className="text-student w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="text-student font-medium text-sm sm:text-base">
                    {selectedExam} Preparation
                  </span>
                </div>
                {/* Batch Badge */}
                {user.batch_name && (
                  <div className="flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-counselor/5 rounded-lg border border-counselor/10">
                    <Users className="text-counselor w-4 h-4 sm:w-5 sm:h-5" />
                    <span className="text-counselor font-medium text-sm sm:text-base">
                      Batch: {user.batch_name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Subject Filter */}
          <motion.div
            className="mb-6 sm:mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}>
            <div className="bg-[var(--color-student)] border border-gray-200 rounded-2xl p-4 sm:p-6 shadow-sm">
              <div className="grid grid-cols-1 gap-4 sm:gap-6">
                {/* Subject Selector */}
                <div>
                  <label className="block text-sm font-semibold text-white mb-2 sm:mb-3">
                    Select Subject
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {subjectsInCourse.map((subject) => {
                      const Icon = {
                        Physics: Atom,
                        Chemistry: FlaskConical,
                        Mathematics: BrainCircuit,
                        Biology: Leaf
                      }[subject];

                      return (
                        <motion.button
                          key={subject}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setSelectedSubject(subject)}
                          className={`flex items-center gap-2 py-1.5 sm:py-2 px-3 sm:px-4 rounded-lg text-xs sm:text-sm font-medium transition-all border ${
                            selectedSubject === subject
                              ? 'bg-[var(--color-counselor)] text-white border-student/20'
                              : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-student/5 hover:border-student/20'
                          }`}>
                          <Icon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                          {subject}
                        </motion.button>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Current Selection */}
              <motion.div className="mt-4 pt-4 border-t border-gray-100 flex items-center gap-2 text-xs sm:text-sm">
                <span className="text-white">Currently viewing:</span>
                <span className="font-semibold text-yellow-400">{selectedExam}</span>
                <ChevronRight className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-400" />
                <div className="flex items-center gap-1">
                  {React.cloneElement(subjectIcons[selectedSubject], {
                    className: 'w-3.5 h-3.5 sm:w-4 sm:h-4'
                  })}
                  <span className="font-semibold text-white">{selectedSubject}</span>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Video Grid */}
          <div className="space-y-6">
            {subjectsInCourse.map((subject) => {
              const subjectVideos = recordedVideos.filter((v) => v.subject === subject);

              return (
                <motion.div
                  key={subject}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-sm">
                  {/* Subject Folder Header */}
                  <div className="bg-student/5 p-4 border-b border-gray-200 flex items-center justify-between cursor-pointer hover:bg-student/10 transition-colors">
                    <div className="flex items-center gap-3">
                      {React.cloneElement(subjectIcons[subject], {
                        className: 'w-6 h-6 text-student'
                      })}
                      <h2 className="text-lg font-semibold text-gray-900">{subject} Videos</h2>
                      <span className="text-sm text-gray-500">({subjectVideos.length} videos)</span>
                    </div>
                  </div>

                  {/* Videos Grid */}
                  <div className="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {subjectVideos.map((video) => (
                      <motion.div
                        key={video.id}
                        variants={item}
                        whileHover={{ y: -4 }}
                        onHoverStart={() => setHoveredVideo(video.id)}
                        onHoverEnd={() => setHoveredVideo(null)}
                        onClick={() => handleVideoClick(video)}
                        className="bg-white rounded-xl border border-gray-200 overflow-hidden group hover:shadow-lg hover:shadow-student/10 transition-all duration-300 relative cursor-pointer">
                        <div className="relative overflow-hidden">
                          <img
                            src={video.thumbnail}
                            alt={video.title}
                            className="w-full h-40 object-cover transform group-hover:scale-105 transition-transform duration-500"
                          />
                          <AnimatePresence>
                            {hoveredVideo === video.id && (
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="absolute inset-0 flex items-center justify-center bg-black/40 backdrop-blur-sm">
                                <motion.div className="p-3 bg-white rounded-full shadow-lg">
                                  <Play className="text-student w-6 h-6" fill="currentColor" />
                                </motion.div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                          <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded-full text-xs flex items-center backdrop-blur-sm">
                            <Clock className="mr-1 w-2.5 h-2.5" />
                            {video.duration}
                          </div>
                        </div>
                        <div className="p-4">
                          <h3 className="text-base font-bold text-gray-900 mb-1 line-clamp-2 leading-tight">
                            {video.title}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {new Date(video.date).toLocaleDateString()}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </div>
          {/* Video Player Modal */}
          <AnimatePresence>
            {selectedVideo && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className={`fixed inset-0 z-50 flex items-center justify-center bg-black/90 ${isFullscreen ? 'p-0' : 'p-4 sm:p-6 md:p-8'}`}>
                <div
                  className={`relative w-full ${isFullscreen ? 'h-full' : 'max-w-6xl max-h-[80vh]'} bg-black rounded-lg overflow-hidden`}>
                  <div className="absolute top-4 right-4 z-10 flex gap-2">
                    <button
                      onClick={toggleFullscreen}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors">
                      <Maximize2 className="w-5 h-5 text-white" />
                    </button>
                    <button
                      onClick={handleCloseVideo}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors">
                      <X className="w-5 h-5 text-white" />
                    </button>
                  </div>
                  <iframe
                    src={selectedVideo.link}
                    title={selectedVideo.title}
                    className="w-full aspect-video"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowFullScreen
                    frameBorder="0"
                    loading="lazy"
                    sandbox="allow-same-origin allow-scripts allow-popups allow-presentation allow-forms"
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      );
    };

    export default RecordedVideos;
