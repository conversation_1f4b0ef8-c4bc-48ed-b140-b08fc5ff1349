import React, { useCallback, useEffect, useState, useMemo, memo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import clsx from 'clsx';
import jsPDF from 'jspdf';
import { toJpeg } from 'html-to-image';

// --- Live Data Integration ---
import { clearOmrData, setOmrData, useLazyGetStudentOmrServiceQuery } from '../dashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';

// Import Highcharts modules
import 'highcharts/highcharts-more';
import 'highcharts/modules/solid-gauge';

// Lucide icons
import {
  CheckCircle2,
  XCircle,
  HelpCircle,
  Download,
  Loader2,
  Calendar,
  Book,
  TrendingUp,
  Grid,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Zap
} from 'lucide-react';

// --- Reusable UI Components ---

const DashboardCard = memo(({ children, className = '', ...props }) => (
  <motion.div
    className={clsx(
      'bg-white/60 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20',
      className
    )}
    variants={{ hidden: { y: 20, opacity: 0 }, visible: { y: 0, opacity: 1 } }}
    {...props}>
    {children}
  </motion.div>
));

const ChartCard = memo(({ title, icon, children, className = '' }) => (
  <DashboardCard className={clsx('p-6 flex flex-col', className)}>
    <div className="flex items-center text-slate-700 mb-4">
      {icon}
      <h3 className="text-lg font-semibold ml-2">{title}</h3>
    </div>
    <div className="flex-grow">{children}</div>
  </DashboardCard>
));

const QuestionListItem = memo(({ item, qNumber }) => {
  let status = { text: 'Unattempted', color: 'text-slate-500', icon: <HelpCircle size={20} /> };
  if (item.student_answer) {
    status = item.is_correct
      ? { text: 'Correct', color: 'text-green-600', icon: <CheckCircle2 size={20} /> }
      : { text: 'Incorrect', color: 'text-red-600', icon: <XCircle size={20} /> };
  }

  return (
    <div className="flex items-center p-3 odd:bg-slate-50/50 rounded-lg transition-colors hover:bg-slate-100/70">
      <div className="w-12 text-center font-bold text-slate-700">#{qNumber}</div>
      <div className="flex-1 flex justify-center">
        <span
          className={clsx(
            'w-10 h-10 flex items-center justify-center rounded-full text-sm font-semibold',
            item.student_answer === item.correct_answer ? 'bg-green-200 text-green-600' : 'bg-red-100 text-red-600'
          )}>
          {item.student_answer || '–'}
        </span>
      </div>
      <div className="flex-1 flex justify-center">
        <span className="w-10 h-10 flex items-center justify-center rounded-full text-sm font-semibold bg-green-100 text-green-800">
          {item.correct_answer}
        </span>
      </div>
      <div className={`w-32 flex items-center justify-end font-semibold text-sm ${status.color}`}>
        {status.icon}
        <span className="ml-2">{status.text}</span>
      </div>
    </div>
  );
});

// --- NEW: Overall Analytics Component ---
const OverallAnalyticsCard = memo(({ analytics }) => {
  if (!analytics) return null;

  return (
    <DashboardCard className="p-8">
      <h3 className="text-xl font-semibold text-slate-900 mb-4">Overall Performance Snapshot</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <BarChart className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <p className="text-slate-500 text-sm font-medium">Tests Logged</p>
            <p className="text-2xl font-bold text-slate-800">{analytics.totalTests}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-indigo-100 rounded-full">
            <Percent className="h-6 w-6 text-indigo-600" />
          </div>
          <div>
            <p className="text-slate-500 text-sm font-medium">Average Score</p>
            <p className="text-2xl font-bold text-slate-800">{analytics.average.toFixed(1)}%</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-green-100 rounded-full">
            <Award className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <p className="text-slate-500 text-sm font-medium">Best Performance</p>
            <p className="text-2xl font-bold text-slate-800">{analytics.best}%</p>
          </div>
        </div>
        <div
          className="flex items-center space-x-4"
          title={`Consistency based on score standard deviation: ${analytics.stdDev.toFixed(2)}`}>
          <div className="p-3 bg-yellow-100 rounded-full">
            <Zap className="h-6 w-6 text-yellow-600" />
          </div>
          <div>
            <p className="text-slate-500 text-sm font-medium">Consistency</p>
            <p className="text-2xl font-bold text-slate-800">{analytics.consistency}</p>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
});

// --- Main OMR Component ---

const OpticalMarkRecognition = () => {
  // --- State and Redux Hooks ---
  const [getStudentOmr] = useLazyGetStudentOmrServiceQuery();
  const [res, setRes] = useState(null);
  const dispatch = useDispatch();
  const omrData = useSelector((state) => state.studentDashboard.omrData);

  const [isLoading, setIsLoading] = useState(true);
  const [selectedTestId, setSelectedTestId] = useState(null);
  const [questionFilter, setQuestionFilter] = useState('All');

  // --- PDF Download Hooks ---
  const [isDownloading, setIsDownloading] = useState(false);
  const reportRef = useRef(null);

  // --- Highcharts Theme ---
  useEffect(() => {
    Highcharts.setOptions({
      chart: { style: { fontFamily: '"Inter", sans-serif' }, backgroundColor: 'transparent' },
      title: { style: { display: 'none' } },
      credits: { enabled: false },
      legend: { itemStyle: { color: '#475569', fontWeight: '500' } }
    });
  }, []);

  // --- API Fetching Logic ---
  const handleGetStudentOmrServiceApi = useCallback(async () => {
    setIsLoading(true);
    try {
      const studentId = sessionStorage.getItem('userId');

      const omr = await getStudentOmr({ studentId }).unwrap();
      dispatch(setOmrData(omr));
    } catch (error) {
      dispatch(clearOmrData());
      setRes(error.data || { message: 'An error occurred while fetching data.' });
    } finally {
      setIsLoading(false);
    }
  }, [getStudentOmr, dispatch]);

  useEffect(() => {
    handleGetStudentOmrServiceApi();
  }, [handleGetStudentOmrServiceApi]);

  useEffect(() => {
    if (omrData && omrData.length > 0 && !selectedTestId) {
      setSelectedTestId(omrData[0]._id);
    }
  }, [omrData, selectedTestId]);

  // --- Data Processing (Memoized with Overall Analytics) ---
  const processedData = useMemo(() => {
    if (!omrData || omrData.length === 0) return null;

    // --- Overall Analytics (Calculated from ALL tests) ---
    let overallAnalytics = null;
    if (omrData.length > 1) {
      const percentages = omrData.map((t) => t.percentage);
      const totalTests = omrData.length;
      const average = percentages.reduce((sum, p) => sum + p, 0) / totalTests;
      const best = Math.max(...percentages);

      const mean = average;
      const stdDev = Math.sqrt(
        percentages.map((x) => Math.pow(x - mean, 2)).reduce((a, b) => a + b) / totalTests
      );

      let consistency = 'Variable';
      if (stdDev < 5) consistency = 'Very High';
      else if (stdDev < 10) consistency = 'Consistent';

      overallAnalytics = { totalTests, average, best, stdDev, consistency };
    }

    // --- Single Test Analytics (Calculated from selected test) ---
    if (!selectedTestId) return { overallAnalytics }; // Return early if no test is selected yet

    const currentTest = omrData.find((test) => test._id === selectedTestId);
    if (!currentTest) return { overallAnalytics };

    const { details, total_questions, percentage } = currentTest;
    const correctCount = details.filter((q) => q.is_correct).length;
    const incorrectCount = details.filter((q) => q.student_answer && !q.is_correct).length;
    const unattemptedCount = total_questions - correctCount - incorrectCount;

    const scoreGaugeOptions = {
      chart: { type: 'solidgauge' },
      pane: {
        center: ['50%', '70%'],
        size: '100%',
        startAngle: -90,
        endAngle: 90,
        background: {
          backgroundColor: '#EEE',
          innerRadius: '60%',
          outerRadius: '100%',
          shape: 'arc',
          borderWidth: 0
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        tickAmount: 2,
        labels: { y: 16 },
        title: { text: null },
        lineWidth: 0,
        minorTickInterval: null
      },
      plotOptions: {
        solidgauge: {
          dataLabels: {
            y: -25,
            borderWidth: 0,
            useHTML: true,
            format:
              '<div style="text-align:center"><span style="font-size:2rem; font-weight:bold;">{y}%</span></div>'
          },
          cornerRadius: '50%'
        }
      },
      series: [
        {
          name: 'Score',
          data: [
            {
              radius: '100%',
              innerRadius: '60%',
              y: percentage,
              color: Highcharts.color(Highcharts.getOptions().colors[0]).setOpacity(0.8).get()
            }
          ]
        }
      ]
    };
    const answerBreakdownOptions = {
      chart: { type: 'pie' },
      colors: ['#22c55e', '#ef4444', '#9ca3af'],
      plotOptions: {
        pie: {
          innerSize: '60%',
          dataLabels: { enabled: false },
          showInLegend: true,
          borderWidth: 0
        }
      },
      series: [
        {
          name: 'Answers',
          data: [
            { name: 'Correct', y: correctCount },
            { name: 'Incorrect', y: incorrectCount },
            { name: 'Unattempted', y: unattemptedCount }
          ]
        }
      ]
    };
    const trendData = omrData
      .map((test) => ({ x: new Date(test.evaluated_at).getTime(), y: test.percentage }))
      .sort((a, b) => a.x - b.x);
    const scoreTrendOptions = {
      chart: { type: 'spline' },
      xAxis: {
        type: 'datetime',
        labels: { format: '{value:%b %d}', style: { color: '#64748b' } },
        gridLineWidth: 0,
        lineWidth: 0,
        tickWidth: 0
      },
      yAxis: {
        title: { text: null },
        labels: { format: '{value}%', style: { color: '#64748b' } },
        min: 0,
        max: 100,
        gridLineColor: '#e2e8f0'
      },
      series: [
        {
          name: 'Score',
          data: trendData,
          color: '#4f46e5',
          marker: { enabled: true, radius: 4 },
          lineWidth: 2
        }
      ],
      legend: { enabled: false },
      tooltip: { xDateFormat: '%B %d, %Y', pointFormat: 'Score: <b>{point.y}%</b>' }
    };
    const filteredQuestions = details
      .map((item, index) => ({ ...item, qNumber: index + 1 }))
      .filter((item) => {
        if (questionFilter === 'All') return true;
        if (questionFilter === 'Correct') return item.is_correct;
        if (questionFilter === 'Incorrect') return item.student_answer && !item.is_correct;
        if (questionFilter === 'Unattempted') return !item.student_answer;
        return true;
      });

    return {
      currentTest,
      filteredQuestions,
      scoreGaugeOptions,
      answerBreakdownOptions,
      scoreTrendOptions,
      overallAnalytics
    };
  }, [omrData, selectedTestId, questionFilter]);

  // --- PDF Download Handler ---
  const handleDownloadPdf = useCallback(async () => {
    if (!reportRef.current || !processedData) return;
    setIsDownloading(true);
    try {
      const dataUrl = await toJpeg(reportRef.current, {
        quality: 0.95,
        pixelRatio: 2,
        backgroundColor: '#f8fafc'
      });
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const img = new Image();
      img.src = dataUrl;
      await new Promise((resolve) => {
        img.onload = resolve;
      });
      const imgHeight = (img.height * pdfWidth) / img.width;
      pdf.addImage(dataUrl, 'JPEG', 0, 0, pdfWidth, imgHeight);
      pdf.save(
        `OMR_Report_${processedData.currentTest.subject.replace(/\s+/g, '_')}_${new Date(processedData.currentTest.evaluated_at).toLocaleDateString()}.pdf`
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      setRes({ message: 'Could not generate PDF report.' });
    } finally {
      setIsDownloading(false);
    }
  }, [processedData]);

  // --- Render Logic ---
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-slate-50">
        <p className="text-xl font-semibold text-slate-600 animate-pulse">Loading OMR Report...</p>
      </div>
    );
  }
  if (!omrData || omrData.length === 0) {
    return (
      <div className="flex h-screen items-center justify-center bg-slate-50 text-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-700">No OMR Reports Found</h2>
          <p className="text-slate-500 mt-2">Could not find any OMR test results.</p>
          <button
            onClick={handleGetStudentOmrServiceApi}
            className="mt-6 bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-blue-700">
            Refresh Data
          </button>
          <Toastify res={res} resClear={() => setRes(null)} />
        </div>
      </div>
    );
  }
  if (!processedData?.currentTest) {
    return (
      <div className="flex h-screen items-center justify-center bg-slate-50">
        <p className="text-xl font-semibold text-slate-600">Initializing...</p>
      </div>
    );
  }

  const {
    currentTest,
    filteredQuestions,
    scoreGaugeOptions,
    answerBreakdownOptions,
    scoreTrendOptions,
    overallAnalytics
  } = processedData;

  return (
    <div className="min-h-screen p-4 sm:p-6 lg:p-8 font-sans text-slate-800 relative overflow-hidden">
      <div className="relative z-10">
        <Toastify res={res} resClear={() => setRes(null)} />

        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">OMR Analysis Report</h1>
            <p className="text-slate-500 mt-1">
              Hello {currentTest.student_name}, here is your detailed analysis.
            </p>
          </div>
          <div className="flex items-center space-x-4 mt-4 sm:mt-0">
            <select
              value={selectedTestId}
              onChange={(e) => setSelectedTestId(e.target.value)}
              className="bg-white/60 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 font-semibold shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none">
              {omrData.map((test) => (
                <option key={test._id} value={test._id}>
                  {new Date(test.evaluated_at).toLocaleDateString()} - {test.subject}
                </option>
              ))}
            </select>
            <button
              onClick={handleDownloadPdf}
              disabled={isDownloading}
              className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg shadow-blue-500/20 disabled:bg-blue-400">
              {isDownloading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Downloading...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </>
              )}
            </button>
          </div>
        </header>

        <div ref={reportRef} className="space-y-6">
          {overallAnalytics && <OverallAnalyticsCard analytics={overallAnalytics} />}

          <motion.div
            className="grid grid-cols-1 lg:grid-cols-3 gap-6"
            initial="hidden"
            animate="visible"
            variants={{ visible: { transition: { staggerChildren: 0.05 } } }}>
            <div className="lg:col-span-2 flex flex-col gap-6">
              <ChartCard title="Score Trend" icon={<TrendingUp className="w-6 h-6" />}>
                <div className="">
                  <HighchartsReact highcharts={Highcharts} options={scoreTrendOptions} />
                </div>
              </ChartCard>
              <motion.div
                variants={{ hidden: { y: 20, opacity: 0 }, visible: { y: 0, opacity: 1 } }}>
                <div className="bg-white/60 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-6">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                    <h3 className="text-xl font-semibold text-slate-900">Detailed Answer Key</h3>
                    <div className="flex items-center space-x-2 mt-3 sm:mt-0 p-1 bg-slate-100/70 rounded-lg">
                      {['All', 'Correct', 'Incorrect', 'Unattempted'].map((filter) => (
                        <button
                          key={filter}
                          onClick={() => setQuestionFilter(filter)}
                          className={clsx(
                            'px-3 py-1 text-sm font-semibold rounded-md transition-colors',
                            {
                              'bg-blue-600 text-white shadow': filter === questionFilter,
                              'text-slate-600 hover:bg-slate-200/70': filter !== questionFilter
                            }
                          )}>
                          {filter}
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center p-3 font-bold text-slate-500 text-sm">
                      <div className="w-12 text-center">Q#</div>
                      <div className="flex-1 text-center">Your Answer</div>
                      <div className="flex-1 text-center">Correct Answer</div>
                      <div className="w-32 text-right">Result</div>
                    </div>
                    <div className="max-h-[400px] overflow-y-auto pr-2">
                      {filteredQuestions.map((item) => (
                        <QuestionListItem key={item.qNumber} item={item} qNumber={item.qNumber} />
                      ))}
                      {filteredQuestions.length === 0 && (
                        <p className="text-center text-slate-500 py-4">
                          No questions match this filter.
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="lg:col-span-1 space-y-6">
              <ChartCard title="Overall Score" icon={<Book className="w-6 h-6" />}>
                <div className="">
                  <HighchartsReact highcharts={Highcharts} options={scoreGaugeOptions} />
                </div>
              </ChartCard>
              <ChartCard title="Answer Breakdown" icon={<PieChart className="w-6 h-6" />}>
                <div className="">
                  <HighchartsReact highcharts={Highcharts} options={answerBreakdownOptions} />
                </div>
              </ChartCard>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default OpticalMarkRecognition;
