import { motion, AnimatePresence } from 'framer-motion';
import { FaHistory, FaTimes, FaTrash } from 'react-icons/fa';

const HistoryPanel = ({
  showHistory,
  setShowHistory,
  responseHistory,
  handleHistoryClick,
  handleResetHistory,
  isDoubtSolverLoading,
  isYoutubeLoading
}) => {
  return (
    <AnimatePresence>
      {showHistory && (
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          className="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl border-l border-gray-200 z-50 p-6 overflow-auto">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Question History
            </h3>
            <motion.button
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowHistory(false)}
              className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-gradient-to-r from-purple-100 to-blue-100 hover:text-purple-600">
              <FaTimes className="text-lg" />
            </motion.button>
          </div>
          <div className="flex-1 overflow-y-auto mb-4">
            {responseHistory.length === 0 ? (
              <div className="text-center py-12">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                  className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaHistory className="text-3xl text-purple-400" />
                </motion.div>
                <p className="text-gray-500 font-medium">No questions asked yet.</p>
              </div>
            ) : (
              <div className="space-y-3">
                {responseHistory.map((item, index) => (
                  <motion.button
                    key={index}
                    whileHover={{ scale: 1.02, x: 5 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleHistoryClick(item)}
                    className="w-full text-left p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl hover:from-purple-100 hover:to-blue-100 border border-purple-100">
                    <p className="text-sm text-gray-800 font-medium line-clamp-3">
                      {item.question.length > 80
                        ? `${item.question.slice(0, 80)}...`
                        : item.question}
                    </p>
                  </motion.button>
                ))}
              </div>
            )}
          </div>
          <motion.button
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleResetHistory}
            disabled={isDoubtSolverLoading || isYoutubeLoading}
            className="w-full p-4 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl text-white font-bold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-3 disabled:opacity-50">
            <FaTrash className="text-lg" />
            <span>Clear History</span>
          </motion.button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default HistoryPanel;