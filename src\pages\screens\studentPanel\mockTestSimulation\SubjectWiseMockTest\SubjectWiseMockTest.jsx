import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faBook,
  faArrowRight,
  faArrowLeft,
  faCheckCircle,
  faTh,
  faCircle,
  faCheck,
  faClock,
  faChartBar,
  faBrain,
  faBullseye,
  faTrophy,
  faBolt,
  faCircleXmark,
  faExpand,
  faCompress,
  faAtom,
  faFlask,
  faSeedling,
  faSquareRootAlt,
  faMicroscope,
  faCalculator,
  faBookOpen,
  faStethoscope,
  faGear,
  faGears
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

// Register all icons
library.add(
  faBook,
  faArrowRight,
  faArrowLeft,
  faCheckCircle,
  faTh,
  faCircle,
  faCheck,
  faClock,
  faChartBar,
  faBrain,
  faBolt,
  faBullseye,
  faTrophy,
  faCircleXmark,
  faExpand,
  faCompress,
  faAtom,
  faFlask,
  faSeedling,
  faSquareRootAlt,
  faMicroscope,
  faCalculator
);
import {
  useEvaluateSubjectMockTestMutation,
  useLazyGetSubjectMockTestQuestionsQuery
} from '../FullLengthMockTest/fullLengthMockTest.slice';
import FeedbackScreen from './FeedbackScreen';
import { MathJax, MathJaxContext } from 'better-react-mathjax';
const MathText = ({ children }) => {
  if (!children) return null;

  const text = children.toString();

  // Split text by math delimiters
  const parts = [];
  let currentIndex = 0;

  // Find all math expressions (both inline and display)
  const mathRegex = /(\$\$([^$]+)\$\$|\$([^$]+)\$)/g;
  let match;

  while ((match = mathRegex.exec(text)) !== null) {
    // Add text before math expression
    if (match.index > currentIndex) {
      parts.push({
        type: 'text',
        content: text.substring(currentIndex, match.index)
      });
    }

    // Add math expression
    if (match[2]) {
      // Display math ($$...$$)
      parts.push({
        type: 'display',
        content: `\\[${match[2].trim()}\\]`
      });
    } else if (match[3]) {
      // Inline math ($...$)
      parts.push({
        type: 'inline',
        content: `$${match[3].trim()}$`
      });
    }

    currentIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (currentIndex < text.length) {
    parts.push({
      type: 'text',
      content: text.substring(currentIndex)
    });
  }

  // If no math found, return original text
  if (parts.length === 0) {
    return <span>{text}</span>;
  }

  return (
    <span>
      {parts.map((part, index) => {
        switch (part.type) {
          case 'display':
          case 'inline':
            return <MathJax key={index}>{part.content}</MathJax>;
          case 'text':
            return <span key={index}>{part.content}</span>;
          default:
            return <span key={index}>{part.content}</span>;
        }
      })}
    </span>
  );
};
const mathJaxConfig = {
  loader: { load: ['[tex]/html'] },
  tex: {
    packages: { '[+]': ['html'] },
    inlineMath: [['$$', '$$']],
    displayMath: [['\\[', '\\]']],
    processEscapes: true,
    processEnvironments: true
  }
};
const exams = [
  {
    value: 'neet',
    label: 'NEET',
    accent: 'var(--color-student)',
    gradient: 'from-red-500 to-orange-500',
    icon: faStethoscope // ← Now using FA icon
  },
  {
    value: 'jee_mains',
    label: 'JEE Mains',
    accent: 'var(--color-counselor)',
    gradient: 'from-blue-500 to-cyan-500',
    icon: faGears // ← FA Bolt for "Zap"
  },
  {
    value: 'jee_advanced',
    label: 'JEE Advanced',
    accent: 'var(--color-counselor)',
    gradient: 'from-purple-500 to-indigo-500',
    icon: faGear // ← FA Bullseye for "Target"
  }
];

const subjectsByExam = {
  neet: [
    {
      name: 'physics',
      icon: faAtom,
      gradient: 'bg-[var(--color-student)]',
      duration: 45,
      hoverGlow: 'shadow-lg shadow-purple-500/30'
    },
    {
      name: 'chemistry',
      icon: faFlask,
      gradient: 'bg-[var(--color-counselor)]',
      duration: 45,
      hoverGlow: 'shadow-lg shadow-blue-500/30'
    },
    {
      name: 'biology',
      icon: faSeedling,
      gradient: 'bg-[var(--color-student)]',
      duration: 90,
      hoverGlow: 'shadow-lg shadow-green-500/30'
    }
  ],
  jee_mains: [
    {
      name: 'physics',
      icon: faAtom,
      gradient: 'bg-[var(--color-student)]',
      duration: 25,
      hoverGlow: 'shadow-lg shadow-indigo-500/30'
    },
    {
      name: 'chemistry',
      icon: faFlask,
      gradient: 'bg-[var(--color-counselor)]',
      duration: 25,
      hoverGlow: 'shadow-lg shadow-cyan-500/30'
    },
    {
      name: 'mathematics',
      icon: faSquareRootAlt,
      gradient: 'bg-[var(--color-student)]',
      duration: 25,
      hoverGlow: 'shadow-lg shadow-rose-500/30'
    }
  ],
  jee_advanced: [
    {
      name: 'physics',
      icon: faMicroscope,
      gradient: 'bg-[var(--color-student)]',
      duration: 25,
      hoverGlow: 'shadow-lg shadow-violet-500/30'
    },
    {
      name: 'chemistry',
      icon: faFlask,
      gradient: 'bg-[var(--color-counselor)]',
      duration: 25,
      hoverGlow: 'shadow-lg shadow-teal-500/30'
    },
    {
      name: 'mathematics',
      icon: faCalculator,
      gradient: 'bg-[var(--color-student)]',
      duration: 25  ,
      hoverGlow: 'shadow-lg shadow-amber-500/30'
    }
  ]
};

const SubjectWiseMockTest = () => {
  const [selectedExam, setSelectedExam] = useState(null);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [answers, setAnswers] = useState({});
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [showImage, setShowImage] = useState(false);
  const [zoomedImage, setZoomedImage] = useState(null);

  let timerRef = null;
  const subjectConfig = selectedSubject
    ? subjectsByExam[selectedExam]?.find((sub) => sub.name === selectedSubject)
    : null;
  const [evaluateSubjectMockTest] = useEvaluateSubjectMockTestMutation();
  const timeLimit = subjectConfig ? subjectConfig.duration * 60 : 0;

  const examConfig = exams.find((e) => e.value === selectedExam);

  // RTK Query hook
  const [
    getSubjectMockTestQuestions,
    { data: questionsData, isFetching: isLoading, isError: isQuestionError }
  ] = useLazyGetSubjectMockTestQuestionsQuery();

  // Extract questions
  const questions =
    selectedSubject && questionsData?.subjects?.[selectedSubject]
      ? questionsData.subjects[selectedSubject]
      : [];

  // NEW: Track numeric/free-text answers for questions without options
  const [numericAnswers, setNumericAnswers] = useState({});

  // Timer effect
  useEffect(() => {
    if (questions.length > 0 && !results) {
      timerRef = setInterval(() => {
        setTimeElapsed((prev) => {
          if (prev >= timeLimit) {
            clearInterval(timerRef);
            submitAnswers(); // Auto-submit when time is up
            return timeLimit;
          }
          return prev + 1;
        });
      }, 1000);
    } else {
      clearInterval(timerRef);
    }

    return () => clearInterval(timerRef);
  }, [questions, results, timeLimit]);

  // Removed fullscreen change handler

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const timeRemaining = timeLimit - timeElapsed;

  // Removed fullscreen helpers

  const fetchQuestions = async () => {
    if (!selectedExam || !selectedSubject) return;
    setError(null);
    setResults(null);
    setAnswers({});
    setCurrentQuestionIndex(0);
    setTimeElapsed(0);
    try {
      await getSubjectMockTestQuestions({
        examName: selectedExam,
        subject: selectedSubject
      }).unwrap();
    } catch (err) {
      setError(err?.data?.message || 'Failed to load questions');
    }
  };

  useEffect(() => {
    if (selectedSubject) {
      fetchQuestions();
    }
  }, [selectedSubject]);

  const handleAnswer = (optionIndex) => {
    const qId = questions[currentQuestionIndex]?.id;
    if (qId) {
      setAnswers((prev) => ({
        ...prev,
        [qId]: optionIndex
      }));
    }
  };

  const handleNumericAnswerChange = (e) => {
    const value = e.target.value;
    const qId = questions[currentQuestionIndex]?.id;
    if (!qId) return;
    setNumericAnswers((prev) => ({
      ...prev,
      [qId]: value
    }));
  };

  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1);
    }
  };

  const goToPrev = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1);
    }
  };

  const goToQuestion = (index) => {
    setCurrentQuestionIndex(index);
  };
  useEffect(() => {
    const setVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setVh();
    window.addEventListener('resize', setVh);
    return () => window.removeEventListener('resize', setVh);
  }, []);

  // Inside your submit function
  const submitAnswers = async () => {
    const responses = questions.map((q, index) => {
      const hasOptions = Array.isArray(q?.options) && q.options.length > 0;
      const isNumerical = q?.question_type === 'numerical' || !hasOptions;

      let selected_option = null;
      if (isNumerical) {
        // For numerical or no-options questions, send the typed value as integer if present
        const typed = numericAnswers[q.id];
        if (typed !== undefined && typed !== null && `${typed}`.trim() !== '') {
          const parsed = parseInt(typed, 10);
          selected_option = Number.isNaN(parsed) ? null : parsed;
        }
      } else {
        // For MCQ, send 1-based index if selected
        selected_option = answers[q.id] !== undefined ? answers[q.id] + 1 : null;
      }

      return {
        question_id: q.id,
        sequence: index + 1, // Required field - sequence number
        selected_option,
        unit: q.unit || null, // Optional field - topic
        subunit: q.subunit || null, // Optional field - subtopic
        question_text: q.question_text || null // Optional field - question text
      };
    });

    try {
      const userId = sessionStorage.getItem('userId'); // or from auth context
      if (!userId) {
        setError('User not logged in');
        return;
      }

      const result = await evaluateSubjectMockTest({
        examName: selectedExam,
        subject: selectedSubject,
        user_id: userId, // ←←← Required!
        responses
      }).unwrap();

      setResults(result);
    } catch (err) {
      setError(err.data?.message || 'Failed to evaluate test');
    }
  };

  const reset = () => {
    setSelectedExam(null);
    setSelectedSubject(null);
    setAnswers({});
    setResults(null);
    setError(null);
    setCurrentQuestionIndex(0);
    setTimeElapsed(0);
  };

  // Calculate stats
  const answeredCount = questions.reduce((count, q) => {
    const hasMcq = answers[q.id] !== undefined;
    const hasTyped = numericAnswers[q.id] !== undefined && `${numericAnswers[q.id]}`.trim() !== '';
    return count + (hasMcq || hasTyped ? 1 : 0);
  }, 0);
  const unattemptedCount = questions.length - answeredCount;

  return (
    <MathJaxContext config={mathJaxConfig}>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 text-gray-800 font-sans">
        <div className="max-w-7xl mx-auto p-6 md:p-8 lg:p-12 flex flex-col min-h-[calc(var(--vh,1vh)*100)]">
          {/* Header - hide when exam questions are being shown */}
          {!selectedSubject && (
            <motion.div
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, ease: [0.23, 1, 0.32, 1] }}
              className="relative mb-12 p-8 bg-[var(--color-student)] rounded-3xl border-2 border-gray-100 shadow-[0_10px_40px_-15px_rgba(0,0,0,0.05)]">
              {/* Geometric background pattern */}
              <div className="absolute inset-0 overflow-hidden rounded-3xl">
                <motion.div
                  className="absolute -top-10 -left-10 w-32 h-32 border-2 border-blue-100 rounded-lg"
                  animate={{ rotate: 45 }}
                  transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
                />
                <motion.div
                  className="absolute -bottom-8 -right-8 w-24 h-24 border-2 border-green-100 rounded-lg"
                  animate={{ rotate: -45 }}
                  transition={{ duration: 25, repeat: Infinity, ease: 'linear' }}
                />
              </div>

              <div className="relative z-10 flex flex-col md:flex-row md:items-center md:justify-between gap-8">
                <div className="flex items-start space-x-5">
                  {/* Floating 3D icon effect */}
                  <motion.div
                    whileHover={{
                      y: -5,
                      rotate: -3,
                      transition: { type: 'spring', stiffness: 300 }
                    }}
                    className="relative">
                    <motion.div
                      animate={{
                        rotate: [0, 2, 0, -2, 0],
                        y: [0, -3, 0]
                      }}
                      transition={{
                        duration: 6,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                      className="p-4 rounded-2xl bg-white border-2 border-gray-100 shadow-lg relative z-10"
                      style={{
                        background: selectedExam
                          ? `var(--${selectedExam === 'neet' ? 'color-student' : 'color-counselor'})`
                          : '#4f46e5'
                      }}>
                      <FontAwesomeIcon
                        icon={faBookOpen}
                        className="w-6 h-6 text-[var(--color-counselor)]"
                      />
                    </motion.div>

                    {/* Shadow effect */}
                    <motion.div
                      className="absolute -bottom-2 left-2 right-0 h-3 bg-gray-200 blur-md rounded-full opacity-50"
                      animate={{ opacity: [0.3, 0.5, 0.3] }}
                      transition={{ duration: 3, repeat: Infinity }}
                    />
                  </motion.div>

                  <div className="flex-1">
                    <motion.h1
                      className="text-4xl font-bold text-white tracking-tight"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2, duration: 0.6 }}>
                      Subject Wise{' '}
                      <span className="text-transparent bg-clip-text bg-gray-100">Mock Test</span>
                    </motion.h1>

                    <motion.div
                      className="flex items-center mt-3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}>
                      <div className="w-2 h-2 rounded-full bg-[var(--color-counselor)] mr-2"></div>
                      <p className="text-gray-200 text-lg">
                        Master one subject at a time with focused practice
                      </p>
                    </motion.div>

                    {/* Animated progress dots */}
                    <motion.div
                      className="flex space-x-2 mt-6"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.6 }}>
                      {[1, 2, 3, 4, 5].map((dot) => (
                        <motion.div
                          key={dot}
                          className={`h-2 rounded-full ${dot <= 3 ? 'bg-[var(--color-counselor)]' : 'bg-gray-200'}`}
                          animate={{
                            scale: dot === 3 ? [1, 1.2, 1] : 1,
                            opacity: dot === 3 ? [1, 0.7, 1] : 1
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            delay: dot * 0.2
                          }}
                          style={{ width: dot === 3 ? '24px' : '12px' }}
                        />
                      ))}
                    </motion.div>
                  </div>
                </div>

                {selectedSubject && (
                  <motion.button
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    whileHover={{
                      x: -5,
                      transition: { type: 'spring', stiffness: 400, damping: 17 }
                    }}
                    whileTap={{ scale: 0.95 }}
                    onClick={reset}
                    className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 bg-white py-3 px-5 rounded-xl border-2 border-gray-200 shadow-sm hover:shadow-md transition-all group self-start md:self-center">
                    <motion.div
                      animate={{ x: [0, -3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}>
                      <FontAwesomeIcon icon={faArrowLeft} className="w-3.5 h-3.5 mr-1" />
                    </motion.div>
                    <span className="font-medium">Exit Practice</span>
                  </motion.button>
                )}
              </div>

              {/* Animated connection lines */}
              <motion.svg
                className="absolute top-0 left-0 w-full h-full pointer-events-none"
                viewBox="0 0 100 100">
                <motion.line
                  x1="10"
                  y1="15"
                  x2="30"
                  y2="25"
                  stroke="#e5e7eb"
                  strokeWidth="0.5"
                  animate={{ opacity: [0, 0.5, 0] }}
                  transition={{ duration: 4, repeat: Infinity }}
                />
                <motion.line
                  x1="80"
                  y1="20"
                  x2="90"
                  y2="40"
                  stroke="#e5e7eb"
                  strokeWidth="0.5"
                  animate={{ opacity: [0, 0.7, 0] }}
                  transition={{ duration: 5, repeat: Infinity, delay: 1 }}
                />
              </motion.svg>
            </motion.div>
          )}

          <div className="flex-1 min-h-0">
            {!selectedExam ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                {exams.map((exam) => (
                  <motion.button
                    key={exam.value}
                    whileHover={{ y: -12, scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => setSelectedExam(exam.value)}
                    className="group relative p-8 rounded-3xl bg-[var(--color-counselor)] shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden h-60 focus:ring-4 focus:ring-blue-100 ring-offset-2 ring-offset-transparent hover:ring-4 hover:ring-blue-100 isolate">
                    {/* Subtle Floating Badge (e.g., "Popular" or "New") */}
                    {exam.badge && (
                      <span
                        className={`absolute top-4 right-4 text-xs font-bold px-2.5 py-1 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-sm transform scale-90 opacity-0 group-hover:opacity-100 group-hover:scale-100 transition-all duration-300`}>
                        {exam.badge}
                      </span>
                    )}

                    {/* Soft Gradient Background (based on subject) */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${exam.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}
                    />

                    {/* Decorative Corner Element */}
                    <div
                      className={`absolute -top-6 -right-6 w-24 h-24 rounded-full ${exam.gradient} opacity-10 group-hover:opacity-20 transition-opacity duration-700 blur-sm`}
                    />

                    {/* Icon Container with Floating Effect */}
                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.1 }}
                      className={`w-14 h-14 rounded-2xl flex items-center justify-center mb-0 bg-white shadow-md group-hover:shadow-lg transform transition-all duration-300 group-hover:scale-110 text-[var(--color-student)]`}>
                      <FontAwesomeIcon icon={exam.icon} />
                    </motion.div>

                    {/* Content */}
                    <div className="relative z-10">
                      <h2 className="text-2xl font-extrabold text-black transition-colors duration-300 leading-tight">
                        {exam.label}
                      </h2>
                      <p className="text-gray-700 text-sm mt-2 group-hover:text-gray-700 transition-colors duration-300 line-clamp-2">
                        {exam.description || 'Practice with subject-specific questions'}
                      </p>
                    </div>

                    {/* Subtle border glow on hover */}
                    <motion.div
                      className={`absolute inset-0 rounded-3xl pointer-events-none transition-opacity duration-300 opacity-0 group-hover:opacity-100`}
                      style={{
                        boxShadow: `inset 0 0 20px 4px ${exam.color || 'rgba(99, 102, 241, 0.3)'}`
                      }}
                    />
                  </motion.button>
                ))}
              </motion.div>
            ) : !selectedSubject ? (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4 }}
                className="space-y-8">
                {/* Enhanced header with animated gradient background */}
                <motion.div
                  className="p-6 rounded-2xl bg-[var(--color-counselor)] border border-blue-100"
                  initial={{ y: -10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.1, duration: 0.5 }}>
                  <div className="flex items-center space-x-4">
                    <motion.button
                      whileHover={{ scale: 1.05, x: -3 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedExam(null)}
                      className="p-3 rounded-3xl bg-white shadow-md border border-gray-200 hover:bg-gray-50 transition-all flex items-center justify-center">
                      <FontAwesomeIcon icon={faArrowLeft} className="w-3.5 h-3.5 mr-1" />
                    </motion.button>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800">
                        Choose a Subject for{' '}
                        <motion.span
                          className="bg-[var(--color-student)] bg-clip-text text-transparent"
                          initial={{ backgroundPosition: '0% 50%' }}
                          animate={{ backgroundPosition: '100% 50%' }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            repeatType: 'reverse',
                            ease: 'linear'
                          }}
                          style={{
                            backgroundSize: '200% 100%'
                          }}>
                          {examConfig.label}
                        </motion.span>
                      </h2>
                      <p className="text-gray-600 mt-1">Select a subject to begin your practice</p>
                    </div>
                  </div>
                </motion.div>

                {/* Enhanced subject grid with staggered animations */}
                <motion.div
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-6"
                  variants={{
                    hidden: { opacity: 0 },
                    show: {
                      opacity: 1,
                      transition: {
                        staggerChildren: 0.15
                      }
                    }
                  }}
                  initial="hidden"
                  animate="show">
                  {subjectsByExam[selectedExam]?.map((subject, index) => {
                    const IconComponent = () => (
                      <FontAwesomeIcon
                        icon={subject.icon}
                        className="text-3xl sm:text-4xl text-white drop-shadow-md"
                      />
                    );

                    return (
                      <motion.div
                        key={subject.name}
                        variants={{
                          hidden: { opacity: 0, y: 30, scale: 0.95 },
                          show: { opacity: 1, y: 0, scale: 1 }
                        }}
                        whileHover={{
                          y: -10,
                          scale: 1.05,
                          transition: { type: 'spring', stiffness: 400, damping: 15 }
                        }}
                        whileTap={{ scale: 0.95 }}
                        className="relative h-48 sm:h-52 group cursor-pointer">
                        {/* Main Button */}
                        <motion.button
                          onClick={() => setSelectedSubject(subject.name)}
                          className={`w-full h-full rounded-3xl ${subject.gradient} ${subject.hoverGlow} text-white font-medium transition-all duration-500 relative overflow-hidden border border-white/20 backdrop-blur-sm`}>
                          {/* Subtle Pattern Overlay */}
                          <div
                            className="absolute inset-0 opacity-10 group-hover:opacity-15 transition-opacity"
                            style={{
                              backgroundImage: `
                        radial-gradient(circle at 20% 30%, rgba(255,255,255,0.2) 1px, transparent 1px),
                        radial-gradient(circle at 80% 70%, rgba(255,255,255,0.15) 1px, transparent 1px)
                      `,
                              backgroundSize: '30px 30px'
                            }}
                          />

                          {/* Floating Icon */}
                          <motion.div
                            className="relative z-10 text-center mt-5"
                            animate={{
                              y: [0, -8, 0]
                            }}
                            transition={{
                              duration: 3.5,
                              repeat: Infinity,
                              delay: index * 0.4,
                              ease: 'easeInOut'
                            }}>
                            <IconComponent />
                            <h3 className="text-lg sm:text-xl font-bold mt-3 capitalize px-4">
                              {subject.name}
                            </h3>
                            <span className="text-white/90 text-sm bg-black/20 px-3 py-1 rounded-full mt-2 inline-block backdrop-blur-sm">
                              {subject.duration} min
                            </span>
                          </motion.div>

                          {/* Shine Effect on Hover */}
                          <motion.div
                            className="absolute -left-20 top-0 w-20 h-full bg-white/30 transform rotate-12 group-hover:left-full transition-left duration-700"
                            style={{ clipPath: 'polygon(0 0, 100% 0%, 80% 100%, 0% 100%)' }}
                          />

                          {/* Floating Particles (on hover) */}
                          <motion.div
                            className="absolute inset-0 pointer-events-none"
                            initial="hidden"
                            whileHover="show">
                            {[...Array(3)].map((_, i) => (
                              <motion.div
                                key={i}
                                className="absolute w-1.5 h-1.5 bg-white/40 rounded-full"
                                style={{
                                  left: `${Math.random() * 100}%`,
                                  top: `${Math.random() * 100}%`
                                }}
                                variants={{
                                  hidden: { y: 0, opacity: 0 },
                                  show: {
                                    y: ['-100%', '100%'],
                                    opacity: [0, 1, 0],
                                    scale: [0.5, 1, 0.5]
                                  }
                                }}
                                transition={{
                                  duration: 1.5 + i * 0.5,
                                  repeat: Infinity,
                                  delay: i * 0.8
                                }}
                              />
                            ))}
                          </motion.div>

                          {/* Border Pulse on hover */}
                          <motion.div
                            className="absolute inset-0 rounded-3xl border-2 border-white/30 group-hover:border-white/60 transition-colors"
                            initial={{ opacity: 0 }}
                            whileHover={{ opacity: 1 }}
                          />
                        </motion.button>

                        {/* Floating Label (on hover) */}
                        <motion.div
                          className="absolute -top-2 -right-2 bg-white text-gray-800 text-xs font-bold px-2 py-1 rounded-full shadow-md z-20 whitespace-nowrap"
                          initial={{ opacity: 0, scale: 0 }}
                          whileHover={{ opacity: 1, scale: 1.1 }}
                          transition={{ type: 'spring', stiffness: 300 }}>
                          Pick
                        </motion.div>
                      </motion.div>
                    );
                  })}
                </motion.div>
              </motion.div>
            ) : (
              <AnimatePresence mode="wait">
                {isLoading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex flex-col items-center justify-center py-24">
                    <motion.div
                      animate={{ rotate: 360, scale: [1, 1.1, 1] }}
                      transition={{ duration: 1.2, repeat: Infinity, ease: 'easeInOut' }}
                      className="w-16 h-16 border-4 border-gray-200 border-t-blue-500 rounded-full"
                    />
                    <p className="mt-6 text-lg text-gray-600 flex items-center">
                      <motion.span
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 1.5, repeat: Infinity }}>
                        Preparing your {selectedSubject} questions...
                      </motion.span>
                    </p>
                  </motion.div>
                ) : isQuestionError || error ? (
                  <motion.div
                    key="error"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center py-20 bg-white rounded-2xl shadow-lg p-8 max-w-md mx-auto">
                    <FontAwesomeIcon
                      icon={faCheckCircle}
                      className="w-16 h-16 text-red-500 mx-auto mb-4"
                    />
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      Something went wrong
                    </h3>
                    <p className="text-red-600 mb-6">{error || 'Failed to load questions'}</p>
                    <button
                      onClick={fetchQuestions}
                      className="px-6 py-2.5 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-xl hover:from-red-600 hover:to-orange-600 transition shadow-md">
                      Try Again
                    </button>
                  </motion.div>
                ) : results ? (
                  <FeedbackScreen
                    questions={questions}
                    onBack={reset}
                    examName={selectedExam}
                    subject={selectedSubject}
                    results={results}
                  />
                ) : questions.length > 0 ? (
                    <motion.div
                      key="question-panel"
                      initial={{ opacity: 0 }}  
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full overflow-hidden">
                      {' '}
                      {/* Reduced gap */}
                      {/* Question Card */}
                          <div className="lg:col-span-3 order-1 h-full flex flex-col">
                            <motion.div
                              key={currentQuestionIndex}
                              initial={{ x: 300, opacity: 0 }}
                              animate={{ x: 0, opacity: 1 }}
                              exit={{ x: -300, opacity: 0 }}
                              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                              className="bg-white p-6 rounded-xl shadow-md border border-gray-200 h-full overflow-y-auto"
                              style={{
                                maxHeight: '100%',
                                scrollbarWidth: 'thin',
                                scrollbarColor: '#cbd5e1 #f1f5f9'
                              }}>
                              {' '}
                              {/* Reduced padding & min-height */}
                              {/* Question Header */}
                              <div className="flex justify-between items-center mb-4">
                                {' '}
                                {/* Tighter margin */}
                                <span className="text-xs font-semibold px-2.5 py-1 rounded-full bg-[var(--color-student)] text-white capitalize">
                                  {selectedSubject} • Q{currentQuestionIndex + 1}
                                </span>
                                <div className="flex items-center space-x-3">
                                  <div className="flex items-center bg-[var(--color-counselor)] px-2.5 py-1 rounded-full">
                                    <FontAwesomeIcon
                                      icon={faClock}
                                      className="w-3.5 h-3.5 mr-1 text-white"
                                    />
                                    <span
                                      className={`text-sm font-mono ${
                                        timeRemaining < 300 ? 'text-red-600' : 'text-black '
                                      }`}>
                                      {formatTime(timeRemaining)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              {/* Question Text */}
                              <h3 className="text-lg font-semibold text-gray-800 leading-relaxed mb-6">
                                <MathText>{questions[currentQuestionIndex]?.question_text}</MathText>
                              </h3>
                              {/* Conditional Image with Show/Hide Toggle */}{' '}
                              {questions[currentQuestionIndex]?.actual_question_image && (
                                <div className="mb-6">
                                  <button
                                    onClick={() => {
                                      if (
                                        showImage &&
                                        zoomedImage ===
                                          questions[currentQuestionIndex]?.actual_question_image
                                      ) {
                                        setShowImage(false);
                                      } else {
                                        setZoomedImage(
                                          questions[currentQuestionIndex]?.actual_question_image
                                        );
                                        setShowImage(true);
                                      }
                                    }}
                                    className="text-sm font-medium text-indigo-600 hover:text-indigo-800 transition flex items-center px-3 py-2 rounded-md bg-indigo-50 hover:bg-indigo-100">
                                    <FontAwesomeIcon
                                      icon={
                                        showImage &&
                                        zoomedImage ===
                                          questions[currentQuestionIndex]?.actual_question_image
                                          ? faCompress
                                          : faExpand
                                      }
                                      className="w-4 h-4 mr-1"
                                    />
                                    {showImage &&
                                    zoomedImage === questions[currentQuestionIndex]?.actual_question_image
                                      ? 'Hide Image'
                                      : 'Show Image'}
                                  </button>

                                  <AnimatePresence>
                                    {showImage &&
                                      zoomedImage ===
                                        questions[currentQuestionIndex]?.actual_question_image && (
                                        <motion.div
                                          initial={{ opacity: 0, height: 0 }}
                                          animate={{ opacity: 1, height: 'auto' }}
                                          exit={{ opacity: 0, height: 0 }}
                                          transition={{ duration: 0.3 }}
                                          className="mt-4 relative overflow-hidden">
                                          {/* Container with controlled dimensions */}
                                          <div className="relative bg-gray-100 rounded-lg border border-gray-200 p-2 flex items-center justify-center min-h-[100px]">
                                            <div className="max-w-full overflow-auto">
                                              <img
                                                src={zoomedImage}
                                                alt="Question visual"
                                                className="mx-auto max-h-60 object-contain"
                                                style={{
                                                  width: 'auto',
                                                  maxWidth: '100%',
                                                  height: 'auto'
                                                }}
                                              />
                                            </div>
                                          </div>
                                        </motion.div>
                                      )}
                                  </AnimatePresence>
                                </div>
                              )}
                              {/* Options */}
                              <div className="space-y-2.5 flex-1 min-h-0">
                                {(() => {
                                  const q = questions[currentQuestionIndex];
                                  const hasOptions = Array.isArray(q?.options) && q.options.length > 0;
                                  const isNumerical = q?.question_type === 'numerical' || !hasOptions;

                                  if (isNumerical) {
                                    return (
                                      <div className="space-y-2">
                                        <label className="block text-sm text-gray-700">
                                          Enter your answer
                                        </label>
                                        <input
                                          type="text"
                                          inputMode="numeric"
                                          step="1"
                                          maxLength={25}
                                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                          value={numericAnswers[q?.id] ?? ''}
                                          onChange={handleNumericAnswerChange}
                                        />
                                      </div>
                                    );
                                  }

                                  return q.options.map((option, idx) => {
                                    const isSelected = answers[q.id] === idx;
                                    return (
                                      <motion.label
                                        key={idx}
                                        whileHover={{ scale: 1.02 }}
                                        className={`flex items-start p-3 rounded-lg border cursor-pointer transition-all text-sm ${
                                          isSelected
                                            ? 'border-indigo-500 bg-indigo-50'
                                            : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                        onClick={() => handleAnswer(idx)}>
                                        <div
                                          className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center flex-shrink-0 mt-0.5 ${
                                            isSelected
                                              ? 'border-indigo-500 bg-indigo-500'
                                              : 'border-gray-400'
                                          }`}>
                                          {isSelected && (
                                            <FontAwesomeIcon
                                              icon={faCheck}
                                              className="w-2.5 h-2.5 text-white"
                                            />
                                          )}
                                        </div>
                                        <span className="text-gray-700">
                                          <MathText>{option}</MathText>
                                        </span>
                                      </motion.label>
                                    );
                                  });
                                })()}
                              </div>
                              {/* Navigation */}
                              <div className="flex justify-between mt-6 pt-4 border-t border-gray-200 sticky bottom-0 bg-white">
                                <button
                                  onClick={goToPrev}
                                  disabled={currentQuestionIndex === 0}
                                  className="text-sm px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 flex items-center">
                                  <FontAwesomeIcon icon={faArrowLeft} className="w-3.5 h-3.5 mr-1" />
                                  Prev
                                </button>

                                <button
                                  onClick={goToNext}
                                  disabled={currentQuestionIndex === questions.length - 1}
                                  className="text-sm px-5 py-2.5 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center">
                                  Next{' '}
                                  <FontAwesomeIcon icon={faArrowRight} className="w-3.5 h-3.5 ml-1" />
                                </button>
                              </div>
                            </motion.div>
                          </div>
                    {/* Progress Sidebar - Compact */}
                    <div className="lg:col-span-1 order-2 h-full flex flex-col">
                      <div className="bg-white p-5 rounded-xl shadow-md border border-gray-100 h-full overflow-y-auto">
                        {/* Title */}
                        <h4 className="font-semibold text-gray-800 mb-4 flex items-center text-sm">
                          <FontAwesomeIcon
                            icon={faChartBar}
                            className="w-4 h-4 mr-1.5 text-indigo-500"
                          />
                          <span>Progress</span>
                        </h4>

                        {/* Stats Section */}
                        <div className="space-y-3 mb-5">
                          {/* Answered */}
                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-600">Answered</span>
                            <span className="text-xs font-semibold text-green-600">
                              {answeredCount}
                            </span>
                          </div>

                          {/* Unattempted */}
                          <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-600">Unattempted</span>
                            <span className="text-xs font-semibold text-gray-500">
                              {unattemptedCount}
                            </span>
                          </div>

                          {/* Progress Bar - Increased Height */}
                          <div className="w-full bg-gray-200 rounded-full h-3 mt-2">
                            <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: `${(answeredCount / questions.length) * 100}%` }}
                              transition={{ duration: 0.6, ease: 'easeOut' }}
                              className="bg-gradient-to-r from-green-400 to-teal-500 h-3 rounded-full shadow-inner"
                            />
                          </div>

                          {/* Percentage Label (Optional) */}
                          <p className="text-xs text-gray-500 text-right mt-1">
                            {Math.round((answeredCount / questions.length) * 100)}% completed
                          </p>
                        </div>

                        {/* Question Grid */}
                        <div className="mb-4">
                          <p className="text-xs font-medium text-gray-700 mb-2.5">Questions</p>
                          <div className="grid grid-cols-5 gap-1.5 max-h-60 overflow-y-auto pr-1 custom-scrollbar">
                            {questions.map((q, index) => {
                              const isCurrent = index === currentQuestionIndex;
                              const hasTyped =
                                numericAnswers?.[q.id] !== undefined &&
                                `${numericAnswers[q.id]}`.trim() !== '';
                              const isAnswered = answers[q.id] !== undefined || hasTyped;

                              return (
                                <motion.button
                                  key={q.id}
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.95 }}
                                  onClick={() => goToQuestion(index)}
                                  className={`text-xs font-medium rounded-md p-1.5 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1 ${
                                    isCurrent
                                      ? 'bg-indigo-500 text-white shadow-sm scale-105'
                                      : isAnswered
                                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                  }`}
                                  aria-label={`Go to question ${index + 1}`}>
                                  {index + 1}
                                </motion.button>
                              );
                            })}
                          </div>
                        </div>

                        {/* Submit Test Button */}
                        <button
                          onClick={submitAnswers}
                          disabled={answeredCount === 0}
                          className={`w-full py-2.5 rounded-lg text-sm font-medium transition-all duration-300 ${
                            answeredCount > 0
                              ? 'bg-[var(--color-counselor)] text-white hover:from-green-600 hover:to-teal-600 shadow-sm hover:shadow'
                              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                          }`}>
                          Submit Test
                        </button>

                        {/* Optional: Footer Note */}
                        <p className="text-xs text-gray-500 text-center mt-3">
                          Review your answers before submitting
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="no-questions"
                    className="text-center py-20 bg-white rounded-2xl shadow-lg p-8 max-w-md mx-auto">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FontAwesomeIcon
                        icon={faCircleXmark}
                        className="w-16 h-16 text-red-500 mx-auto mb-4"
                      />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      No questions available
                    </h3>
                    <p className="text-gray-500 mb-6">
                      There are no questions for this subject yet.
                    </p>
                    <button
                      onClick={() => setSelectedSubject(null)}
                      className="px-6 py-2.5 bg-gray-800 text-white rounded-xl hover:bg-gray-900 transition">
                      Choose Another Subject
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            )}
          </div>
        </div>
      </div>
    </MathJaxContext>
  );
};

export default SubjectWiseMockTest;
