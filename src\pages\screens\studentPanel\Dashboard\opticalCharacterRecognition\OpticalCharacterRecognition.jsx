import React, { useEffect, useState, useMemo, memo, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import clsx from 'clsx';
import jsPDF from 'jspdf';
import { toJpeg } from 'html-to-image';

// --- Live Data Integration ---
import { clearOcrData, setOcrData, useLazyGetStudentOcrServiceQuery } from '../dashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';

// Import Highcharts modules for their side effects
import 'highcharts/highcharts-more';
import 'highcharts/modules/solid-gauge';
import 'highcharts/modules/variable-pie';

// Lucide icons for a clean look
import {
  CheckCircle2,
  XCircle,
  AlertTriangle,
  ChevronDown,
  Award,
  Lightbulb,
  TrendingUp,
  SlidersHorizontal,
  Target,
  Download,
  Loader2
} from 'lucide-react';

// --- Reusable UI Components (Memoized for performance) ---

const DashboardCard = memo(({ children, className, ...props }) => (
  <motion.div
    className={clsx(
      'bg-white rounded-2xl shadow-md border border-slate-200/80 p-6 overflow-hidden',
      className
    )}
    variants={{ hidden: { y: 20, opacity: 0 }, visible: { y: 0, opacity: 1 } }}
    {...props}>
    {children}
  </motion.div>
));

const StatCard = memo(({ icon, label, value, color }) => (
  <div className="flex items-center">
    <div className={`p-3 rounded-full ${color.bg}`}>
      {React.cloneElement(icon, { className: `h-6 w-6 ${color.text}` })}
    </div>
    <div className="ml-4">
      <p className="text-slate-500 text-sm font-medium">{label}</p>
      <p className="text-2xl font-bold text-slate-800">{value}</p>
    </div>
  </div>
));

const ActionPlan = memo(({ strategy }) => (
  <div className="h-full flex flex-col justify-between">
    <div>
      <div className="flex items-center text-slate-600 mb-4">
        <Lightbulb className="w-6 h-6" />
        <h3 className="text-lg font-semibold ml-2">Strategic Action Plan</h3>
      </div>
      <div className="space-y-4 text-sm">
        <div>
          <h4 className="font-semibold text-blue-600 flex items-center mb-1">
            <TrendingUp size={16} className="mr-2" />
            Primary Focus
          </h4>
          <p className="text-slate-600 pl-4 border-l-2 border-blue-500">
            {strategy.primary_area_for_improvement}
          </p>
        </div>
        <div>
          <h4 className="font-semibold text-green-600 flex items-center mb-1">
            <Award size={16} className="mr-2" />
            Strengths to Leverage
          </h4>
          <ul className="pl-4 text-slate-600 list-disc list-inside">
            {strategy.conceptual_strengths.map((s) => (
              <li key={s}>{s}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
    <div className="mt-6">
      <button className="w-full bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
        View Recommended Practice
      </button>
    </div>
  </div>
));

const QuestionAccordionItem = memo(({ question }) => {
  const [isOpen, setIsOpen] = useState(false);
  const status = question.is_correct
    ? { color: 'border-green-500', icon: <CheckCircle2 className="text-green-500" /> }
    : { color: 'border-red-500', icon: <XCircle className="text-red-500" /> };
  return (
    <div className={`border-l-4 rounded-r-lg bg-slate-50/50 transition-all ${status.color}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-4 text-left">
        <div className="flex items-center space-x-4">
          <span className="font-bold text-slate-800">Q{question.question_number}</span>
          {status.icon}
          <span className="font-semibold text-slate-700 hidden sm:block">
            {question.feedback.core_concept_tested}
          </span>
        </div>
        <ChevronDown
          className={`transform transition-transform text-slate-500 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden">
            <div className="p-4 border-t border-slate-200 space-y-4 text-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold text-slate-800">Your Answer:</p>
                  <p className="p-2 mt-1 bg-white rounded border border-slate-200 font-mono text-slate-700">
                    {question.student_final_answer}
                  </p>
                </div>
                <div>
                  <p className="font-semibold text-green-800">Correct Answer:</p>
                  <p className="p-2 mt-1 bg-green-50 rounded border border-green-200 font-mono text-green-800">
                    {question.expert_calculated_answer}
                  </p>
                </div>
              </div>
              <div>
                <h5 className="font-semibold mb-1 text-slate-800">Expert Explanation</h5>
                <p className="text-slate-600 whitespace-pre-wrap">
                  {question.expert_method_explanation}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

// --- Main Dashboard Component ---

const OpticalCharacterRecognition = () => {
  // --- State and Redux Hooks ---
  const [getStudentOcr] = useLazyGetStudentOcrServiceQuery();
  const [res, setRes] = useState(null);
  const dispatch = useDispatch();
  const ocrData = useSelector((state) => state.studentDashboard.ocrData);

  const [isLoading, setIsLoading] = useState(true);
  const [selectedTestDate, setSelectedTestDate] = useState(null);
  const [questionFilter, setQuestionFilter] = useState('All');

  // --- Hooks for PDF Download ---
  const [isDownloading, setIsDownloading] = useState(false);
  const summaryRef = useRef(null); // Ref for the summary/charts section ONLY

  // --- Global Highcharts Theme ---
  useEffect(() => {
    Highcharts.setOptions({
      chart: { style: { fontFamily: '"Inter", sans-serif' }, backgroundColor: 'transparent' },
      title: { style: { display: 'none' } },
      credits: { enabled: false },
      legend: {
        itemStyle: { color: '#475569', fontWeight: '500' },
        itemHoverStyle: { color: '#0f172a' }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e2e8f0',
        borderRadius: 8,
        borderWidth: 1,
        shadow: true,
        style: { color: '#334155' }
      }
    });
  }, []);

  // --- API Fetching Logic ---
  const handleGetStudentOcrApi = useCallback(async () => {
    setIsLoading(true);
    try {
      const studentId = sessionStorage.getItem('userId');
      if (!studentId) {
        throw new Error('Student ID not found. Please log in again.');
      }
      const ocr = await getStudentOcr({ studentId }).unwrap();
      dispatch(setOcrData(ocr));
    } catch (error) {
      dispatch(clearOcrData());
      setRes(error.data || { message: 'An error occurred while fetching data.' });
    } finally {
      setIsLoading(false);
    }
  }, [getStudentOcr, dispatch]);

  useEffect(() => {
    handleGetStudentOcrApi();
  }, []);

  // --- State Management for Selected Test ---
  useEffect(() => {
    if (ocrData && ocrData.length > 0 && !selectedTestDate) {
      setSelectedTestDate(ocrData[0].date);
    }
  }, [ocrData, selectedTestDate]);

  // --- Hybrid PDF Download Handler ---
  const handleDownloadPdf = useCallback(async () => {
    const summaryElement = summaryRef.current;
    if (!summaryElement || !processedData) return;

    setIsDownloading(true);

    try {
      // PART 1: Capture the visual summary as an image
      const dataUrl = await toJpeg(summaryElement, {
        quality: 0.95,
        pixelRatio: 2,
        cacheBust: true
      });

      // PART 2: Programmatically build the PDF
      const doc = new jsPDF('p', 'mm', 'a4');
      const page = {
        width: doc.internal.pageSize.getWidth(),
        height: doc.internal.pageSize.getHeight()
      };
      const margin = 15;
      const contentWidth = page.width - margin * 2;

      // Reusable Header/Footer Functions
      const addHeader = (pdfDoc) => {
        pdfDoc.setFontSize(18);
        pdfDoc.setFont('helvetica', 'bold');
        pdfDoc.text('Performance Analysis Report', margin, margin + 5);
        pdfDoc.setFontSize(10);
        pdfDoc.setFont('helvetica', 'normal');
        pdfDoc.text(`Student: ${processedData.currentTest.student_name}`, margin, margin + 12);
        const testDate = new Date(selectedTestDate).toLocaleDateString('en-US', {
          dateStyle: 'long'
        });
        pdfDoc.text(`Test Date: ${testDate}`, page.width - margin, margin + 12, { align: 'right' });
        pdfDoc.setDrawColor(200, 200, 200);
        pdfDoc.line(margin, margin + 16, page.width - margin, margin + 16);
      };

      const addFooter = (pdfDoc, pageNum, totalPages) => {
        pdfDoc.setFontSize(8);
        pdfDoc.setFont('helvetica', 'italic');
        const genDate = new Date().toLocaleString();
        pdfDoc.text(`Generated on: ${genDate}`, margin, page.height - margin + 5);
        pdfDoc.text(
          `Page ${pageNum} of ${totalPages}`,
          page.width - margin,
          page.height - margin + 5,
          { align: 'right' }
        );
      };

      // Page 1: Add Summary Image
      addHeader(doc);
      const img = new Image();
      img.src = dataUrl;
      await new Promise((resolve) => {
        img.onload = resolve;
      });
      const summaryImgHeight = (img.height * contentWidth) / img.width;
      doc.addImage(dataUrl, 'JPEG', margin, 25, contentWidth, summaryImgHeight);

      // PART 3: Programmatically add the detailed question list
      doc.addPage();
      addHeader(doc);

      let yPosition = 30; // Initial Y position after header
      const pageBottom = page.height - margin - 15;
      let pageNum = 2;

      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Detailed Question Breakdown', margin, yPosition);
      yPosition += 10;

      for (const question of processedData.qBreakdown) {
        const questionTitle = `Question ${question.question_number}: ${question.feedback.core_concept_tested}`;
        const studentAnswer = question.student_final_answer || 'No answer';
        const correctAnswer = question.expert_calculated_answer || 'N/A';
        const explanation = question.expert_method_explanation || 'No explanation provided.';

        // Estimate height needed for the next block and check if it fits
        const textLines = (text, width) => doc.splitTextToSize(text, width).length;
        const estimatedHeight =
          15 +
          textLines(studentAnswer, contentWidth) * 4 +
          textLines(correctAnswer, contentWidth) * 4 +
          textLines(explanation, contentWidth) * 4;

        if (yPosition + estimatedHeight > pageBottom) {
          addFooter(doc, pageNum, 0);
          doc.addPage();
          pageNum++;
          addHeader(doc);
          yPosition = 30;
        }

        // Render Question Block
        doc.setDrawColor(220, 220, 220);
        doc.line(margin, yPosition, page.width - margin, yPosition);
        yPosition += 8;

        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(questionTitle, margin, yPosition);
        yPosition += 8;

        const drawWrappedText = (label, text, color = [0, 0, 0]) => {
          doc.setFontSize(10);
          doc.setFont('helvetica', 'bold');
          doc.setTextColor.apply(null, color);
          doc.text(label, margin, yPosition);
          yPosition += 5;

          doc.setFont('courier', 'normal');
          doc.setTextColor(80, 80, 80);
          const lines = doc.splitTextToSize(text, contentWidth);
          doc.text(lines, margin + 2, yPosition);
          yPosition += lines.length * 4 + 5;
        };

        drawWrappedText(
          'Your Answer:',
          studentAnswer,
          question.is_correct ? [0, 100, 0] : [200, 0, 0]
        );
        drawWrappedText('Correct Answer:', correctAnswer, [0, 100, 0]);
        drawWrappedText('Expert Explanation:', explanation, [0, 0, 0]);

        yPosition += 5;
      }

      // Update total page count on all footers
      const totalPages = doc.internal.getNumberOfPages();
      for (let i = 1; i <= totalPages; i++) {
        doc.setPage(i);
        addFooter(doc, i, totalPages);
      }

      doc.save(
        `report_${processedData.currentTest.student_name.replace(/\s+/g, '_')}_${new Date(selectedTestDate).toISOString().split('T')[0]}.pdf`
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      setRes({ message: 'Could not generate PDF report. Please try again.' });
    } finally {
      setIsDownloading(false);
    }
  }, [selectedTestDate]);

  // --- Data Processing Logic (Memoized) ---
  const processedData = useMemo(() => {
    if (!ocrData || ocrData.length === 0 || !selectedTestDate) return null;

    const currentTest = ocrData.find((test) => test.date === selectedTestDate);
    if (!currentTest) return null;

    const qBreakdown = currentTest.performance_analysis.question_by_question_breakdown;
    const totalQuestions = qBreakdown.length;
    const accuracy =
      totalQuestions > 0
        ? Math.round((qBreakdown.filter((q) => q.is_correct).length / totalQuestions) * 100)
        : 0;
    const errorTypes = qBreakdown.reduce((acc, q) => {
      const type = q.is_correct
        ? 'Correct'
        : q.error_type === 'CALCULATION_MISTAKE'
          ? 'Calculation Error'
          : 'Conceptual Error';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const accuracyGaugeOptions = {
      chart: { type: 'solidgauge', height: '110%' },
      pane: {
        center: ['50%', '85%'],
        size: '100%',
        startAngle: -90,
        endAngle: 90,
        background: {
          backgroundColor: '#EEE',
          innerRadius: '60%',
          outerRadius: '100%',
          shape: 'arc'
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        tickAmount: 2,
        lineWidth: 0,
        minorTickInterval: null,
        labels: { y: 16 },
        title: { text: 'Accuracy', y: -70 }
      },
      plotOptions: {
        solidgauge: {
          dataLabels: {
            y: 5,
            borderWidth: 0,
            useHTML: true,
            format: '<div style="text-align:center"><span style="font-size:25px">{y}%</span></div>'
          }
        }
      },
      series: [
        {
          name: 'Accuracy',
          data: [
            {
              y: accuracy,
              color: accuracy > 80 ? '#22c55e' : accuracy > 60 ? '#f59e0b' : '#ef4444'
            }
          ]
        }
      ]
    };
    const errorAnalysisOptions = {
      chart: { type: 'variablepie' },
      colors: ['#22c55e', '#f59e0b', '#ef4444'],
      series: [
        {
          minPointSize: 10,
          innerSize: '20%',
          zMin: 0,
          name: 'questions',
          data: Object.entries(errorTypes).map(([name, value]) => ({ name, y: value, z: value }))
        }
      ],
      tooltip: { pointFormat: '<b>{point.y}</b> questions ({point.percentage:.1f}%)' }
    };
    const conceptMastery = qBreakdown.reduce((acc, q) => {
      const concept = q.feedback.core_concept_tested;
      if (!acc[concept]) acc[concept] = { correct: 0, incorrect: 0 };
      q.is_correct ? acc[concept].correct++ : acc[concept].incorrect++;
      return acc;
    }, {});
    const conceptMasteryOptions = {
      chart: { type: 'bar' },
      xAxis: {
        categories: Object.keys(conceptMastery),
        title: null,
        labels: { style: { color: '#64748b' } }
      },
      yAxis: {
        min: 0,
        title: { text: 'Number of Questions' },
        gridLineColor: '#e2e8f0',
        reversedStacks: false
      },
      plotOptions: { bar: { stacking: 'normal', borderRadius: 4 } },
      colors: ['#22c55e', '#fca5a5'],
      series: [
        { name: 'Correct', data: Object.values(conceptMastery).map((d) => d.correct) },
        { name: 'Incorrect', data: Object.values(conceptMastery).map((d) => d.incorrect) }
      ]
    };
    const filteredQuestions = qBreakdown.filter((q) => {
      if (questionFilter === 'All') return true;
      if (questionFilter === 'Correct') return q.is_correct;
      if (questionFilter === 'Incorrect') return !q.is_correct;
      return true;
    });

    return {
      currentTest,
      qBreakdown,
      accuracyGaugeOptions,
      errorAnalysisOptions,
      conceptMasteryOptions,
      errorTypes,
      totalQuestions,
      filteredQuestions
    };
  }, [ocrData, selectedTestDate, questionFilter]);

  // --- Loading and Empty States ---
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-slate-100">
        <p className="text-xl font-semibold text-slate-600 animate-pulse">Loading Your Report...</p>
      </div>
    );
  }

  if (!processedData) {
    return (
      <div className="flex h-screen items-center justify-center bg-slate-100 text-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-700">No Reports Found</h2>
          <p className="text-slate-500 mt-2">
            We couldn't find any OCR test results for your account.
          </p>
          <button
            onClick={handleGetStudentOcrApi}
            className="mt-6 bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
            Refresh Data
          </button>
          <Toastify res={res} resClear={() => setRes(null)} />
        </div>
      </div>
    );
  }

  const {
    currentTest,
    accuracyGaugeOptions,
    errorAnalysisOptions,
    conceptMasteryOptions,
    errorTypes,
    totalQuestions,
    filteredQuestions
  } = processedData;

  return (
    <div className="min-h-screen p-4 sm:p-6 lg:p-8 font-sans text-slate-800">
      <Toastify res={res} resClear={() => setRes(null)} />

      <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Performance Report</h1>
          <p className="text-slate-500 mt-1">
            Hello {currentTest.student_name}, here's your detailed analysis.
          </p>
        </div>
        <div className="flex items-center space-x-4 mt-4 sm:mt-0">
          <select
            value={selectedTestDate}
            onChange={(e) => setSelectedTestDate(e.target.value)}
            className="bg-white border border-slate-300 rounded-lg px-4 py-2 text-slate-700 font-semibold shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none">
            {ocrData.map((test) => (
              <option key={test.date} value={test.date}>
                {new Date(test.date).toLocaleDateString('en-US', {
                  month: 'long',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </option>
            ))}
          </select>
          <button
            onClick={handleDownloadPdf}
            disabled={isDownloading}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm disabled:bg-blue-400 disabled:cursor-not-allowed">
            {isDownloading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Downloading...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </>
            )}
          </button>
        </div>
      </header>

      <div className="space-y-6">
        <div ref={summaryRef}>
          <motion.div
            className="grid grid-cols-1 lg:grid-cols-3 gap-6"
            initial="hidden"
            animate="visible"
            variants={{ hidden: {}, visible: { transition: { staggerChildren: 0.05 } } }}>
            <div className="lg:col-span-2 space-y-6">
              <DashboardCard className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
                <div className="">
                  <HighchartsReact highcharts={Highcharts} options={accuracyGaugeOptions} />
                </div>
                <div className="md:col-span-2">
                  <h2 className="text-xl font-bold mb-2">
                    Summary for{' '}
                    {new Date(currentTest.date).toLocaleDateString('en-US', {
                      month: 'long',
                      day: 'numeric'
                    })}
                  </h2>
                  <p className="text-slate-600 text-sm max-w-xl">
                    {
                      currentTest.performance_analysis.overall_feedback_and_strategy
                        .performance_snapshot
                    }
                  </p>
                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <StatCard
                      icon={<CheckCircle2 />}
                      label="Correct"
                      value={`${errorTypes['Correct'] || 0}/${totalQuestions}`}
                      color={{ bg: 'bg-green-100', text: 'text-green-600' }}
                    />
                    <StatCard
                      icon={<AlertTriangle />}
                      label="Calc. Errors"
                      value={errorTypes['Calculation Error'] || 0}
                      color={{ bg: 'bg-amber-100', text: 'text-amber-600' }}
                    />
                    <StatCard
                      icon={<XCircle />}
                      label="Concept Errors"
                      value={errorTypes['Conceptual Error'] || 0}
                      color={{ bg: 'bg-red-100', text: 'text-red-600' }}
                    />
                  </div>
                </div>
              </DashboardCard>
              <DashboardCard>
                <div className="flex items-center text-slate-600 mb-4">
                  <SlidersHorizontal className="w-6 h-6" />
                  <h3 className="text-lg font-semibold ml-2">Concept Mastery</h3>
                </div>
                <div className="h-96">
                  <HighchartsReact highcharts={Highcharts} options={conceptMasteryOptions} />
                </div>
              </DashboardCard>
            </div>
            <div className="lg:col-span-1 space-y-6">
              <DashboardCard>
                <ActionPlan
                  strategy={currentTest.performance_analysis.overall_feedback_and_strategy}
                />
              </DashboardCard>
              <DashboardCard>
                <div className="flex items-center text-slate-600 mb-4">
                  <Target className="w-6 h-6" />
                  <h3 className="text-lg font-semibold ml-2">Error Analysis</h3>
                </div>
                <div className="">
                  <HighchartsReact highcharts={Highcharts} options={errorAnalysisOptions} />
                </div>
              </DashboardCard>
            </div>
          </motion.div>
        </div>

        <DashboardCard>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
            <h3 className="text-lg font-semibold text-slate-800">Detailed Question View</h3>
            <div className="flex items-center space-x-2 mt-3 sm:mt-0 p-1 bg-slate-100 rounded-lg">
              {['All', 'Correct', 'Incorrect'].map((filter) => (
                <button
                  key={filter}
                  onClick={() => setQuestionFilter(filter)}
                  className={clsx('px-3 py-1 text-sm font-semibold rounded-md transition-colors', {
                    'bg-blue-600 text-white shadow': filter === questionFilter,
                    'text-slate-600 hover:bg-slate-200': filter !== questionFilter
                  })}>
                  {filter}
                </button>
              ))}
            </div>
          </div>
          <div className="space-y-3">
            {filteredQuestions.map((q) => (
              <QuestionAccordionItem key={q.question_number} question={q} />
            ))}
            {filteredQuestions.length === 0 && (
              <p className="text-center text-slate-500 py-4">No questions match this filter.</p>
            )}
          </div>
        </DashboardCard>
      </div>
    </div>
  );
};

export default OpticalCharacterRecognition;
