// studentCommunitySlice.js
import { studentCommunityApi } from '../../../../redux/api/api';

// Inject endpoints into existing API
export const studentCommunitySlice = studentCommunityApi.injectEndpoints({
  endpoints: (builder) => ({
    // // === Chat & Message Endpoints ===
    // getCommunityMessages: builder.query({
    //   query: () => '/message',
    //   transformResponse: (response) => {
    //     return Array.isArray(response) ? response : response.data || [];
    //   },
    //   providesTags: ['studentCommunity'],
    //   keepUnusedDataFor: 60
    // }),

    // sendCommunityMessage: builder.mutation({
    //   query: (body) => ({
    //     url: '/community_messages',
    //     method: 'POST',
    //     body: { content: body.content }
    //   }),
    //   invalidatesTags: ['studentCommunity']
    // }),

    // === Thread Endpoints ===
    getThreads: builder.query({
      query: () => '/get_threads',
      transformResponse: (response) => {
        return Array.isArray(response) ? response : response.data || [];
      },
      providesTags: ['studentCommunity']
    }),

    createThread: builder.mutation({
      query: (body) => ({
        url: '/post_threads',
        method: 'POST',
        body: { content: body.content }
      }),
      invalidatesTags: ['studentCommunity']
    }),

    addReply: builder.mutation({
      query: (body) => ({
        url: '/thread/subthread',
        method: 'POST',
        body: { content: body.content, parent_id: body.parent_id }
      }),
      invalidatesTags: ['studentCommunity']
    }),

    // === Image Gallery Endpoints ===
    getCommunityImages: builder.query({
      query: () => '/community_get_images',
      transformResponse: (response) => {
        return Array.isArray(response) ? response : response.data || [];
      },
      providesTags: ['studentCommunity']
    }),

    // NEW: Get single image by ID (with threads)
    getCommunityImageById: builder.query({
      query: (imageId) => `/api/get_community_images/${imageId}`,
      providesTags: ['studentCommunity']
    }),

    uploadCommunityImage: builder.mutation({
      query: (formData) => ({
        url: '/community_images',
        method: 'POST',
        body: formData
      }),
      invalidatesTags: ['studentCommunity']
    }),

    // NEW: Add thread to an image
    addThreadToImage: builder.mutation({
      query: ({ imageId, content }) => ({
        url: `/api/post_community_images/${imageId}/thread`,
        method: 'POST',
        body: { content }
      }),
      invalidatesTags: ['studentCommunity']
    }),

    // NEW: Add reply to a thread on an image
    addReplyToImageThread: builder.mutation({
      query: ({ imageId, parentThreadId, content }) => ({
        url: `/api/community_images/${imageId}/thread/${parentThreadId}/reply`,
        method: 'POST',
        body: { content }
      }),
      invalidatesTags: ['studentCommunity']
    })
  })
});

// Export hooks
export const {
  useGetCommunityMessagesQuery,
  useGetThreadsQuery,
  useGetCommunityImagesQuery,
  useSendCommunityMessageMutation,
  useCreateThreadMutation,
  useAddReplyMutation,
  useUploadCommunityImageMutation,

  // Newly exported hooks
  useGetCommunityImageByIdQuery,
  useAddThreadToImageMutation,
  useAddReplyToImageThreadMutation
} = studentCommunitySlice;

export default studentCommunitySlice;
