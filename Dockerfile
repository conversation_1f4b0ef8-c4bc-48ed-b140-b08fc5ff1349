# # Build stage
# FROM node:18 AS builder
# WORKDIR /app

# # Copy dependency descriptors
# COPY package.json package-lock.json ./

# # Install dependencies using npm
# RUN npm install --force

# # Copy all remaining source code
# COPY . .

# # Build Vite project
# RUN npm run build


# # Serve stage
# FROM node:alpine
# WORKDIR /app

# # Install 'serve' globally to serve static assets
# RUN npm install -g serve

# # Copy built frontend from builder stage
# COPY --from=builder /app/dist ./dist

# EXPOSE 5173
# CMD ["serve", "-s", "dist", "-l", "5173"]
# --- Build Stage ---
    FROM node:18 AS builder
    WORKDIR /app
    
    # Accept build mode (default = production)
    ARG MODE=production
    ENV MODE=$MODE
    
    # Copy package files first for caching
    COPY package.json package-lock.json ./
    
    # Install all dependencies (including devDependencies for build)
    RUN npm install --production=false --force
    
    # Copy the rest of the source code
    COPY . .
    
    # Log env details and run build
    RUN echo "============================" && \
        echo "ðŸ”¹ Building in MODE=$MODE" && \
        echo "ðŸ”¹ Using environment file: .env.$MODE" && \
        if [ -f ".env.$MODE" ]; then cat .env.$MODE; else echo "âš  No .env.$MODE file found"; fi && \
        echo "ðŸ”¹ Environment variables passed to Vite:" && \
        env | grep '^VITE_' || echo "No VITE_ variables found" && \
        echo "============================" && \
        npm run build -- --mode $MODE
    
    # --- Serve Stage ---
    FROM node:alpine
    WORKDIR /app
    
    RUN npm install -g serve
    
    # Copy only the built files
    COPY --from=builder /app/dist ./dist
    
    # Expose Vite default port
    EXPOSE 5173
    
    # Serve the built app
    CMD ["serve", "-s", "dist", "-l", "5173"]
    
    