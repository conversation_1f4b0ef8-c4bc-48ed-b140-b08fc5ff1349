// src/components/student-community/GalleryTab.jsx
import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const GalleryTab = ({
  images = [],
  loadingImages,
  selectedImage,
  setSelectedImage,
  fullImage,
  refetchFullImage,
  user,
  addThreadToImage,
  uploadImage,
  handleDownload
}) => {
  const [newReply, setNewReply] = useState('');
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [uploadPreview, setUploadPreview] = useState('');
  const [uploadDescription, setUploadDescription] = useState('');
  const fileInputRef = useRef(null);
  const messagesEndRef = useRef();
  const rolee = sessionStorage.getItem('role');
  const [isImageExpanded, setIsImageExpanded] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Handle file selection
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadFile(file);
      setUploadPreview(URL.createObjectURL(file));
      setIsUploadOpen(true);
    }
  };

  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === 'Escape') setIsImageExpanded(false);
    };
    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, []);

  // Handle upload
  const handleUpload = async () => {
    if (!uploadFile || isUploading) return;

    setIsUploading(true);
    const formData = new FormData();
    formData.append('file', uploadFile);
    if (uploadDescription.trim()) formData.append('description', uploadDescription.trim());

    try {
      await uploadImage(formData).unwrap();

      // Reset all states
      setUploadFile(null);
      setUploadPreview('');
      setUploadDescription('');
      setIsUploadOpen(false);
      setIsUploading(false);

      if (fileInputRef.current) fileInputRef.current.value = '';
    } catch (err) {
      console.error('Upload failed:', err);
      alert('Upload failed. Please try again.');
      setIsUploading(false);
    }
  };

  // Handle reply
  const handleReply = async () => {
    if (!newReply.trim() || !selectedImage) return;
    try {
      await addThreadToImage({
        imageId: selectedImage.id,
        content: newReply.trim()
      }).unwrap();
      setNewReply('');
      refetchFullImage();
    } catch (err) {
      console.error('Failed to send reply:', err);
      alert('Could not send reply.');
    }
  };

  // Auto-scroll to latest reply
  useEffect(() => {
    if (fullImage?.threads?.length > 0) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [fullImage?.threads]);

  return (
    <div className="flex flex-col lg:flex-row h-[calc(100vh-80px)] bg-gray-50 overflow-hidden">
      {/* === LEFT PANEL: Creative Image Grid === */}
      <div className="w-full lg:w-1/3 bg-white border-b lg:border-b-0 lg:border-r border-gray-200 p-4 overflow-y-auto">
        {/* Header with Floating Label */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-5 gap-4 sm:gap-0">
          <h3 className="font-bold text-gray-800 flex items-center gap-2 text-lg">
            <motion.span
              animate={{ rotate: [0, 5, -5, 0], scale: [1, 1.1, 1] }}
              transition={{ repeat: Infinity, duration: 4, ease: 'easeInOut' }}
              className="text-2xl">
              🎓
            </motion.span>
            <span className="bg-gradient-to-r from-indigo-600 to-blue-500 bg-clip-text text-transparent font-bold">
              My Gallery
            </span>
          </h3>

          {/* Upload Button – Floating & Magnetic */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.05 }}
            onClick={() => fileInputRef.current?.click()}
            className="px-5 py-3 bg-gradient-to-r hover:cursor-pointer from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-medium rounded-full shadow-lg transition-all duration-300 relative group flex items-center gap-2"
            aria-label="Upload a note or doubt">
            {/* Upload Text */}
            <span className="font-semibold text-sm whitespace-nowrap">Upload Image</span>

            {/* Floating Sparkle */}
            <motion.span
              animate={{ scale: [1, 1.4, 1], opacity: [0.7, 1, 0.7] }}
              transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
              className="text-lg">
              📸
            </motion.span>

            {/* Tooltip on Hover */}
            <motion.div
              className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1.5 px-3 rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition pointer-events-none z-10"
              initial={{ y: 10 }}
              animate={{ y: 0 }}>
              Share your doubt, solution, or note
            </motion.div>
          </motion.button>
          <input
            type="file"
            accept="image/*"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
          />
        </motion.div>

        {/* Upload Preview Modal */}
        <AnimatePresence>
          {isUploadOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 300, damping: 25 }}
              className="mb-5 p-5 bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-indigo-200">
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-1">
                📤 Ready to Share?
              </h4>
              <img
                src={uploadPreview}
                alt="Preview"
                className="w-full h-32 object-cover rounded-xl mb-3"
              />
              <textarea
                placeholder="Add a description (e.g., 'Doubt in Electrostatics')"
                value={uploadDescription}
                onChange={(e) => setUploadDescription(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-300 text-sm resize-none"
                rows="2"
              />
              <div className="flex gap-2 mt-3">
                {/* Cancel Button – Still works during upload */}
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    if (isUploading) return; // Prevent cancel during upload?
                    setIsUploadOpen(false);
                    setUploadFile(null);
                    setUploadPreview('');
                    setUploadDescription('');
                    if (fileInputRef.current) fileInputRef.current.value = '';
                  }}
                  disabled={isUploading}
                  className="flex-1 py-2 text-gray-600 hover:text-gray-800 bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-200 text-sm font-medium border border-gray-200 disabled:opacity-60 disabled:cursor-not-allowed">
                  🛑 Cancel
                </motion.button>

                {/* Post Button – With Loading State */}
                <motion.button
                  type="button"
                  whileHover={
                    isUploading
                      ? {}
                      : { scale: 1.05, boxShadow: '0 0 15px rgba(59, 130, 246, 0.4)' }
                  }
                  whileTap={isUploading ? {} : { scale: 0.95 }}
                  onClick={handleUpload}
                  disabled={isUploading}
                  className={`flex-1 text-white py-2 rounded-lg transition-all duration-300 text-sm font-semibold flex items-center justify-center gap-1.5 group
      ${
        isUploading
          ? 'bg-gradient-to-r from-indigo-400 to-blue-500 cursor-not-allowed'
          : 'bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700'
      }`}>
                  {isUploading ? (
                    <>
                      {/* Spinner */}
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Uploading...</span>
                    </>
                  ) : (
                    <>
                      {/* Rocket with Pulse */}
                      <motion.span
                        className="text-base"
                        animate={{ y: [-2, 0, -2] }}
                        transition={{ repeat: Infinity, duration: 1.5, ease: 'easeInOut' }}>
                        🚀
                      </motion.span>
                      <span>Post</span>
                      {/* Star Trail */}
                      <div className="relative w-2 h-2 overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <motion.div
                          className="absolute top-0 left-0 w-1 h-1 bg-yellow-200 rounded-full"
                          animate={{ y: [-20, -40], opacity: [1, 0] }}
                          transition={{ duration: 0.8, repeat: Infinity, delay: 0 }}
                        />
                      </div>
                    </>
                  )}
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Image Grid – Card Stack with Depth */}
        {loadingImages ? (
          <div className="flex justify-center py-8">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ repeat: Infinity, duration: 1.5, ease: 'linear' }}
              className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full"
            />
          </div>
        ) : !images.length ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-10 text-gray-500">
            <div className="text-5xl mb-3 animate-bounce">📸</div>
            <p className="text-sm text-gray-500">No images yet. Be the first!</p>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-2">
            {' '}
            {/* Responsive grid */}
            <AnimatePresence initial={false}>
              {images.map((img) => (
                <motion.div
                  key={img.id}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  whileHover="hover"
                  transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                  className={`cursor-pointer rounded-xl overflow-hidden transition-all relative ${
                    selectedImage?.id === img.id
                      ? 'ring-2 ring-indigo-500 shadow-lg scale-105 z-20'
                      : 'hover:shadow-md hover:ring-1 hover:ring-indigo-300'
                  }`}
                  onClick={() => setSelectedImage(img)}
                  variants={{
                    hover: { y: -2, scale: 1.02 }
                  }}>
                  {/* Image */}
                  <div className="aspect-square">
                    <img
                      src={img.cloudfront_url}
                      alt={img.description || 'Community image'}
                      className="w-full h-full object-cover"
                    />
                    {/* Gradient overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/20 to-transparent opacity-0 group-hover:opacity-80 transition-opacity"></div>
                  </div>

                  {/* Compact Info Bar */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2 text-white text-xs leading-tight">
                    <p className="font-semibold truncate">
                      {img.first_name || 'You'} {img.uploader_type}
                    </p>
                    <p className="opacity-90 truncate">{img.description || 'Note'}</p>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* === RIGHT PANEL: Compact & Chat-First === */}
      <div className="flex-1 flex flex-col bg-gradient-to-br from-indigo-50 to-white">
        {selectedImage ? (
          <>
            {/* Compact Image Header - Click to View, Not Download */}
            <div
              className="p-3 border-b border-gray-200 bg-white cursor-pointer group"
              onClick={() => setIsImageExpanded(true)} // ✅ Opens Lightbox
              title="Click to view full image">
              <div className="flex items-center gap-3">
                {/* Image Preview */}
                <div className="relative flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden ring-2 ring-transparent group-hover:ring-indigo-300 transition">
                  <img
                    src={fullImage?.cloudfront_url || selectedImage.cloudfront_url}
                    alt="Preview"
                    className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                  />
                  {/* Hover View Icon */}
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition flex items-center justify-center">
                    <span className="text-white text-xs font-bold">👁️</span>
                  </div>
                </div>

                {/* Image Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-800 truncate">
                    {fullImage?.first_name || selectedImage.first_name || 'Anonymous'} {selectedImage.uploader_type}
                  </p>
                  <p className="text-xs text-gray-500">
                    {new Date(
                      fullImage?.created_at || selectedImage.created_at
                    ).toLocaleDateString()}
                  </p>
                  {fullImage?.description && (
                    <p
                      className="text-xs text-gray-500 truncate mt-1"
                      title={fullImage.description}>
                      “{fullImage.description}”
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* === LIGHTBOX: Full-Screen View === */}
            {isImageExpanded && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black/90"
                onClick={() => setIsImageExpanded(false)} // Close on backdrop click
              >
                <motion.div
                  initial={{ scale: 0.9, y: 20 }}
                  animate={{ scale: 1, y: 0 }}
                  exit={{ scale: 0.9, y: 20 }}
                  className="relative max-w-4xl w-full max-h-full p-4"
                  onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
                >
                  {/* Close Button */}
                  <button
                    onClick={() => setIsImageExpanded(false)}
                    className="absolute -top-12 right-0 hover:cursor-pointer text-white bg-gray-900 hover:bg-black rounded-full w-10 h-10 flex items-center justify-center text-xl transition">
                    ✕
                  </button>

                  {/* Full Image */}
                  <img
                    src={fullImage?.cloudfront_url || selectedImage.cloudfront_url}
                    alt="Full view"
                    className="max-w-full max-h-[80vh] object-contain rounded-xl shadow-2xl"
                  />

                  {/* Image Info + Action Buttons */}
                  <div className="absolute bottom-4 left-4 right-4 bg-black/60 text-white p-4 rounded-lg text-sm">
                    <p className="font-semibold">
                      {fullImage?.first_name || selectedImage.first_name || 'Anonymous'}{' '}
                      {fullImage?.uploader_type || selectedImage.uploader_type}
                    </p>
                    {fullImage?.description && (
                      <p className="italic mt-1">“{fullImage.description}”</p>
                    )}
                    <div className="flex justify-end gap-2 mt-2">
                      {/* Download Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(fullImage?.cloudfront_url || selectedImage.cloudfront_url);
                        }}
                        className="bg-indigo-600 hover:bg-indigo-700 hover:cursor-pointer text-white px-3 py-1.5 rounded-full text-xs font-medium transition flex items-center gap-1">
                        ⬇️ Download
                      </button>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            )}

            {/* Messages – Maximized Chat Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              <AnimatePresence initial={false}>
                {Array.isArray(fullImage?.threads) && fullImage.threads.length === 0 ? (
                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-gray-500 text-sm text-center py-4 italic">
                    💬 Be the first to comment!
                  </motion.p>
                ) : Array.isArray(fullImage?.threads) ? (
                  fullImage.threads.map((reply) => (
                    <motion.div
                      key={reply.id}
                      initial={{ opacity: 0, y: 15 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      className={`flex ${
                        reply.sender_id === user?.id ? 'justify-end' : 'justify-start'
                      }`}>
                      <div
                        className={`max-w-[80%] sm:max-w-xs px-4 py-2.5 rounded-2xl text-sm ${
                          reply.sender_id === user?.id
                            ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-tr-none shadow-md'
                            : 'bg-white text-gray-800 rounded-tl-none shadow border border-gray-200'
                        }`}>
                        <div className="font-semibold text-xs opacity-80">
                          {reply.first_name} {reply.sender_type}
                        </div>
                        {reply.content}
                      </div>
                    </motion.div>
                  ))
                ) : (
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-gray-500 text-sm text-center py-4">
                    Loading replies...
                  </motion.p>
                )}
              </AnimatePresence>
              <div ref={messagesEndRef} style={{ height: 0 }} />
            </div>

            {/* Reply Input */}
            <div className="p-4 border-t bg-white">
              <div className="flex gap-2">
                <motion.input
                  type="text"
                  value={newReply}
                  onChange={(e) => setNewReply(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleReply()}
                  placeholder="Reply to this image..."
                  className="flex-1 p-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-300 focus:border-indigo-500 text-sm transition-all"
                  whileFocus={{ scale: 1.01 }}
                />
                <motion.button
                  onClick={handleReply}
                  disabled={!newReply.trim()}
                  whileTap={{ scale: 0.95 }}
                  className="bg-indigo-600 disabled:bg-gray-400 text-white p-3 rounded-full hover:bg-indigo-700 transition">
                  <motion.svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    animate={{ x: [0, 2, 0] }}
                    transition={{ repeat: Infinity, duration: 1.5, delay: 0.5 }}>
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                    />
                  </motion.svg>
                </motion.button>
              </div>
            </div>
          </>
        ) : (
          /* Empty State */
          <div className="flex-1 flex items-center justify-center text-gray-500 p-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center max-w-md">
              <motion.div
                animate={{ rotateY: 360, scale: [1, 1.05, 1] }}
                transition={{ repeat: Infinity, duration: 5, ease: 'easeInOut' }}
                className="text-6xl mb-4">
                🖼️
              </motion.div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-blue-500 bg-clip-text text-transparent mb-2">
                Your Learning Gallery
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Select an image to discuss, solve, or upload to share with peers.
              </p>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GalleryTab;
