import React, { useState, useEffect } from 'react';

const SlideContent = ({ 
  processSelectorId, 
  onSlideDataChange, 
  onCurrentSlideIndexChange 
}) => {
  const [slideData, setSlideData] = useState([]);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  // Fetch MongoDB Slide Content
  useEffect(() => {
    if (processSelectorId) {
      console.log(`Fetching MongoDB data for processSelectorId: ${processSelectorId}`);
      const controller = new AbortController();
       
      const role = sessionStorage.getItem('role'); 

      fetch(`${import.meta.env.VITE_BASE_URL}/api/get-mongo-data`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ process_selector_id: processSelectorId , role: role}),
        signal: controller.signal
      })
        .then((res) => {
          if (!res.ok) throw new Error(`Request failed with status ${res.status}`);
          return res.json();
        })
        .then((data) => {
          console.log('MongoDB response:', data);
          if (data.data && data.data.slides) {
            const slidesArray = [];
            const slidesObj = data.data.slides;
            const sortedSlideKeys = Object.keys(slidesObj).sort((a, b) => {
              const numA = parseInt(a.replace('slide', ''));
              const numB = parseInt(b.replace('slide', ''));
              return numA - numB;
            });

            sortedSlideKeys.forEach((key) => {
              if (slidesObj[key]) {
                slidesArray.push(slidesObj[key]);
              }
            });

            setSlideData(slidesArray);
            setCurrentSlideIndex(0);
            onSlideDataChange(slidesArray);
            onCurrentSlideIndexChange(0);
            console.log(`Loaded ${slidesArray.length} slides from MongoDB.`);
          } else if (data.data && data.data.narration) {
            const narrationArray = [data.data.narration];
            setSlideData(narrationArray);
            setCurrentSlideIndex(0);
            onSlideDataChange(narrationArray);
            onCurrentSlideIndexChange(0);
            console.log('Loaded single narration from MongoDB.');
          } else {
            setSlideData([]);
            onSlideDataChange([]);
            console.log('No slides or narration found in MongoDB response.');
          }
        })
        .catch((err) => {
          if (err.name !== 'AbortError') {
            console.error('❌ Error fetching MongoDB data:', err);
            setSlideData([]);
            onSlideDataChange([]);
          }
        });
      return () => {
        console.log('Aborting MongoDB fetch request.');
        controller.abort();
      };
    }
  }, [processSelectorId, onSlideDataChange, onCurrentSlideIndexChange]);

  const updateSlideIndex = (newIndex) => {
    setCurrentSlideIndex(newIndex);
    onCurrentSlideIndexChange(newIndex);
  };

  const getCurrentSlideContent = (pageNumber) => {
    const slideIndex = pageNumber - 1;
    return slideData[slideIndex] || '';
  };

  return {
    slideData,
    currentSlideIndex,
    updateSlideIndex,
    getCurrentSlideContent
  };
};

export default SlideContent;