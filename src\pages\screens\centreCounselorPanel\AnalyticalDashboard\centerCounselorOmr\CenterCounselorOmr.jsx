import React, { useCallback, useEffect, useState, useMemo, memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import clsx from 'clsx';

// --- Redux & API ---
import Toastify from '../../../../../components/PopUp/Toastify';
import {
  clearCenterOmrData,
  setCenterOmrData,
  useLazyGetCenterOmrDataQuery
} from '../AnalyticalDashboard.slice';

// --- Lucide Icons ---
import {
  BarChart2,
  Percent,
  TrendingUp,
  Search,
  ChevronDown,
  X,
  BookOpen,
  PieChart,
  CheckCircle2,
  XCircle,
  HelpCircle,
  Trophy,
  ChevronsRight,
  User,
  CalendarDays
} from 'lucide-react';

// --- Highcharts Theme (Amber Light) ---
Highcharts.setOptions({
  chart: { style: { fontFamily: '"Inter", sans-serif' }, backgroundColor: 'transparent' },
  title: { style: { display: 'none' } },
  credits: { enabled: false },
  legend: { itemStyle: { color: '#78350f' }, itemHoverStyle: { color: '#000000' } }, // amber-900
  xAxis: { labels: { style: { color: '#92400e' } }, lineColor: '#fde68a', tickColor: '#fde68a' }, // amber-800, amber-200
  yAxis: {
    gridLineColor: '#fef3c7',
    labels: { style: { color: '#92400e' } },
    title: { style: { color: '#92400e' } }
  }, // amber-50
  tooltip: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderColor: '#fcd34d',
    style: { color: '#78350f' }
  } // amber-300
});

// --- Reusable UI Components (Amber Light Theme) ---

const DashboardCard = memo(({ children, className = '' }) => (
  <div
    className={clsx(
      'rounded-2xl bg-white/50 backdrop-blur-md border border-amber-300/60 shadow-lg',
      className
    )}>
    {children}
  </div>
));

const StatCard = memo(
  ({
    icon,
    label,
    value,
    valueColor = 'bg-gradient-to-r from-amber-500 to-orange-600 bg-clip-text text-transparent'
  }) => (
    <DashboardCard className="p-5">
      <div className="flex items-center space-x-4">
        <div className="p-3 bg-amber-100 rounded-xl text-amber-500">{icon}</div>
        <div>
          <p className="text-amber-800 text-sm font-medium">{label}</p>
          <p className={`text-2xl font-bold ${valueColor}`}>{value}</p>
        </div>
      </div>
    </DashboardCard>
  )
);

// --- Main Component ---

const CenterCounselorOmr = () => {
  // --- State & Redux ---
  const [res, setRes] = useState(null);
  const [getCenterStudentOmr, { isLoading }] = useLazyGetCenterOmrDataQuery();
  const dispatch = useDispatch();
  const omrData = useSelector((state) => state.centreCounselorDashboard.centerOmrData);

  // --- UI State ---
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name-asc');
  const [selectedTest, setSelectedTest] = useState(null);
  const [expandedStudentId, setExpandedStudentId] = useState(null);

  // --- API Fetch & Debounce ---
  useEffect(() => {
    if (!omrData || omrData.length === 0) {
      getCenterStudentOmr({ center_code: sessionStorage.centercode })
        .unwrap()
        .then((data) => dispatch(setCenterOmrData(data)))
        .catch((error) => {
          setRes(error.data || { message: 'Failed to fetch OMR data.' });
          dispatch(clearCenterOmrData());
        });
    }
  }, [getCenterStudentOmr, dispatch, omrData]);

  useEffect(() => {
    const timerId = setTimeout(() => setDebouncedSearchTerm(searchTerm), 500);
    return () => clearTimeout(timerId);
  }, [searchTerm]);

  // --- Memoized Data Grouping and Processing ---
  const processedData = useMemo(() => {
    if (!omrData || omrData.length === 0) return null;

    // 1. Center-wide stats
    const totalTests = omrData.length;
    const averageScore = omrData.reduce((sum, test) => sum + test.percentage, 0) / totalTests;
    const topPerformer = omrData.reduce(
      (top, test) => (test.percentage > top.percentage ? test : top),
      omrData[0]
    );

    // 2. Group tests by student_id
    const studentsMap = omrData.reduce((acc, test) => {
      if (!acc[test.student_id]) {
        acc[test.student_id] = {
          student_id: test.student_id,
          student_name: test.student_name,
          tests: []
        };
      }
      acc[test.student_id].tests.push(test);
      return acc;
    }, {});

    // 3. Convert map to array and calculate aggregate data for each student
    let studentGroupedData = Object.values(studentsMap).map((student) => {
      const testCount = student.tests.length;
      const totalScore = student.tests.reduce((sum, t) => sum + t.percentage, 0);
      const average_score = totalScore / testCount;
      const latest_test_date = student.tests.reduce(
        (latest, t) => (new Date(t.evaluated_at) > new Date(latest) ? t.evaluated_at : latest),
        student.tests[0].evaluated_at
      );

      return { ...student, test_count: testCount, average_score, latest_test_date };
    });

    // 4. Filter and Sort the grouped student data
    if (debouncedSearchTerm) {
      studentGroupedData = studentGroupedData.filter((student) =>
        student.student_name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      );
    }

    studentGroupedData.sort((a, b) => {
      switch (sortBy) {
        case 'score-desc':
          return b.average_score - a.average_score;
        case 'score-asc':
          return a.average_score - b.average_score;
        case 'tests-desc':
          return b.test_count - a.test_count;
        case 'tests-asc':
          return a.test_count - b.test_count;
        case 'date-desc':
          return new Date(b.latest_test_date) - new Date(a.latest_test_date);
        case 'date-asc':
          return new Date(a.latest_test_date) - new Date(b.latest_test_date);
        case 'name-desc':
          return b.student_name.localeCompare(a.student_name);
        default:
          return a.student_name.localeCompare(b.student_name); // name-asc
      }
    });

    // 5. Chart Data (unchanged)
    const scoreBins = { '0-20': 0, '21-40': 0, '41-60': 0, '61-80': 0, '81-100': 0 };
    omrData.forEach((test) => {
      if (test.percentage <= 20) scoreBins['0-20']++;
      else if (test.percentage <= 40) scoreBins['21-40']++;
      else if (test.percentage <= 60) scoreBins['41-60']++;
      else if (test.percentage <= 80) scoreBins['61-80']++;
      else scoreBins['81-100']++;
    });
    const scoreDistributionOptions = {
      chart: { type: 'column' },
      xAxis: { categories: Object.keys(scoreBins), title: { text: 'Score Range (%)' } },
      yAxis: { min: 0, title: { text: 'Number of Tests' } },
      legend: { enabled: false },
      series: [{ name: 'Tests', data: Object.values(scoreBins), color: '#f59e0b' }]
    };

    return {
      stats: { totalTests, averageScore: averageScore.toFixed(1), topPerformer },
      charts: { scoreDistributionOptions },
      tableData: studentGroupedData
    };
  }, [omrData, debouncedSearchTerm, sortBy]);

  const handleRowClick = (studentId) => {
    setExpandedStudentId((prevId) => (prevId === studentId ? null : studentId));
  };

  // --- Render Logic ---
  if (isLoading && !processedData) {
    return <div className="p-8 text-center text-amber-700">Loading OMR Analytics...</div>;
  }

  if (!processedData) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold text-amber-900">No OMR Data Found</h2>
        <p className="mt-2 text-amber-700">
          No OMR test results are available for this center yet.
        </p>
      </div>
    );
  }

  const { stats, charts, tableData } = processedData;

  return (
    <div className="min-h-full p-4 sm:p-6 font-sans">
      <Toastify res={res} resClear={() => setRes(null)} />
      <header className="mb-8">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
          OMR Center Analytics
        </h1>
        <p className="text-amber-700 mt-1">
          Student performance overview for center: {sessionStorage.centercode}
        </p>
      </header>

      <motion.div
        className="space-y-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            icon={<BookOpen className="h-6 w-6" />}
            label="Total Tests Taken"
            value={stats.totalTests}
          />
          <StatCard
            icon={<Percent className="h-6 w-6" />}
            label="Center Average Score"
            value={`${stats.averageScore}%`}
          />
          <StatCard
            icon={<TrendingUp className="h-6 w-6" />}
            label="Highest Score"
            value={`${stats.topPerformer.percentage}%`}
          />
          <StatCard
            icon={<Trophy className="h-6 w-6" />}
            label="Top Performer"
            value={stats.topPerformer.student_name}
          />
        </div>

        <DashboardCard className="p-6">
          <h3 className="text-lg font-semibold text-amber-900 mb-4 flex items-center">
            <BarChart2 className="w-5 h-5 mr-2 text-amber-600" />
            Score Distribution
          </h3>
          <HighchartsReact highcharts={Highcharts} options={charts.scoreDistributionOptions} />
        </DashboardCard>

        <DashboardCard className="p-0 overflow-hidden">
          <div className="p-4 border-b border-amber-300/60">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <h2 className="text-xl font-semibold text-amber-900">Student Performance</h2>
              <div className="flex items-center gap-2">
                <FilterInput
                  icon={<Search size={16} />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search students..."
                />
                <FilterSelect
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  options={[
                    { value: 'name-asc', label: 'Sort by Name (A-Z)' },
                    { value: 'name-desc', label: 'Sort by Name (Z-A)' },
                    { value: 'score-desc', label: 'Avg Score (High-Low)' },
                    { value: 'score-asc', label: 'Avg Score (Low-High)' },
                    { value: 'tests-desc', label: 'Most Tests' },
                    { value: 'tests-asc', label: 'Fewest Tests' },
                    { value: 'date-desc', label: 'Most Recent' }
                  ]}
                />
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-amber-200/40">
                <tr>
                  <th className="p-4 w-12"></th>
                  <th className="p-4 text-xs font-semibold text-amber-700 uppercase">
                    Student Name
                  </th>
                  <th className="p-4 text-xs font-semibold text-amber-700 uppercase">
                    Tests Taken
                  </th>
                  <th className="p-4 text-xs font-semibold text-amber-700 uppercase">
                    Average Score
                  </th>
                  <th className="p-4 text-xs font-semibold text-amber-700 uppercase">
                    Last Test Date
                  </th>
                </tr>
              </thead>
              <tbody>
                {tableData.map((student) => (
                  <StudentRow
                    key={student.student_id}
                    student={student}
                    isExpanded={expandedStudentId === student.student_id}
                    onRowClick={handleRowClick}
                    onTestClick={setSelectedTest}
                  />
                ))}
              </tbody>
            </table>
            {tableData.length === 0 && (
              <div className="text-center py-16 text-amber-700">
                No students found for your search criteria.
              </div>
            )}
          </div>
        </DashboardCard>
      </motion.div>

      <AnimatePresence>
        {selectedTest && (
          <TestDetailModal test={selectedTest} onClose={() => setSelectedTest(null)} />
        )}
      </AnimatePresence>
    </div>
  );
};

// --- Child Components ---

const StudentRow = ({ student, isExpanded, onRowClick, onTestClick }) => {
  return (
    <>
      <tr
        onClick={() => onRowClick(student.student_id)}
        className="border-b border-amber-200 hover:bg-amber-100/70 cursor-pointer transition-colors">
        <td className="p-4 text-center">
          <motion.div animate={{ rotate: isExpanded ? 90 : 0 }} className="text-amber-500">
            <ChevronsRight size={18} />
          </motion.div>
        </td>
        <td className="p-4 font-medium text-amber-900 flex items-center gap-3">
          <div className="w-9 h-9 rounded-full flex-shrink-0 flex items-center justify-center font-bold text-sm bg-gradient-to-br from-amber-500 to-orange-500 text-white">
            {student.student_name.charAt(0).toUpperCase()}
          </div>
          {student.student_name}
        </td>
        <td className="p-4 text-amber-800 font-medium">{student.test_count}</td>
        <td className="p-4 text-amber-800 font-semibold">
          {student.average_score.toFixed(1)}%
        </td>
        <td className="p-4 text-amber-700">
          {new Date(student.latest_test_date).toLocaleDateString()}
        </td>
      </tr>
      <AnimatePresence>
        {isExpanded && (
          <tr className="bg-amber-50/50">
            <td colSpan="5" className="p-0">
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="overflow-hidden p-4">
                <h4 className="font-semibold text-amber-800 mb-2 ml-2">Test History:</h4>
                <div className="space-y-2">
                  {student.tests
                    .sort((a, b) => new Date(b.evaluated_at) - new Date(a.evaluated_at))
                    .map((test) => (
                      <div
                        key={test._id}
                        onClick={() => onTestClick(test)}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-amber-100 cursor-pointer">
                        <div className="flex items-center gap-3">
                          <CalendarDays size={16} className="text-amber-600" />
                          <div>
                            <p className="font-medium text-amber-900">{test.subject}</p>
                            <p className="text-xs text-amber-700">
                              {new Date(test.evaluated_at).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-lg text-orange-600">
                            {test.percentage}%
                          </p>
                          <p className="text-xs text-amber-700">
                            {test.score}/{test.total_questions} correct
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              </motion.div>
            </td>
          </tr>
        )}
      </AnimatePresence>
    </>
  );
};

const FilterInput = ({ icon, ...props }) => (
  <div className="relative">
    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-amber-600">
      {icon}
    </div>
    <input
      type="text"
      {...props}
      className="bg-white/70 border border-amber-400/50 text-amber-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block w-full pl-10 p-2.5 placeholder:text-amber-600"
    />
  </div>
);

const FilterSelect = ({ options, ...props }) => (
  <div className="relative">
    <select
      {...props}
      className="appearance-none w-full bg-white/70 border border-amber-400/50 text-amber-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block p-2.5 pr-8">
      {options.map((o) => (
        <option key={o.value} value={o.value}>
          {o.label}
        </option>
      ))}
    </select>
    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-amber-600">
      <ChevronDown size={16} />
    </div>
  </div>
);

// The Modal component remains largely the same, but styled for light theme
const TestDetailModal = ({ test, onClose }) => {
  // ... analysis logic is the same ...
  const analysis = useMemo(() => {
    const correct = test.details.filter((d) => d.is_correct).length;
    const incorrect = test.details.filter((d) => d.student_answer && !d.is_correct).length;
    const unattempted = test.total_questions - correct - incorrect;
    const breakdownOptions = {
      chart: { type: 'pie' },
      colors: ['#22c55e', '#ef4444', '#9ca3af'],
      plotOptions: {
        pie: {
          innerSize: '60%',
          dataLabels: { enabled: false },
          showInLegend: true,
          borderWidth: 0
        }
      },
      legend: { itemStyle: { color: '#78350f' } },
      series: [
        {
          name: 'Answers',
          data: [
            { name: 'Correct', y: correct },
            { name: 'Incorrect', y: incorrect },
            { name: 'Unattempted', y: unattempted }
          ]
        }
      ]
    };
    return { breakdownOptions, correct, incorrect, unattempted };
  }, [test]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}>
      <motion.div
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        transition={{ type: 'spring', stiffness: 300, damping: 25 }}
        className="bg-amber-50 border border-amber-300 rounded-2xl w-full max-w-4xl max-h-[90vh] flex flex-col shadow-2xl"
        onClick={(e) => e.stopPropagation()}>
        <header className="flex justify-between items-center p-4 border-b border-amber-200">
          <div>
            <h2 className="text-xl font-bold text-amber-900">{test.student_name}'s Report</h2>
            <p className="text-sm text-amber-700">
              {test.subject} - {new Date(test.evaluated_at).toLocaleDateString()}
            </p>
          </div>
          <button onClick={onClose} className="p-2 rounded-full hover:bg-amber-200/50">
            <X className="w-6 h-6 text-amber-600" />
          </button>
        </header>
        <div className="p-6 overflow-y-auto grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1 space-y-6">
            <DashboardCard className="p-4">
              <h3 className="text-lg font-semibold text-amber-900 mb-4 flex items-center">
                <PieChart className="w-5 h-5 mr-2 text-amber-600" />
                Answer Breakdown
              </h3>
              <HighchartsReact highcharts={Highcharts} options={analysis.breakdownOptions} />
            </DashboardCard>
            <DashboardCard className="p-4 space-y-3 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-amber-700">Total Score:</span>
                <span className="font-bold text-orange-600 text-lg">{test.percentage}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-amber-700">Correct:</span>
                <span className="font-semibold text-emerald-600">{analysis.correct}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-amber-700">Incorrect:</span>
                <span className="font-semibold text-rose-600">{analysis.incorrect}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-amber-700">Unattempted:</span>
                <span className="font-semibold text-slate-500">{analysis.unattempted}</span>
              </div>
            </DashboardCard>
          </div>
          <div className="lg:col-span-2">
            <DashboardCard className="p-0">
              <div className="p-4 border-b border-amber-200">
                <h3 className="text-lg font-semibold text-amber-900">Detailed Answer Key</h3>
              </div>
              <div className="max-h-[55vh] overflow-y-auto">
                <div className="sticky top-0 flex items-center p-3 font-bold text-amber-700 text-sm bg-amber-50 z-10 border-b border-amber-200">
                  <div className="w-16 text-center">Q#</div>
                  <div className="flex-1 text-center">Your Answer</div>
                  <div className="flex-1 text-center">Correct Answer</div>
                  <div className="w-32 text-right">Result</div>
                </div>
                <div className="p-2 space-y-1">
                  {test.details.map((item, index) => (
                    <QuestionListItem key={index} item={item} qNumber={index + 1} />
                  ))}
                </div>
              </div>
            </DashboardCard>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

const QuestionListItem = memo(({ item, qNumber }) => {
  // ... component logic is the same, but text colors might need slight adjustments for light theme if needed ...
  let status = { text: 'Unattempted', color: 'text-slate-500', icon: <HelpCircle size={18} /> };
  if (item.student_answer) {
    status = item.is_correct
      ? { text: 'Correct', color: 'text-emerald-600', icon: <CheckCircle2 size={18} /> }
      : { text: 'Incorrect', color: 'text-rose-600', icon: <XCircle size={18} /> };
  }
  return (
    <div className="flex items-center p-2 odd:bg-amber-100/50 rounded-lg text-sm">
      <div className="w-16 text-center font-bold text-amber-800">#{qNumber}</div>
      <div className="flex-1 flex justify-center">
        <span
          className={clsx(
            'w-8 h-8 flex items-center justify-center rounded-full font-semibold',
            !item.student_answer
              ? 'bg-slate-200 text-slate-600'
              : item.is_correct
                ? 'bg-emerald-100 text-emerald-700'
                : 'bg-rose-100 text-rose-700'
          )}>
          {item.student_answer || '–'}
        </span>
      </div>
      <div className="flex-1 flex justify-center">
        <span className="w-8 h-8 flex items-center justify-center rounded-full font-semibold bg-emerald-100 text-emerald-700">
          {item.correct_answer}
        </span>
      </div>
      <div className={`w-32 flex items-center justify-end font-semibold ${status.color}`}>
        {status.icon} <span className="ml-2">{status.text}</span>
      </div>
    </div>
  );
});

export default CenterCounselorOmr;
