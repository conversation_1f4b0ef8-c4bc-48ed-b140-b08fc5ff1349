import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BeakerIcon,
  CalculatorIcon,
  ChevronLeftIcon,
  CircleArrowOutUpRightIcon,
  Earth,
  FileSearch2,
  Globe,
  PlayCircleIcon,
  SparklesIcon,
  X
} from 'lucide-react';

const subjectsWithSimulations = [
  {
    id: 1,
    name: 'Physics',
    Icon: SparklesIcon,
    gradient: 'from-sky-500 to-indigo-600',
    simulations: [
      { name: 'astro-simulations', link: 'https://ccnmtl.github.io/astro-simulations/' },
      { name: 'myphysicslab', link: 'https://www.myphysicslab.com/' },
      {
        name: 'thephysicsaviary',
        link: 'https://www.thephysicsaviary.com/Physics/Programs/Labs/find.php'
      },
      { name: 'ophysics', link: 'https://ophysics.com/' },
      { name: 'astro', link: 'https://astro.unl.edu/' },
      { name: 'physics.bu.edu', link: 'https://physics.bu.edu/~duffy/HTML5/index.html' }
    ]
  },
  {
    id: 2,
    name: 'Chemistry',
    Icon: BeakerIcon,
    gradient: 'from-amber-500 to-orange-600',
    simulations: [
      { name: 'chemcollective', link: 'https://chemcollective.org/vlabs' },
      { name: 'chemagic', link: 'https://chemagic.org/molecules/amini.html' },
      { name: 'chemix', link: 'https://chemix.org/' }
    ]
  },
  {
    id: 3,
    name: 'Biology',
    Icon: Globe,
    gradient: 'from-emerald-500 to-green-600',
    simulations: []
  },
  {
    id: 4,
    name: 'Mathematics',
    Icon: CalculatorIcon,
    gradient: 'from-rose-500 to-pink-600',
    simulations: []
  },
  {
    id: 5,
    name: 'Earth & Space',
    Icon: Earth,
    gradient: 'from-sky-500 to-red-600',
    simulations: []
  }
];

// --- Animation Variants ---
const listContainerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.08 } }
};

const listItemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { type: 'spring', stiffness: 100 } }
};

const springTransition = { type: 'spring', stiffness: 400, damping: 30 };

// --- A Simple Spinner Component ---
const Spinner = () => (
  <div className="w-12 h-12 border-4 border-t-transparent border-blue-500 rounded-full animate-spin"></div>
);

const VirtualLabs = () => {
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [selectedSimulation, setSelectedSimulation] = useState(null);
  const [isIframeLoading, setIsIframeLoading] = useState(true);

  return (
    <div className="min-h-screen font-sans animated-gradient">
      <div className="p-4 sm:p-6 lg:p-8">
        <AnimatePresence mode="wait">
          {/* View 3: Iframe for a single simulation */}
          {selectedSimulation && selectedSubject && (
            <motion.div
              key="simulation-view"
              initial={{ y: '100%', opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: '100%', opacity: 0 }}
              transition={{ type: 'spring', stiffness: 400, damping: 40 }}
              className="fixed inset-0 bg-white z-30 flex flex-col">
              <header
                className={`flex-shrink-0 flex items-center justify-between p-4 text-white bg-gradient-to-r ${selectedSubject.gradient} shadow-lg`}>
                <button
                  onClick={() => setSelectedSimulation(null)}
                  className="flex items-center gap-2 px-4 py-2 font-semibold bg-white/20 rounded-lg hover:bg-white/30 transition-all">
                  <ChevronLeftIcon className="w-5 h-5" />
                  Back
                </button>
                <h2 className="text-lg font-bold text-center truncate px-4">
                  {selectedSimulation.name}
                </h2>
                <a
                  href={selectedSimulation.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 px-4 py-2 font-semibold bg-white/20 rounded-lg hover:bg-white/30 transition-all">
                  New Tab <CircleArrowOutUpRightIcon className="w-5 h-5" />
                </a>
              </header>
              <div className="relative flex-grow w-full bg-gray-200">
                {isIframeLoading && (
                  <div className="absolute inset-0 flex flex-col items-center justify-center gap-4 text-gray-600">
                    <Spinner />
                    <p className="text-lg">Loading Simulation...</p>
                  </div>
                )}
                <iframe
                  src={selectedSimulation.link}
                  title={selectedSimulation.name}
                  className="w-full h-full border-0"
                  onLoad={() => setIsIframeLoading(false)}
                  style={{ visibility: isIframeLoading ? 'hidden' : 'visible' }}
                />
              </div>
            </motion.div>
          )}

          {/* View 2: List of simulations for a selected subject */}
          {selectedSubject && !selectedSimulation && (
            <motion.div key="subject-view" className="max-w-5xl mx-auto">
              <motion.div
                layoutId={`card-container-${selectedSubject.id}`}
                transition={springTransition}
                className={`relative p-6 rounded-2xl text-white bg-gradient-to-br ${selectedSubject.gradient} shadow-2xl`}>
                <div className="flex items-center gap-4">
                  <motion.div layoutId={`card-icon-${selectedSubject.id}`}>
                    <selectedSubject.Icon className="w-12 h-12" />
                  </motion.div>
                  <motion.h1
                    layoutId={`card-title-${selectedSubject.id}`}
                    className="text-4xl font-bold">
                    {selectedSubject.name}
                  </motion.h1>
                </div>
                <button
                  onClick={() => setSelectedSubject(null)}
                  className="absolute top-4 right-4 flex items-center justify-center w-8 h-8 bg-black/20 rounded-full hover:bg-black/40 transition text-2xl"
                  aria-label="Close">
                  <X className="w-5 h-5 text-white" />
                </button>
              </motion.div>

              <motion.div
                className="mt-8"
                variants={listContainerVariants}
                initial="hidden"
                animate="visible">
                {selectedSubject.simulations.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {selectedSubject.simulations.map((sim, index) => (
                      <motion.div
                        key={index}
                        variants={listItemVariants}
                        onClick={() => setSelectedSimulation(sim)}
                        className="p-5 bg-white/70 backdrop-blur-sm rounded-xl shadow-lg cursor-pointer group transition-all duration-300 hover:!scale-105 hover:shadow-2xl hover:bg-white">
                        <div className="flex items-center gap-4">
                          <PlayCircleIcon
                            className={`w-10 h-10 text-gray-500 transition-colors group-hover:text-blue-500`}
                          />
                          <h3 className="font-semibold text-lg text-gray-800 flex-1">{sim.name}</h3>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <motion.div
                    variants={listItemVariants}
                    className="text-center text-gray-700 mt-16 flex flex-col items-center gap-4">
                    <FileSearch2 className="w-16 h-16 text-gray-400" />
                    <h3 className="text-2xl font-bold">Coming Soon!</h3>
                    <p className="max-w-md">
                      We're busy building new simulations for {selectedSubject.name}. Please check
                      back later!
                    </p>
                  </motion.div>
                )}
              </motion.div>
            </motion.div>
          )}

          {/* View 1: Main grid of all subjects */}
          {!selectedSubject && !selectedSimulation && (
            <motion.div key="main-view" className="max-w-6xl mx-auto text-center">
              <motion.h1
                initial={{ y: -50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ type: 'spring', delay: 0.2 }}
                className="text-5xl sm:text-6xl font-extrabold text-gray-900 mb-4">
                Virtual Labs
              </motion.h1>
              <motion.p
                initial={{ y: -50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ type: 'spring', delay: 0.4 }}
                className="text-lg text-gray-700 mb-16">
                Select a subject to explore our collection of interactive simulations.
              </motion.p>

              <motion.div
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
                variants={listContainerVariants}
                initial="hidden"
                animate="visible">
                {subjectsWithSimulations.map((subject) => (
                  <motion.div
                    key={subject.id}
                    layoutId={`card-container-${subject.id}`}
                    onClick={() => setSelectedSubject(subject)}
                    className={`relative p-8 rounded-2xl text-white cursor-pointer overflow-hidden bg-gradient-to-br ${subject.gradient} shadow-lg hover:shadow-2xl transition-shadow duration-300`}
                    variants={listItemVariants}
                    transition={springTransition}
                    whileHover={{ y: -10 }}>
                    <motion.div
                      className="absolute -bottom-8 -right-6 w-32 h-32 opacity-20"
                      transition={{ type: 'spring', stiffness: 300 }}>
                      <subject.Icon className="w-full h-full" />
                    </motion.div>
                    <div className="relative z-10 text-left">
                      <motion.div layoutId={`card-icon-${subject.id}`}>
                        <subject.Icon className="w-12 h-12 mb-4" />
                      </motion.div>
                      <motion.h2
                        layoutId={`card-title-${subject.id}`}
                        className="text-3xl font-bold">
                        {subject.name}
                      </motion.h2>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default VirtualLabs;
