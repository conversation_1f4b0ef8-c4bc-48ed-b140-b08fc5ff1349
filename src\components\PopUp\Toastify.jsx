import { useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Bounce, toast, ToastContainer } from 'react-toastify';

const DEFAULT_OPTIONS = {
  position: 'top-right',
  autoClose: 3000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  pauseOnFocusLoss: true,
  theme: 'light',
  transition: Bounce
};

const Toastify = ({ res, resClear, options = {}, containerOptions = {} }) => {
  const toastOptions = useMemo(
    () => ({
      ...DEFAULT_OPTIONS,
      ...options
    }),
    [options]
  );

  const extractMessage = useCallback((res) => {
    if (!res) return 'Unknown response';

    const message =
      res?.data?.message ||
      res?.message ||
      res?.response?.data?.message ||
      res?.response?.data ||
      res?.data ||
      res?.statusText;

    if (typeof message === 'object') return JSON.stringify(message);
    if (Array.isArray(message)) return message.join(', ');
    return message || 'Unexpected error occurred';
  }, []);

  const showToast = useCallback(
    (res) => {
      if (!res) return;

      const message = extractMessage(res);

      const type = res?.toastType; // e.g., "success", "error", "warn", "info"
      const toastId = `${res.status || 'custom'}-${message}`;

      if (toast.isActive(toastId)) return; // prevent duplicate toasts

      if (type) {
        toast[type](message, { toastId, ...toastOptions });
      } else if (res.status >= 200 && res.status < 300) {
        toast.success(message, { toastId, ...toastOptions });
      } else if (res.status >= 400 && res.status < 500) {
        toast.warn(message, { toastId, ...toastOptions });
      } else if (res.status >= 500) {
        toast.error(message, { toastId, ...toastOptions });
      } else {
        toast.info(message, { toastId, ...toastOptions });
      }
    },
    [extractMessage, toastOptions]
  );

  useEffect(() => {
    if (!res) return;
    showToast(res);
    resClear?.(); // clear after showing toast
  }, [res, showToast, resClear]);

  return <ToastContainer {...containerOptions} />;
};

Toastify.propTypes = {
  res: PropTypes.shape({
    status: PropTypes.number,
    statusText: PropTypes.string,
    message: PropTypes.string,
    toastType: PropTypes.oneOf(['success', 'error', 'info', 'warn']),
    data: PropTypes.oneOfType([PropTypes.string, PropTypes.object, PropTypes.array]),
    response: PropTypes.shape({
      data: PropTypes.any,
      message: PropTypes.string
    })
  }),
  resClear: PropTypes.func.isRequired,
  options: PropTypes.object,
  containerOptions: PropTypes.object
};

export default Toastify;
