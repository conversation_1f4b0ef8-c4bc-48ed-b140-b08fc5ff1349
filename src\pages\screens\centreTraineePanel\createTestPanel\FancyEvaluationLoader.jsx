// <CHANGE> Fancy multi-part loader inspired by bird + speech bubble; no gradients, amber + neutrals only.
import React from "react"
import { AnimatePresence, motion } from "framer-motion"
import { FaFilePdf, FaCog, FaCheckCircle } from "react-icons/fa"

// <CHANGE> Flying Bird loader (amber + neutrals only, no gradients). Uses Framer Motion.
 export default function EvaluationFlyingBirdLoader({ open }) {
  const messages = [
    "Warming up wings",
    "Reading pages",
    "Analyzing answers",
    "Scoring sheets",
    "Preparing feedback",
  ]
  const [idx, setIdx] = React.useState(0)

  React.useEffect(() => {
    if (!open) return
    const id = setInterval(() => setIdx((i) => (i + 1) % messages.length), 1300)
    return () => clearInterval(id)
  }, [open])

  // Colors: primary amber + 3 neutrals (white, charcoal, gray-300)
  const AMBER = "#F59E0B"
  const CHARCOAL = "#1F2937"
  const GRAY_300 = "#D1D5DB"
  const WHITE = "#FFFFFF"

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-white/90 backdrop-blur-sm z-50 flex items-center justify-center"
          role="alert"
          aria-live="assertive"
        >
          <motion.div
            initial={{ scale: 0.92, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.96, opacity: 0 }}
            transition={{ type: "spring", stiffness: 240, damping: 18 }}
            className="bg-white border border-[#F59E0B]/25 rounded-3xl shadow-2xl px-6 py-6 md:px-10 md:py-8 max-w-xl w-[92%] text-center"
          >
            {/* Sky area */}
            <div className="relative overflow-hidden rounded-2xl border border-[#F59E0B]/20 bg-white">
              {/* Moving clouds (subtle depth) */}
              <motion.div
                aria-hidden="true"
                className="absolute top-4 left-0 h-3 w-20 rounded-full"
                style={{ backgroundColor: GRAY_300 }}
                animate={{ x: ["-15%", "115%"] }}
                transition={{ duration: 6, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
              />
              <motion.div
                aria-hidden="true"
                className="absolute top-8 left-0 h-2 w-12 rounded-full opacity-70"
                style={{ backgroundColor: GRAY_300 }}
                animate={{ x: ["115%", "-15%"] }}
                transition={{ duration: 5.5, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
              />
              <motion.div
                aria-hidden="true"
                className="absolute top-14 left-0 h-2 w-16 rounded-full opacity-60"
                style={{ backgroundColor: GRAY_300 }}
                animate={{ x: ["-10%", "120%"] }}
                transition={{ duration: 7, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
              />

              {/* Flying bird (SVG + wing flaps + gentle flight path) */}
              <div className="relative flex items-center justify-center py-10">
                <motion.svg
                  width="240"
                  height="140"
                  viewBox="0 0 240 140"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  initial={false}
                >
                  {/* Bird group: drift along a soft arc and slight tilt for realism */}
                  <motion.g
                    animate={{
                      x: [-30, 28, -30],
                      y: [8, -10, 8],
                      rotate: [-4, 5, -4],
                    }}
                    transition={{ duration: 3.2, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
                  >
                    {/* Tail */}
                    <polygon points="55,78 40,70 40,86" fill={AMBER} />

                    {/* Body */}
                    <ellipse cx="90" cy="80" rx="38" ry="28" fill={AMBER} />

                    {/* Belly highlight (solid white, not gradient) */}
                    <path
                      d="M72 82c0 10 9 18 20 18 8 0 15-4 18-10-6-6-15-10-26-10-4 0-8 1-12 2z"
                      fill={WHITE}
                      opacity="0.95"
                    />

                    {/* Head */}
                    <circle cx="120" cy="66" r="20" fill={AMBER} />

                    {/* Eye */}
                    <circle cx="127" cy="62" r="4.8" fill={CHARCOAL} />
                    <circle cx="128.8" cy="60.8" r="1.6" fill={WHITE} />

                    {/* Beak */}
                    <polygon points="138,64 150,60 138,56" fill={CHARCOAL} />

                    {/* Back wing (slightly darker + behind) */}
                    <motion.path
                      d="M78 66c-16 8-26 22-26 30 10-2 22-2 32-6 8-3 12-8 14-12-6-6-12-10-20-12z"
                      fill={AMBER}
                      opacity="0.85"
                      style={{ originX: 0.25, originY: 0.5 }}
                      animate={{ rotate: [-18, 12, -18] }}
                      transition={{ duration: 0.6, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
                    />

                    {/* Front wing (faster flap for liveliness) */}
                    <motion.path
                      d="M88 62c-18 10-30 24-30 36 12-2 26-4 36-8 10-4 16-10 18-16-6-6-14-10-24-12z"
                      fill={AMBER}
                      style={{ originX: 0.28, originY: 0.58 }}
                      animate={{ rotate: [-26, 18, -26] }}
                      transition={{ duration: 0.5, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
                    />
                  </motion.g>
                </motion.svg>
              </div>
            </div>

            {/* Status line + segmented progress (solid blocks) */}
            <div className="mt-6 mb-3">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-[#F59E0B] bg-white">
                <span className="h-2 w-2 rounded-full" style={{ backgroundColor: AMBER }} />
                <span className="text-sm font-medium" style={{ color: CHARCOAL }}>
                  {messages[idx]}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-10 gap-1 mb-2">
              {Array.from({ length: 10 }).map((_, i) => (
                <motion.div key={i} className="h-2 rounded" style={{ backgroundColor: GRAY_300, overflow: "hidden" }}>
                  <motion.div
                    className="h-full"
                    style={{ backgroundColor: AMBER }}
                    initial={{ width: "0%" }}
                    animate={{ width: ["0%", "100%"] }}
                    transition={{
                      duration: 0.7,
                      delay: i * 0.075,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: "mirror",
                      ease: "easeInOut",
                    }}
                  />
                </motion.div>
              ))}
            </div>

            <p className="text-xs text-[#1F2937]">Processing evaluation — this may take a moment</p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}