import { useState, useEffect, useRef, useCallback } from 'react'; // Import useCallback
import {
  useDoubtSolverServiceMutation,
  useGenerateAudioServiceMutation,
  useYoutubeSearchServiceMutation,
  useWebSearchServiceMutation
} from './problemSolver.Slice';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router';
import { MathJaxContext } from 'better-react-mathjax';
import ChatInterface from './ChatInterface';
import HistoryPanel from './HistoryPanel';
import ReferTutorModal from './ReferTutorModal';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FaHistory,
  FaChalkboardTeacher
} from 'react-icons/fa';

// --- Helper functions moved outside the component ---
// These are pure functions and do not need to be recreated on every render.

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

const getYouTubeVideoId = (url) => {
  try {
    const urlParams = new URLSearchParams(new URL(url).search);
    return urlParams.get('v') || url.split('v=')[1]?.split('&')[0] || url.split('/').pop();
  } catch (e) {
    return null; // Handle invalid URLs gracefully
  }
};

function getCroppedImg(image, crop, fileName) {
  const canvas = document.createElement('canvas');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  canvas.width = crop.width;
  canvas.height = crop.height;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(
    image,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width,
    crop.height
  );
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'));
          return;
        }
        blob.name = fileName;
        resolve(blob);
      },
      'image/jpeg',
      0.9
    );
  });
}

const mathJaxConfig = {
  loader: { load: ['[tex]/physics'] },
  tex: {
    inlineMath: [['$', '$'], ['\\(', '\\)']],
    displayMath: [['$$', '$$'], ['\\[', '\\]']],
    macros: {
      'pdiff': ['\\frac{\\partial #1}{\\partial #2}', 2]
    }
  }
};

const ProblemSolver = () => {
  // ... all your useState, useRef hooks remain the same ...
  const [userId, setUserId] = useState('');
  const [text, setText] = useState('');
  const [audio, setAudio] = useState(null);
  const [image, setImage] = useState(null);
  const [lastText, setLastText] = useState('');
  const [lastAudio, setLastAudio] = useState(null);
  const [lastImage, setLastImage] = useState(null);
  const [mode, setMode] = useState('tutor');
  const [language, setLanguage] = useState('English');
  const [response, setResponse] = useState([]);
  const [audioUrl, setAudioUrl] = useState('');
  const [youtubeResults, setYoutubeResults] = useState([]);
  const [previousYoutubeResults, setPreviousYoutubeResults] = useState([]);
  const [webSearchResults, setWebSearchResults] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [activeInput, setActiveInput] = useState(null);
  const [particles, setParticles] = useState([]);
  const [showHistory, setShowHistory] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [responseHistory, setResponseHistory] = useState([]);
  const [reset, setReset] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [isYouTubeMaximized, setIsYouTubeMaximized] = useState(false);
  const [showMediaOptions, setShowMediaOptions] = useState(false);
  const [showReferTutorModal, setShowReferTutorModal] = useState(false);
  const [showCropModal, setShowCropModal] = useState(false);
  const [imageSrc, setImageSrc] = useState(null);
  const [crop, setCrop] = useState({ aspect: 1, unit: '%', width: 50, height: 50 });
  const [croppedImage, setCroppedImage] = useState(null);
  const [aiThinking, setAiThinking] = useState(false);
  const [showWebcamModal, setShowWebcamModal] = useState(false);
  const [videoStream, setVideoStream] = useState(null);
  const [res, setRes] = useState(null);

  const chatContainerRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioInputRef = useRef(null);
  const imageInputRef = useRef(null);
  const audioRef = useRef(null);
  const imageRef = useRef(null);
  const videoRef = useRef(null);

  const [doubtSolver, { isLoading: isDoubtSolverLoading }] = useDoubtSolverServiceMutation();
  const [youtubeSearch, { isLoading: isYoutubeLoading }] = useYoutubeSearchServiceMutation();
  const [generateAudio, { isLoading: isAudioLoading }] = useGenerateAudioServiceMutation();
  const [webSearch, { isLoading: isWebSearchLoading }] = useWebSearchServiceMutation();

  // --- STABILIZED FUNCTIONS with `useCallback` ---

  const stopWebcam = useCallback(() => {
    if (videoStream) {
      videoStream.getTracks().forEach((track) => track.stop());
      setVideoStream(null);
      setShowWebcamModal(false);
    }
  }, [videoStream]); // Depends on videoStream state

  const startWebcam = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      setVideoStream(stream);
      setShowWebcamModal(true);
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.play().catch((err) => console.error('Play error:', err));
        }
      }, 100);
      setHasUserInteracted(true);
    } catch (err) {
      console.error('Webcam error:', err);
      setRes({ status: 400, message: `Failed to access webcam: ${err.message}.` });
    }
  }, []); // No dependencies, safe to use empty array

  const handleCaptureImage = useCallback(() => {
    if (videoRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setImageSrc(imageDataUrl);
      setShowCropModal(true);
      stopWebcam();
    }
  }, [stopWebcam]); // Depends on the stable stopWebcam function

  const scrollToBottom = useCallback(() => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current.scrollTo({ top: chatContainerRef.current.scrollHeight, behavior: 'smooth' });
      }, 100);
    }
  }, []);

  const handleDeleteAudio = useCallback(() => {
    setAudio(null);
    if (activeInput === 'audio') {
      setActiveInput(null);
    }
  }, [activeInput]); // Depends on activeInput

  const handleDeleteImage = useCallback(() => {
    setImage(null);
    if (activeInput === 'image') {
      setActiveInput(null);
    }
  }, [activeInput]); // Depends on activeInput

  const handleRecord = useCallback(async () => {
    setHasUserInteracted(true);
    if (isRecording) {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
        setRecordingTime(0);
      }
    } else {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorderRef.current = new MediaRecorder(stream, { mimeType: 'audio/webm' });
        const chunks = [];
        mediaRecorderRef.current.ondataavailable = (e) => e.data.size > 0 && chunks.push(e.data);
        mediaRecorderRef.current.onstop = () => {
          const blob = new Blob(chunks, { type: 'audio/webm' });
          const audioFile = new File([blob], `recorded_audio.webm`, { type: 'audio/webm' });
          setAudio(audioFile);
          stream.getTracks().forEach((track) => track.stop());
        };
        mediaRecorderRef.current.start();
        setIsRecording(true);
        setActiveInput('audio');
      } catch (err) {
        setRes({ status: 400, message: 'Failed to access microphone.' });
      }
    }
  }, [isRecording]); // Depends on isRecording

  const handleAudioUpload = useCallback((e) => {
    setHasUserInteracted(true);
    if (isRecording) return;
    const file = e.target.files[0];
    if (file) {
      setAudio(file);
      setActiveInput('audio');
    }
  }, [isRecording]); // Depends on isRecording

  const handleImageUpload = useCallback((e) => {
    setHasUserInteracted(true);
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => setImageSrc(reader.result);
      reader.readAsDataURL(file);
      setShowCropModal(true);
    }
  }, []);

  const handleCropComplete = useCallback(async () => {
    if (imageRef.current && crop.width && crop.height) {
      try {
        const croppedImageBlob = await getCroppedImg(imageRef.current, crop, 'cropped-image.jpeg');
        const croppedImageFile = new File([croppedImageBlob], `cropped.jpeg`, { type: 'image/jpeg' });
        setImage(croppedImageFile);
        setCroppedImage(URL.createObjectURL(croppedImageBlob));
        setShowCropModal(false);
        setActiveInput('image');
      } catch (err) {
        console.error('Error cropping image:', err);
      }
    }
  }, [crop]); // Depends on the crop state

  const languageMap = {
    English: 'english',
    'Hindi+English': 'hinglish',
    'Telugu+English': 'tenglish',
    'Malayalam+English': 'manglish',
    'Kannada+English': 'kanglish',
    'Tamil+English': 'tanglish'
  };

  const handleGenerateAudio = useCallback(async (responseText) => {
    setHasUserInteracted(true);
    if (!userId) return;
    const formData = new FormData();
    formData.append('response', responseText);
    formData.append('language', languageMap[language] || 'english');
    formData.append('user_id', userId);
    try {
      const audioResult = await generateAudio(formData).unwrap();
      setAudioUrl(`data:audio/mp3;base64,${audioResult.audio_base64}`);
    } catch (err) {
      console.error('Audio generation error:', err);
    }
  }, [userId, language, generateAudio]); // Stable dependencies

  const handleWebSearch = useCallback(async () => {
    setHasUserInteracted(true);
    if (!userId || (!lastText && !lastAudio && !lastImage)) return;

    const formData = new FormData();
    if (lastText) formData.append('text', lastText);
    if (lastAudio) formData.append('audio', lastAudio);
    if (lastImage) formData.append('image', lastImage);
    formData.append('user_id', userId);

    try {
      const webSearchResult = await webSearch(formData).unwrap();
      setWebSearchResults(webSearchResult.results || []);
    } catch (err) {
      console.error('Web search error:', err);
    }
  }, [lastText, lastAudio, lastImage, userId, webSearch]); // Stable dependencies

const handleSubmit = useCallback(async () => {
  if (!userId || (!text && !audio && !image)) return;

  setHasUserInteracted(true);
  if (audioRef.current && isAudioPlaying) {
    audioRef.current.pause();
  }
  setAudioUrl('');
  setLastText(text);
  setLastAudio(audio);
  setLastImage(image);

  const formData = new FormData();
  if (text) formData.append('text', text);
  if (audio) formData.append('audio', audio);
  if (image) formData.append('image', image);
  formData.append('mode', mode || 'tutor');
  formData.append('language', languageMap[language] || 'english');
  formData.append('user_id', userId);
  formData.append('reset', reset.toString());
  formData.append('include_history', 'true');

  setResponse((prev) => [...prev, { type: 'user', content: text || (audio ? 'Audio Query' : 'Image Query') }]);
  
  setText('');
  setAudio(null);
  setImage(null);
  setActiveInput(null);
  setReset(false);

  try {
    const [doubtResult, youtubeResult] = await Promise.all([
      doubtSolver(formData).unwrap(),
      youtubeSearch(formData).unwrap()
    ]);

    // Handle response based on mode
    let responseContent;
    if (mode === 'conversation' && doubtResult.response?.dialogue) {
      responseContent = doubtResult.response.dialogue; // Store dialogue array
    } else {
      responseContent = doubtResult.response?.response || doubtResult.response || 'No response received.';
    }

    setResponse((prev) => [...prev, { type: 'bot', content: responseContent }]);

    if (doubtResult.history) {
      setResponseHistory(doubtResult.history);
    }

    const newVideos = youtubeResult.videos || [];
    setYoutubeResults(newVideos);
    if (newVideos.length > 0) {
      setPreviousYoutubeResults(newVideos);
    }
  } catch (err) {
    setRes({ status: err.status || 500, message: 'Failed to process your query. Please try again.' });
    console.error('Doubt solver error:', err);
  }
}, [text, audio, image, mode, language, userId, reset, isAudioPlaying, doubtSolver, youtubeSearch]);

  const handleHistoryClick = useCallback((historyItem) => {
    setResponse((prev) => [
      ...prev,
      { type: 'user', content: historyItem.question },
      { type: 'bot', content: historyItem.response }
    ]);
    setAudioUrl('');
    setShowHistory(false);
  }, []);

  const handleResetHistory = useCallback(async () => {
    setHasUserInteracted(true);
    setReset(true);
    setResponseHistory([]);
    setResponse([]);
    setYoutubeResults([]);
    setPreviousYoutubeResults([]);
    setWebSearchResults([]);
    setLastText('');
    setLastAudio(null);
    setLastImage(null);
    // ... logic to call reset on backend ...
  }, [userId, doubtSolver]);

  // --- useEffect hooks remain largely the same, but now depend on STABLE functions ---
  useEffect(() => {
    let storedUserId = sessionStorage.getItem('user_id');
    console .log('Stored User ID:', storedUserId);
    if (!storedUserId) {
      storedUserId = 'student_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('user_id', storedUserId);
    }
    setUserId(storedUserId);
  }, []);

  useEffect(() => {
    const loading = isDoubtSolverLoading || isYoutubeLoading || isAudioLoading || isWebSearchLoading;
    setAiThinking(loading);
  }, [isDoubtSolverLoading, isYoutubeLoading, isAudioLoading, isWebSearchLoading]);

  useEffect(() => {
    scrollToBottom();
  }, [response, aiThinking, scrollToBottom]);

  // ... other useEffects ...

  return (
    <MathJaxContext config={mathJaxConfig}>
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
        <Toastify res={res} resClear={() => setRes(null)} />
        {/* ... Particles and Header JSX ... */}
         <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="absolute top-0 left-0 right-0 z-20 p-4 flex items-center justify-end">
          <ReferTutorModal
            isOpen={showReferTutorModal}
            onClose={() => setShowReferTutorModal(false)}
          />
          <div className="flex items-center space-x-4">
        
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowReferTutorModal(true)}
              className="cursor-pointer px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
              <motion.div
                transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}>
                <FaChalkboardTeacher className="text-sm" />
              </motion.div>
              <span>Refer a Tutor!</span>
            </motion.button>
            {/* <motion.button
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowHistory(!showHistory)}
              className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white shadow-xl hover:shadow-2xl transition-all duration-300 relative">
              <FaHistory className="text-lg" />
              {responseHistory.length > 0 && (
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full text-xs text-white flex items-center justify-center font-bold">
                  {responseHistory.length}
                </motion.div>
              )}
            </motion.button> */}
          </div>
        </motion.div>
        <div className="pb-6 px-4 h-screen flex flex-col">
          <ChatInterface
            // Pass all the props, which are now stable
            response={response}
            youtubeResults={youtubeResults}
            previousYoutubeResults={previousYoutubeResults}
            isYoutubeLoading={isYoutubeLoading}
            isYouTubeMaximized={isYouTubeMaximized}
            setIsYouTubeMaximized={setIsYouTubeMaximized}
            getYouTubeVideoId={getYouTubeVideoId}
            handleGenerateAudio={handleGenerateAudio}
            isAudioLoading={isAudioLoading}
            audioUrl={audioUrl}
            isAudioPlaying={isAudioPlaying}
            setIsAudioPlaying={setIsAudioPlaying}
            audioRef={audioRef}
            aiThinking={aiThinking}
            chatContainerRef={chatContainerRef}
            text={text}
            setText={setText}
            audio={audio}
            image={image}
            mode={mode}
            setMode={setMode}
            language={language}
            setLanguage={setLanguage}
            isRecording={isRecording}
            recordingTime={recordingTime}
            activeInput={activeInput}
            showMediaOptions={showMediaOptions}
            setShowMediaOptions={setShowMediaOptions}
            isDoubtSolverLoading={isDoubtSolverLoading}
            handleSubmit={handleSubmit}
            handleRecord={handleRecord}
            handleAudioUpload={handleAudioUpload}
            handleImageUpload={handleImageUpload}
            handleDeleteAudio={handleDeleteAudio}
            handleDeleteImage={handleDeleteImage}
            audioInputRef={audioInputRef}
            imageInputRef={imageInputRef}
            showCropModal={showCropModal}
            setShowCropModal={setShowCropModal}
            imageSrc={imageSrc}
            crop={crop}
            setCrop={setCrop}
            imageRef={imageRef}
            croppedImage={croppedImage}
            handleCropComplete={handleCropComplete}
            showWebcamModal={showWebcamModal}
            setShowWebcamModal={setShowWebcamModal}
            videoRef={videoRef}
            startWebcam={startWebcam}
            handleCaptureImage={handleCaptureImage}
            stopWebcam={stopWebcam}
            formatTime={formatTime}
            userId={userId}
            webSearchResults={webSearchResults}
            isWebSearchLoading={isWebSearchLoading}
            handleWebSearch={handleWebSearch}
          />
          {/* <HistoryPanel
            showHistory={showHistory}
            setShowHistory={setShowHistory}
            responseHistory={responseHistory}
            handleHistoryClick={handleHistoryClick}
            handleResetHistory={handleResetHistory}
            isDoubtSolverLoading={isDoubtSolverLoading}
            isYoutubeLoading={isYoutubeLoading}
          /> */}
        </div>
      </div>
    </MathJaxContext>
  );
};

export default ProblemSolver;