import { createSlice } from '@reduxjs/toolkit';
import { omrEvaluationApi } from '../../../../redux/api/api'; // Ensure this path is correct

const initialState = {
  omrEvaluation: [],
};

export const omrEvaluationApiSlice = omrEvaluationApi.injectEndpoints({
  endpoints: (builder) => ({
    omrEvaluationService: builder.mutation({
      query: (data) => ({
        url: '/omr_evaluate',
        method: 'POST',
        body: data,
        responseHandler: async (res) => res.json(),
      }),
      transformResponse: (response) => {
        console.log(response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data,
      }),
      invalidatesTags: ['omrEvaluation'],
    }),
    getOmrEvaluationService: builder.query({
      query: (studentId) => ({
        url: `/get_omr_evaluations?student_id=${studentId}`, // Dynamic URL with studentId
        method: 'GET',
        // No body needed for GET requests
      }),
      transformResponse: (response) => {
        console.log(response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data,
      }),
      providesTags: ['omrEvaluation'],
    }),
  }),
});

const omrEvaluationSlice = createSlice({
  name: 'omrEvaluation',
  initialState,
  reducers: {
    setOmrEvaluation: (state, action) => {
      state.omrEvaluation = action.payload;
    },
  },
});

export default omrEvaluationSlice.reducer;
export const { setOmrEvaluation } = omrEvaluationSlice.actions;
export const { useOmrEvaluationServiceMutation, useGetOmrEvaluationServiceQuery } =
  omrEvaluationApiSlice; // Corrected export