import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Trophy, 
  Target, 
  BookOpen,
  Users,
  CheckCircle2,
  AlertCircle,
  Calendar,
  Download,
  Share2,
  Bell,
  Star,
  Activity
} from 'lucide-react';

const WeeklyNotification = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  const performanceData = {
    weeklyScore: 87,
    previousWeek: 82,
    totalStudyHours: 24.5,
    completedTasks: 18,
    totalTasks: 22,
    averageGrade: 'A-',
    streak: 12
  };

  const weeklyActivities = [
    { day: 'Mon', hours: 4.2, completion: 95, color: 'bg-blue-500' },
    { day: 'Tue', hours: 3.8, completion: 87, color: 'bg-green-500' },
    { day: 'Wed', hours: 5.1, completion: 92, color: 'bg-purple-500' },
    { day: 'Thu', hours: 2.9, completion: 78, color: 'bg-yellow-500' },
    { day: 'Fri', hours: 4.5, completion: 88, color: 'bg-red-500' },
    { day: 'Sat', hours: 2.0, completion: 65, color: 'bg-indigo-500' },
    { day: 'Sun', hours: 2.0, completion: 45, color: 'bg-pink-500' }
  ];

  const achievements = [
    { title: 'Study Streak Champion', desc: '12 days in a row!', icon: Trophy, color: 'text-yellow-500' },
    { title: 'Early Bird', desc: 'Started before 8 AM 5 times', icon: Clock, color: 'text-blue-500' },
    { title: 'Task Master', desc: 'Completed 18/22 tasks', icon: Target, color: 'text-green-500' }
  ];

  const upcomingGoals = [
    { title: 'Complete Math Assignment', deadline: 'Tomorrow', priority: 'high' },
    { title: 'Review Chemistry Notes', deadline: '2 days', priority: 'medium' },
    { title: 'Prepare for Physics Quiz', deadline: '4 days', priority: 'high' },
    { title: 'Submit English Essay', deadline: '1 week', priority: 'low' }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-white border-t-transparent rounded-full mx-auto mb-4"
          />
          <p className="text-white text-lg font-medium">Loading your weekly report...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <motion.h1 
            className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 mb-4"
            animate={{ 
              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
            }}
            transition={{ 
              duration: 5, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            Weekly Performance Report
          </motion.h1>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3 }}
            className="flex items-center justify-center gap-2 text-white/80"
          >
            <Calendar className="w-5 h-5" />
            <span className="text-lg">Week of {new Date().toLocaleDateString()}</span>
          </motion.div>
        </motion.div>

        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[
            { 
              title: 'Weekly Score', 
              value: performanceData.weeklyScore, 
              suffix: '%',
              change: performanceData.weeklyScore - performanceData.previousWeek,
              icon: TrendingUp,
              color: 'from-green-400 to-green-600'
            },
            { 
              title: 'Study Hours', 
              value: performanceData.totalStudyHours, 
              suffix: 'hrs',
              change: 2.3,
              icon: Clock,
              color: 'from-blue-400 to-blue-600'
            },
            { 
              title: 'Tasks Completed', 
              value: `${performanceData.completedTasks}/${performanceData.totalTasks}`, 
              suffix: '',
              change: 3,
              icon: CheckCircle2,
              color: 'from-purple-400 to-purple-600'
            },
            { 
              title: 'Study Streak', 
              value: performanceData.streak, 
              suffix: ' days',
              change: 1,
              icon: Activity,
              color: 'from-pink-400 to-pink-600'
            }
          ].map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.color}`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                  <motion.div
                    className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      stat.change > 0 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                    }`}
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    {stat.change > 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                    {Math.abs(stat.change)}
                  </motion.div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">
                  {stat.value}{stat.suffix}
                </div>
                <div className="text-white/70 text-sm">{stat.title}</div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="flex justify-center mb-8"
        >
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-2 border border-white/20">
            {['overview', 'achievements', 'goals'].map((tab) => (
              <motion.button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  activeTab === tab
                    ? 'bg-white text-purple-900 shadow-lg'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-8"
            >
              {/* Daily Activity Chart */}
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                  <Activity className="w-5 h-5 text-blue-400" />
                  Daily Activity Overview
                </h3>
                <div className="space-y-4">
                  {weeklyActivities.map((day, index) => (
                    <motion.div
                      key={day.day}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center gap-4"
                    >
                      <div className="w-12 text-white/80 font-medium">{day.day}</div>
                      <div className="flex-1 bg-white/10 rounded-full h-6 overflow-hidden">
                        <motion.div
                          className={`h-full ${day.color} flex items-center justify-end pr-2`}
                          initial={{ width: 0 }}
                          animate={{ width: `${day.completion}%` }}
                          transition={{ delay: index * 0.1, duration: 1 }}
                        >
                          <span className="text-white text-xs font-medium">{day.completion}%</span>
                        </motion.div>
                      </div>
                      <div className="w-16 text-white/80 text-sm">{day.hours}h</div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Performance Insights */}
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                  <Target className="w-5 h-5 text-green-400" />
                  Performance Insights
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 bg-green-500/20 rounded-xl border border-green-500/30">
                    <CheckCircle2 className="w-8 h-8 text-green-400" />
                    <div>
                      <div className="text-white font-medium">Excellent Progress!</div>
                      <div className="text-green-200 text-sm">Your weekly score improved by 5 points</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-4 bg-yellow-500/20 rounded-xl border border-yellow-500/30">
                    <AlertCircle className="w-8 h-8 text-yellow-400" />
                    <div>
                      <div className="text-white font-medium">Weekend Dip</div>
                      <div className="text-yellow-200 text-sm">Consider maintaining study routine on weekends</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-4 bg-blue-500/20 rounded-xl border border-blue-500/30">
                    <BookOpen className="w-8 h-8 text-blue-400" />
                    <div>
                      <div className="text-white font-medium">Strong Consistency</div>
                      <div className="text-blue-200 text-sm">Maintained 4+ hours study on weekdays</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'achievements' && (
            <motion.div
              key="achievements"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.title}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.2 }}
                  className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 text-center group hover:bg-white/15 transition-all duration-300"
                >
                  <motion.div
                    className={`inline-flex p-4 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 mb-4`}
                    whileHover={{ scale: 1.1, rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <achievement.icon className="w-8 h-8 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white mb-2">{achievement.title}</h3>
                  <p className="text-white/80">{achievement.desc}</p>
                  <motion.div
                    className="flex justify-center gap-1 mt-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.2 + 0.5 }}
                  >
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </motion.div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'goals' && (
            <motion.div
              key="goals"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="max-w-4xl mx-auto"
            >
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
                <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
                  <Target className="w-6 h-6 text-purple-400" />
                  Upcoming Goals & Tasks
                </h3>
                <div className="space-y-4">
                  {upcomingGoals.map((goal, index) => (
                    <motion.div
                      key={goal.title}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`flex items-center justify-between p-4 rounded-xl border-l-4 ${
                        goal.priority === 'high' ? 'bg-red-500/20 border-red-500' :
                        goal.priority === 'medium' ? 'bg-yellow-500/20 border-yellow-500' :
                        'bg-green-500/20 border-green-500'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          goal.priority === 'high' ? 'bg-red-500' :
                          goal.priority === 'medium' ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`} />
                        <div>
                          <h4 className="text-white font-medium">{goal.title}</h4>
                          <p className="text-white/70 text-sm">Due: {goal.deadline}</p>
                        </div>
                      </div>
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                        goal.priority === 'high' ? 'bg-red-500/30 text-red-200' :
                        goal.priority === 'medium' ? 'bg-yellow-500/30 text-yellow-200' :
                        'bg-green-500/30 text-green-200'
                      }`}>
                        {goal.priority.toUpperCase()}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="flex flex-col sm:flex-row gap-4 justify-center mt-8"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Download className="w-5 h-5" />
            Download Report
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center gap-2 px-6 py-3 bg-white/10 backdrop-blur-xl text-white font-medium rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300"
          >
            <Share2 className="w-5 h-5" />
            Share Progress
          </motion.button>
        </motion.div>
      </div>
    </div>
  );
};

export default WeeklyNotification;