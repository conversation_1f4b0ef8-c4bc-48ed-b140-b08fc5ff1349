import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  BookOpen,
  Clock,
  Target,
  TrendingUp,
  Award,
  BarChart3,
  LineChart, // Replaced with a more standard name
  ChevronDown,
  Trophy,
  Zap,
  CheckCircle,
  XCircle,
  MinusCircle,
  ChevronLeft,
  ChevronRight,
  User,
  GraduationCap,
  Users,
  Calendar
} from 'lucide-react';
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import {
  clearStudentData,
  setStudentData,
  useLazyGetStudentDataByStudentIdServiceQuery
} from './directorAnalyticalDashboard.slice';
import Button from '../../../../components/Field/Button';
import Toastify from '../../../../components/PopUp/Toastify';

// --- CONSTANTS ---
const CONSTANTS = {
  ITEMS_PER_PAGE: 10,
  SECTIONS: {
    OVERVIEW: 'overview',
    CHARTS: 'charts',
    SUBJECTS: 'subjects',
    HISTORY: 'history'
  },
  SUBJECT_CONFIG: {
    biology: {
      gradient: 'from-emerald-400 to-teal-500',
      dot: 'bg-emerald-500',
      text: 'text-emerald-600'
    },
    chemistry: { gradient: 'from-sky-400 to-blue-500', dot: 'bg-sky-500', text: 'text-sky-600' },
    physics: {
      gradient: 'from-violet-400 to-purple-500',
      dot: 'bg-violet-500',
      text: 'text-violet-600'
    },
    maths: {
      gradient: 'from-amber-400 to-orange-500',
      dot: 'bg-amber-500',
      text: 'text-amber-600'
    },
    default: {
      gradient: 'from-slate-400 to-slate-500',
      dot: 'bg-slate-500',
      text: 'text-slate-600'
    }
  }
};

const getSubjectConfig = (subject) =>
  CONSTANTS.SUBJECT_CONFIG[subject?.toLowerCase()] || CONSTANTS.SUBJECT_CONFIG.default;

// --- CUSTOM HOOK FOR DATA PROCESSING ---
const useStudentAnalytics = (studentData) => {
  // ... (No changes to the logic inside this hook, it's already well-structured)
  const calculateOverallStats = useCallback((data) => {
    if (!data || Object.keys(data)?.length === 0) {
      return {
        totalTests: 0,
        totalQuestions: 0,
        totalCorrect: 0,
        totalIncorrect: 0,
        totalUnattempted: 0,
        accuracy: 0,
        averageScore: 0
      };
    }
    const allTests = Object.values(data).flat();
    const totalTests = allTests?.length;
    if (totalTests === 0) {
      return {
        totalTests: 0,
        totalQuestions: 0,
        totalCorrect: 0,
        totalIncorrect: 0,
        totalUnattempted: 0,
        accuracy: 0,
        averageScore: 0
      };
    }

    const totals = allTests.reduce(
      (acc, test) => {
        const { evaluation_results: res } = test;
        acc.questions += res.total_questions;
        acc.correct += res.num_correct;
        acc.incorrect += res.num_incorrect;
        acc.unattempted += res.num_unattempted;
        acc.score += res.score;
        return acc;
      },
      { questions: 0, correct: 0, incorrect: 0, unattempted: 0, score: 0 }
    );

    const numAttempted = totals.correct + totals.incorrect;
    const accuracy = numAttempted > 0 ? (totals.correct / numAttempted) * 100 : 0;
    const averageScore = totalTests > 0 ? totals.score / totalTests : 0;

    return {
      totalTests,
      totalQuestions: totals.questions,
      totalCorrect: totals.correct,
      totalIncorrect: totals.incorrect,
      totalUnattempted: totals.unattempted,
      accuracy: accuracy.toFixed(1),
      averageScore: averageScore.toFixed(1)
    };
  }, []);

  const calculateSubjectStats = useCallback((subjectData) => {
    if (!subjectData || subjectData?.length === 0) return null;

    const totals = subjectData.reduce(
      (acc, test) => {
        const { evaluation_results: res } = test;
        acc.correct += res.num_correct;
        acc.incorrect += res.num_incorrect;
        acc.score += res.score;
        return acc;
      },
      { correct: 0, incorrect: 0, score: 0 }
    );

    const numAttempted = totals.correct + totals.incorrect;
    const accuracy = numAttempted > 0 ? (totals.correct / numAttempted) * 100 : 0;
    const averageScore = subjectData?.length > 0 ? totals.score / subjectData?.length : 0;

    return {
      testsCount: subjectData?.length,
      accuracy: accuracy.toFixed(1),
      averageScore: averageScore.toFixed(1)
    };
  }, []);

  const overallStats = useMemo(
    () => calculateOverallStats(studentData),
    [studentData, calculateOverallStats]
  );

  const subjectStats = useMemo(() => {
    if (!studentData) return {};
    return Object.fromEntries(
      Object.entries(studentData)
        .map(([subject, tests]) => [subject, calculateSubjectStats(tests)])
        .filter(([, stats]) => stats !== null)
    );
  }, [studentData, calculateSubjectStats]);

  const chartData = useMemo(() => {
    if (!studentData) return null;

    const allTests = Object.entries(studentData)
      .flatMap(([subject, tests]) => tests.map((test) => ({ ...test, subject })))
      .sort(
        (a, b) => new Date(a.start_time.split(' & ')[0]) - new Date(b.start_time.split(' & ')[0])
      );

    if (allTests?.length === 0) return null;

    const recentTests = allTests.slice(-10);

    const performanceTrend = recentTests.map((test, index) => ({
      test: `Test ${allTests?.length - recentTests?.length + index + 1}`,
      correct: test.evaluation_results.num_correct,
      incorrect: test.evaluation_results.num_incorrect,
      unattempted: test.evaluation_results.num_unattempted
    }));

    return { performanceTrend };
  }, [studentData]);

  const allTestsForHistory = useMemo(() => {
    if (!studentData) return [];
    return Object.entries(studentData)
      .flatMap(([subject, tests]) => tests.map((test) => ({ ...test, subject })))
      .sort(
        (a, b) => new Date(b.start_time.split(' & ')[0]) - new Date(a.start_time.split(' & ')[0])
      );
  }, [studentData]);

  const helpers = useMemo(
    () => ({
      getAccuracyColor: (accuracy) => {
        if (accuracy >= 80) return 'text-emerald-500';
        if (accuracy >= 60) return 'text-amber-500';
        return 'text-red-500';
      }
    }),
    []
  );

  return { overallStats, subjectStats, chartData, allTestsForHistory, helpers };
};

// --- REUSABLE UI COMPONENTS ---
const LoadingSkeleton = React.memo(() => (
  <div className="p-6 lg:p-8 space-y-8">
    <div className="animate-pulse bg-slate-200/50 h-40 rounded-3xl"></div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="w-full h-32 bg-slate-200/50 rounded-3xl" />
      ))}
    </div>
    <div className="w-full h-96 bg-slate-200/50 rounded-3xl" />
    <div className="w-full h-96 bg-slate-200/50 rounded-3xl" />
  </div>
));

// eslint-disable-next-line
const StatCard = React.memo(({ title, value, icon: Icon, color, delay = 0 }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0, transition: { delay, duration: 0.5, ease: 'easeOut' } }}
    whileHover={{ y: -5, scale: 1.03, boxShadow: '0 25px 50px -12px rgba(0,0,0,0.15)' }}
    className="bg-white/70 backdrop-blur-xl rounded-2xl shadow-lg p-6 border border-white/30 hover:border-slate-300/50 transition-all duration-300 relative overflow-hidden group">
    <div className="absolute top-0 right-0 -mt-10 -mr-10 w-24 h-24 bg-blue-100/30 rounded-full blur-2xl group-hover:scale-150 transition-transform duration-500"></div>
    <div className="relative z-10 flex items-start justify-between">
      <div className="space-y-1">
        <p className="text-sm font-semibold text-slate-600 tracking-wide uppercase">{title}</p>
        <p className={`text-4xl font-black ${color}`}>{value}</p>
      </div>
      <div className="p-3 rounded-xl bg-slate-100/80 shadow-inner">
        <Icon className={`w-6 h-6 ${color}`} />
      </div>
    </div>
  </motion.div>
));

// eslint-disable-next-line
const SectionHeader = React.memo(({ title, icon: Icon, isExpanded, onToggle }) => (
  <motion.button
    onClick={onToggle}
    whileHover={{ scale: 1.01 }}
    className="flex items-center justify-between w-full mb-6 group">
    <div className="flex items-center space-x-4">
      <div className="p-3 rounded-xl bg-white/80 backdrop-blur-md shadow-lg border border-white/50">
        <Icon className="w-6 h-6 text-slate-700" />
      </div>
      <h2 className="text-2xl font-bold text-slate-800 tracking-tight">{title}</h2>
    </div>
    <motion.div
      animate={{ rotate: isExpanded ? 0 : -90 }}
      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
      className="p-2 rounded-full bg-slate-100 group-hover:bg-slate-200 transition-colors">
      <ChevronDown className="w-5 h-5 text-slate-500" />
    </motion.div>
  </motion.button>
));

// --- MAIN COMPONENT ---
const DirectorAnalyticalByStudent = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const profile = location.state;
  const [res, setRes] = useState(null);

  const [getStudentDataByStudentId, { isLoading: isApiLoading }] =
    useLazyGetStudentDataByStudentIdServiceQuery();
  const studentData = useSelector((state) => state.directorAnalyticsDashboard.studentData);

  const { overallStats, subjectStats, chartData, allTestsForHistory, helpers } =
    useStudentAnalytics(studentData);

  const hasTestData = useMemo(() => {
    if (!studentData || Object.keys(studentData)?.length === 0) return false;
    return Object.values(studentData).some((tests) => Array.isArray(tests) && tests?.length > 0);
  }, [studentData]);

  const [expandedSections, setExpandedSections] = useState({
    [CONSTANTS.SECTIONS.OVERVIEW]: true,
    [CONSTANTS.SECTIONS.CHARTS]: true,
    [CONSTANTS.SECTIONS.SUBJECTS]: true,
    [CONSTANTS.SECTIONS.HISTORY]: true
  });

  const [filterSubject, setFilterSubject] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  // ... (useMemo and useEffect hooks remain the same)
  const filteredTests = useMemo(() => {
    if (filterSubject === 'all') return allTestsForHistory;
    return allTestsForHistory.filter(
      (test) => test.subject.toLowerCase() === filterSubject.toLowerCase()
    );
  }, [allTestsForHistory, filterSubject]);

  const totalPages = useMemo(
    () => Math.ceil(filteredTests?.length / CONSTANTS.ITEMS_PER_PAGE),
    [filteredTests]
  );

  const paginatedTests = useMemo(() => {
    const startIndex = (currentPage - 1) * CONSTANTS.ITEMS_PER_PAGE;
    return filteredTests.slice(startIndex, startIndex + CONSTANTS.ITEMS_PER_PAGE);
  }, [filteredTests, currentPage]);

  useEffect(() => {
    setCurrentPage(1);
  }, [filterSubject]);

  useEffect(() => {
    if (!profile) {
      navigate(-1);
      return;
    }

    const handleGetStudentDataByStudentId = async () => {
      try {
        const res = await getStudentDataByStudentId({
          studentId: profile.id,
          course: profile.course
        }).unwrap();
        dispatch(setStudentData(res.data));
      } catch (error) {
        setRes(error);
        dispatch(setStudentData(null));
      }
    };

    handleGetStudentDataByStudentId();

    return () => {
      dispatch(clearStudentData());
    };
  }, [profile, getStudentDataByStudentId, dispatch, navigate]);

  const handleBack = useCallback(() => navigate(-1), [navigate]);
  const toggleSection = useCallback((section) => {
    setExpandedSections((prev) => ({ ...prev, [section]: !prev[section] }));
  }, []);
  const handleFilterChange = useCallback((subject) => setFilterSubject(subject), []);
  const handlePageChange = useCallback(
    (page) => {
      if (page > 0 && page <= totalPages) setCurrentPage(page);
    },
    [totalPages]
  );

  const DirectorHeader = ({ onBack, profileName }) => (
    <motion.header
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0, transition: { duration: 0.6, ease: 'easeOut' } }}
      className="bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 shadow-2xl sticky top-0 z-30 backdrop-blur-xl border-b border-white/10">
      {/* <div className='absolute inset-0 bg-[url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.04\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")]'></div> */}
      <div className="relative px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div whileHover={{ scale: 1.1, rotate: -8 }} whileTap={{ scale: 0.9 }}>
              <Button
                className="bg-white/10 backdrop-blur-md border border-white/20 p-3 rounded-xl hover:bg-white/20 transition-all"
                icon={<ArrowLeft className="text-white w-5 h-5" />}
                onClick={onBack}
              />
            </motion.div>
            <div>
              <h1 className="text-2xl font-bold text-white tracking-tight">Student Analytics</h1>
              <p className="text-purple-200/80 text-sm mt-0.5">
                {profileName || 'Student'}'s Dashboard
              </p>
            </div>
          </div>
        </div>
      </div>
    </motion.header>
  );

  const StudentInfoBox = ({ profile }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0, transition: { delay: 0.2, duration: 0.5 } }}
      className="mx-6 mt-6 bg-white/60 backdrop-blur-2xl rounded-3xl shadow-xl border border-white/30 p-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
        {[
          { label: 'Name', value: profile.name, icon: User, color: 'text-purple-600' },
          { label: 'Course', value: profile.course, icon: GraduationCap, color: 'text-sky-600' },
          { label: 'Batch', value: profile.batchname, icon: Users, color: 'text-emerald-600' },
          { label: 'Grade', value: profile.grade, icon: Calendar, color: 'text-amber-600' }
          // eslint-disable-next-line
        ].map(({ label, value, icon: Icon, color }) => (
          <div key={label} className="flex items-center space-x-3">
            <div className="p-3 bg-white rounded-lg shadow-inner border border-slate-200/50">
              <Icon className={`w-5 h-5 ${color}`} />
            </div>
            <div>
              <p className="text-xs font-semibold text-slate-500 uppercase tracking-wider">
                {label}
              </p>
              <p className="font-bold text-slate-800 capitalize">{value || 'N/A'}</p>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );

  if (!profile) return null;

  return (
    <div className="min-h-screen bg-slate-50 relative overflow-hidden">
      {/* Aurora Background */}
      <div className="absolute top-0 left-0 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-purple-200/40 rounded-full blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2 w-[800px] h-[800px] bg-sky-200/40 rounded-full blur-3xl -z-10 animate-ping delay-1000"></div>

      <Toastify res={res} resClear={() => setRes(null)} />
      <DirectorHeader onBack={handleBack} profileName={profile?.name} />

      {isApiLoading ? (
        <LoadingSkeleton />
      ) : (
        <>
          <StudentInfoBox profile={profile} />
          {!hasTestData ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center justify-center min-h-[60vh] p-6">
              <div className="text-center bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl p-12 border border-white/50">
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
                  className="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <BookOpen className="w-10 h-10 text-slate-500" />
                </motion.div>
                <h2 className="text-2xl font-bold text-slate-800 mb-2">No Test Data Found</h2>
                <p className="text-slate-500 max-w-md mx-auto">
                  It seems {profile.name} hasn't completed any tests yet. Data will appear here
                  automatically.
                </p>
              </div>
            </motion.div>
          ) : (
            <main className="px-6 py-8 space-y-10">
              <motion.section
                variants={{
                  hidden: { opacity: 0 },
                  visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
                }}
                initial="hidden"
                animate="visible">
                <SectionHeader
                  title="Performance Overview"
                  icon={TrendingUp}
                  isExpanded={expandedSections.overview}
                  onToggle={() => toggleSection(CONSTANTS.SECTIONS.OVERVIEW)}
                />
                <AnimatePresence>
                  {expandedSections.overview && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.4, ease: 'easeInOut' }}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <StatCard
                        title="Total Tests"
                        value={overallStats.totalTests}
                        icon={BookOpen}
                        color="text-purple-600"
                        delay={0.1}
                      />
                      <StatCard
                        title="Overall Accuracy"
                        value={`${overallStats.accuracy}%`}
                        icon={Target}
                        color={helpers.getAccuracyColor(overallStats.accuracy)}
                        delay={0.2}
                      />
                      <StatCard
                        title="Average Score"
                        value={overallStats.averageScore}
                        icon={Award}
                        color="text-sky-600"
                        delay={0.3}
                      />
                      <StatCard
                        title="Correct Answers"
                        value={overallStats.totalCorrect}
                        icon={Zap}
                        color="text-emerald-600"
                        delay={0.4}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.section>

              <motion.section>
                <SectionHeader
                  title="Performance Analytics"
                  icon={BarChart3}
                  isExpanded={expandedSections.charts}
                  onToggle={() => toggleSection(CONSTANTS.SECTIONS.CHARTS)}
                />
                <AnimatePresence>
                  {expandedSections.charts && chartData && (
                    <AnalyticsCharts chartData={chartData} />
                  )}
                </AnimatePresence>
              </motion.section>

              <motion.section>
                <SectionHeader
                  title="Subject-wise Performance"
                  icon={BookOpen}
                  isExpanded={expandedSections.subjects}
                  onToggle={() => toggleSection(CONSTANTS.SECTIONS.SUBJECTS)}
                />
                <AnimatePresence>
                  {expandedSections.subjects && (
                    <SubjectPerformanceGrid subjectStats={subjectStats} helpers={helpers} />
                  )}
                </AnimatePresence>
              </motion.section>

              <motion.section>
                <SectionHeader
                  title="Detailed Test History"
                  icon={Clock}
                  isExpanded={expandedSections.history}
                  onToggle={() => toggleSection(CONSTANTS.SECTIONS.HISTORY)}
                />
                <AnimatePresence>
                  {expandedSections.history && (
                    <TestHistoryTable
                      tests={paginatedTests}
                      helpers={helpers}
                      subjects={Object.keys(subjectStats)}
                      activeFilter={filterSubject}
                      onFilterChange={handleFilterChange}
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  )}
                </AnimatePresence>
              </motion.section>
            </main>
          )}
        </>
      )}
    </div>
  );
};

const AnalyticsCharts = React.memo(({ chartData }) => (
  <motion.div
    initial={{ opacity: 0, height: 0 }}
    animate={{ opacity: 1, height: 'auto' }}
    exit={{ opacity: 0, height: 0 }}
    transition={{ duration: 0.4, ease: 'easeInOut' }}>
    <div className="bg-white/70 backdrop-blur-xl rounded-2xl shadow-lg p-6 border border-white/30">
      <h3 className="text-lg font-bold text-slate-700 mb-1">Test Performance Trend</h3>
      <p className="text-sm text-slate-500 mb-4">Performance in the last 10 tests</p>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <RechartsLineChart data={chartData.performanceTrend}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis dataKey="test" stroke="#64748b" fontSize={12} tick={{ fill: '#64748b' }} />
            <YAxis stroke="#64748b" fontSize={12} tick={{ fill: '#64748b' }} />
            <Tooltip
              contentStyle={{
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                backdropFilter: 'blur(5px)',
                border: '1px solid #e2e8f0',
                borderRadius: '12px',
                boxShadow: '0 10px 15px -3px rgba(0,0,0,0.1)'
              }}
            />
            <Line
              type="monotone"
              dataKey="correct"
              stroke="#10b981"
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
            <Line
              type="monotone"
              dataKey="incorrect"
              stroke="#ef4444"
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
            <Line
              type="monotone"
              dataKey="unattempted"
              stroke="#64748b"
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
          </RechartsLineChart>
        </ResponsiveContainer>
      </div>
    </div>
  </motion.div>
));

const SubjectPerformanceGrid = React.memo(({ subjectStats, helpers }) => (
  <motion.div
    initial={{ opacity: 0, height: 0 }}
    animate={{ opacity: 1, height: 'auto' }}
    exit={{ opacity: 0, height: 0 }}
    transition={{ duration: 0.4, ease: 'easeInOut' }}>
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {Object.entries(subjectStats).map(([subject, stats], index) => {
        const config = getSubjectConfig(subject);
        return (
          <motion.div
            key={subject}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0, transition: { delay: index * 0.05 } }}
            whileHover={{ y: -5, scale: 1.03, boxShadow: '0 25px 50px -12px rgba(0,0,0,0.15)' }}
            className="bg-white/70 backdrop-blur-xl rounded-2xl shadow-lg border border-white/30 overflow-hidden group transition-all duration-300">
            <div className={`h-2 bg-gradient-to-r ${config.gradient}`}></div>
            <div className="p-5">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-slate-800 capitalize">{subject}</h3>
                {stats && stats.accuracy >= 80 && <Trophy className="w-5 h-5 text-amber-400" />}
              </div>
              <div className="text-center mb-4 p-4 bg-slate-50/70 rounded-lg">
                <p className={`text-4xl font-black ${helpers.getAccuracyColor(stats.accuracy)}`}>
                  {stats.accuracy}%
                </p>
                <p className="text-xs font-semibold text-slate-500 uppercase tracking-wider">
                  Accuracy
                </p>
              </div>
              <div className="flex justify-around text-center text-sm">
                <div>
                  <p className="font-bold text-slate-700">{stats.testsCount}</p>
                  <p className="text-xs text-slate-500">Tests</p>
                </div>
                <div>
                  <p className="font-bold text-slate-700">{stats.averageScore}</p>
                  <p className="text-xs text-slate-500">Avg. Score</p>
                </div>
              </div>
            </div>
          </motion.div>
        );
      })}
    </div>
  </motion.div>
));

const TestHistoryTable = React.memo(
  ({ tests, subjects, activeFilter, onFilterChange, currentPage, totalPages, onPageChange }) => (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.4, ease: 'easeInOut' }}
      className="bg-white/70 backdrop-blur-xl rounded-2xl shadow-lg border border-white/30">
      <div className="p-4 flex flex-wrap items-center gap-2 border-b border-slate-200/80">
        <span className="text-sm font-semibold text-slate-600 mr-2">Filter:</span>
        <FilterButton
          label="All"
          isActive={activeFilter === 'all'}
          onClick={() => onFilterChange('all')}
        />
        {subjects.map((subject) => (
          <FilterButton
            key={subject}
            label={subject}
            isActive={activeFilter === subject}
            onClick={() => onFilterChange(subject)}
          />
        ))}
      </div>

      <div className="overflow-x-auto">
        {tests?.length > 0 ? (
          <table className="w-full min-w-[1000px] text-sm">
            <thead className="text-xs text-slate-500 uppercase">
              <tr>
                <th className="px-6 py-3 text-left font-semibold">Test Name</th>
                <th className="px-6 py-3 text-left font-semibold">Subject</th>
                <th className="px-6 py-3 text-left font-semibold">Date</th>
                <th className="px-6 py-3 text-center font-semibold">Score</th>
                <th className="px-6 py-3 text-left font-semibold">Accuracy</th>
                <th className="px-6 py-3 text-center font-semibold">Performance</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-200/50">
              {tests.map((test, index) => (
                <TestHistoryRow key={index} test={test} index={index} />
              ))}
            </tbody>
          </table>
        ) : (
          <div className="text-center py-16">
            <Clock className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-bold text-slate-700">No Tests Found</h3>
            <p className="text-slate-500">No tests match the current filter.</p>
          </div>
        )}
      </div>

      {totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
        />
      )}
    </motion.div>
  )
);

const FilterButton = ({ label, isActive, onClick }) => (
  <motion.button
    whileHover={{ scale: 1.05 }}
    whileTap={{ scale: 0.95 }}
    onClick={onClick}
    className={`px-3 py-1.5 text-sm font-semibold rounded-lg transition-all capitalize ${
      isActive
        ? 'bg-slate-800 text-white shadow-md'
        : 'bg-white text-slate-700 hover:bg-slate-100 border border-slate-200'
    }`}>
    {label}
  </motion.button>
);

const TestHistoryRow = ({ test, index }) => {
  const { num_correct, num_incorrect, num_unattempted, score } = test.evaluation_results;
  const attempted = num_correct + num_incorrect;
  const accuracy = attempted > 0 ? (num_correct / attempted) * 100 : 0;
  const config = getSubjectConfig(test.subject);
  const accuracyColor =
    accuracy >= 80 ? 'bg-emerald-500' : accuracy >= 60 ? 'bg-amber-500' : 'bg-red-500';

  return (
    <motion.tr
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0, transition: { delay: index * 0.03 } }}
      className="hover:bg-slate-50/50 transition-colors">
      <td className="px-6 py-4 font-bold text-slate-800">{test.unit_name}</td>
      <td className="px-6 py-4">
        <div className="flex items-center gap-2">
          <div className={`w-2.5 h-2.5 rounded-full ${config.dot}`}></div>
          <span className="font-semibold capitalize">{test.subject}</span>
        </div>
      </td>
      <td className="px-6 py-4 text-slate-600">
        {new Date(test.start_time.split(' & ')[0]).toLocaleDateString()}
      </td>
      <td className="px-6 py-4 text-center">
        <span className="font-black text-lg text-slate-700">{score?.toFixed(1)}</span>
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center gap-3">
          <span className={`w-14 font-bold ${config.text}`}>{accuracy.toFixed(1)}%</span>
          <div className="w-full bg-slate-200 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full ${accuracyColor}`}
              style={{ width: `${accuracy}%` }}></div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="flex justify-center items-center gap-3 text-xs font-bold">
          <span className="flex items-center gap-1 text-emerald-600 bg-emerald-100/70 px-2 py-1 rounded">
            <CheckCircle size={14} />
            {num_correct}
          </span>
          <span className="flex items-center gap-1 text-red-600 bg-red-100/70 px-2 py-1 rounded">
            <XCircle size={14} />
            {num_incorrect}
          </span>
          <span className="flex items-center gap-1 text-slate-500 bg-slate-100/70 px-2 py-1 rounded">
            <MinusCircle size={14} />
            {num_unattempted}
          </span>
        </div>
      </td>
    </motion.tr>
  );
};

const PaginationControls = ({ currentPage, totalPages, onPageChange }) => (
  <div className="flex items-center justify-between p-4 border-t border-slate-200/80">
    <span className="text-sm font-semibold text-slate-600">
      Page {currentPage} of {totalPages}
    </span>
    <div className="flex items-center gap-2">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="p-2 rounded-lg bg-white border border-slate-200 disabled:opacity-50 hover:bg-slate-100">
        <ChevronLeft size={16} />
      </button>
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="p-2 rounded-lg bg-white border border-slate-200 disabled:opacity-50 hover:bg-slate-100">
        <ChevronRight size={16} />
      </button>
    </div>
  </div>
);

export default DirectorAnalyticalByStudent;
