'use client';

import { useRef, useState } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import TopicMapping from './TopicMapping';
import SubTopicMapping from './SubTopicMapping';
import ContentMapping from './ContentMapping';
import { Layers3, Layers, FileText } from 'lucide-react'; // Added icons for better UX

const ProcessSelector = () => {
  const [activeTab, setActiveTab] = useState('topic');
  const tabRefs = useRef([]);

  const tabs = [
    {
      id: 'topic',
      label: 'Topic Mapping',
      component: <TopicMapping />,
      icon: <Layers3 size={16} />
    },
    {
      id: 'subtopic',
      label: 'Sub-Topic Mapping',
      component: <SubTopicMapping />,
      icon: <Layers size={16} />
    },
    {
      id: 'content',
      label: 'Content Mapping',
      component: <ContentMapping />,
      icon: <FileText size={16} />
    }
  ];

  const activeComponent = tabs.find((tab) => tab.id === activeTab)?.component;

  // Keyboard navigation logic remains unchanged - it's great for accessibility!
  const handleKeyDown = (e) => {
    const currentIndex = tabs.findIndex((t) => t.id === activeTab);
    let nextIndex = currentIndex;

    if (e.key === 'ArrowRight') nextIndex = (currentIndex + 1) % tabs.length;
    else if (e.key === 'ArrowLeft') nextIndex = (currentIndex - 1 + tabs.length) % tabs.length;
    else if (e.key === 'Home') nextIndex = 0;
    else if (e.key === 'End') nextIndex = tabs.length - 1;
    else return;

    e.preventDefault();
    const nextTab = tabs[nextIndex];
    setActiveTab(nextTab.id);
    requestAnimationFrame(() => {
      tabRefs.current[nextIndex]?.focus();
    });
  };

  return (
    <div className="w-full max-w-full mx-auto p-2">
      {/* Modern Pill-Style Tab Navigation */}
      <div
        role="tablist"
        aria-label="Process Mapping Tabs"
        onKeyDown={handleKeyDown}
        className="relative flex items-center gap-2 rounded-lg bg-slate-100 p-1.5">
        {tabs.map((tab, idx) => {
          const isActive = activeTab === tab.id;
          return (
            <button
              key={tab.id}
              ref={(el) => (tabRefs.current[idx] = el)}
              role="tab"
              aria-selected={isActive}
              aria-controls={`panel-${tab.id}`}
              id={`tab-${tab.id}`}
              onClick={() => setActiveTab(tab.id)}
              className={`relative w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-semibold transition-colors rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-100 ${
                isActive ? 'text-white' : 'text-slate-600 hover:text-slate-900'
              }`}>
              {/* Active tab background slider */}
              {isActive && (
                <motion.div
                  layoutId="active-pill"
                  className="absolute inset-0 bg-indigo-600 rounded-md shadow-sm"
                  transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                />
              )}
              {/* Icon and Label */}
              <span className="relative z-10">{tab.icon}</span>
              <span className="relative z-10 whitespace-nowrap">{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            id={`panel-${activeTab}`}
            role="tabpanel"
            aria-labelledby={`tab-${activeTab}`}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.25, ease: 'easeInOut' }}
            className="rounded-md">
            {activeComponent}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ProcessSelector;
