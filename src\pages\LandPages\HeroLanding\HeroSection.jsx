import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faRobot,
  faUserTie,
  faClock,
  faShieldAlt,
  faMobileScreenButton,
  faGlobe
} from '@fortawesome/free-solid-svg-icons';
const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      image:
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/testimonial/smart_class_023.jpg',
      title: (
        <>
          Crack NEET 2026 from Your Hometown,
          <br />
          <span className="text-[var(--color-counselor)]">With AI + IIT Mentors.</span>
        </>
      ),
      description:
        'No coaching center? No problem. Sasthra brings Kota-level JEE/NEET 2026 coaching to your screen. AI personalizes your study plan. Live doubt-solving with IITians & AIIMS doctors. Start free today!',
      buttonText: 'Start NEET 2026 Free Trial',
      buttonLink: '/auth',
      seoKeywords:
        'Best NEET 2026 online coaching India, Top medical entrance 2026 coaching, AIIMS preparation 2026, Hindi medium NEET 2026, Sasthra NEET online course'
    },
    {
      image: 'https://cdn.itm.ac.in/2024/05/tech-jobs-in-India--6--5.webp',
      title: (
        <>
          Master JEE 2026 Physics & Math,
          <br />
          <span className="text-[var(--color-counselor)] font-bold">
            AI Highlights Your Growth Zones{' '}
          </span>
        </>
      ),
      description:
        'Stop wasting time. Our AI analyzes your mock tests and pinpoints exactly which JEE 2026 chapters you need to revise. Get video lectures, PYQs, and future AIR predictor. Designed for foundation students & Class 11 starters.',
      buttonText: 'Fix My JEE 2026 Weak Areas',
      buttonLink: '/jee',
      seoKeywords:
        'Best JEE Main 2026 coaching, Top JEE Advanced 2026 coaching, Physics Chemistry Math JEE 2026, IIT JEE online classes 2026, Sasthra JEE online course'
    },
    {
      image:
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/s3_imgs_sasthra/1.jpg',
      title: (
        <>
          JEE/NEET 2026 ,
          <br />
          <span className="text-[var(--color-counselor)] font-bold">
            Balance Boards & Entrance Exams.
          </span>
        </>
      ),
      description: `Don't sacrifice boards for JEE/NEET. Sasthra's AI creates a dual-track plan — cover CBSE 2026 syllabus while prepping for entrance exams. Daily targets, and full mock tests. Join 500+ Indian students.`,
      buttonText: 'Balance CBSE + JEE/NEET 2026',
      buttonLink: '/aboutus',
      seoKeywords:
        'CBSE Class 12 2026 with JEE NEET, board exam preparation 2026, NCERT based JEE 2026, CBSE + competitive exam strategy, Foundation Coaching'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 7000);
    return () => clearInterval(interval);
  }, [slides.length]);

  const slideVariants = {
    initial: { opacity: 0, scale: 0.95 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    },
    exit: {
      opacity: 0,
      scale: 1.05,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const contentVariants = {
    initial: { y: 30, opacity: 0 },
    animate: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.7,
        ease: [0.22, 1, 0.36, 1]
      }
    },
    exit: {
      y: -30,
      opacity: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  // Indian Pattern Background Component
  const IndianPatternBackground = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-[0.03]">
      <svg width="100%" height="100%" className="absolute inset-0">
        <pattern
          id="indian-pattern"
          x="0"
          y="0"
          width="80"
          height="80"
          patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="2" fill="#c05621" />
          <circle cx="30" cy="30" r="2" fill="#c05621" />
          <circle cx="50" cy="50" r="2" fill="#c05621" />
          <circle cx="70" cy="70" r="2" fill="#c05621" />
          <path d="M0,40 L80,40 M40,0 L40,80" stroke="#c05621" strokeWidth="1" />
        </pattern>
        <rect x="0" y="0" width="100%" height="100%" fill="url(#indian-pattern)" />
      </svg>
    </div>
  );

  return (
    <section
      className="min-h-screen relative overflow-hidden bg-[var(--color-teacher)] pt-20"
      aria-label="Sasthra: Best NEET 2026 Coaching, Top JEE 2026 Coaching & CBSE 2026 Preparation in India with AI Personalized Learning and Expert Faculty">
      {/* Background with Indian aesthetic */}
      <div className="absolute inset-0">
        <IndianPatternBackground />
      </div>

     

      <div className="relative z-10 h-full flex items-center justify-center py-12">
        <div className="container mx-auto px-4 flex flex-col lg:flex-row items-center justify-between gap-10">
          {/* Text Content */}
          <div className="text-gray-800 max-w-2xl lg:max-w-2xl">
            <AnimatePresence mode="wait">
              <motion.h1
                key={`title-${currentSlide}`}
                variants={contentVariants}
                initial="initial"
                animate="animate"
                exit="exit"
                className="text-4xl text-white md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                {slides[currentSlide].title}
              </motion.h1>
            </AnimatePresence>

            <AnimatePresence mode="wait">
              <motion.p
                key={`description-${currentSlide}`}
                variants={contentVariants}
                initial="initial"
                animate="animate"
                exit="exit"
                transition={{ delay: 0.2 }}
                className="text-lg md:text-xl text-gray-300 leading-relaxed mb-8">
                {slides[currentSlide].description}
              </motion.p>
            </AnimatePresence>

            {/* Regional Language Support */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="mb-8 flex flex-wrap gap-3">
              <div className="flex items-center bg-amber-100 px-4 py-2 rounded-full border border-amber-200">
               <FontAwesomeIcon icon={faGlobe} className="mr-2 text-amber-600" />
                <span className="text-sm font-medium text-amber-700">
                  Available in Hindi, Tamil, Telugu, Kannada, Malayalam, English 
                </span>
              </div>
              <div className="flex items-center bg-blue-100 px-4 py-2 rounded-full border border-blue-200">
               <FontAwesomeIcon icon={faMobileScreenButton} className="mr-2 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">Mobile & Desktop Friendly</span>
              </div>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="flex items-center space-x-8">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-[var(--color-counselor)] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <span className="text-white text-sm font-bold">✓</span>
                </div>
                <span className="text-sm font-medium text-gray-300">IITian Mentors</span>
              </div>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <span className="text-sm font-medium text-gray-300">Personalized Learning</span>
              </div>
            </motion.div>
          </div>

          {/* Image Content */}
          <div className="relative w-full lg:w-2/5 mt-10 lg:mt-0">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                className="relative"
                variants={slideVariants}
                initial="initial"
                animate="animate"
                exit="exit"
                role="img"
                aria-label={`Indian student preparing for ${slides[currentSlide].seoKeywords}`}>
                {/* Image Container with Indian-style border */}
                <div className="relative rounded-2xl overflow-hidden shadow-2xl border-4 border-amber-100">
                  <img
                    src={slides[currentSlide].image}
                    alt={`NEET 2026 or JEE 2026 student studying with AI mentor - ${slides[currentSlide].buttonText}`}
                    className="w-full h-80 object-cover"
                    loading="eager"
                    fetchpriority="high"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-amber-900/10 to-transparent"></div>

                  {/* Decorative corner elements */}
                  <div className="absolute top-3 left-3 w-5 h-5 border-t-2 border-l-2 border-amber-500"></div>
                  <div className="absolute top-3 right-3 w-5 h-5 border-t-2 border-r-2 border-amber-500"></div>
                  <div className="absolute bottom-3 left-3 w-5 h-5 border-b-2 border-l-2 border-amber-500"></div>
                  <div className="absolute bottom-3 right-3 w-5 h-5 border-b-2 border-r-2 border-amber-500"></div>
                </div>

                {/* Sasthra Value Preview Card — Creative UI/UX with Font Awesome */}
                <motion.div
                  className="absolute -bottom-5 left-0 right-0 mx-auto w-11/12 bg-white rounded-xl shadow-lg p-4 border border-amber-200 hover:shadow-xl transition-shadow duration-300"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ scale: 1.02 }}
                  role="region"
                  aria-label="Sasthra platform features preview">
                  <div className="flex justify-between items-center">
                    {/* Role Support */}
                    <motion.div
                      className="text-center flex flex-col items-center group"
                      whileHover={{ y: -3 }}
                      transition={{ type: 'spring', stiffness: 300 }}>
                      <div className="relative">
                      
                        <FontAwesomeIcon
                          icon={faUsers}
                          className="text-[var(--color-trainee)]  text-2xl  group-hover:scale-110 transition-transform"
                        />
                      </div>
                      <div className="text-xs text-gray-600 font-medium mt-1">3 Roles Supported</div>
                    </motion.div>

                    <div className="h-8 w-px bg-amber-200"></div>

                    {/* AI + Human */}
                    <motion.div
                      className="text-center flex flex-col items-center group"
                      whileHover={{ y: -3 }}
                      transition={{ type: 'spring', stiffness: 300 }}>
                      <div className="relative">
                        <div className="text-2xl font-bold text-blue-600">AI+    <FontAwesomeIcon
                          icon={faUserTie}
                          className="text-[var(--color-trainee)]  text-2xl  group-hover:scale-110 transition-transform"
                        /></div>
                      
                     
                      </div>
                      <div className="text-xs text-gray-600 font-medium mt-1">Smart + Human</div>
                    </motion.div>

                    <div className="h-8 w-px bg-amber-200"></div>

                    {/* Access Type */}
                    <motion.div
                      className="text-center flex flex-col items-center group"
                      whileHover={{ y: -3 }}
                      transition={{ type: 'spring', stiffness: 300 }}>
                      <div className="relative">
                        <div className="text-2xl font-bold text-green-600">24/7</div>
                      
                      </div>
                      <div className="text-xs text-gray-600 font-medium mt-1">Live & On-Demand</div>
                    </motion.div>
                  </div>

                </motion.div>
              </motion.div>
            </AnimatePresence>

            {/* Slide Indicators */}
            <div className="flex justify-center gap-3 mt-16">
              {slides.map((_, index) => (
                <motion.button
                  key={index}
                  className={`w-3 h-3 rounded-full cursor-pointer transition-all ${
                    index === currentSlide
                      ? 'bg-amber-600 scale-125 shadow-md shadow-amber-600/30'
                      : 'bg-amber-300 hover:bg-amber-400'
                  }`}
                  animate={{ scale: index === currentSlide ? 1.25 : 1 }}
                  transition={{ duration: 0.3 }}
                  onClick={() => setCurrentSlide(index)}
                  aria-label={`Navigate to slide ${index + 1}: ${slides[index].buttonText}`}>
                  <span className="sr-only">{slides[index].buttonText}</span>
                </motion.button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Structured SEO Data - Hidden but crawlable */}
      <div className="sr-only" aria-hidden="true">
        <h1>Best AI-Powered NEET 2026 Coaching, Top JEE 2026 Coaching | Sasthra</h1>
        <p>
          Sasthra is India's best online coaching platform for NEET 2026, JEE Main 2026, JEE
          Advanced 2026. Get personalized AI study plans, live mentorship from IITians and AIIMS
          doctors, chapter-wise weakness analysis, and All India Rank prediction. Start your free
          trial today and join thousands of students from Kota, Delhi, Chennai, Mumbai, and Tier 2/3
          cities across India. Available in Hindi, Tamil, Telugu, Bengali, and English.
        </p>
        <h2>Why Choose Sasthra for NEET/JEE 2026?</h2>
        <p>
          India's best NEET/JEE coaching with our hybrid learning platform offering NEET & JEE
          coaching through AI-powered tools and expert teachers. Experience Sasthra's live
          multilingual classes, mock tests, and personalized learning. Crack NEET/JEE 2026 with
          Sasthra AI personalized learning + expert faculty. Unlike traditional coaching, Sasthra
          adapts to YOU. Whether you're from a small town without access to Kota coaching, a working
          professional, or a student juggling boards and entrance exams — our AI + human mentorship
          model ensures you stay on track for 2026 success.
        </p>
        <h2>Key Features for NEET 2026 & JEE 2026 Students:</h2>
        <ul>
          <li>Personalized AI Study Planner for NEET 2026 and JEE 2026</li>
          <li>Live Doubt Solving with IIT </li>

          <li>Chapter-wise Weakness Detection in Physics, Chemistry, Biology, Math</li>
          <li>Real-time AI Performance Analytics</li>
          <li>Regional Language Support: Hindi, Tamil, Telugu, Bengali Medium</li>
          <li>Mobile App for Learning On-the-Go — Anytime, Anywhere</li>
          <li>Designed for foundation Students and First-Time Aspirants</li>
        </ul>
        <h2>Sasthra Courses:</h2>
        <ul>
          <li>NEET Coaching</li>
          <li>JEE Coaching</li>
          <li>JEE Advanced Coaching</li>
          <li>Coaching for Class 8th</li>
          <li>Coaching for Class 9th</li>
          <li>Coaching for Class 10th</li>
          <li>Coaching for class 11th</li>
          <li>Coaching for Class 12th</li>
          <li>Olympiad Coaching</li>
          <li>Foundation Coaching</li>
          <li>Best Coaching for NEET</li>
          <li>Best Coaching for JEE</li>
          <li>NEET UG coaching</li>
          <li>AI personalised coaching</li>
          <li>Kota experts coaching</li>
          <li>Best NEET online coaching</li>
          <li>Best JEE online coaching</li>
        </ul>
        <p>
          Talk to our expert, Talk to our chatbot for personalized guidance on your NEET/JEE 2026
          preparation journey.
        </p>
      </div>
    </section>
  );
};

export default HeroSection;
