import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { setStudentQuizData, useLazyGetStudentQuizServiceQuery } from '../dashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { motion } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

// Import Highcharts extensions for their side effects
import 'highcharts/highcharts-more';

import {
  <PERSON><PERSON><PERSON>,
  Award,
  CheckSquare,
  Info,
  RefreshCw,
  X,
  PieChart,
  TrendingUp,
  GitCommit
} from 'lucide-react';

// --- Constants & Global Highcharts Config ---
const PRIMARY_GRADIENT = 'bg-gradient-to-br from-blue-500 to-indigo-600';

Highcharts.setOptions({
  chart: { backgroundColor: 'transparent', style: { fontFamily: 'Inter, sans-serif' } },
  credits: { enabled: false },
  title: { text: null },
  legend: { itemStyle: { color: '#64748b' }, itemHoverStyle: { color: '#1e293b' } },
  xAxis: { labels: { style: { color: '#64748b' } }, lineColor: '#e2e8f0', tickColor: '#e2e8f0' },
  yAxis: {
    gridLineColor: '#f1f5f9',
    labels: { style: { color: '#94a3b8' } },
    title: { style: { color: '#94a3b8' } }
  },
  tooltip: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e2e8f0',
    style: { color: '#334155' },
    headerFormat: '<span style="font-size: 10px">{point.key}</span><br/>',
    shadow: true
  },
  accessibility: { enabled: false }
});

// --- Helper & UI Components (Consistent with other dashboard tabs) ---
const GlassCard = ({ children, className = '' }) => (
  <div
    className={`bg-white/60 backdrop-blur-xl border border-white/30 rounded-2xl shadow-lg shadow-blue-500/10 ring-1 ring-black/5 ${className}`}>
    {children}
  </div>
);
const QuizSkeleton = () => (
  <div className="animate-pulse space-y-6">
    <div className="h-10 w-48 bg-slate-200 rounded-lg"></div>
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(3)]?.map((_, i) => (
        <div key={i} className="h-28 bg-slate-200 rounded-xl"></div>
      ))}
    </div>
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="h-80 bg-slate-200 rounded-xl"></div>
      <div className="h-80 bg-slate-200 rounded-xl"></div>
    </div>
    <div className="h-64 bg-slate-200 rounded-xl"></div>
  </div>
);
const ErrorDisplay = ({ error, onRetry }) => (
  <div className="flex flex-col items-center justify-center text-center p-8 bg-red-50/50 border border-red-200 rounded-xl">
    <div className="w-14 h-14 flex items-center justify-center bg-red-100 rounded-full mb-4">
      <X className="w-8 h-8 text-red-600" />
    </div>
    <h3 className="text-xl font-semibold text-red-800">Failed to Load Data</h3>
    <p className="text-red-600 mt-2 mb-4 max-w-md">
      {error?.data?.message || 'An unexpected error occurred.'}
    </p>
    <button
      onClick={onRetry}
      className="flex items-center px-4 py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 transition-colors shadow-sm">
      <RefreshCw className="w-4 h-4 mr-2" /> Try Again
    </button>
  </div>
);
const EmptyState = ({ message }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    className="text-center p-12 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/30 shadow-lg shadow-blue-500/10">
    <div className="w-14 h-14 flex items-center justify-center bg-slate-100 rounded-full mb-4 mx-auto">
      <Info className="w-8 h-8 text-slate-400" />
    </div>
    <h3 className="text-xl font-semibold text-slate-700">No Quizzes Found</h3>
    <p className="text-slate-500 mt-2">{message}</p>
  </motion.div>
);

const StudentQuizHeader = () => (
  <div className="mb-8">
    <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
      Student Quiz Analytics
    </h1>
    <p className="text-slate-500 mt-1">Analyze your self-assessed quiz performance.</p>
  </div>
);
const StatCard = ({ icon, title, value, unit = '' }) => (
  <motion.div
    variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}
    className="relative p-6 bg-white rounded-2xl shadow-lg shadow-blue-500/10 border border-slate-200/50 overflow-hidden">
    <div className="flex items-center gap-4">
      <div className={`p-3 rounded-full text-white ${PRIMARY_GRADIENT}`}>{icon}</div>
      <div>
        <p className="text-sm font-medium text-slate-500">{title}</p>
        <p className="text-2xl font-bold text-slate-800">
          {value} <span className="text-lg font-medium text-slate-400">{unit}</span>
        </p>
      </div>
    </div>
  </motion.div>
);

// --- Chart Components ---
const PerformanceTrendChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      xAxis: { type: 'datetime', labels: { format: '{value:%b %d}' } },
      yAxis: { title: { text: 'Score (%)' }, max: 100 },
      tooltip: { pointFormat: 'Score: <b>{point.y}%</b>' },
      series: [
        {
          name: 'Score',
          data: data,
          color: '#4f46e5',
          fillColor: {
            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
            stops: [
              [0, 'rgba(99, 102, 241, 0.4)'],
              [1, 'rgba(99, 102, 241, 0.05)']
            ]
          },
          marker: {
            enabled: true,
            radius: 4,
            symbol: 'circle',
            fillColor: '#fff',
            lineColor: '#4f46e5',
            lineWidth: 2
          },
          lineWidth: 2
        }
      ]
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <TrendingUp className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Score Over Time</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const AccuracyDonutChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { type: 'pie' },
      title: {
        text: `${data.overallAccuracy.toFixed(1)}%`,
        align: 'center',
        verticalAlign: 'middle',
        y: 10,
        style: { fontSize: '24px', fontWeight: 'bold', color: '#334155' }
      },
      subtitle: {
        text: 'Accuracy',
        align: 'center',
        verticalAlign: 'middle',
        y: 30,
        style: { fontSize: '14px', color: '#64748b' }
      },
      tooltip: { pointFormat: '<b>{point.y}</b> questions ({point.percentage:.1f}%)' },
      plotOptions: {
        pie: {
          innerSize: '70%',
          dataLabels: { enabled: false },
          showInLegend: true,
          borderWidth: 3
        }
      },
      series: [
        {
          name: 'Questions',
          data: [
            { name: 'Correct', y: data.correct, color: '#10b981' },
            { name: 'Incorrect', y: data.incorrect, color: '#ef4444' },
            { name: 'Unattempted', y: data.unattempted, color: '#94a3b8' }
          ]
        }
      ]
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <PieChart className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Overall Accuracy</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const SubjectPerformanceChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { type: 'bar' },
      xAxis: { categories: data?.map((d) => d.name), labels: { style: { color: '#64748b' } } },
      yAxis: { title: { text: 'Average Score (%)' }, max: 100 },
      legend: { enabled: false },
      tooltip: { pointFormat: 'Avg. Score: <b>{point.y:.1f}%</b>' },
      series: [{ name: 'Average Score', data: data?.map((d) => ({ y: d.avg, color: d.color })) }],
      plotOptions: {
        bar: {
          borderRadius: 5,
          dataLabels: {
            enabled: true,
            format: '{y:.1f}%',
            style: { color: '#334155', textOutline: 'none' }
          }
        }
      }
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <BarChart className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Performance by Subject</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const TopicRadarChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { polar: true, type: 'area' },
      pane: { size: '85%' },
      xAxis: { categories: data?.map((d) => d.name), tickmarkPlacement: 'on', lineWidth: 0 },
      yAxis: {
        gridLineInterpolation: 'polygon',
        lineWidth: 0,
        min: 0,
        max: 100,
        labels: {
          formatter: function () {
            return this.value + '%';
          }
        }
      },
      tooltip: { pointFormat: 'Avg. Score: <b>{point.y:,.0f}%</b>' },
      legend: { enabled: false },
      series: [
        {
          name: 'Average Score',
          data: data?.map((d) => d.avg),
          pointPlacement: 'on',
          color: '#4f46e5',
          fillColor: 'rgba(99, 102, 241, 0.25)'
        }
      ]
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <GitCommit className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Performance by Topic</h3>
      </div>
      {data?.length > 2 ? (
        <HighchartsReact highcharts={Highcharts} options={chartOptions} />
      ) : (
        <div className="h-64 flex items-center justify-center text-center text-slate-500">
          <p>Need at least 3 topics with quiz data to display this chart.</p>
        </div>
      )}
    </GlassCard>
  );
};

const RecentQuizzesTable = ({ data }) => (
  <GlassCard className="overflow-hidden">
    <h3 className="p-4 text-lg font-bold text-slate-700 border-b border-slate-900/10">
      Recent Quizzes
    </h3>
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead className="bg-slate-50/50">
          <tr>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Topic</th>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Subject</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Score</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Correct</th>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Date</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-slate-900/5">
          {data.slice(0, 10)?.map((quiz) => (
            <motion.tr
              key={quiz.quiz_id}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.05 }}
              className="hover:bg-blue-500/10 transition-colors">
              <td className="px-6 py-4 font-medium text-slate-800">{quiz.topic}</td>
              <td className="px-6 py-4">
                <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  {quiz.subject}
                </span>
              </td>
              <td className="px-6 py-4 text-center font-bold text-indigo-600">{quiz.score}%</td>
              <td className="px-6 py-4 text-center text-slate-600 font-semibold">
                {quiz.correctCount} / {quiz.totalQuestions}
              </td>
              <td className="px-6 py-4 text-slate-500">{quiz.completed_at}</td>
            </motion.tr>
          ))}
        </tbody>
      </table>
    </div>
  </GlassCard>
);

// --- Main Component ---
const StudentQuiz = () => {
  const [toastRes, setToastRes] = useState(null);
  const [trigger, { isLoading, isError, error }] = useLazyGetStudentQuizServiceQuery();
  const dispatch = useDispatch();
  const studentQuizData = useSelector((state) => state.studentDashboard.studentQuizData);

  const handleGetStudentQuizServiceApi = useCallback(async () => {
    try {
      const res = await trigger({ user_id: sessionStorage.userId }).unwrap();
      dispatch(setStudentQuizData(res));
    } catch (err) {
      setToastRes(err);
    }
  }, [dispatch, trigger]);

  useEffect(() => {
    handleGetStudentQuizServiceApi();
  }, [handleGetStudentQuizServiceApi]);

  // --- Memoized Data Processing for Analytics ---
  const performanceStats = useMemo(() => {
    if (studentQuizData?.length === 0) return { total: 0, avgScore: 'N/A', bestScore: 'N/A' };

    const totalQuizzes = studentQuizData?.length;
    const totalScore = studentQuizData?.reduce((sum, quiz) => sum + quiz.score, 0);
    const bestScore = Math.max(...(studentQuizData?.map((quiz) => quiz.score) || [0]));

    return {
      total: totalQuizzes,
      avgScore: (totalScore / totalQuizzes).toFixed(1),
      bestScore: bestScore
    };
  }, [studentQuizData]);

  const accuracyData = useMemo(() => {
    const totals = studentQuizData?.reduce(
      (acc, quiz) => {
        acc.correct += quiz.correctCount || 0;
        acc.incorrect += quiz.wrongCount || 0;
        acc.total += quiz.totalQuestions || 0;
        return acc;
      },
      { correct: 0, incorrect: 0, total: 0 }
    ) || { correct: 0, incorrect: 0, total: 0 }; // fallback

    const unattempted = totals.total - (totals.correct + totals.incorrect);
    const overallAccuracy = totals.total > 0 ? (totals.correct / totals.total) * 100 : 0;

    return { ...totals, unattempted, overallAccuracy };
  }, [studentQuizData]);

  const performanceBySubject = useMemo(() => {
    const subjectMap = new Map();
    studentQuizData?.forEach((quiz) => {
      if (!subjectMap.has(quiz.subject)) {
        subjectMap.set(quiz.subject, { totalScore: 0, count: 0 });
      }
      const current = subjectMap.get(quiz.subject);
      current.totalScore += quiz.score;
      current.count += 1;
    });
    const colors = ['#4f46e5', '#10b981', '#f97316', '#8b5cf6', '#ec4899'];
    return Array.from(subjectMap.entries())?.map(([name, data], i) => ({
      name,
      avg: data.totalScore / data.count,
      color: colors[i % colors?.length]
    }));
  }, [studentQuizData]);

  const performanceByTopic = useMemo(() => {
    const topicMap = new Map();
    studentQuizData?.forEach((quiz) => {
      if (!topicMap.has(quiz.topic)) {
        topicMap.set(quiz.topic, { totalScore: 0, count: 0 });
      }
      const current = topicMap.get(quiz.topic);
      current.totalScore += quiz.score;
      current.count += 1;
    });
    return Array.from(topicMap.entries())?.map(([name, data]) => ({
      name,
      avg: data.totalScore / data.count
    }));
  }, [studentQuizData]);

  const performanceTrendData = useMemo(() => {
    return studentQuizData
      ?.map((quiz) => {
        // Robust date parsing
        const dateStr = quiz.completed_at.replace(' &', '');
        return [new Date(dateStr).getTime(), quiz.score];
      })
      .sort((a, b) => a[0] - b[0]); // Sort by date ascending
  }, [studentQuizData]);

  // --- Render Logic ---
  const renderContent = () => {
    if (isLoading) return <QuizSkeleton />;
    if (isError) return <ErrorDisplay error={error} onRetry={handleGetStudentQuizServiceApi} />;

    return (
      <>
        <StudentQuizHeader />
        {studentQuizData?.length > 0 ? (
          <div className="space-y-6">
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
              initial="hidden"
              animate="visible"
              variants={{ visible: { transition: { staggerChildren: 0.1 } } }}>
              <StatCard
                icon={<CheckSquare />}
                title="Total Quizzes"
                value={performanceStats.total}
              />
              <StatCard
                icon={<BarChart />}
                title="Average Score"
                value={performanceStats.avgScore}
                unit="%"
              />
              <StatCard
                icon={<Award />}
                title="Best Score"
                value={performanceStats.bestScore}
                unit="%"
              />
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}>
                <PerformanceTrendChart data={performanceTrendData} />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}>
                <AccuracyDonutChart data={accuracyData} />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}>
                <TopicRadarChart data={performanceByTopic} />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}>
                <SubjectPerformanceChart data={performanceBySubject} />
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}>
              <RecentQuizzesTable data={studentQuizData} />
            </motion.div>
          </div>
        ) : (
          <EmptyState message="You haven't attempted any student quizzes yet." />
        )}
      </>
    );
  };

  return (
    <div className="p-0">
      <Toastify res={toastRes} resClear={() => setToastRes(null)} />
      {renderContent()}
    </div>
  );
};

export default StudentQuiz;
