import { createSlice } from '@reduxjs/toolkit';
import { AnalyticsApi } from '../../../../redux/api/api';

const initialState = {
  centers: null,
  centerByData: null,
  studentData: null,
  centerBasedAnalyticsData: null
};

export const directorAnalyticalDashboardApiSlice = AnalyticsApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllCentersService: builder.query({
      query: () => `/analytics/all-centers`,
      transformResponse: (res) => res,
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Analytics'],
      // Invalidate cache when new data is added
      invalidatesTags: (result, error) => (error ? [] : ['Analytics'])
    }),
    getCenterByStudentDataService: builder.query({
      query: (query) => `/analytics/center/${query.centerCode}`,
      transformResponse: (res) => res,
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Analytics']
    }),
    getStudentDataByStudentIdService: builder.query({
      query: (query) => `/analytics/center/cbt/${query.studentId}/${query.course}`,
      transformResponse: (res) => res,
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Analytics']
    }),
    getCenterBasedAnalyticsService: builder.query({
      query: (query) => `/analytics/director/${query.center_code}`,
      transformResponse: (res) => res,
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Analytics']
    })
  })
});

const directorAnalyticalDashboardSlice = createSlice({
  name: 'directorAnalyticalDashboard',
  initialState,
  reducers: {
    setCenters(state, action) {
      state.centers = action.payload;
    },
    setCenterByData(state, action) {
      state.centerByData = action.payload;
    },
    clearCenterByData(state) {
      state.centerByData = null;
    },
    setStudentData(state, action) {
      state.studentData = action.payload;
    },
    clearStudentData(state) {
      state.studentData = null;
    },
    setCenterBasedAnalyticsData(state, action) {
      state.centerBasedAnalyticsData = action.payload;
    },
    clearCenterBasedAnalyticsData(state) {
      state.centerBasedAnalyticsData = null;
    },

    resetDashboardState() {
      return initialState; // reset everything on logout
    }
  }
});

// ✅ Export API hooks
export const {
  useLazyGetAllCentersServiceQuery,
  useLazyGetCenterByStudentDataServiceQuery,
  useLazyGetStudentDataByStudentIdServiceQuery,
  useLazyGetCenterBasedAnalyticsServiceQuery
} = directorAnalyticalDashboardApiSlice;

// ✅ Export reducers/actions
export const {
  setCenters,
  setCenterByData,
  clearCenterByData,
  setStudentData,
  clearStudentData,
  setCenterBasedAnalyticsData,
  clearCenterBasedAnalyticsData,
  resetDashboardState
} = directorAnalyticalDashboardSlice.actions;

// ✅ Export selectors
export const selectCenters = (state) => state.directorAnalyticalDashboard.centers;
export const selectCenterByData = (state) => state.directorAnalyticalDashboard.centerByData;
export const selectStudentData = (state) => state.directorAnalyticalDashboard.studentData;

export default directorAnalyticalDashboardSlice.reducer;
