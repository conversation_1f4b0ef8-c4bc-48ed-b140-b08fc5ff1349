import { useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useContactFlashCardMutation } from './flashCardContactPageSlice';
import Toastify from '../../components/PopUp/Toastify';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faUser,
  faEnvelope,
  faPhone,
  faComment,
  faBullhorn,
  faChevronDown,
  faPaperPlane,
  faBookOpen,
  faPlayCircle
} from '@fortawesome/free-solid-svg-icons';

export default function FlashCardContactPopup({ isOpen, onClose }) {
  const iframeRef = useRef(null);
  const [showOverlay, setShowOverlay] = useState(false);

  const [loading, setLoading] = useState(false);
  const [res, setRes] = useState(null);
  const [activeInput, setActiveInput] = useState(null);
  const [contactFlashCard] = useContactFlashCardMutation();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    description: '',
    source: ''
  });

  const handlePlayClick = () => {
    setShowOverlay(true);
    setTimeout(() => setShowOverlay(false), 1000);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setRes(null); // Clear previous result

    try {
      const response = await contactFlashCard(formData);

      if (response.error) {
        // Handle error response
        const errorMessage =
          response.error.data?.message || response.error.message || 'Submission failed';
        setRes({ status: 'error', message: errorMessage });
      } else {
        // Success
        const successMessage = response.data?.message || 'Message sent successfully!';
        setRes({ status: 'success', message: successMessage });

        // Close popup after 1 second
        setTimeout(() => {
          onClose(); // 👈 Use the prop to close
        }, 1000);
      }

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        description: '',
        source: ''
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      setRes({
        status: 'error',
        message: 'Something went wrong. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };
  if (!isOpen) return null;
  // Input focus animation
  const inputFocusAnimation = {
    focus: {
      scale: 1.02,
      boxShadow: '0 5px 20px rgba(59, 130, 246, 0.4)',
      transition: { duration: 0.3 }
    }
  };

  // Button hover effect
  const buttonHoverEffect = {
    hover: {
      scale: 1.05,
      boxShadow: '0 10px 25px rgba(59, 130, 246, 0.5)'
    },
    tap: { scale: 0.98 }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/50 backdrop-blur-md flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}>
          <Toastify res={res} resClear={() => setRes(null)} />

          <motion.div
            className="bg-white/10 backdrop-blur-lg rounded-2xl shadow-xl p-4 md:p-6 w-full max-w-4xl relative overflow-hidden border border-white/20 flex flex-col md:flex-row"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}>
            {/* Close Button */}
            <motion.button
              onClick={onClose}
              className="absolute top-3 right-3  text-white hover:text-white hover:cursor-pointer z-10  p-2 rounded-full"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}>
              <FontAwesomeIcon className="text-3xl" icon={faTimes} />
            </motion.button>


            {/* Left Side - Flashcard AI Demo */}
            <div className="w-full md:w-1/2 md:pr-4 flex flex-col mb-4 md:mb-0">
              <motion.div
                className="text-center mb-4"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}>
                <h2 className="text-lg md:text-xl font-bold text-transparent bg-clip-text bg-[var(--color-counselor)] mb-2 flex items-center justify-center gap-2">
                  Meet Your AI Sasthra Bot
                </h2>
              </motion.div>

              {/* Iframe */}
              <div className="relative flex-1 border border-white/30 rounded-xl overflow-hidden bg-white/5 min-h-[300px] md:min-h-[500px] lg:min-h-[75vh]">
                <iframe
                  ref={iframeRef}
                  title="Sasthra AI Counselor"
                  src="https://sasthra-ai-counselor-ii-671317745974.us-west1.run.app/"
                  className="w-full h-[300px] md:h-[500px] lg:h-[75vh]"
                  style={{ border: 'none' }}
                  allow="autoplay; microphone; camera"
                  loading="lazy"
                />

                {/* Play overlay */}
                {showOverlay && (
                  <motion.div
                    className="absolute inset-0 flex items-center justify-center bg-black/50"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}>
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity }}>
                      <FontAwesomeIcon icon={faPlayCircle} size="3x" className="text-white" />
                    </motion.div>
                  </motion.div>
                )}
              </div>
            </div>

            {/* Divider */}
            <div className="hidden md:block w-px bg-gradient-to-b from-transparent via-blue-400/50 to-transparent mx-2 my-6"></div>

            {/* Right Side - Contact Form */}
            <div className="w-full md:w-1/2 md:pl-4">
              <motion.div
                className="text-center mb-6"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}>
                <h2 className="text-lg md:text-xl font-bold text-[var(--color-counselor)] bg-clip-text  flex items-center justify-center gap-2">
                
                  Contact Us for Sasthra
                </h2>
                <p className="text-white/80 text-sm mt-1">
                  Share your details and our team will guide you.
                </p>
              </motion.div>

              <motion.form
                className="space-y-4"
                onSubmit={handleSubmit}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}>
                {/* Name */}
                <motion.div className="relative" whileFocus="focus" variants={inputFocusAnimation}>
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-cyan-300">
                    <FontAwesomeIcon icon={faUser} className='text-[var(--color-counselor)]' />
                  </div>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    onFocus={() => setActiveInput('name')}
                    onBlur={() => setActiveInput(null)}
                    placeholder="Your name"
                    className="w-full bg-white/10 border border-white/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300"
                    required
                  />
                </motion.div>

                {/* Email */}
                <motion.div className="relative" whileFocus="focus" variants={inputFocusAnimation}>
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-cyan-300">
                    <FontAwesomeIcon icon={faEnvelope} className='text-[var(--color-counselor)]' />
                  </div>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    onFocus={() => setActiveInput('email')}
                    onBlur={() => setActiveInput(null)}
                    placeholder="Email address"
                    className="w-full bg-white/10 border border-white/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300"
                    required
                  />
                </motion.div>

                {/* Phone */}
                <motion.div className="relative" whileFocus="focus" variants={inputFocusAnimation}>
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-cyan-300">
                    <FontAwesomeIcon icon={faPhone} className='text-[var(--color-counselor)]' />
                  </div>
                  <input
                    type="number"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    onFocus={() => setActiveInput('phone')}
                    onBlur={() => setActiveInput(null)}
                    placeholder="Phone number"
                    className="w-full bg-white/10 border border-white/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300 no-spinner"
                    required
                    maxLength={15}
                  />
                </motion.div>

                {/* Message */}
                <motion.div className="relative" whileFocus="focus" variants={inputFocusAnimation}>
                  <div className="absolute top-3 left-3 pointer-events-none text-cyan-300">
                    <FontAwesomeIcon icon={faComment} className='text-[var(--color-counselor)]' />
                  </div>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    onFocus={() => setActiveInput('description')}
                    onBlur={() => setActiveInput(null)}
                    placeholder="Enter your purpose of visit to Sasthra...(optional)"
                    rows="3"
                    className="w-full bg-white/10 border border-white/30 rounded-lg pl-10 pr-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300 resize-none"
                  />
                </motion.div>

                {/* Source */}
                <motion.div className="relative" whileFocus="focus" variants={inputFocusAnimation}>
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-cyan-300">
                    <FontAwesomeIcon icon={faBullhorn} className='text-[var(--color-counselor)]' />
                  </div>
                  <select
                    name="source"
                    value={formData.source}
                    onChange={handleChange}
                    onFocus={() => setActiveInput('source')}
                    onBlur={() => setActiveInput(null)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg pl-10 pr-8 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-400/50 appearance-none"
                    required>
                    <option value="" className="text-black">
                      How did you hear about us?
                    </option>
                    <option value="google" className="text-black">
                      Google
                    </option>
                    <option value="youtube" className="text-black">
                      YouTube
                    </option>
                    <option value="instagram" className="text-black">
                      Instagram
                    </option>
                    <option value="facebook" className="text-black">
                      Facebook
                    </option>
                    <option value="linkedin" className="text-black">
                      LinkedIn
                    </option>
                    <option value="school" className="text-black">
                      From School/College
                    </option>
                    <option value="friends" className="text-black">
                      Friends
                    </option>
                    <option value="others" className="text-black">
                      Others
                    </option>
                  </select>

                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-white/60">
                    <FontAwesomeIcon icon={faChevronDown} />
                  </div>
                </motion.div>

                {/* Submit Button */}
                <motion.button
                  type="submit"
                  variants={buttonHoverEffect}
                  whileHover="hover"
                  whileTap="tap"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium rounded-lg py-3 flex items-center justify-center gap-2 transition-all duration-300 group relative overflow-hidden">
                  <span className="relative z-10">{loading ? 'Sending...' : 'Send Message'}</span>
                  <FontAwesomeIcon icon={faPaperPlane} />
                  <motion.div
                    className="absolute inset-0 bg-white/20 -translate-x-full group-hover:translate-x-full transition-transform duration-500"
                    initial={false}
                  />
                </motion.button>
              </motion.form>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
