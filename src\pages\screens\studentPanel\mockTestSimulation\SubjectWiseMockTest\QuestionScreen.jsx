// src/components/QuestionScreen.jsx
import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faCheck,
  faClock,
  faExpand,
  faCompress,
  faCircleXmark
} from '@fortawesome/free-solid-svg-icons';

import { MathText } from './MathText    ';

const QuestionScreen = ({
  questions,
  isLoading,
  selectedExam,
  selectedSubject,
  onSubmit,
  onBack
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef(null);

  // Duration based on exam and subject
  const durations = {
    neet: { physics: 45, chemistry: 45, biology: 90 },
    jee_mains: { physics: 60, chemistry: 60, mathematics: 60 },
    jee_advanced: { physics: 60, chemistry: 60, mathematics: 60 }
  };
  const duration = durations[selectedExam]?.[selectedSubject] * 60 || 0;

  useEffect(() => {
    setTimeRemaining(duration);
  }, [duration]);

  // Timer
  useEffect(() => {
    if (timeRemaining <= 0 || currentQuestionIndex >= questions.length) return;
    const timer = setInterval(() => {
      setTimeRemaining((t) => Math.max(0, t - 1));
    }, 1000);
    return () => clearInterval(timer);
  }, [timeRemaining, currentQuestionIndex, questions.length]);

  // Fullscreen
  const enterFullscreen = async () => {
    if (containerRef.current?.requestFullscreen) {
      await containerRef.current.requestFullscreen();
    }
  };

  const exitFullscreen = async () => {
    if (document.exitFullscreen) {
      await document.exitFullscreen();
    }
  };

  useEffect(() => {
    const handler = () => setIsFullscreen(!!document.fullscreenElement);
    document.addEventListener('fullscreenchange', handler);
    return () => document.removeEventListener('fullscreenchange', handler);
  }, []);

  // Answer Handling
  const handleAnswer = (idx) => {
    const qId = questions[currentQuestionIndex]?.id;
    if (qId) {
      setAnswers((prev) => ({ ...prev, [qId]: idx }));
    }
  };

  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1);
    }
  };

  const goToPrev = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1);
    }
  };

  const goToQuestion = (index) => {
    setCurrentQuestionIndex(index);
  };

  const submitTest = () => {
    const responses = questions.map((q) => ({
      question_id: q.id,
      selected_option: answers[q.id] !== undefined ? answers[q.id] + 1 : null
    }));
    onSubmit(responses);
  };

  // Loading State
  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="flex flex-col items-center justify-center py-24">
        <motion.div
          animate={{ rotate: 360, scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          className="w-12 h-12 border-4 border-t-transparent border-white rounded-full"
        />
        <p className="text-gray-600 mt-4">Loading questions...</p>
      </motion.div>
    );
  }

  if (!questions.length) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center py-20 bg-white rounded-2xl shadow-lg p-8 max-w-md mx-auto">
        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
          <FontAwesomeIcon icon={faCircleXmark} className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-800 mb-2">No questions available</h3>
        <p className="text-gray-500 mb-6">There are no questions for this subject yet.</p>
        <button onClick={onBack} className="px-6 py-2 bg-gray-500 text-white rounded-lg">
          Back
        </button>
      </motion.div>
    );
  }

  const formatTime = (secs) => {
    const mins = Math.floor(secs / 60);
    const secsRem = secs % 60;
    return `${mins.toString().padStart(2, '0')}:${secsRem.toString().padStart(2, '0')}`;
  };

  const answeredCount = Object.keys(answers).length;

  return (
    <motion.div
      key="question-screen"
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: -300, opacity: 0 }}
      className="grid grid-cols-1 lg:grid-cols-4 gap-6"
      ref={containerRef}>
      {/* Question Panel */}
      <div className="lg:col-span-3 bg-white p-6 rounded-xl shadow-md border border-gray-200 h-[85vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <span className="text-xs font-semibold px-2.5 py-1 rounded-full bg-[var(--color-student)] text-white capitalize">
            {selectedSubject} • Q{currentQuestionIndex + 1}
          </span>
          <div className="flex items-center space-x-3">
            <div className="flex items-center bg-[var(--color-counselor)] px-2.5 py-1 rounded-full">
              <FontAwesomeIcon icon={faClock} className="w-3.5 h-3.5 mr-1 text-white" />
              <span
                className={`text-sm font-mono ${
                  timeRemaining < 300 ? 'text-red-600' : 'text-white'
                }`}>
                {formatTime(timeRemaining)}
              </span>
            </div>
            <button
              onClick={isFullscreen ? exitFullscreen : enterFullscreen}
              className="text-gray-500 hover:text-gray-700">
              <FontAwesomeIcon icon={isFullscreen ? faCompress : faExpand} />
            </button>
          </div>
        </div>

        <h3 className="text-lg font-semibold text-gray-800 leading-relaxed mb-6">
          <MathText>{questions[currentQuestionIndex]?.question_text}</MathText>
        </h3>

        {questions[currentQuestionIndex]?.actual_question_image && (
          <img
            src={questions[currentQuestionIndex].actual_question_image}
            alt="Question visual"
            className="w-full max-h-64 object-contain mb-6 rounded-lg border"
          />
        )}

        {/* Options */}
        <div className="space-y-2.5">
          {questions[currentQuestionIndex]?.options.map((option, idx) => {
            const isSelected = answers[questions[currentQuestionIndex].id] === idx;
            return (
              <motion.label
                key={idx}
                whileHover={{ scale: 1.02 }}
                className={`flex items-start p-3 rounded-lg border cursor-pointer transition-all text-sm ${
                  isSelected
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleAnswer(idx)}>
                <div
                  className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center mt-0.5 ${
                    isSelected ? 'border-indigo-500 bg-indigo-500' : 'border-gray-400'
                  }`}>
                  {isSelected && (
                    <FontAwesomeIcon icon={faCheck} className="w-2.5 h-2.5 text-white" />
                  )}
                </div>
                <MathText>{option}</MathText>
              </motion.label>
            );
          })}
        </div>

        <div className="flex justify-between mt-8">
          <button
            onClick={goToPrev}
            disabled={currentQuestionIndex === 0}
            className="flex items-center gap-1.5 px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50">
            <FontAwesomeIcon icon={faArrowLeft} /> Prev
          </button>
          {currentQuestionIndex === questions.length - 1 ? (
            <button
              onClick={submitTest}
              disabled={answeredCount === 0}
              className={`px-6 py-2.5 rounded-lg text-sm font-medium transition-all ${
                answeredCount > 0
                  ? 'bg-[var(--color-counselor)] text-white hover:bg-opacity-90'
                  : 'bg-gray-200 text-gray-500 cursor-not-allowed'
              }`}>
              Submit Test
            </button>
          ) : (
            <button
              onClick={goToNext}
              className="px-5 py-2.5 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition">
              Next <FontAwesomeIcon icon={faArrowLeft} className="rotate-180" />
            </button>
          )}
        </div>
      </div>

      {/* Progress Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white p-5 rounded-xl shadow-md border border-gray-100 h-fit">
          <h4 className="font-semibold text-gray-800 mb-4">Progress</h4>
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
            <div
              className="bg-indigo-500 h-2.5 rounded-full"
              style={{ width: `${(answeredCount / questions.length) * 100}%` }}
            />
          </div>
          <p className="text-xs text-gray-500">
            {answeredCount} / {questions.length} answered
          </p>

          <div className="mt-4">
            <p className="text-xs font-medium text-gray-700 mb-2">Questions</p>
            <div className="grid grid-cols-5 gap-1.5 max-h-60 overflow-y-auto pr-1">
              {questions.map((q, index) => {
                const isCurrent = index === currentQuestionIndex;
                const isAnswered = answers[q.id] !== undefined;
                return (
                  <motion.button
                    key={q.id}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => goToQuestion(index)}
                    className={`text-xs font-medium rounded-md p-1.5 transition-all ${
                      isCurrent
                        ? 'bg-indigo-500 text-white shadow-sm scale-105'
                        : isAnswered
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}>
                    {index + 1}
                  </motion.button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default QuestionScreen;
