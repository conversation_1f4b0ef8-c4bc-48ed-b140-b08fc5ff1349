import { createSlice } from '@reduxjs/toolkit';
import { AnalyticsApi } from '../../../../redux/api/api';

const initialState = {
  onboardAssessment: null,
  cbt: null,
  liveQuizData: null,
  studentQuizData: null,
  ocrData: null,
  omrData: null
};

export const studentDashboardApiSlice = AnalyticsApi.injectEndpoints({
  endpoints: (builder) => ({
    getOnboardAssessmentService: builder.query({
      query: (query) => {
        return `/analytics/onboard-assessment/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getCbtService: builder.query({
      query: (query) => {
        return `/analytics/cbt/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getLiveQuizService: builder.query({
      query: (query) => {
        return `/analytics/live-quiz/${query.center_code}/${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentQuizService: builder.query({
      query: (query) => {
        return `/analytics/students-quiz/${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentOcrService: builder.query({
      query: (query) => {
        return `/analytics/ocr/student/${query.studentId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentOmrService: builder.query({
      query: (query) => {
        return `/analytics/omr/student/${query.studentId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    })
  })
});

const studentDashboardSlice = createSlice({
  name: 'studentDashboard',
  initialState,
  reducers: {
    setOnboardAssessment(state, action) {
      state.onboardAssessment = action.payload;
    },
    clearOnboardAssessment(state) {
      state.onboardAssessment = null;
    },
    setCbt(state, action) {
      state.cbt = action.payload;
    },
    clearCbt(state) {
      state.cbt = null;
    },
    setLiveQuizData(state, action) {
      state.liveQuizData = action.payload;
    },
    clearLiveQuizData(state) {
      state.liveQuizData = null;
    },
    setStudentQuizData(state, action) {
      state.studentQuizData = action.payload;
    },
    clearStudentQuizData(state) {
      state.studentQuizData = null;
    },
    setOcrData(state, action) {
      state.ocrData = action.payload;
    },
    clearOcrData(state) {
      state.ocrData = null;
    },
    setOmrData(state, action) {
      state.omrData = action.payload;
    },
    clearOmrData(state) {
      state.omrData = null;
    }
  }
});

export const {
  useLazyGetOnboardAssessmentServiceQuery,
  useLazyGetCbtServiceQuery,
  useLazyGetLiveQuizServiceQuery,
  useLazyGetStudentQuizServiceQuery,
  useLazyGetStudentOcrServiceQuery,
  useLazyGetStudentOmrServiceQuery
} = studentDashboardApiSlice;
export const {
  setOnboardAssessment,
  clearOnboardAssessment,
  setCbt,
  clearCbt,
  setLiveQuizData,
  clearLiveQuizData,
  setStudentQuizData,
  clearStudentQuizData,
  setOcrData,
  clearOcrData,
  setOmrData,
  clearOmrData
} = studentDashboardSlice.actions;
export const selectStudentOverview = (state) => state.studentDashboard.onboardAssessment;
export default studentDashboardSlice.reducer;
