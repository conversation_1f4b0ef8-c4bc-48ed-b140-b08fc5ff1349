import { motion, AnimatePresence } from 'framer-motion';
import ReactCrop from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { FaImage, FaVideo, FaUpload, FaMicrophone, FaStop, FaPlus, FaTrash } from 'react-icons/fa';
import {
  FaHistory,
 
  FaPaperPlane,
  
  
 
 
  FaPlay,
  FaTimes,
  FaExpand,
  
  FaPhone,
  FaVolumeUp,
  FaChalkboardTeacher
} from 'react-icons/fa';


const MediaInput = ({
  text,
  setText,
  audio,
  image,
  mode,
  setMode,
  language,
  setLanguage,
  isRecording,
  recordingTime,
  activeInput,
  showMediaOptions,
  setShowMediaOptions,
  isDoubtSolverLoading,
  isYoutubeLoading,
  handleSubmit,
  handleRecord,
  handleAudioUpload,
  handleImageUpload,
  handleDeleteAudio,
  handleDeleteImage,
  audioInputRef,
  imageInputRef,
  showCropModal,
  setShowCropModal,
  imageSrc,
  crop,
  setCrop,
  imageRef,
  croppedImage,
  handleCropComplete,
  showWebcamModal,
  setShowWebcamModal,
  videoRef,
  startWebcam,
  handleCaptureImage,
  stopWebcam,
  formatTime
}) => {
  const languageMap = {
    English: 'english',
    'Hindi+English': 'hinglish',
    'Telugu+English': 'tenglish',
    'Malayalam+English': 'manglish',
    'Kannada+English': 'kanglish',
    'Tamil+English': 'tanglish'
  };

  return (
    <>
      <AnimatePresence>
        {(audio || image) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mx-4 mb-4 flex flex-wrap gap-3">
            {audio && (
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="flex items-center bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl px-4 py-3 shadow-lg">
                <FaMicrophone className="text-blue-600 mr-3 text-lg" />
                <span className="text-blue-800 font-medium mr-3">
                  {audio.name} {isRecording && `(${formatTime(recordingTime)})`}
                </span>
                <motion.button
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleDeleteAudio}
                  className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100 transition-all duration-200">
                  <FaTrash className="text-sm" />
                </motion.button>
              </motion.div>
            )}
            {image && (
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="flex items-center bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl px-4 py-3 shadow-lg">
                <div className="relative">
                  <img
                    src={croppedImage || URL.createObjectURL(image)}
                    alt="Uploaded content"
                    className="w-16 h-16 object-cover rounded-lg mr-3"
                  />
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleDeleteImage}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full text-white flex items-center justify-center">
                    <FaTrash className="text-xs" />
                  </motion.button>
                </div>
                <span className="text-green-800 font-medium mr-3">
                  {image.name.length > 15
                    ? `${image.name.substring(0, 12)}...`
                    : image.name}
                </span>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
      <motion.div
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className=" bg-white rounded-3xl shadow-2xl border border-gray-100 p-4">
        <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-100">
          <div className="flex items-center space-x practice">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-600">Mode:</span>
              <select
                className="text-sm bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-xl px-3 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                value={mode}
                onChange={(e) => setMode(e.target.value)}
                disabled={isDoubtSolverLoading || isYoutubeLoading}>
                <option value="text">Text</option>
                <option value="tutor">Tutor</option>
                <option value="conversation">Conversation</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-600">Language:</span>
              <select
                className="text-sm bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-xl px-3 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                disabled={isDoubtSolverLoading || isYoutubeLoading}>
                {Object.keys(languageMap).map((lang) => (
                  <option key={lang} value={lang}>
                    {lang}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        <div className="flex items-end space-x-3">
          <div className="flex-1 relative">
            <textarea
              className="w-full p-4 pr-14 bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-white transition-all duration-300 resize-none"
              placeholder="Ask me anything about your studies..."
              value={text}
              onChange={(e) => setText(e.target.value)}
              disabled={isDoubtSolverLoading || isYoutubeLoading}
              rows={2}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit();
                }
              }}
            />
            <motion.button
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleSubmit}
              disabled={
                isDoubtSolverLoading || isYoutubeLoading || (!text && !audio && !image)
              }
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center disabled:opacity-50"
              title="Send Message">
              {isDoubtSolverLoading || isYoutubeLoading ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 1,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: 'linear'
                  }}
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                />
              ) : (
                <FaPaperPlane className="text-lg" />
              )}
            </motion.button>
          </div>
          <div className="relative">
            <motion.button
              initial={{ scale: 1, opacity: 1 }}
              animate={{ scale: [1, 1.1, 1], opacity: [1, 0.9, 1] }}
              transition={{
                duration: 1.5,
                repeat: Number.POSITIVE_INFINITY,
                repeatType: 'reverse'
              }}
              whileHover={{ scale: 1.2, y: -3 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowMediaOptions(!showMediaOptions)}
              className="w-12 h-12 bg-gradient-to-r from-gray-400 to-blue-500 rounded-2xl text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
              title="Add Media">
              <FaPlus className="text-lg" />
            </motion.button>
            <AnimatePresence>
              {showMediaOptions && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  className="absolute bottom-full mb-4 mt-8 right-0 bg-white rounded-2xl shadow-lg p-2 flex flex-col space-y-2 z-10">
                  <motion.button
                    custom={0}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ delay: 0 }}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => imageInputRef.current.click()}
                    disabled={isDoubtSolverLoading || isYoutubeLoading}
                    className="w-10 h-10 bg-gradient-to-r from-orange-400 to-red-500 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                    title="Upload Image">
                    <FaImage className="text-lg" />
                  </motion.button>
                  <motion.button
                    custom={1}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ delay: 0.2 }}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={startWebcam}
                    disabled={isDoubtSolverLoading || isYoutubeLoading}
                    className="w-10 h-10 bg-gradient-to-r from-teal-400 to-cyan-500 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                    title="Capture Live Image">
                    <FaVideo className="text-lg" />
                  </motion.button>
                  <motion.button
                    custom={1}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ delay: 0.2 }}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => audioInputRef.current.click()}
                    disabled={isRecording || isDoubtSolverLoading || isYoutubeLoading}
                    className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                    title="Upload Audio">
                    <FaUpload className="text-lg" />
                  </motion.button>
                  <motion.button
                    custom={2}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ delay: 0.4 }}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleRecord}
                    disabled={isDoubtSolverLoading || isYoutubeLoading}
                    className={`w-10 h-10 rounded-xl text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center ${
                      isRecording
                        ? 'bg-gradient-to-r from-red-500 to-pink-600 animate-pulse'
                        : 'bg-gradient-to-r from-purple-500 to-indigo-600'
                    }`}
                    title={isRecording ? 'Stop Recording' : 'Record Audio'}>
                    {isRecording ? (
                      <FaStop className="text-lg" />
                    ) : (
                      <FaMicrophone className="text-lg" />
                    )}
                  </motion.button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
        <AnimatePresence>
          {isRecording && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center justify-center mt-3 space-x-3">
              <motion.div
                animate={{ scale: [1, 1.3, 1] }}
                transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
                className="w-3 h-3 bg-red-500 rounded-full"
              />
              <span className="text-red-600 font-medium">
                Recording... {formatTime(recordingTime)}
              </span>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
      <AnimatePresence>
        {showCropModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
            onClick={() => setShowCropModal(false)}>
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-xl shadow-2xl p-6 max-w-3xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-gray-800">Crop Your Image</h3>
                <button
                  onClick={() => setShowCropModal(false)}
                  className="text-gray-500 hover:text-gray-700">
                  <FaTimes className="text-xl" />
                </button>
              </div>
              <div className="mb-4">
                <ReactCrop
                  crop={crop}
                  onChange={(newCrop) => setCrop(newCrop)}
                  onComplete={(c) => setCrop(c)}>
                  <img
                    ref={imageRef}
                    src={imageSrc || '/placeholder.svg'}
                    alt="Uploaded content"
                    className="max-h-[60vh]"
                  />
                </ReactCrop>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowCropModal(false);
                    setImageSrc(null);
                  }}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition">
                  Cancel
                </button>
                <button
                  onClick={handleCropComplete}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                  Apply Crop
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence>
        {showWebcamModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
            onClick={stopWebcam}>
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-xl shadow-2xl p-6 max-w-3xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-gray-800">Capture Live Image</h3>
                <button onClick={stopWebcam} className="text-gray-500 hover:text-gray-700">
                  <FaTimes className="text-xl" />
                </button>
              </div>
              <div className="mb-4">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  className="w-full max-h-[60vh] rounded-lg"
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={stopWebcam}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition">
                  Cancel
                </button>
                <button
                  onClick={handleCaptureImage}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                  Capture
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      <input
        type="file"
        accept="audio/*"
        ref={audioInputRef}
        onChange={handleAudioUpload}
        className="hidden"
      />
      <input
        type="file"
        accept="image/*"
        ref={imageInputRef}
        onChange={handleImageUpload}
        className="hidden"
      />
    </>
  );
};

export default MediaInput;