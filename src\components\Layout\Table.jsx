import React, { useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  X,
  Edit,
  Trash2,
  Eye,
  FileText,
  ChevronLeft,
  ChevronRight,
  Table2,
  ArrowDown,
  ArrowUp,
  RefreshCw,
  Plus
} from 'lucide-react';
import clsx from 'clsx';

const DOTS = '...';

const Table = ({
  title = 'Table',
  titleIcon = <Table2 className="h-6 w-6 text-blue-600" />,
  buttonName = 'Add New',
  onAddNew,
  header = [],
  data = [],
  searchBy = [],
  searchPlaceholder = 'Search records...',
  onView,
  onEdit,
  onDelete,
  onPdf,
  customColumn,
  itemsPerPage = 10,
  loading = false,
  emptyMessage = 'No records found'
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState('');
  const [pageSize, setPageSize] = useState(itemsPerPage);
  const [sortConfig, setSortConfig] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hoverData, setHoverData] = useState({ visible: false, content: '', style: {} });

  const visibleHeaders = useMemo(() => header.filter((col) => col.show), [header]);

  const processedData = useMemo(() => {
    let filtered = Array.isArray(data) ? [...data] : [];

    if (search.trim()) {
      const lowercasedSearchTerm = search.toLowerCase();
      filtered = filtered.filter((item) => {
        const keysToSearch =
          searchBy?.length > 0 ? searchBy : visibleHeaders.map((h) => h.data).filter(Boolean);
        return keysToSearch.some((key) => {
          const value = key
            .split('.')
            .reduce((o, i) => (o && o[i] !== undefined ? o[i] : null), item);
          return String(value).toLowerCase().includes(lowercasedSearchTerm);
        });
      });
    }

    if (sortConfig !== null) {
      filtered.sort((a, b) => {
        const valA = sortConfig.key.split('.').reduce((o, i) => o?.[i], a);
        const valB = sortConfig.key.split('.').reduce((o, i) => o?.[i], b);
        if (valA === null || valA === undefined) return 1;
        if (valB === null || valB === undefined) return -1;
        if (valA < valB) return sortConfig.direction === 'ascending' ? -1 : 1;
        if (valA > valB) return sortConfig.direction === 'ascending' ? 1 : -1;
        return 0;
      });
    }
    return filtered;
  }, [search, data, visibleHeaders, searchBy, sortConfig]);

  const totalItems = processedData.length;
  const totalPages = Math.ceil(totalItems / pageSize);

  const paginatedData = useMemo(() => {
    if (totalItems === 0) return [];
    const startIndex = (currentPage - 1) * pageSize;
    return processedData.slice(startIndex, startIndex + pageSize);
  }, [processedData, currentPage, pageSize, totalItems]);

  useEffect(() => setCurrentPage(1), [search, pageSize, sortConfig]);
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [totalPages, currentPage]);

  const handleSort = (key) => {
    if (!key) return;
    let direction = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const handleMouseEnter = (e, content) => {
    if (String(content).length < 25) return;
    const rect = e.currentTarget.getBoundingClientRect();
    setHoverData({
      visible: true,
      content,
      style: { top: rect.bottom + 8, left: rect.left }
    });
  };

  const range = (start, end) => Array.from({ length: end - start + 1 }, (_, idx) => idx + start);
  const paginationRange = useMemo(() => {
    const totalPageNumbers = 7;
    if (totalPages <= totalPageNumbers) return range(1, totalPages);
    const siblingCount = 1;
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 2;
    if (!shouldShowLeftDots && shouldShowRightDots) return [...range(1, 5), DOTS, totalPages];
    if (shouldShowLeftDots && !shouldShowRightDots)
      return [1, DOTS, ...range(totalPages - 4, totalPages)];
    if (shouldShowLeftDots && shouldShowRightDots)
      return [1, DOTS, ...range(leftSiblingIndex, rightSiblingIndex), DOTS, totalPages];
    return [];
  }, [totalPages, currentPage]);

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: 'easeOut' } }
  };
  const itemVariants = { hidden: { opacity: 0, scale: 0.95 }, visible: { opacity: 1, scale: 1 } };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
      {/* --- Toolbar --- */}
      <div className="p-4 flex flex-col md:flex-row items-center justify-between gap-4 border-b border-slate-200">
        <div className="flex items-center gap-3">
          <div className="p-2.5 bg-blue-100 rounded-lg">{titleIcon}</div>
          <h2 className="text-xl font-bold text-slate-800">{title}</h2>
        </div>
        <div className="w-full md:w-auto flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          <div className="relative flex-1 sm:w-64">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400" />
            <input
              placeholder={searchPlaceholder}
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-slate-50 border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none transition-shadow placeholder-slate-400 text-slate-800"
            />
          </div>
          <button
            onClick={handleRefresh}
            className="p-2.5 bg-white border border-slate-200 rounded-lg text-slate-500 hover:bg-slate-100 transition-colors"
            disabled={isRefreshing}
            title="Refresh">
            <RefreshCw className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
          {onAddNew && (
            <button
              onClick={onAddNew}
              className="px-4 py-2.5 bg-blue-600 text-white rounded-lg font-semibold flex items-center gap-2 shadow-sm hover:bg-blue-700 transition-colors">
              <Plus className="h-5 w-5" /> {buttonName}
            </button>
          )}
        </div>
      </div>

      {/* --- Table --- */}
      <div className="relative overflow-x-auto">
        <AnimatePresence>
          {loading && (
            <motion.div
              key="loader"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-white/70 backdrop-blur-sm flex items-center justify-center z-20">
              <div className="h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
            </motion.div>
          )}
        </AnimatePresence>

        <table className="w-full">
          <thead className="sticky top-0 z-10 bg-slate-50 border-b border-slate-200">
            <tr>
              {visibleHeaders.map((column) => (
                <th
                  key={column.data || column.header}
                  className="px-5 py-3.5 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">
                  <div
                    className={clsx('flex items-center gap-1.5', {
                      'cursor-pointer hover:text-blue-600': column.sortable
                    })}
                    onClick={() => column.sortable && handleSort(column.data)}>
                    {column.header}
                    {column.sortable && sortConfig?.key === column.data && (
                      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
                        {sortConfig.direction === 'ascending' ? (
                          <ArrowUp size={14} />
                        ) : (
                          <ArrowDown size={14} />
                        )}
                      </motion.div>
                    )}
                  </div>
                </th>
              ))}
              {(onView || onEdit || onDelete || onPdf || customColumn) && (
                <th className="px-5 py-3.5 text-center text-xs font-semibold text-slate-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>

          <tbody className="divide-y divide-slate-100">
            <AnimatePresence>
              {paginatedData.length > 0 ? (
                paginatedData.map((item, i) => {
                  const startIndex = (currentPage - 1) * pageSize;
                  return (
                    <motion.tr
                      key={item.id || startIndex + i}
                      variants={itemVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      layout
                      transition={{ delay: i * 0.03 }}
                      className="hover:bg-blue-50/50 transition-colors">
                      {visibleHeaders.map((column) => (
                        <td
                          key={column.data}
                          className={clsx(
                            'px-5 py-4 text-sm text-slate-700 truncate',
                            column.className
                          )}
                          onMouseEnter={(e) => handleMouseEnter(e, item[column.data])}
                          onMouseLeave={() =>
                            setHoverData((prev) => ({ ...prev, visible: false }))
                          }>
                          {column.render
                            ? column.render(item, startIndex + i)
                            : column.data === 'sno'
                              ? startIndex + i + 1
                              : (item[column.data] ?? '–')}
                        </td>
                      ))}
                      {(onView || onEdit || onDelete || onPdf || customColumn) && (
                        <td className="px-5 py-4 text-center">
                          <div className="flex justify-center gap-1">
                            {onView && (
                              <ActionButton
                                icon={<Eye size={16} />}
                                onClick={() => onView(item)}
                                title="View"
                                className="text-blue-500 hover:bg-blue-100"
                              />
                            )}
                            {onEdit && (
                              <ActionButton
                                icon={<Edit size={16} />}
                                onClick={() => onEdit(item)}
                                title="Edit"
                                className="text-emerald-500 hover:bg-emerald-100"
                              />
                            )}
                            {onPdf && (
                              <ActionButton
                                icon={<FileText size={16} />}
                                onClick={() => onPdf(item)}
                                title="PDF"
                                className="text-purple-500 hover:bg-purple-100"
                              />
                            )}
                            {onDelete && (
                              <ActionButton
                                icon={<Trash2 size={16} />}
                                onClick={() => onDelete(item)}
                                title="Delete"
                                className="text-red-500 hover:bg-red-100"
                              />
                            )}
                          </div>
                        </td>
                      )}
                    </motion.tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={visibleHeaders.length + 1}>
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex flex-col items-center justify-center gap-4 text-slate-500 py-16">
                      <Search className="h-12 w-12 text-slate-300" />
                      <p className="text-lg font-medium">
                        {search ? `No results for "${search}"` : emptyMessage}
                      </p>
                      {search && (
                        <button
                          onClick={() => setSearch('')}
                          className="mt-2 px-4 py-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 font-semibold">
                          Clear search
                        </button>
                      )}
                    </motion.div>
                  </td>
                </tr>
              )}
            </AnimatePresence>
          </tbody>
        </table>

        <AnimatePresence>
          {hoverData.visible && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 400, damping: 25 }}
              className="fixed bg-white/80 backdrop-blur-lg rounded-lg shadow-xl border border-slate-200/50 p-3 max-w-xs z-50 pointer-events-none"
              style={hoverData.style}>
              <p className="text-sm text-slate-800 break-words">{hoverData.content}</p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* --- Pagination --- */}
      {totalItems > 0 && (
        <div className="p-4 border-t border-slate-200">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-2 text-sm text-slate-600">
              <span>Rows:</span>
              <select
                value={pageSize}
                onChange={(e) => setPageSize(Number(e.target.value))}
                className="border border-slate-300 rounded-md px-2 py-1 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500">
                {[10, 20, 50, 100].map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
              <span className="hidden md:inline text-slate-400">|</span>
              <span className="hidden md:inline">
                Page {currentPage} of {totalPages}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <PaginationButton onClick={() => setCurrentPage(1)} disabled={currentPage === 1}>
                <ChevronLeft />
              </PaginationButton>
              {paginationRange.map((pageNumber, index) =>
                pageNumber === DOTS ? (
                  <span key={index} className="px-2 py-1 text-slate-500">
                    ...
                  </span>
                ) : (
                  <PaginationButton
                    key={index}
                    onClick={() => setCurrentPage(pageNumber)}
                    isActive={pageNumber === currentPage}>
                    {pageNumber}
                  </PaginationButton>
                )
              )}
              <PaginationButton
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}>
                <ChevronRight />
              </PaginationButton>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

// --- Sub-components for better organization ---
const ActionButton = ({ icon, onClick, title, className }) => (
  <button
    onClick={onClick}
    className={clsx('p-2 rounded-full transition-colors', className)}
    title={title}>
    {icon}
  </button>
);

const PaginationButton = ({ children, onClick, disabled, isActive }) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={clsx(
      'px-3 h-9 flex items-center justify-center rounded-md text-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
      {
        'bg-blue-600 text-white font-semibold shadow-sm': isActive,
        'hover:bg-slate-100 text-slate-700': !isActive && typeof children === 'number',
        'hover:bg-slate-100 text-slate-700 text-xs font-semibold':
          !isActive && typeof children !== 'number'
      }
    )}>
    {children}
  </button>
);

// --- PropTypes for robust component usage ---
Table.propTypes = {
  title: PropTypes.string,
  titleIcon: PropTypes.node,
  buttonName: PropTypes.string,
  onAddNew: PropTypes.func,
  header: PropTypes.arrayOf(
    PropTypes.shape({
      header: PropTypes.string.isRequired,
      show: PropTypes.bool.isRequired,
      data: PropTypes.string,
      className: PropTypes.string,
      render: PropTypes.func,
      sortable: PropTypes.bool
    })
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  searchBy: PropTypes.arrayOf(PropTypes.string),
  searchPlaceholder: PropTypes.string,
  onView: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onPdf: PropTypes.func,
  customColumn: PropTypes.bool,
  itemsPerPage: PropTypes.number,
  loading: PropTypes.bool,
  emptyMessage: PropTypes.string
};

export default Table;
