import React, { useState, useRef } from 'react';

const VoiceBot = ({ onClose }) => {
  const [shareUrl, setShareUrl] = useState(
    'https://sasthra-ai-live-teachers-671317745974.us-west1.run.app'
  );
  const [showIframe, setShowIframe] = useState(false);
  const [inputUrl, setInputUrl] = useState('');
  const iframeRef = useRef(null);

  const handleButtonClick = () => {
    const url = prompt('Please enter the share URL for VoiceBot:', shareUrl);
    if (url) {
      setShareUrl(url);
      setShowIframe(true);
    }
  };

  const handleClose = () => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow.postMessage({ type: 'STOP_AUDIO' }, '*');
    }
    setShowIframe(false);
    onClose();
  };

  return (
    <div className="relative w-full h-[600px] bg-gradient-to-br from-blue-50 to-cyan-100 rounded-xl shadow-2xl overflow-hidden">
      {showIframe ? (
        <iframe
          ref={iframeRef}
          src={shareUrl}
          title="Sasthra VoiceBot"
          width="100%"
          height="100%"
          style={{ border: 'none' }}
          allow="microphone; clipboard-write; fullscreen"
          className="rounded-xl"
        ></iframe>
      ) : (
        <div className="flex flex-col items-center justify-center h-full bg-gradient-to-br from-blue-100 to-cyan-200">
          <div className="text-center p-8">
            <div className="mb-6">
              <svg
                className="w-24 h-24 mx-auto text-blue-600"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-3">VoiceBot Ready</h3>
            <p className="text-gray-600 mb-6 max-w-md">
              Engage in natural conversations with our AI-powered voice assistant. Click below to
              begin your audio interaction.
            </p>
            <button
              className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white px-8 py-3 rounded-full hover:from-blue-700 hover:to-cyan-700 transform hover:scale-105 transition-all duration-200 shadow-lg font-semibold"
              onClick={handleButtonClick}
            >
              Load VoiceBot
            </button>
          </div>
        </div>
      )}
      <button
        className="absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-2 rounded-full hover:from-red-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-200 shadow-lg font-medium"
        onClick={handleClose}
      >
        Close
      </button>
    </div>
  );
};

export default VoiceBot;
