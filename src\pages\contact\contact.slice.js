import { contactApi } from '../../redux/api/api';

export const contactApiSlice = contactApi.injectEndpoints({
  endpoints: (builder) => ({
    submitStudentInquiry: builder.mutation({
      query: (body) => ({
        url: '/contact/student',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['contactApi']
    }),
    submitBusinessInquiry: builder.mutation({
      query: (body) => ({
        url: '/contact/business',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['contactApi']
    }),
    submitSchoolHubInquiry: builder.mutation({
      query: (body) => ({
        url: '/contact/schoolhub',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['contactApi']
    })
  })
});

export const {
  useSubmitStudentInquiryMutation,
  useSubmitBusinessInquiryMutation,
  useSubmitSchoolHubInquiryMutation
} = contactApiSlice;
