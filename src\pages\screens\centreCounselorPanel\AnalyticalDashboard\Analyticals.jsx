import React, { useState } from 'react';
// eslint-disable-next-line
import { AnimatePresence, motion } from 'framer-motion';
import { BarChart4 } from 'lucide-react'; // Using a more consistent icon name
import AnalyticalDashboard from './ComputerBasedTest/AnalyticalDashboard';
import CenterLiveQuiz from './LiveQuiz/CenterLiveQuiz';
import CenterStudentsQuiz from './StudentsQuiz/CenterStudentsQuiz';
import CenterOcr from './centerCounselorOcr/CenterOcr';
import CenterCounselorOmr from './centerCounselorOmr/CenterCounselorOmr';

const Analyticals = () => {
  const [activeTab, setActiveTab] = useState('cbt');

  const tabItems = [
    { id: 'cbt', label: 'Computer Based Test', component: <AnalyticalDashboard /> },
    { id: 'livequiz', label: 'Live Quiz', component: <CenterLiveQuiz /> },
    { id: 'studentquiz', label: 'Student Quiz', component: <CenterStudentsQuiz /> },
    { id: 'ocr', label: 'OCR', component: <CenterOcr /> },
    { id: 'omr', label: 'OMR', component: <CenterCounselorOmr /> },
  ];

  const activeComponent = tabItems.find((tab) => tab.id === activeTab)?.component;

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-yellow-50 to-amber-200 text-amber-900 isolate overflow-x-hidden">
      {/* Background Aurora Effect */}
      <div
        className="absolute inset-x-0 -z-10 transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true">
        <div
          className="relative left-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#fbbf24] to-[#f97316] opacity-30 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
          }}
        />
      </div>

      <div className="max-w-screen-2xl mx-auto px-4 py-4 space-y-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, ease: 'easeOut' }}
          className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl shadow-lg shadow-amber-500/30">
            <BarChart4 size={28} className="text-white" />
          </div>
          <div>
            <h1 className="text-3xl md:text-4xl font-extrabold text-amber-900">Analytical Hub</h1>
            <p className="text-amber-700 mt-1">Real-time insights and performance metrics</p>
          </div>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.1, ease: 'easeOut' }}
          className="flex justify-center">
          <div className="flex space-x-2 bg-white/40 backdrop-blur-md p-2 rounded-2xl border border-amber-300/60 shadow-lg">
            {tabItems.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`relative cursor-pointer px-4 sm:px-6 py-3 text-sm sm:text-base font-semibold rounded-xl transition-colors duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-amber-200 focus-visible:ring-orange-500 ${
                  activeTab === tab.id ? 'text-white' : 'text-amber-800 hover:text-amber-900'
                }`}>
                {activeTab === tab.id && (
                  <motion.div
                    layoutId="activeTabBackground"
                    className="absolute inset-0 bg-gradient-to-r from-amber-500 to-orange-600 rounded-xl shadow-md"
                    transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                  />
                )}
                <span className="relative z-10 tracking-wide">{tab.label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Tab Content */}
        <div className="mt-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}>
              {activeComponent}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default Analyticals;
