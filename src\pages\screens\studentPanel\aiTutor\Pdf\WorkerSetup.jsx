import { pdfjs } from 'react-pdf';

// Fallback options for PDF worker
const workerOptions = [
  `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`,
  `https://unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`,
  `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`,
  `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`,
  `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`,
  `/pdf.worker.min.js`
];

// Function to test worker URL availability
const testWorkerUrl = async (url) => {
  try {
    console.log(`Testing PDF worker URL: ${url}`);
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};

// Function to set up PDF worker with fallbacks
const setupPdfWorker = async () => {
  for (const url of workerOptions) {
    try {
      console.log(`Attempting to set PDF worker: ${url}`);
      const isAvailable = await testWorkerUrl(url);
      if (isAvailable) {
        pdfjs.GlobalWorkerOptions.workerSrc = url;
        console.log(`✅ PDF worker successfully set to: ${url}`);
        return true;
      }
    } catch (error) {
      console.warn(`❌ Failed to load worker from ${url}:`, error);
    }
  }
  console.warn('⚠️ All worker URLs failed, using fake worker (performance may be affected)');
  pdfjs.GlobalWorkerOptions.workerSrc = false;
  return false;
};

// Initialize worker setup
let workerInitialized = false;
const initializeWorker = async () => {
  if (!workerInitialized) {
    console.log('Initializing PDF worker...');
    await setupPdfWorker();
    workerInitialized = true;
    console.log('PDF worker initialization complete.');
  }
};

export { initializeWorker };