import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON>rkles,
  Target,
  Info,
  Gauge,
  ArrowRight,
  X,
  BookOpen,
  Lightbulb,
  HelpCircle,
  Trophy,
  PartyPopper,
  RotateCw,
  ClockFading
} from 'lucide-react';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { motion, AnimatePresence, useMotionValue, useTransform, animate } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import Button from '../../../../components/Field/Button';
import {
  setCompletedRecommendations,
  setRecommendationData,
  setRecommendationPendingData,
  useLazyGetCompletedRecommendationsQuery,
  useLazyGetRecommendationsByIdQuery,
  useLazyGetRecommendationsQuery,
  useSubmitRecommendationsMutation
} from './recommendation.slice';
import { useDispatch, useSelector } from 'react-redux';

const PopupSkeleton = ({ onClose, layoutId }) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.3 }}
    className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm p-4"
    onClick={onClose}>
    <motion.div
      layoutId={layoutId}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      className="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl flex flex-col overflow-hidden"
      onClick={(e) => e.stopPropagation()}>
      <div className="flex items-center justify-between p-5 border-b border-gray-200">
        <div className="h-8 w-3/5 bg-gray-200 rounded-md animate-pulse"></div>
        <div className="p-2 rounded-full bg-gray-200 animate-pulse">
          <X size={24} className="text-transparent" />
        </div>
      </div>
      <div className="flex-grow p-8 space-y-10 animate-pulse">
        <div className="space-y-4">
          <div className="h-6 w-1/4 bg-gray-200 rounded-md"></div>
          <div className="h-4 w-full bg-gray-200 rounded-md"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded-md"></div>
        </div>
        <div className="space-y-4">
          <div className="h-6 w-1/3 bg-gray-200 rounded-md"></div>
          <div className="h-24 w-full bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    </motion.div>
  </motion.div>
);

const MissionCompletedPopup = ({ results, onClose }) => {
  const { score } = results;
  const passed = score >= 70;

  const count = useMotionValue(0);
  const rounded = useTransform(count, (latest) => Math.round(latest));

  useEffect(() => {
    const controls = animate(count, score, { duration: 1.5, ease: 'circOut' });
    return controls.stop;
  }, [score, count]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.1 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm p-4"
      onClick={onClose}>
      <motion.div
        initial={{ scale: 0.7, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.2, opacity: 0 }}
        transition={{ type: 'spring', stiffness: 400, damping: 25 }}
        className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl text-center p-8"
        onClick={(e) => e.stopPropagation()}>
        <motion.div
          initial={{ y: -50, scale: 0 }}
          animate={{ y: 0, scale: 1 }}
          transition={{ type: 'spring', delay: 0.2, stiffness: 300, damping: 15 }}
          className={`mx-auto w-24 h-24 rounded-full flex items-center justify-center text-white mb-6 ${
            passed
              ? 'bg-gradient-to-br from-green-500 to-emerald-500'
              : 'bg-gradient-to-br from-amber-500 to-orange-500'
          }`}>
          {passed ? <Trophy size={50} /> : <RotateCw size={50} />}
        </motion.div>
        <h2 className="text-3xl font-bold text-gray-800">
          {passed ? 'Mission Accomplished!' : 'Good Effort!'}
        </h2>
        <p className="text-gray-500 mt-2 mb-6">
          {passed
            ? 'Excellent work! You have successfully completed the mission.'
            : "You're on the right track! Review the concepts and try again."}
        </p>
        <div className="bg-gray-100 rounded-xl p-6 mb-8">
          <p className="text-sm font-medium text-gray-500">YOUR SCORE</p>
          <motion.p className="text-6xl font-bold text-gray-900 my-1">{rounded}</motion.p>
          <p className="text-sm font-medium text-gray-500">%</p>
        </div>
        <Button
          name="Continue Learning"
          onClick={onClose}
          className={`${passed ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} text-white w-full px-6 py-3`}
        />
      </motion.div>
    </motion.div>
  );
};

const MissionDetailPopup = ({ mission, onMissionSubmit, onClose, layoutId }) => {
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [quizStatus, setQuizStatus] = useState('answering');
  const [quizResults, setQuizResults] = useState(null);
  const isReviewMode = mission?.is_review;

  useEffect(() => {
    if (isReviewMode && mission.student_answers) {
      setQuizStatus('submitted');
      const previousAnswers = mission.student_answers.reduce((acc, ans) => {
        acc[ans.question_text] = ans.selected_option;
        return acc;
      }, {});
      setSelectedAnswers(previousAnswers);
      const results = mission.learning_content?.quiz_questions?.map((q) => {
        const userAnswer = previousAnswers[q.question_text];
        const isCorrect = userAnswer === q.correct_option;
        return {
          question: q.question_text,
          selected: userAnswer,
          correct: q.correct_option,
          isCorrect
        };
      });
      const correctCount = results?.filter((r) => r.isCorrect).length;
      const totalCount = mission.learning_content?.quiz_questions?.length;
      setQuizResults({ results, score: (correctCount / totalCount) * 100 });
    } else {
      setSelectedAnswers({});
      setQuizStatus('answering');
      setQuizResults(null);
    }
  }, [mission, isReviewMode]);

  const handleAnswerChange = (questionText, optionKey) => {
    setSelectedAnswers((prev) => ({ ...prev, [questionText]: optionKey }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (isReviewMode || quizStatus !== 'answering') return;

    onMissionSubmit(selectedAnswers);
  };

  if (!mission) return <PopupSkeleton layoutId={layoutId} onClose={onClose} />;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm p-4">
      <motion.div
        layoutId={layoutId}
        transition={{ type: 'spring', stiffness: 300, damping: 30, duration: 0.4 }}
        className="relative w-full max-w-5xl max-h-[90vh] bg-white rounded-2xl shadow-2xl flex flex-col overflow-y-scroll"
        onClick={(e) => e.stopPropagation()}>
        <div className="flex items-center justify-between p-5 border-b border-gray-200 bg-gray-50/70 sticky top-0 z-10">
          <h2 className="text-2xl font-bold text-gray-800">{mission.title}</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 rounded-full hover:bg-gray-200 hover:text-gray-800 transition-colors">
            <X size={24} />
          </button>
        </div>
        <div className="flex-grow p-8 space-y-12">
          {mission.motivational_tip && (
            <section>
              <div className="bg-amber-50 border-l-4 border-amber-400 p-4 rounded-r-lg">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Lightbulb className="h-5 w-5 text-amber-500" aria-hidden="true" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-amber-800">{mission.motivational_tip}</p>
                  </div>
                </div>
              </div>
            </section>
          )}

          {mission.recommended_videos.map((v, i) => (
            <iframe
              key={i}
              width="100%"
              height="500"
              src={`https://www.youtube.com/embed/${v.videoId}`}
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen></iframe>
          ))}

          <section>
            <div className="flex items-center gap-3 mb-4">
              <BookOpen className="text-indigo-500" size={22} />
              <h3 className="text-xl font-semibold text-gray-700">Explanation</h3>
            </div>
            <div className="prose prose-lg max-w-none text-gray-600">
              <ReactMarkdown>
                {mission.learning_content?.explanation_markdown || 'No explanation provided.'}
              </ReactMarkdown>
            </div>
          </section>

          {mission.personal_feedback && (
            <section>
              <div className="bg-amber-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <ClockFading className="h-5 w-5 text-blue-500" aria-hidden="true" />
                  </div>
                  <div className="ml-3">
                    <span className="text-md font-semibold text-blue-800">
                      {mission.personal_feedback?.title}
                    </span>
                    <p className="text-sm text-blue-800">
                      {mission.personal_feedback?.analysis_markdown}
                    </p>
                  </div>
                </div>
              </div>
            </section>
          )}

          <section>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <HelpCircle className="text-indigo-500" size={22} />
                <h3 className="text-xl font-semibold text-gray-700">Check Your Understanding</h3>
              </div>
              {quizResults && (
                <div className="text-right">
                  <p className="font-semibold text-lg text-gray-800">
                    Your Score:{' '}
                    <span className="font-bold text-indigo-600">
                      {quizResults.score.toFixed(0)}%
                    </span>
                  </p>
                </div>
              )}
            </div>
            <form onSubmit={handleSubmit} className="space-y-6">
              {mission.learning_content?.quiz_questions?.map((q, index) => {
                const isSubmitted = quizStatus === 'submitted';
                const userAnswer = selectedAnswers[q.question_text];
                return (
                  <div
                    key={q.question_text}
                    className="p-4 border border-gray-200 rounded-lg bg-gray-50/50">
                    <p className="font-semibold text-gray-800 mb-4">
                      {index + 1}. {q.question_text}
                    </p>
                    <div className="space-y-3">
                      {Object.entries(q.options).map(([key, value]) => {
                        const isSelected = userAnswer === key;
                        const isCorrect = q.correct_option === key;
                        let optionClass =
                          'flex items-center p-3 rounded-md border-2 transition-all';
                        if (isSubmitted) {
                          optionClass += ' cursor-default';
                          if (isCorrect)
                            optionClass +=
                              ' bg-green-100/70 border-green-500 text-green-900 font-medium';
                          else if (isSelected && !isCorrect)
                            optionClass += ' bg-red-100/70 border-red-500 text-red-900';
                          else optionClass += ' bg-white border-gray-200 opacity-70';
                        } else {
                          optionClass += ' cursor-pointer';
                          if (isSelected) optionClass += ' bg-blue-50 border-blue-500 shadow-sm';
                          else optionClass += ' bg-white border-gray-200 hover:border-blue-300';
                        }
                        return (
                          <label key={key} className={optionClass}>
                            <input
                              type="radio"
                              name={`question-${index}`}
                              value={key}
                              checked={isSelected}
                              disabled={isSubmitted}
                              onChange={() => handleAnswerChange(q.question_text, key)}
                              className="w-4 h-4 text-blue-600 form-radio focus:ring-blue-500 disabled:opacity-50"
                            />
                            <span className="ml-4 flex-1">{value}</span>
                            {isSubmitted && isCorrect && (
                              <CircleCheck size={20} className="text-green-600" />
                            )}
                            {isSubmitted && isSelected && !isCorrect && (
                              <X size={20} className="text-red-600" />
                            )}
                          </label>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
              <div className="flex justify-end pt-4">
                {isReviewMode ? (
                  <Button
                    name="Done Reviewing"
                    onClick={onClose}
                    className="bg-gray-600 text-white hover:bg-gray-700 px-6 py-2.5"
                  />
                ) : (
                  <Button
                    name="Finish Mission"
                    type="submit"
                    disabled={
                      Object.keys(selectedAnswers).length !==
                      mission.learning_content?.quiz_questions?.length
                    }
                    className="bg-blue-600 text-white hover:bg-blue-700 px-6 py-2.5 disabled:bg-gray-400 disabled:cursor-not-allowed"
                  />
                )}
              </div>
            </form>
          </section>
        </div>
      </motion.div>
    </motion.div>
  );
};

const RecommendationCard = ({ recommendation, onClick, isCompleted = false, layoutId }) => {
  const { title, subject_name, topic_name, sub_topic_name, triggering_event_type, completed_at } =
    recommendation;

  const cardTheme = {
    gradient: isCompleted ? 'from-green-500 to-emerald-500' : 'from-blue-500 to-purple-500',
    hoverText: isCompleted ? 'group-hover:text-green-600' : 'group-hover:text-blue-600',
    actionText: isCompleted ? 'text-green-600' : 'text-blue-600'
  };

  const date = completed_at
    ? new Date(completed_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    : null;

  return (
    <motion.div
      layoutId={layoutId}
      layout
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.9 }}
      transition={{ type: 'spring', stiffness: 260, damping: 25 }}
      className="bg-white rounded-2xl shadow-lg border border-gray-200/80 overflow-hidden transform hover:-translate-y-1 hover:shadow-xl transition-all duration-300 cursor-pointer group"
      onClick={onClick}>
      <div className="p-6">
        <div className="flex items-start gap-4 mb-4">
          <div
            className={`bg-gradient-to-br ${cardTheme.gradient} text-white p-3 rounded-xl shadow-md`}>
            {isCompleted ? <CircleCheck size={24} /> : <Target size={24} />}
          </div>
          <div>
            <h3
              className={`font-bold text-xl text-gray-800 ${cardTheme.hoverText} transition-colors`}>
              {title}
            </h3>
            <p className="text-sm text-gray-500 font-medium">
              {subject_name} - {topic_name} - {sub_topic_name}
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 text-sm">
          <div className="flex items-center gap-2 bg-gray-100 p-2.5 rounded-lg">
            <Info size={18} className="text-gray-500 flex-shrink-0" />
            <div>
              <span className="font-semibold text-gray-700">Event Tag:</span>
              <span className="text-gray-600 ml-1">{triggering_event_type}</span>
            </div>
          </div>
          {isCompleted && date && (
            <div className="flex items-center gap-2 bg-gray-100 p-2.5 rounded-lg">
              <ClockFading size={18} className="text-gray-500 flex-shrink-0" />
              <div>
                <span className="font-semibold text-gray-700">Completed On:</span>
                <span className="text-gray-600 ml-1">{date}</span>
              </div>
            </div>
          )}
        </div>
        <div className="flex justify-end items-center">
          <div className={`flex items-center gap-2 ${cardTheme.actionText} font-semibold`}>
            {isCompleted ? 'Review Mission' : 'Start Mission'}
            <ArrowRight
              size={18}
              className="transform group-hover:translate-x-1 transition-transform"
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const Recommendations = () => {
  const [screenHeight, setScreenHeight] = useState(0);
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedMission, setSelectedMission] = useState(null);
  const [missionResultsToShow, setMissionResultsToShow] = useState(null);

  const [getRecommendations] = useLazyGetRecommendationsQuery();
  const [getRecommendationsById] = useLazyGetRecommendationsByIdQuery();
  const [getCompletedRecommendations] = useLazyGetCompletedRecommendationsQuery();
  const [submitRecommendations] = useSubmitRecommendationsMutation();

  const recommendationData = useSelector((state) => state.recommendation.recommendationData);
  const completedRecommendationData = useSelector(
    (state) => state.recommendation.completedRecommendations
  );
  const recommendationPendingData = useSelector(
    (state) => state.recommendation.recommendationPendingData
  );
  const dispatch = useDispatch();
  useEffect(() => {
    handleGetRecommendations();
    handleGetCompletedRecommendations();
  }, []);
  const handleGetRecommendations = async () => {
    try {
      const recommendations = await getRecommendations({ user_id: sessionStorage.userId }).unwrap();
      dispatch(setRecommendationData(recommendations.data));
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    }
  };

  const handleGetCompletedRecommendations = async () => {
    try {
      const recommendations = await getCompletedRecommendations({
        user_id: sessionStorage.userId
      }).unwrap();
      dispatch(setCompletedRecommendations(recommendations.data));
    } catch (error) {
      console.error('Error fetching completed recommendations:', error);
    }
  };

  const handleSumbitRecommendations = async (submittedAnswers) => {
    if (!selectedMission || !recommendationPendingData) {
      console.error('Mission data not available for submission.');
      return;
    }

    const formattedAnswers = Object.entries(submittedAnswers).map(([questionText, optionKey]) => ({
      question_text: questionText,
      selected_option: optionKey
    }));

    const payload = {
      user_id: sessionStorage.userId,
      attempt_id: selectedMission.mission_id,
      answers: formattedAnswers
    };

    try {
      await submitRecommendations(payload).unwrap();

      const correctCount = recommendationPendingData.learning_content.quiz_questions.filter(
        (q) => submittedAnswers[q.question_text] === q.correct_option
      ).length;
      const totalCount = recommendationPendingData.learning_content.quiz_questions.length;
      const score = totalCount > 0 ? (correctCount / totalCount) * 100 : 0;

      // FIX: Close the detail popup by clearing both local and Redux state
      setSelectedMission(null);
      dispatch(setRecommendationPendingData(null));

      // Show the completion results popup
      setMissionResultsToShow({ score });

      // Refresh the data lists in the background
      handleGetRecommendations();
      handleGetCompletedRecommendations();
    } catch (error) {
      console.error('Error submitting recommendations:', error);
    }
  };

  const handleCardClick = async (mission, isCompleted = false) => {
    setSelectedMission(mission);
    if (isCompleted) {
      const missionDetails = {
        ...mission.content_json,
        student_answers: mission.student_answers,
        is_review: true
      };
      dispatch(setRecommendationPendingData(missionDetails));
    } else {
      try {
        const result = await getRecommendationsById({
          mission_id: mission.mission_id,
          user_id: sessionStorage.userId
        }).unwrap();
        dispatch(setRecommendationPendingData(result.data));
      } catch (error) {
        console.error('Error fetching recommendations by ID:', error);
        setSelectedMission(null);
      }
    }
  };
  const handleCloseDetailPopup = () => {
    setSelectedMission(null);
    dispatch(setRecommendationPendingData(null));
  };

  const handleCloseCompletionPopup = () => {
    setMissionResultsToShow(null);
  };
  const tabs = [
    {
      id: 'pending',
      tabName: 'Pending',
      icon: <ClockAlert />,
      count: recommendationData?.length || 0
    },
    {
      id: 'completed',
      tabName: 'Completed',
      icon: <CircleCheck />,
      count: completedRecommendationData?.length || 0
    }
  ];
  useLayoutEffect(() => {
    const updateHeight = () => setScreenHeight(window.innerHeight);
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);
  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: 'easeInOut' } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.3, ease: 'easeInOut' } }
  };
  return (
    <div className="relative">
      <section className="p-2 border-b-2 border-gray-200">
        <div className="flex items-center gap-3">
          <Sparkles
            size={50}
            className="bg-gradient-to-br from-blue-600 to-purple-600 text-white p-2 rounded-xl"
          />
          <div>
            <h1 className="text-2xl font-bold">Recommendations</h1>
            <span className="text-gray-500">AI-Powered Recommendations Platform</span>
          </div>
        </div>
      </section>
      <section
        className="grid grid-cols-[75%_25%] space-x-10 my-5 mx-10"
        style={{ height: screenHeight - 195 }}>
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="flex flex-col">
          <div className="relative flex items-center justify-evenly bg-gray-200 p-1 rounded-xl">
            {tabs.map((tab) => (
              <motion.div key={tab.id} className="relative z-10 flex-1">
                <Button
                  name={
                    <p className="flex items-center gap-2">
                      {tab.tabName}
                      <span
                        className={`text-sm font-medium px-2 py-0.5 rounded-full ${tab.id === 'pending' ? ' text-blue-600 bg-blue-300 ' : 'text-green-600 bg-green-300'}`}>
                        {tab.count}
                      </span>
                    </p>
                  }
                  icon={tab.icon}
                  className={`w-full bg-transparent flex justify-center transition-colors duration-300 p-2 font-semibold cursor-pointer ${activeTab === tab.id ? `${tab.id === 'pending' ? 'text-blue-600' : 'text-green-600'}` : 'text-gray-700 hover:text-black'}`}
                  onClick={() => setActiveTab(tab.id)}
                />
                {activeTab === tab.id && (
                  <motion.div
                    className="absolute inset-0 bg-white rounded-lg -z-10"
                    layoutId="active-tab-indicator"
                    transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                  />
                )}
              </motion.div>
            ))}
          </div>

          <div
            className="flex-grow overflow-y-auto p-1 pr-2 mt-4 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100"
            style={{ maxHeight: screenHeight - 295 }}>
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                variants={contentVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="space-y-4">
                {activeTab === 'pending' && (
                  <>
                    <h2 className="text-xl font-semibold text-gray-800 px-2">Your Next Missions</h2>
                    {recommendationData?.length > 0 ? (
                      recommendationData.map((rec) => (
                        <RecommendationCard
                          key={rec.mission_id}
                          recommendation={rec}
                          layoutId={`mission-card-${rec.mission_id}`}
                          onClick={() => handleCardClick(rec, false)}
                        />
                      ))
                    ) : (
                      <div className="text-center text-gray-500 py-10">
                        No pending recommendations. Great job!
                      </div>
                    )}
                  </>
                )}
                {activeTab === 'completed' && (
                  <>
                    <h2 className="text-xl font-semibold text-gray-800 px-2">Completed Missions</h2>
                    {completedRecommendationData?.length > 0 ? (
                      completedRecommendationData.map((rec) => (
                        <RecommendationCard
                          key={rec.mission_id}
                          recommendation={rec}
                          isCompleted={true}
                          layoutId={`mission-card-${rec.mission_id}`}
                          onClick={() => handleCardClick(rec, true)}
                        />
                      ))
                    ) : (
                      <div className="text-center text-gray-500 py-10">
                        You haven't completed any missions yet.
                      </div>
                    )}
                  </>
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="border-2 border-gray-300 rounded-2xl p-6 bg-gray-50 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100"
          style={{ maxHeight: screenHeight - 195 }}>
          <h2 className="text-xl font-bold mb-4">Your Learning Journey</h2>
          <p className="text-gray-600 text-pretty mb-4">
            Welcome! Your personalized missions are ready. Each one is designed to help you master a
            specific concept where you need a little boost.
          </p>
          <h2 className="text-xl font-bold mb-4">How to Succeed</h2>
          <ul className="list-decimal mx-4 space-y-2 text-gray-700">
            <li>
              Click a mission from the "Pending" list. The one with the lowest mastery score is a
              great place to start.
            </li>
            <li>Read the explanation carefully before attempting the quiz.</li>
            <li>
              After completing, review your results. Understanding why an answer was right or wrong
              is key.
            </li>
            <li>
              If you don't pass, a new, more targeted mission may be generated. This is part of the
              learning process!
            </li>
          </ul>
        </motion.div>
      </section>
      <AnimatePresence>
        {selectedMission && (
          <MissionDetailPopup
            mission={recommendationPendingData}
            onClose={handleCloseDetailPopup}
            onMissionSubmit={handleSumbitRecommendations}
            layoutId={`mission-card-${selectedMission.mission_id}`}
          />
        )}
      </AnimatePresence>
      <AnimatePresence>
        {missionResultsToShow && (
          <MissionCompletedPopup
            results={missionResultsToShow}
            onClose={handleCloseCompletionPopup}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Recommendations;
