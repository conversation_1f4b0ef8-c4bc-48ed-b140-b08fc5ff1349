import { useEffect, useRef } from "react"
import { X } from "lucide-react"
import * as THREE from "three"

export default function BreakTime({ showScreensaver, setShowScreensaver }) {
  const screensaverRef = useRef(null)
  const animationRef = useRef(null)

  useEffect(() => {
    if (showScreensaver && screensaverRef.current) {
      const scene = new THREE.Scene()
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
      const renderer = new THREE.WebGLRenderer({ antialias: true })
      
      renderer.setSize(window.innerWidth, window.innerHeight)
      renderer.setClearColor(0x1e3a8a, 1)
      screensaverRef.current.appendChild(renderer.domElement)

      const geometries = []
      
      for (let i = 0; i < 20; i++) {
        const geometry = new THREE.BoxGeometry(0.5 + Math.random() * 0.5, 0.5 + Math.random() * 0.5, 0.5 + Math.random() * 0.5)
        const material = new THREE.MeshPhongMaterial({ 
          color: new THREE.Color().setHSL(Math.random() * 0.3 + 0.5, 0.7, 0.6),
          transparent: true,
          opacity: 0.8
        })
        const cube = new THREE.Mesh(geometry, material)
        
        cube.position.set(
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20
        )
        cube.rotation.set(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI)
        cube.userData = {
          initialPosition: cube.position.clone(),
          rotationSpeed: new THREE.Vector3(
            (Math.random() - 0.5) * 0.02,
            (Math.random() - 0.5) * 0.02,
            (Math.random() - 0.5) * 0.02
          ),
          floatSpeed: Math.random() * 0.01 + 0.005
        }
        
        scene.add(cube)
        geometries.push(cube)
      }

      for (let i = 0; i < 15; i++) {
        const geometry = new THREE.SphereGeometry(0.3 + Math.random() * 0.4, 16, 16)
        const material = new THREE.MeshPhongMaterial({ 
          color: new THREE.Color().setHSL(Math.random() * 0.3 + 0.1, 0.8, 0.7),
          transparent: true,
          opacity: 0.7,
          shininess: 100
        })
        const sphere = new THREE.Mesh(geometry, material)
        
        sphere.position.set(
          (Math.random() - 0.5) * 15,
          (Math.random() - 0.5) * 15,
          (Math.random() - 0.5) * 15
        )
        sphere.userData = {
          initialPosition: sphere.position.clone(),
          floatSpeed: Math.random() * 0.008 + 0.003,
          phase: Math.random() * Math.PI * 2
        }
        
        scene.add(sphere)
        geometries.push(sphere)
      }

      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(10, 10, 5)
      scene.add(directionalLight)

      const pointLight = new THREE.PointLight(0x4a90ff, 1, 100)
      pointLight.position.set(0, 0, 10)
      scene.add(pointLight)

      const textGeometry = new THREE.BufferGeometry()
      const textMaterial = new THREE.PointsMaterial({
        color: 0xffffff,
        size: 0.1,
        transparent: true,
        opacity: 0.8
      })

      const positions = new Float32Array(500 * 3)
      for (let i = 0; i < 500; i++) {
        positions[i * 3] = (Math.random() - 0.5) * 25
        positions[i * 3 + 1] = (Math.random() - 0.5) * 25
        positions[i * 3 + 2] = (Math.random() - 0.5) * 25
      }
      textGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
      const particles = new THREE.Points(textGeometry, textMaterial)
      scene.add(particles)

      camera.position.z = 15

      let time = 0
      const animate = () => {
        if (!showScreensaver) return
        
        time += 0.01
        
        geometries.forEach((object, index) => {
          if (object.geometry instanceof THREE.BoxGeometry) {
            object.rotation.x += object.userData.rotationSpeed.x
            object.rotation.y += object.userData.rotationSpeed.y
            object.rotation.z += object.userData.rotationSpeed.z
            
            object.position.y = object.userData.initialPosition.y + Math.sin(time + index) * 2
            object.position.x = object.userData.initialPosition.x + Math.cos(time * 0.5 + index) * 1
          } else if (object.geometry instanceof THREE.SphereGeometry) {
            const phase = object.userData.phase + time * object.userData.floatSpeed
            object.position.y = object.userData.initialPosition.y + Math.sin(phase) * 3
            object.position.x = object.userData.initialPosition.x + Math.cos(phase * 0.7) * 2
            object.position.z = object.userData.initialPosition.z + Math.sin(phase * 1.3) * 1.5
          }
        })

        particles.rotation.y += 0.005
        particles.rotation.x += 0.002

        camera.position.x = Math.cos(time * 0.1) * 15
        camera.position.z = Math.sin(time * 0.1) * 15
        camera.lookAt(0, 0, 0)

        pointLight.position.x = Math.cos(time * 0.5) * 10
        pointLight.position.z = Math.sin(time * 0.5) * 10

        renderer.render(scene, camera)
        animationRef.current = requestAnimationFrame(animate)
      }

      animate()

      const handleResize = () => {
        camera.aspect = window.innerWidth / window.innerHeight
        camera.updateProjectionMatrix()
        renderer.setSize(window.innerWidth, window.innerHeight)
      }
      window.addEventListener('resize', handleResize)

      return () => {
        window.removeEventListener('resize', handleResize)
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current)
        }
        if (screensaverRef.current && renderer.domElement) {
          screensaverRef.current.removeChild(renderer.domElement)
        }
        renderer.dispose()
      }
    }
  }, [showScreensaver])

  return (
    <>
      {showScreensaver && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center">
          <div ref={screensaverRef} className="w-full h-full"></div>
          <button
            onClick={() => setShowScreensaver(false)}
            className="absolute top-4 right-4 p-2 bg-white rounded-full text-slate-900 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      )}
    </>
  )
}