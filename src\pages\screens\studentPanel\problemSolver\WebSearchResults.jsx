import { motion } from 'framer-motion';
import { FaExternalLinkAlt, FaGlobe } from 'react-icons/fa';

const WebSearchResults = ({ webSearchResults, isWebSearchLoading }) => {
  return (
    <div className="min-h-full">
      {isWebSearchLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex flex-col items-center justify-center h-64 space-y-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
            className="w-12 h-12 border-4 border-green-500 border-t-transparent rounded-full"
          />
          <div className="text-center">
            <h4 className="text-lg font-semibold text-gray-700 mb-2">Searching the web...</h4>
            <p className="text-gray-500">Finding relevant information for you</p>
          </div>
        </motion.div>
      )}
      
      {!isWebSearchLoading && webSearchResults.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col items-center justify-center h-64 space-y-4 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <FaGlobe className="text-2xl text-gray-400" />
          </div>
          <div>
            <h4 className="text-lg font-semibold text-gray-700 mb-2">No search results yet</h4>
            <p className="text-gray-500 max-w-sm">
              Click the Web tab or ask a question to search for relevant information
            </p>
          </div>
        </motion.div>
      )}
      
      {!isWebSearchLoading && webSearchResults.length > 0 && (
        <div className="pb-4">
          <div className="flex items-center space-x-2 mb-4 sticky top-0 bg-white py-2 z-10">
            <FaGlobe className="text-green-600 text-lg" />
            <h4 className="text-lg font-semibold text-gray-700">
              Web Search Results ({webSearchResults.length})
            </h4>
          </div>
          
          <div className="space-y-3">
            {webSearchResults.map((result, index) => (
              <motion.a
                key={index}
                href={result.url}
                target="_blank"
                rel="noopener noreferrer"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02, y: -2 }}
                className="block p-4 bg-gradient-to-r from-gray-50 to-green-50 rounded-xl border border-gray-200 hover:border-green-300 hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h5 className="text-base font-semibold text-green-700 truncate mb-1">
                      {result.title}
                    </h5>
                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                      {result.snippet}
                    </p>
                    <p className="text-xs text-gray-400 truncate flex items-center space-x-1">
                      <FaGlobe className="text-xs" />
                      <span>{result.url}</span>
                    </p>
                  </div>
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    className="ml-3 p-2 bg-white rounded-full shadow-sm">
                    <FaExternalLinkAlt className="text-green-500 text-sm" />
                  </motion.div>
                </div>
              </motion.a>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WebSearchResults;
