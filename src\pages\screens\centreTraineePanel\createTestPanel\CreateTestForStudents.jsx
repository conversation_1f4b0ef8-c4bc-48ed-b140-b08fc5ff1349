

import { useState, useRef, useEffect, useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"
import { motion, AnimatePresence } from "framer-motion"
import { useCreateEvaluationServiceMutation, setEvaluationData } from "./evaluation.Slice"
import { useListSubjectsQuery } from "../../directorPanel/subjects/subjects.Slice"
import { useLazyGetCenterStudentsFacultyQuery } from "../overView/centerTraineedOverview.slice"
import Toastify from "../../../../components/PopUp/Toastify"
import {
  FaClipboardList,
  FaPen,
  FaUserGraduate,
  FaIdBadge,
  FaUsers,
  FaGraduationCap,
  FaBookOpen,
  FaGlobe,
  FaBuilding,
  FaFileUpload,
  FaCloudUploadAlt,
  FaCheckCircle,
  FaWeightHanging,
  FaFilePdf,
  FaPlus,
  FaRedo,
  FaExclamationTriangle,
  FaChevronDown,
  FaCog,
  FaTimes,
} from "react-icons/fa"
import EvaluationFlyingFeatherBirdLoader from "./FancyEvaluationLoader"

const CreateTestForStudents = () => {
  const dispatch = useDispatch()
  const { EvaluationData } = useSelector((state) => state.evaluation || { EvaluationData: null })

  const [createEvaluation, { isLoading: isCreatingEvaluation }] = useCreateEvaluationServiceMutation()
  const { data: subjectsData, error: subjectsError, isLoading: isLoadingSubjects } = useListSubjectsQuery()
  const [triggerGetCenterData, { data: centerData, error: centerError, isLoading: isLoadingCenterData }] =
    useLazyGetCenterStudentsFacultyQuery()

  const [res, setRes] = useState(null)
  const [evaluationFile, setEvaluationFile] = useState(null)
  const [submitAttempted, setSubmitAttempted] = useState(false)
  const fileInputRef = useRef(null)

  const [evaluationForm, setEvaluationForm] = useState({
    student_id: "",
    student_name: "",
    batch_id: "",
    exam: "",
    subject: "General",
    language: "",
    center_code: "",
    faculty_id: "",
    student_email: "",
  })

  useEffect(() => {
    const facultyId = sessionStorage.getItem("userId")
    if (facultyId) {
      setEvaluationForm((prev) => ({ ...prev, faculty_id: facultyId }))
    }
  }, [])

  useEffect(() => {
    triggerGetCenterData()
  }, [triggerGetCenterData])

  useEffect(() => {
    if (centerData?.faculty?.center_code) {
      setEvaluationForm((prev) => ({
        ...prev,
        center_code: centerData.faculty.center_code,
      }))
    }
  }, [centerData])

  const containerVariants = {
    hidden: { opacity: 0, y: 16 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.45, staggerChildren: 0.06 } },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 8 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.35 } },
  }

  const buttonVariants = {
    hover: { scale: 1.02, transition: { duration: 0.15 } },
    tap: { scale: 0.98, transition: { duration: 0.1 } },
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    if (name === "student_id") {
      const selectedStudent = centerData?.students?.find((student) => student.id === value)
      setEvaluationForm((prev) => ({
        ...prev,
        student_id: value,
        student_name: selectedStudent ? `${selectedStudent.first_name} ${selectedStudent.last_name}` : "",
        batch_id: selectedStudent?.batch_id || "",
        exam: selectedStudent?.course || "",
        student_email: selectedStudent?.student_email || "",
      }))
    } else {
      setEvaluationForm((prev) => ({ ...prev, [name]: value }))
    }
  }

  const handleFileChange = (e) => {
    const file = e.target.files?.[0]
    if (!file) return
    if (file.type !== "application/pdf") {
      setRes({ status: 400, data: { message: "Only PDF files are allowed" } })
      return
    }
    if (file.size > 10 * 1024 * 1024) {
      setRes({ status: 400, data: { message: "File size exceeds 10MB limit" } })
      return
    }
    setEvaluationFile(file)
  }

  const clearSelectedFile = () => {
    setEvaluationFile(null)
    if (fileInputRef.current) fileInputRef.current.value = ""
  }

  const resetForm = () => {
    const facultyId = sessionStorage.getItem("userId")
    setEvaluationForm({
      student_id: "",
      student_name: "",
      batch_id: "",
      exam: "",
      subject: "General",
      language: "",
      center_code: centerData?.faculty?.center_code || "",
      faculty_id: facultyId || "",
      student_email: "",
    })
    setEvaluationFile(null)
    setSubmitAttempted(false)
    if (fileInputRef.current) fileInputRef.current.value = ""
  }

  const resClear = () => setRes(null)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setSubmitAttempted(true)

    if (!evaluationFile) {
      setRes({ status: 400, data: { message: "Please upload a PDF file" } })
      return
    }
    if (!evaluationForm.batch_id) {
      setRes({ status: 400, data: { message: "Batch ID is missing" } })
      return
    }
    if (!evaluationForm.subject) {
      setRes({ status: 400, data: { message: "Please select a subject" } })
      return
    }
    if (!evaluationForm.student_id) {
      setRes({ status: 400, data: { message: "Please select a student" } })
      return
    }
    if (!evaluationForm.exam) {
      setRes({ status: 400, data: { message: "Course is missing" } })
      return
    }
    if (!evaluationForm.language) {
      setRes({ status: 400, data: { message: "Please select a language" } })
      return
    }
    if (!evaluationForm.faculty_id) {
      setRes({ status: 400, data: { message: "Faculty ID is missing in session storage" } })
      return
    }

    const formData = new FormData()
    formData.append("file", evaluationFile)
    Object.keys(evaluationForm).forEach((key) => {
      formData.append(key, evaluationForm[key])
    })

    try {
      const response = await createEvaluation(formData).unwrap()
      const evaluationData = response.data || response
      dispatch(setEvaluationData(evaluationData))
      setRes({ status: 200, data: { message: "Evaluation created successfully!" } })
      resetForm()
    } catch (error) {
      setRes({
        status: error.status || 500,
        data: { message: error.data?.message || "Failed to create evaluation" },
      })
    }
  }

  // Derived UI state for stepper
  const isStep1Done = !!evaluationForm.student_id
  const isStep2Done = !!(evaluationForm.subject && evaluationForm.language)
  const isStep3Done = !!evaluationFile

  // Simple validators for inline error borders after submitAttempted
  const showError = useMemo(() => {
    if (!submitAttempted) return {}
    return {
      student_id: !evaluationForm.student_id,
      subject: !evaluationForm.subject,
      language: !evaluationForm.language,
      file: !evaluationFile,
    }
  }, [submitAttempted, evaluationForm.student_id, evaluationForm.subject, evaluationForm.language, evaluationFile])

  return (
    <div className="min-h-screen p-6 bg-white font-poppins relative">
      <Toastify res={res} resClear={resClear} />

      <div className="max-w-6xl mx-auto relative z-10">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.45 }}
          className="text-center mb-10"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-3 text-[#F59E0B]">
            <FaClipboardList className="inline-block mr-3" />
            Test Evaluation
          </h1>
          <p className="text-gray-600 text-base md:text-lg leading-relaxed">
            Create and manage student evaluations with precision and ease
          </p>
        </motion.header>

        {/* Stepper */}
        <motion.div initial={{ opacity: 0, y: 12 }} animate={{ opacity: 1, y: 0 }} className="mb-6">
          <div className="bg-white border border-[#F59E0B]/20 rounded-2xl p-4">
            <ol className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { label: "Select Student", done: isStep1Done },
                { label: "Choose Details", done: isStep2Done },
                { label: "Upload & Submit", done: isStep3Done },
              ].map((step, idx) => (
                <li key={idx} className="flex items-center gap-3">
                  <div
                    className={`h-6 w-6 rounded-full flex items-center justify-center border-2 ${
                      step.done ? "bg-[#F59E0B] border-[#F59E0B] text-white" : "border-[#F59E0B] text-[#F59E0B]"
                    }`}
                    aria-hidden="true"
                  >
                    {step.done ? (
                      <FaCheckCircle className="text-white text-xs" />
                    ) : (
                      <span className="text-[10px] font-bold">{idx + 1}</span>
                    )}
                  </div>
                  <span className={`text-sm ${step.done ? "text-gray-800 font-semibold" : "text-gray-600"}`}>
                    {step.label}
                  </span>
                </li>
              ))}
            </ol>
          </div>
        </motion.div>

        {/* Info banner */}
        <div className="mb-6">
          <div className="flex items-start gap-3 bg-[#F59E0B]/10 border border-[#F59E0B]/20 text-gray-800 rounded-xl p-3">
            <FaExclamationTriangle className="mt-0.5 text-[#F59E0B]" />
            <p className="text-sm">All fields are required. PDF only, up to 10MB.</p>
          </div>
        </div>

        {/* Main Card (no gradients) */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="bg-white rounded-3xl shadow-xl border border-[#F59E0B]/20 overflow-hidden"
        >
          <div className="p-6 md:p-10">
            {/* Card header */}
            <motion.div
              variants={itemVariants}
              className="flex items-center gap-4 mb-8 pb-5 border-b border-[#F59E0B]/20"
            >
              <div className="w-14 h-14 rounded-xl bg-[#F59E0B] flex items-center justify-center shadow-md">
                <FaPen className="text-white text-xl" />
              </div>
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-800">Create New Evaluation</h2>
                <p className="text-gray-600 text-sm md:text-base">
                  Upload student answer sheets and generate detailed assessments
                </p>
              </div>
            </motion.div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Sections */}
              <section aria-labelledby="student-details">
                <h3 id="student-details" className="text-lg font-semibold text-gray-800 mb-4">
                  Student Details
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Select Student */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaUserGraduate className="text-[#F59E0B] mr-2" />
                      Select Student
                    </label>
                    <div className="relative">
                      <select
                        name="student_id"
                        value={evaluationForm.student_id}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-xl text-gray-700 bg-white shadow-sm focus:ring-4 focus:ring-[#F59E0B]/20 focus:border-[#F59E0B] transition ${
                          showError.student_id ? "border-red-400" : "border-[#F59E0B]/40"
                        }`}
                        disabled={isLoadingCenterData}
                        aria-invalid={showError.student_id ? "true" : "false"}
                        aria-describedby="student_id_help"
                      >
                        <option value="">Choose a student...</option>
                        {centerData?.students?.length > 0 ? (
                          centerData.students.map((student) => (
                            <option key={student.id} value={student.id}>
                              {student.first_name} {student.last_name} ({student.student_email})
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {isLoadingCenterData ? "Loading students..." : "No students available"}
                          </option>
                        )}
                      </select>
                      <FaChevronDown className="absolute right-4 top-1/2 -translate-y-1/2 text-[#F59E0B] pointer-events-none" />
                    </div>
                    <p id="student_id_help" className="text-xs text-gray-500">
                      Selecting a student auto-fills name, batch, course and email.
                    </p>
                    {centerError && (
                      <div className="flex items-center text-red-600 bg-red-50 p-2 rounded-lg border border-red-200 text-xs">
                        <FaExclamationTriangle className="mr-2" />
                        Error loading students: {centerError.data?.message || "Please try again"}
                      </div>
                    )}
                  </motion.div>

                  {/* Student Name */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaIdBadge className="text-[#F59E0B] mr-2" />
                      Student Name
                    </label>
                    <input
                      type="text"
                      name="student_name"
                      value={evaluationForm.student_name}
                      readOnly
                      placeholder="Auto-filled when student is selected"
                      className="w-full px-4 py-3 border border-[#F59E0B]/40 rounded-xl text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>

                  {/* Batch */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaUsers className="text-[#F59E0B] mr-2" />
                      Batch
                    </label>
                    <input
                      type="text"
                      name="batch_id"
                      value={evaluationForm.batch_id}
                      readOnly
                      placeholder="Auto-filled when student is selected"
                      className="w-full px-4 py-3 border border-[#F59E0B]/40 rounded-xl text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>

                  {/* Course */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaGraduationCap className="text-[#F59E0B] mr-2" />
                      Course
                    </label>
                    <input
                      type="text"
                      name="exam"
                      value={evaluationForm.exam}
                      readOnly
                      placeholder="Auto-filled when student is selected"
                      className="w-full px-4 py-3 border border-[#F59E0B]/40 rounded-xl text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>
                </div>
              </section>

              <section aria-labelledby="evaluation-details" className="pt-2">
                <h3 id="evaluation-details" className="text-lg font-semibold text-gray-800 mb-4">
                  Evaluation Details
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Subject */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaBookOpen className="text-[#F59E0B] mr-2" />
                      Select Subject
                    </label>
                    <div className="relative">
                      <select
                        name="subject"
                        value={evaluationForm.subject}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-xl text-gray-700 bg-white shadow-sm focus:ring-4 focus:ring-[#F59E0B]/20 focus:border-[#F59E0B] transition ${
                          showError.subject ? "border-red-400" : "border-[#F59E0B]/40"
                        }`}
                        disabled={isLoadingSubjects}
                        aria-invalid={showError.subject ? "true" : "false"}
                        aria-describedby="subject_help"
                      >
                        <option value="">Choose a subject...</option>
                        {subjectsData && subjectsData.length > 0 ? (
                          subjectsData.map((subject) => (
                            <option key={subject.subject_id} value={subject.subject_name}>
                              {subject.subject_name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {isLoadingSubjects ? "Loading subjects..." : "No subjects available"}
                          </option>
                        )}
                      </select>
                      <FaChevronDown className="absolute right-4 top-1/2 -translate-y-1/2 text-[#F59E0B] pointer-events-none" />
                    </div>
                    <p id="subject_help" className="text-xs text-gray-500">
                      Pick the subject for this evaluation.
                    </p>
                    {subjectsError && (
                      <div className="flex items-center text-red-600 bg-red-50 p-2 rounded-lg border border-red-200 text-xs">
                        <FaExclamationTriangle className="mr-2" />
                        Error loading subjects: {subjectsError.data?.message || "Please try again"}
                      </div>
                    )}
                  </motion.div>

                  {/* Language */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaGlobe className="text-[#F59E0B] mr-2" />
                      Select Language
                    </label>
                    <div className="relative">
                      <select
                        name="language"
                        value={evaluationForm.language}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 border rounded-xl text-gray-700 bg-white shadow-sm focus:ring-4 focus:ring-[#F59E0B]/20 focus:border-[#F59E0B] transition ${
                          showError.language ? "border-red-400" : "border-[#F59E0B]/40"
                        }`}
                        aria-invalid={showError.language ? "true" : "false"}
                      >
                        <option value="">Choose language...</option>
                        <option value="tanglish">Tamil + English</option>
                        <option value="english">English</option>
                        <option value="kanglish">Kannada + English</option>
                      </select>
                      <FaChevronDown className="absolute right-4 top-1/2 -translate-y-1/2 text-[#F59E0B] pointer-events-none" />
                    </div>
                  </motion.div>

                  {/* Center Code */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaBuilding className="text-[#F59E0B] mr-2" />
                      Center Code
                    </label>
                    <input
                      type="text"
                      name="center_code"
                      value={evaluationForm.center_code}
                      readOnly
                      placeholder="Auto-filled from center data"
                      className="w-full px-4 py-3 border border-[#F59E0B]/40 rounded-xl text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>

                  {/* Student Email (fixed name) */}
                  <motion.div variants={itemVariants} className="space-y-2">
                    <label className="flex items-center text-sm font-semibold text-gray-700">
                      <FaBuilding className="text-[#F59E0B] mr-2" />
                      Student Email
                    </label>
                    <input
                      type="text"
                      name="student_email"
                      value={evaluationForm.student_email}
                      readOnly
                      placeholder="Auto-filled when student is selected"
                      className="w-full px-4 py-3 border border-[#F59E0B]/40 rounded-xl text-gray-600 bg-gray-50 cursor-not-allowed shadow-sm"
                    />
                  </motion.div>
                </div>
              </section>

              {/* Upload */}
              <section aria-labelledby="upload-section">
                <h3 id="upload-section" className="text-lg font-semibold text-gray-800 mb-4">
                  Upload Answer Sheet
                </h3>

                <motion.div variants={itemVariants} className="space-y-3">
                  <label className="flex items-center text-sm font-semibold text-gray-700">
                    <FaFileUpload className="text-[#F59E0B] mr-2" />
                    Upload Answer Sheet (PDF)
                  </label>

                  <div
                    className={`relative group border-2 border-dashed rounded-2xl p-8 text-center transition ${
                      showError.file ? "border-red-400" : "border-[#F59E0B]/50"
                    } hover:border-[#F59E0B] hover:bg-[#F59E0B]/5`}
                    role="button"
                    tabIndex={0}
                    aria-label="Upload PDF"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        fileInputRef.current?.click()
                      }
                    }}
                  >
                    <input
                      type="file"
                      name="file"
                      accept="application/pdf"
                      onChange={handleFileChange}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      ref={fileInputRef}
                      aria-required="true"
                    />
                    <FaCloudUploadAlt className="text-4xl text-[#F59E0B] mb-3 mx-auto" />
                    <h4 className="text-base font-semibold text-gray-700">Drop your PDF here</h4>
                    <p className="text-gray-500 text-sm">or click to browse files</p>
                    <div className="mt-3 flex items-center justify-center gap-4 text-xs text-gray-500">
                      <span className="flex items-center">
                        <FaCheckCircle className="text-green-500 mr-1" />
                        PDF Format
                      </span>
                      <span className="flex items-center">
                        <FaWeightHanging className="text-[#F59E0B] mr-1" />
                        Max 10MB
                      </span>
                    </div>
                  </div>

                  {evaluationFile && (
                    <div className="bg-green-50 border border-green-200 rounded-2xl p-4 flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        <FaFilePdf className="text-green-600 text-xl mt-1" />
                        <div>
                          <p className="text-gray-800 font-semibold">{evaluationFile.name}</p>
                          <p className="text-green-700 text-sm">
                            Size: {(evaluationFile.size / (1024 * 1024)).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={clearSelectedFile}
                        className="flex items-center gap-2 px-3 py-2 rounded-lg border border-red-200 text-red-600 hover:bg-red-50 text-sm"
                        aria-label="Remove selected file"
                      >
                        <FaTimes />
                        Remove
                      </button>
                    </div>
                  )}
                </motion.div>
              </section>

              {/* Review Summary */}
              <section aria-labelledby="review-summary" className="pt-2">
                <h3 id="review-summary" className="text-lg font-semibold text-gray-800 mb-4">
                  Review Summary
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    { label: "Student", value: evaluationForm.student_name || "—" },
                    { label: "Email", value: evaluationForm.student_email || "—" },
                    { label: "Batch", value: evaluationForm.batch_id || "—" },
                    { label: "Course", value: evaluationForm.exam || "—" },
                    { label: "Subject", value: evaluationForm.subject || "—" },
                    { label: "Language", value: evaluationForm.language || "—" },
                  ].map((item, idx) => (
                    <div key={idx} className="rounded-xl border border-[#F59E0B]/20 bg-white p-4">
                      <p className="text-xs text-gray-500">{item.label}</p>
                      <p className="text-sm font-semibold text-gray-800">{item.value}</p>
                    </div>
                  ))}
                </div>
              </section>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <motion.button
                  type="submit"
                  disabled={isCreatingEvaluation || isLoadingSubjects || isLoadingCenterData}
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                  className="bg-[#F59E0B] text-white px-6 py-3 rounded-xl font-semibold shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed border border-[#F59E0B]"
                >
                  <span className="flex items-center justify-center">
                    {isCreatingEvaluation ? (
                      <>
                        <motion.span
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                        />
                        Processing...
                      </>
                    ) : (
                      <>
                        <FaPlus className="mr-2" />
                        Create Evaluation
                      </>
                    )}
                  </span>
                </motion.button>

                <motion.button
                  type="button"
                  onClick={resetForm}
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                  className="px-6 py-3 border border-[#F59E0B]/50 text-[#F59E0B] rounded-xl font-semibold hover:border-[#F59E0B] hover:bg-[#F59E0B]/5"
                >
                  <span className="flex items-center justify-center">
                    <FaRedo className="mr-2" />
                    Reset Form
                  </span>
                </motion.button>
              </div>
            </form>
          </div>
        </motion.div>
      </div>

      {/* Loading Overlay */}
      <AnimatePresence>
       <EvaluationFlyingFeatherBirdLoader open={isCreatingEvaluation} />
      </AnimatePresence>
    </div>
  )
}

export default CreateTestForStudents
