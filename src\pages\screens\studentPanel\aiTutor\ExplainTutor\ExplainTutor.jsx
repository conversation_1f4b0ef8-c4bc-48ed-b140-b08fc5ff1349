import { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  Pause,
  SkipB<PERSON>,
  <PERSON><PERSON><PERSON>or<PERSON>,
  ChevronLeft,
  ChevronRight,
  BookOpen,
  Volume2,
  Eye,
  EyeOff,
  RotateCcw,
  Monitor,
  Search,
} from "lucide-react";

import BreakTime from "../Timer/BreakTime";

function ExplainTutor({ processId, subject, onClose }) {
  const [language, setLanguage] = useState("english");
  const [chemicalData, setChemicalData] = useState(null);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);

  const [currentProblem, setCurrentProblem] = useState(0);
  const [visibleSteps, setVisibleSteps] = useState(1);
  const [showNarration, setShowNarration] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [autoProgression, setAutoProgression] = useState(false);
  const [showScreensaver, setShowScreensaver] = useState(false);
  const [hasReadQuestion, setHasReadQuestion] = useState(false);
  const audioRef = useRef(null);
  const stepRef = useRef(null);

  const fetchData = async () => {
    if (!processId.trim()) {
      setDataError("Please enter a process selector ID");
      return;
    }
    console.log("the subject im selecting", subject);
    setIsLoadingData(true);
    setDataError(null);

    const url =
      subject === "physics"
        ? `${import.meta.env.VITE_BASE_URL}/physics/solved_problems`
        : subject === "maths"
        ? `${import.meta.env.VITE_BASE_URL}/mathapi/solve_sums`
        : subject === "chemistry"
        ? `${import.meta.env.VITE_BASE_URL}/chemapi/solve_sums`
        : `${import.meta.env.VITE_BASE_URL}/physics/solved_problems`;

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          process_selector_id: processId.trim(),
          language: language,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setChemicalData(data);
      setCurrentProblem(0);
      setVisibleSteps(1);
      setShowNarration(true);
      setAutoProgression(false);
      setIsPlaying(false);
      setHasReadQuestion(false);
    } catch (error) {
      console.error("Data fetch error:", error);
      setDataError(error.message);
    } finally {
      setIsLoadingData(false);
    }
  };

  // Load initial data
  useEffect(() => {
    fetchData();
  }, [processId, language]);

  const currentQuestion = chemicalData?.questions?.[currentProblem];
  const totalProblems = chemicalData?.questions?.length || 0;
  const totalSteps = currentQuestion?.steps?.length || 0;

  useEffect(() => {
    if (!autoProgression && audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  }, [currentProblem, autoProgression]);

  useEffect(() => {
    if (stepRef.current) {
      stepRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [visibleSteps]);

  // Reset hasReadQuestion when problem changes
  useEffect(() => {
    setHasReadQuestion(false);
  }, [currentProblem]);

  const playTTS = async (text) => {
    if (!text) return;

    setIsLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_BASE_URL}/tts`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: text,
        }),
      });

      if (!response.ok) throw new Error("TTS request failed");
      const data = await response.json();

      if (data.audio && audioRef.current) {
        const audioData = `data:${data.mimetype};base64,${data.audio}`;
        audioRef.current.src = audioData;
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error("TTS Error:", error);
      if (autoProgression) {
        setTimeout(handleAutoNext, 1000);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const playNarration = async (stepIndex) => {
    // If we haven't read the question yet, read it first
    if (!hasReadQuestion && currentQuestion?.problem) {
      await playTTS(currentQuestion.problem);
      setHasReadQuestion(true);
      return;
    }

    // Read the step narration
    const narrationText = currentQuestion?.narration_content?.[stepIndex];
    if (narrationText) {
      await playTTS(narrationText);
    }
  };

  const handleAutoNext = () => {
    if (visibleSteps < totalSteps) {
      setVisibleSteps((prev) => prev + 1);
    } else if (currentProblem < totalProblems - 1) {
      setCurrentProblem((prev) => prev + 1);
      setVisibleSteps(1);
    } else {
      setAutoProgression(false);
      setIsPlaying(false);
    }
  };

  const startAutoProgression = () => {
    setAutoProgression(true);
    setIsPlaying(true);
    playNarration(visibleSteps - 1);
  };

  const stopAutoProgression = () => {
    setAutoProgression(false);
    setIsPlaying(false);
    if (audioRef.current) {
      audioRef.current.pause();
    }
  };

  const handleAudioEnded = () => {
    if (autoProgression) {
      // If we just read the question, now start reading steps
      if (!hasReadQuestion) {
        setHasReadQuestion(true);
        setTimeout(() => playNarration(visibleSteps - 1), 500);
      } else {
        setTimeout(handleAutoNext, 500);
      }
    } else {
      setIsPlaying(false);
    }
  };

  useEffect(() => {
    if (autoProgression && !isLoading && hasReadQuestion) {
      const timer = setTimeout(() => {
        playNarration(visibleSteps - 1);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [visibleSteps, currentProblem, autoProgression, hasReadQuestion]);

  const handleNextStep = () => {
    if (autoProgression) return;

    if (visibleSteps < totalSteps) {
      setVisibleSteps(visibleSteps + 1);
    } else if (currentProblem < totalProblems - 1) {
      setCurrentProblem(currentProblem + 1);
      setVisibleSteps(1);
    }
  };

  const handlePrevStep = () => {
    if (autoProgression) return;

    if (visibleSteps > 1) {
      setVisibleSteps(visibleSteps - 1);
    } else if (currentProblem > 0) {
      setCurrentProblem(currentProblem - 1);
      setVisibleSteps(chemicalData.questions[currentProblem - 1].steps.length);
    }
  };

  const handleNextProblem = () => {
    if (autoProgression) return;

    if (currentProblem < totalProblems - 1) {
      setCurrentProblem(currentProblem + 1);
      setVisibleSteps(1);
    }
  };

  const handlePrevProblem = () => {
    if (autoProgression) return;

    if (currentProblem > 0) {
      setCurrentProblem(currentProblem - 1);
      setVisibleSteps(1);
    }
  };

  const isFirstStep = currentProblem === 0 && visibleSteps === 1;
  const isLastStep = currentProblem === totalProblems - 1 && visibleSteps === totalSteps;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <audio ref={audioRef} onEnded={handleAudioEnded} style={{ display: "none" }} />
      <BreakTime showScreensaver={showScreensaver} setShowScreensaver={setShowScreensaver} />

      {/* Header with Topic and Back Button */}
      <div className="bg-white/80 backdrop-blur-lg border-b border-slate-200/50 shadow-lg sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-br from-blue-600 to-indigo-600 p-4 rounded-2xl shadow-lg">
                <BookOpen className="h-8 w-8 text-white" />
              </div>
              <div>
                <p className="text-slate-600 mt-1 text-lg">{chemicalData?.greeting || "Welcome!"}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-3 rounded-full text-white text-lg font-semibold shadow-lg">
                Problem {currentProblem + 1} of {totalProblems}
              </div>
              <button
                onClick={onClose}
                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-sm"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Back
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Control Panel */}
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="bg-white/90 backdrop-blur-lg rounded-xl border border-slate-200/50 shadow-lg mb-6">
          <div className="p-4">
            <div className="flex flex-wrap items-center justify-center gap-2">
              {/* Audio Control */}
              {!isPlaying ? (
                <button
                  onClick={startAutoProgression}
                  disabled={isLoading || isLastStep}
                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm"
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  {isLoading ? "Loading..." : "Start"}
                </button>
              ) : (
                <button
                  onClick={stopAutoProgression}
                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-sm"
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </button>
              )}

              {/* Problem Navigation */}
              <button
                onClick={handlePrevProblem}
                disabled={currentProblem === 0 || autoProgression}
                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm"
              >
                <SkipBack className="h-4 w-4 mr-1" />
                Prev Problem
              </button>

              <button
                onClick={handlePrevStep}
                disabled={isFirstStep || autoProgression}
                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Prev Step
              </button>

              <button
                onClick={handleNextStep}
                disabled={isLastStep || autoProgression}
                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm"
              >
                {visibleSteps === totalSteps && currentProblem < totalProblems - 1
                  ? "Next Problem"
                  : "Next Step"}
                <ChevronRight className="h-4 w-4 ml-1" />
              </button>

              <button
                onClick={handleNextProblem}
                disabled={currentProblem === totalProblems - 1 || autoProgression}
                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-teal-600 to-cyan-600 hover:from-teal-700 hover:to-cyan-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm"
              >
                Next Problem
                <SkipForward className="h-4 w-4 ml-1" />
              </button>

              <button
                onClick={() => setShowScreensaver(true)}
                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-sm"
              >
                <Monitor className="h-4 w-4 mr-1" />
                Break
              </button>

              {/* Show/Hide Narration Toggle */}
              {!showNarration ? (
                <button
                  onClick={() => setShowNarration(true)}
                  className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-sm"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Show Details
                </button>
              ) : (
                <button
                  onClick={() => setShowNarration(false)}
                  className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-gray-500 to-slate-600 hover:from-gray-600 hover:to-slate-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-sm"
                >
                  <EyeOff className="h-4 w-4 mr-1" />
                  Hide Details
                </button>
              )}

              {/* Status indicator */}
              {autoProgression && (
                <div className="flex items-center gap-2 bg-blue-100 px-3 py-2 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium text-blue-700">Auto-playing</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Loading State */}
        {isLoadingData && (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
              <div className="text-xl text-slate-600 font-medium">Loading data...</div>
            </div>
          </div>
        )}

        {/* Error State */}
        {dataError && (
          <div className="flex items-center justify-center py-20">
            <div className="bg-red-50 border border-red-200 rounded-2xl p-8 text-center max-w-md">
              <div className="text-red-600 text-lg font-medium">Error: {dataError}</div>
            </div>
          </div>
        )}

        {/* Main Content - Centered */}
        {!isLoadingData && !dataError && chemicalData && (
          <div className="flex justify-center">
            <div className="w-full max-w-4xl">
              <div className="bg-white/95 backdrop-blur-lg rounded-2xl border border-slate-200/50 shadow-2xl overflow-hidden">
                {/* Problem Header */}
                <div className="bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 text-white px-8 py-6">
                  <h2 className="text-2xl font-bold leading-relaxed">
                    {currentQuestion?.problem || "Loading..."}
                  </h2>
                  <div className="mt-2 flex items-center gap-4 text-slate-300">
                    <span className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      Step {visibleSteps} of {totalSteps}
                    </span>
                  </div>
                </div>

                {/* Steps Content */}
                <div className="p-8">
                  <div className="space-y-8">
                    {currentQuestion?.steps?.slice(0, visibleSteps).map((step, index) => (
                      <div
                        key={index}
                        ref={index === visibleSteps - 1 ? stepRef : null}
                        className={`relative rounded-2xl p-8 transition-all duration-500 transform ${
                          index === visibleSteps - 1
                            ? isPlaying && autoProgression
                              ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-400 shadow-lg scale-102"
                              : "bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-300 shadow-md"
                            : "bg-gradient-to-r from-slate-50 to-gray-50 border border-slate-200"
                        }`}
                      >
                        {/* Step indicator */}
                        <div className="absolute -left-4 top-8">
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg ${
                              index === visibleSteps - 1
                                ? "bg-gradient-to-br from-blue-500 to-indigo-600"
                                : "bg-gradient-to-br from-slate-400 to-slate-500"
                            }`}
                          >
                            {index + 1}
                          </div>
                        </div>

                        <div className="ml-8">
                          <div className="flex items-start justify-between mb-4">
                            <h3
                              className={`text-xl font-bold ${
                                index === visibleSteps - 1 ? "text-blue-800" : "text-slate-700"
                              }`}
                            >
                              Step {index + 1}
                            </h3>
                            {index === visibleSteps - 1 && (
                              <span className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg animate-pulse">
                                Current
                              </span>
                            )}
                          </div>

                          <p
                            className={`text-lg leading-relaxed font-mono bg-white/70 rounded-xl p-6 border ${
                              index === visibleSteps - 1
                                ? "text-slate-800 border-blue-200"
                                : "text-slate-600 border-slate-200"
                            }`}
                          >
                            {step}
                          </p>

                          {/* Narration Content */}
                          {index === visibleSteps - 1 &&
                            showNarration &&
                            currentQuestion.narration_content?.[index] && (
                              <div className="mt-6 bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-200 rounded-2xl p-6 shadow-lg">
                                <div className="flex justify-between items-start mb-4">
                                  <h4 className="text-lg font-bold text-emerald-800 flex items-center gap-3">
                                    <div className="bg-gradient-to-br from-emerald-500 to-green-600 p-2 rounded-lg">
                                      <BookOpen className="h-5 w-5 text-white" />
                                    </div>
                                    Detailed Explanation
                                  </h4>
                                </div>
                                <p className="text-emerald-800 leading-relaxed text-lg bg-white/60 rounded-xl p-4">
                                  {currentQuestion.narration_content[index]}
                                </p>
                                {autoProgression && isPlaying && (
                                  <div className="mt-4 flex items-center gap-3 text-emerald-700">
                                    <div className="flex gap-1">
                                      <div
                                        className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce"
                                      ></div>
                                      <div
                                        className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce"
                                        style={{ animationDelay: "0.1s" }}
                                      ></div>
                                      <div
                                        className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce"
                                        style={{ animationDelay: "0.2s" }}
                                      ></div>
                                    </div>
                                    <span className="text-sm font-medium italic">
                                      Playing narration...
                                    </span>
                                  </div>
                                )}
                              </div>
                            )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Completion Message */}
              {isLastStep && (
                <div className="mt-8 bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-2xl shadow-2xl overflow-hidden">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-6">
                    <div className="text-center text-white">
                      <div className="text-6xl mb-4 animate-bounce">🎉</div>
                      <h3 className="text-3xl font-bold mb-2">Excellent Work!</h3>
                    </div>
                  </div>
                  <div className="p-8 text-center">
                    <p className="text-green-700 text-xl mb-8 leading-relaxed max-w-2xl mx-auto">
                      You have successfully completed all problems in this lesson. Your understanding
                      of the concepts has been demonstrated through each step.
                    </p>
                    <button
                      onClick={() => {
                        setCurrentProblem(0);
                        setVisibleSteps(1);
                        setShowNarration(true);
                        setAutoProgression(false);
                        setIsPlaying(false);
                        setShowScreensaver(false);
                        setHasReadQuestion(false);
                        if (audioRef.current) {
                          audioRef.current.pause();
                        }
                      }}
                      className="inline-flex items-center px-10 py-4 bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                    >
                      <RotateCcw className="h-6 w-6 mr-3" />
                      Start Over
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ExplainTutor;