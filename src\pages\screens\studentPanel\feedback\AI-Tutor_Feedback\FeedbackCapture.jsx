"use client";

import { useState, useRef } from "react";

export default function FeedbackCapture({ processSelectorId }) {
  const [isRunning, setIsRunning] = useState(false);
  const [hasStarted, setHasStarted] = useState(false); // new flag to track button click

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);

  const updateStatus = (message, type = "info") => {
    console.log(`[${type.toUpperCase()}] ${message}`);
  };

  const cleanup = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      if (videoRef.current) {
        videoRef.current.srcObject = null;
        videoRef.current.style.display = "none";
      }
      streamRef.current = null;
    }
    // only stop camera, do not reset hasStarted
    setIsRunning(false);
  };

  const handleCaptureAndSend = async () => {
    const centerCode = sessionStorage.getItem("centercode")?.trim();

    if (!processSelectorId || !centerCode) {
      updateStatus("Please provide all required IDs.", "error");
      cleanup();
      return;
    }

    const apiUrl = `${import.meta.env.VITE_BASE_URL}/api/aitutor_feedback?process_selector_id=${processSelectorId}&center_code=${centerCode}`;

    const frames = [];
    const duration = 10; // 10 seconds
    const frameCount = 10;
    const captureInterval = (duration * 1000) / frameCount;

    for (let i = 1; i <= frameCount; i++) {
      updateStatus(`Capturing frame ${i} of ${frameCount}...`, "info");
      const canvas = canvasRef.current;
      const video = videoRef.current;
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      canvas.getContext("2d").drawImage(video, 0, 0, canvas.width, canvas.height);
      frames.push(canvas.toDataURL("image/jpeg", 0.8));
      await new Promise((resolve) => setTimeout(resolve, captureInterval));
    }

    updateStatus("All frames captured. Uploading to server...", "info");

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ frames: frames }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || `Server responded with status ${response.status}`);
      }

      updateStatus("Processing complete!", "success");
      console.log("✅ Final Server Response:", data);

      const studentList = data.attended_students || [];
      console.log("🎓 Recognized Students:", studentList);
    } catch (error) {
      updateStatus(`Error: ${error.message}`, "error");
      console.error("❌ Fetch Error:", error);
    } finally {
      cleanup();
    }
  };

  const startSession = async () => {
    if (isRunning || hasStarted) return;

    setIsRunning(true);
    setHasStarted(true); // permanently hide the button after first click
    console.clear();
    console.log("🚀 Starting process...");

    try {
      updateStatus("Requesting camera access...", "info");
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
      videoRef.current.srcObject = stream;
      videoRef.current.style.display = "block";
      streamRef.current = stream;
      await new Promise((resolve) => {
        videoRef.current.onloadedmetadata = resolve;
      });
    } catch (err) {
      updateStatus(`Camera Error: ${err.message}. Please grant permission.`, "error");
      cleanup();
      return;
    }

    await handleCaptureAndSend();
  };

  return (
    <div className="flex justify-center items-start bg-gray-100 p-6">
      {/* Start Button - disappears permanently after click */}
      {!hasStarted && (
        <button
          id="start-session-btn"
          onClick={startSession}
          className="w-full py-3 text-lg font-bold text-white rounded-md transition bg-blue-600 hover:bg-blue-700"
        >
          Start Class
        </button>
      )}

      {/* Hidden Video + Canvas (used internally) */}
      <video ref={videoRef} autoPlay muted playsInline className="hidden" />
      <canvas ref={canvasRef} className="hidden"></canvas>
    </div>
  );
}
