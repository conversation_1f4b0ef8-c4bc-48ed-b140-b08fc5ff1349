


//below is the oneside working code 


import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { 
  Room, 
  Track,
  VideoPresets,
  createLocalTracks,
  RoomEvent
} from 'livekit-client';
import {
  Mic, MicOff, Video, VideoOff, Monitor, PhoneOff, Users, VolumeX
} from 'lucide-react';

const IncomingCall = ({ mentorId, incomingCall: activeCall, onCallStatusChange, showVideoCall }) => {
  const [callState, setCallState] = useState('idle'); // idle, connecting, connected, ended, error
  const [room, setRoom] = useState(null);
  const [participants, setParticipants] = useState([]);
  const [localTracks, setLocalTracks] = useState([]);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [error, setError] = useState('');
  const [mediaError, setMediaError] = useState('');
  const [remoteParticipants, setRemoteParticipants] = useState([]);
  const [localVideoTrack, setLocalVideoTrack] = useState(null);
  const [localAudioTrack, setLocalAudioTrack] = useState(null);
  const [isRemoteAudioMuted, setIsRemoteAudioMuted] = useState(false);
  const [remoteVideoTrack, setRemoteVideoTrack] = useState(null);
  const [remoteAudioTrack, setRemoteAudioTrack] = useState(null);
  const [remoteScreenTrack, setRemoteScreenTrack] = useState(null);
  const [remoteScreenAudioTrack, setRemoteScreenAudioTrack] = useState(null);

  const localVideoRef = useRef(null);
  const remoteVideoRef = useRef(null);
  const screenShareRef = useRef(null);
  const remoteAudioRef = useRef(null);
  const screenAudioRef = useRef(null);
  const roomRef = useRef(null);
  const callTimerRef = useRef(null);
  const tracksInitialized = useRef(false);

  const API_BASE_URL = 'https://testing.sasthra.in';
  const LIVEKIT_URL = 'wss://livekit.sasthra.in';

  const startCallTimer = () => {
    const startTime = Date.now();
    callTimerRef.current = setInterval(() => {
      setCallDuration(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);
  };

  const stopCallTimer = () => {
    if (callTimerRef.current) clearInterval(callTimerRef.current);
    callTimerRef.current = null;
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const cleanup = () => {
    console.log('Cleaning up resources...');
    if (roomRef.current) {
      roomRef.current.disconnect();
      roomRef.current = null;
    }
    setRoom(null);
    localTracks.forEach(track => track.stop());
    setLocalTracks([]);
    setLocalVideoTrack(null);
    setLocalAudioTrack(null);
    setParticipants([]);
    setRemoteParticipants([]);
    setIsScreenSharing(false);
    setIsMuted(false);
    setIsVideoOff(false);
    setError('');
    setMediaError('');
    setRemoteVideoTrack(null);
    setRemoteAudioTrack(null);
    setRemoteScreenTrack(null);
    setRemoteScreenAudioTrack(null);
    tracksInitialized.current = false;
    stopCallTimer();
  };
  
  const endCall = async () => {
    try {
      if (activeCall?.room_name) {
        await fetch(`${API_BASE_URL}/call/end`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ room_name: activeCall.room_name })
        });
      }
    } catch (error) {
      console.error('Error sending end call request:', error);
    }
    cleanup();
    setCallState('ended');
    onCallStatusChange?.('ended');
  };

  const initializeLocalTracks = async () => {
    if (tracksInitialized.current) return localTracks;
    try {
      setMediaError('');
      const tracks = await createLocalTracks({
        audio: true,
        video: { resolution: VideoPresets.h720.resolution },
      });
      
      const videoTrack = tracks.find(t => t.kind === Track.Kind.Video);
      const audioTrack = tracks.find(t => t.kind === Track.Kind.Audio);
      
      setLocalTracks(tracks);
      setLocalVideoTrack(videoTrack);
      setLocalAudioTrack(audioTrack);

      if (videoTrack && localVideoRef.current) {
        videoTrack.attach(localVideoRef.current);
      }
      tracksInitialized.current = true;
      return tracks;
    } catch (err) {
      console.error('Error initializing local tracks:', err);
      let errorMessage = 'Failed to access camera/microphone. ';
      if (err.name === 'NotAllowedError') errorMessage += 'Please grant permissions in your browser.';
      else if (err.name === 'NotFoundError') errorMessage += 'No camera or microphone found.';
      else errorMessage += err.message;
      setMediaError(errorMessage);
      setCallState('error');
      onCallStatusChange?.('error');
      throw err;
    }
  };

  const publishLocalTracks = async (roomInstance) => {
    try {
      console.log('Publishing local tracks...');
      let tracksToPublish = localTracks;
      if (tracksToPublish.length === 0) {
        console.log('No existing tracks, creating new ones...');
        tracksToPublish = await createLocalTracks({
          audio: true,
          video: { resolution: VideoPresets.h720.resolution },
        });
        setLocalTracks(tracksToPublish);
        const videoTrack = tracksToPublish.find(t => t.kind === Track.Kind.Video);
        const audioTrack = tracksToPublish.find(t => t.kind === Track.Kind.Audio);
        setLocalVideoTrack(videoTrack);
        setLocalAudioTrack(audioTrack);
        if (videoTrack && localVideoRef.current) {
          videoTrack.attach(localVideoRef.current);
        }
      }
      for (const track of tracksToPublish) {
        console.log(`Publishing ${track.kind} track...`);
        const publication = await roomInstance.localParticipant.publishTrack(track);
        console.log(`Successfully published ${track.kind} track:`, publication.trackSid);
      }
      console.log('Local tracks publishing completed');
    } catch (error) {
      console.error('Error in publishLocalTracks:', error);
    }
  };
  
  // Connect to the room when activeCall prop is provided
  useEffect(() => {
    if (!activeCall || !activeCall.token) {
      if (callState !== 'idle') {
        cleanup();
        setCallState('idle');
      }
      return;
    }

    const joinRoom = async (token) => {
      setCallState('connecting');
      try {
        await initializeLocalTracks();
        
        const room = new Room({
          adaptiveStream: true,
          dynacast: true,
          autoSubscribe: true,
        });
        roomRef.current = room;
        
        room
          .on(RoomEvent.Connected, async () => {
            console.log('Connected to room');
            setCallState('connected');
            startCallTimer();
            onCallStatusChange?.('connected');
            // Publish local tracks after connecting
            await publishLocalTracks(room);
            // Handle existing remote participants and their tracks
            const remoteParts = Array.from(room.remoteParticipants.values());
            setParticipants(remoteParts);
            setRemoteParticipants(remoteParts);
            remoteParts.forEach(participant => {
              participant.trackPublications.forEach(publication => {
                if (publication.isSubscribed && publication.track) {
                  const track = publication.track;
                  console.log('Handling existing track:', track.kind, track.source);
                  if (track.kind === Track.Kind.Video) {
                    if (track.source === Track.Source.Camera) {
                      setRemoteVideoTrack(track);
                    } else if (track.source === Track.Source.ScreenShare) {
                      setRemoteScreenTrack(track);
                      setIsScreenSharing(true);
                    }
                  } else if (track.kind === Track.Kind.Audio) {
                    if (track.source === Track.Source.Microphone) {
                      setRemoteAudioTrack(track);
                    } else if (track.source === Track.Source.ScreenShareAudio) {
                      setRemoteScreenAudioTrack(track);
                    }
                  }
                }
              });
            });
          })
          .on(RoomEvent.Disconnected, () => {
            console.log('Disconnected from room');
            endCall();
          })
          .on(RoomEvent.ParticipantConnected, (participant) => {
            console.log('Participant connected:', participant.identity);
            setParticipants(prev => [...prev, participant]);
            setRemoteParticipants(prev => [...prev, participant]);
          })
          .on(RoomEvent.ParticipantDisconnected, (participant) => {
            console.log('Participant disconnected:', participant.identity);
            setParticipants(prev => prev.filter(p => p.sid !== participant.sid));
            setRemoteParticipants(prev => prev.filter(p => p.sid !== participant.sid));
          })
          .on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
            console.log('Track subscribed:', track.kind, track.source);
            if (track.kind === Track.Kind.Video) {
              if (track.source === Track.Source.Camera) {
                setRemoteVideoTrack(track);
              } else if (track.source === Track.Source.ScreenShare) {
                setRemoteScreenTrack(track);
                setIsScreenSharing(true);
              }
            } else if (track.kind === Track.Kind.Audio) {
              if (track.source === Track.Source.Microphone) {
                setRemoteAudioTrack(track);
              } else if (track.source === Track.Source.ScreenShareAudio) {
                setRemoteScreenAudioTrack(track);
              }
            }
          })
          .on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
            console.log('Track unsubscribed:', track.kind, track.source);
            track.detach();
            if (track.kind === Track.Kind.Video) {
              if (track.source === Track.Source.Camera) {
                setRemoteVideoTrack(null);
              } else if (track.source === Track.Source.ScreenShare) {
                setRemoteScreenTrack(null);
                setIsScreenSharing(false);
              }
            } else if (track.kind === Track.Kind.Audio) {
              if (track.source === Track.Source.Microphone) {
                setRemoteAudioTrack(null);
              } else if (track.source === Track.Source.ScreenShareAudio) {
                setRemoteScreenAudioTrack(null);
              }
            }
          })
          .on(RoomEvent.TrackMuted, (publication, participant) => {
            if (publication.kind === Track.Kind.Audio && participant.isRemote) {
              setIsRemoteAudioMuted(true);
            }
          })
          .on(RoomEvent.TrackUnmuted, (publication, participant) => {
            if (publication.kind === Track.Kind.Audio && participant.isRemote) {
              setIsRemoteAudioMuted(false);
            }
          });

        await room.connect(LIVEKIT_URL, token);
        setRoom(room);
      } catch (err) {
        console.error('Error joining room:', err);
        setError(`Failed to connect: ${err.message}`);
        setCallState('error');
        onCallStatusChange?.('error');
      }
    };
    
    joinRoom(activeCall.token);
    
    // Cleanup on unmount or if activeCall changes
    return () => {
      cleanup();
    };
  }, [activeCall]);

  useEffect(() => {
    if (localVideoTrack && localVideoRef.current) {
      localVideoTrack.attach(localVideoRef.current);
      return () => localVideoTrack.detach(localVideoRef.current);
    }
  }, [localVideoTrack]);

  useEffect(() => {
    if (remoteVideoTrack && remoteVideoRef.current) {
      remoteVideoTrack.attach(remoteVideoRef.current);
      return () => remoteVideoTrack.detach(remoteVideoRef.current);
    }
  }, [remoteVideoTrack, remoteVideoRef.current]);

  useEffect(() => {
    if (remoteAudioTrack && remoteAudioRef.current) {
      remoteAudioTrack.attach(remoteAudioRef.current);
      return () => remoteAudioTrack.detach(remoteAudioRef.current);
    }
  }, [remoteAudioTrack, remoteAudioRef.current]);

  useEffect(() => {
    if (remoteScreenTrack && screenShareRef.current) {
      remoteScreenTrack.attach(screenShareRef.current);
      return () => remoteScreenTrack.detach(screenShareRef.current);
    }
  }, [remoteScreenTrack, screenShareRef.current]);

  useEffect(() => {
    if (remoteScreenAudioTrack && screenAudioRef.current) {
      remoteScreenAudioTrack.attach(screenAudioRef.current);
      return () => remoteScreenAudioTrack.detach(screenAudioRef.current);
    }
  }, [remoteScreenAudioTrack, screenAudioRef.current]);

  const toggleMute = async () => {
    if (localAudioTrack) {
      if (isMuted) {
        await localAudioTrack.unmute();
      } else {
        await localAudioTrack.mute();
      }
      setIsMuted(!isMuted);
    }
  };

  const toggleVideo = async () => {
    if (localVideoTrack) {
      if (isVideoOff) {
        await localVideoTrack.unmute();
      } else {
        await localVideoTrack.mute();
      }
      setIsVideoOff(!isVideoOff);
    }
  };

  const toggleScreenShare = async () => {
    if (!room) return;
    if (isScreenSharing) {
      await room.localParticipant.setScreenShareEnabled(false);
      setIsScreenSharing(false);
    } else {
      await room.localParticipant.setScreenShareEnabled(true);
      setIsScreenSharing(true);
    }
  };

  if (!showVideoCall || !activeCall) return null;

  if (callState === 'connecting') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[100]">
        <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-16 h-16 border-4 border-indigo-600 border-t-transparent rounded-full mx-auto mb-6"
          />
          <h2 className="text-xl font-bold text-gray-800 mb-2">Connecting...</h2>
          <p className="text-gray-600 mb-4">Setting up video call with student...</p>
          {mediaError && <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg text-sm">{mediaError}</div>}
          {error && <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg text-sm">{error}</div>}
          <div className="mt-6"><video ref={localVideoRef} autoPlay muted playsInline className="w-full h-32 bg-gray-900 rounded-lg object-cover" /></div>
          <button onClick={endCall} className="mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">Cancel</button>
        </div>
      </div>
    );
  }

  if (callState === 'connected') {
    return (
      <div className="fixed inset-0 bg-gray-900 z-[100] flex flex-col">
        <div className="bg-gray-800 text-white px-6 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <motion.div animate={{ scale: [1, 1.2, 1] }} transition={{ duration: 2, repeat: Infinity }} className="w-3 h-3 bg-green-500 rounded-full" />
            <span className="font-medium">Connected</span>
            <span className="text-sm text-gray-300">| Student ID: {activeCall?.student_id}</span>
          </div>
          <div className="text-sm text-gray-300">Duration: {formatDuration(callDuration)}</div>
        </div>
        
        <div className="flex-1 p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Remote Video */}
          <div className="relative bg-black rounded-lg overflow-hidden">
            <video ref={remoteVideoRef} autoPlay playsInline className="w-full h-full object-cover" />
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm flex items-center gap-2">
              <span>Student: {activeCall?.student_id}</span>
              {isRemoteAudioMuted && <VolumeX size={16} />}
            </div>
            {remoteParticipants.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-white"><Users size={48} className="opacity-50" /> <span className="ml-2">Waiting for student...</span></div>
            )}
          </div>
          
          {/* Local Video */}
          <div className="relative bg-black rounded-lg overflow-hidden">
            <video ref={localVideoRef} autoPlay muted playsInline className="w-full h-full object-cover" />
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">You (Mentor)</div>
          </div>

          {/* Screen Share (if active) */}
          {isScreenSharing && (
            <div className="relative bg-black rounded-lg overflow-hidden col-span-2">
              <video ref={screenShareRef} autoPlay playsInline className="w-full h-full object-cover" />
              <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">Screen Share</div>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="bg-gray-800 px-6 py-4">
          <div className="flex justify-center space-x-6">
            <button onClick={toggleMute} className={`p-4 rounded-full ${isMuted ? 'bg-red-600' : 'bg-gray-600'} text-white`}><MicOff size={24} /></button>
            <button onClick={toggleVideo} className={`p-4 rounded-full ${isVideoOff ? 'bg-red-600' : 'bg-gray-600'} text-white`}><VideoOff size={24} /></button>
            <button onClick={toggleScreenShare} className={`p-4 rounded-full ${isScreenSharing ? 'bg-blue-600' : 'bg-gray-600'} text-white`}><Monitor size={24} /></button>
            <button onClick={endCall} className="p-4 rounded-full bg-red-600 text-white"><PhoneOff size={24} /></button>
          </div>
        </div>

        <audio ref={remoteAudioRef} autoPlay />
        <audio ref={screenAudioRef} autoPlay />
      </div>
    );
  }

  if (callState === 'ended' || callState === 'error') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[100]">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl p-8 max-w-sm w-full mx-4 text-center"
        >
          <div className="text-6xl mb-4">📞</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">{callState === 'error' ? 'Call Failed' : 'Call Ended'}</h2>
          {callState === 'ended' && <p className="text-gray-600 mb-4">Duration: {formatDuration(callDuration)}</p>}
          {error && <p className="text-red-600 text-sm mb-4">{error}</p>}
          {mediaError && <p className="text-red-600 text-sm mb-4">{mediaError}</p>}
          <button onClick={() => onCallStatusChange?.('ended')} className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">Close</button>
        </motion.div>
      </div>
    );
  }

  return null;
};

export default IncomingCall;//modified 


// import React, { useState, useEffect, useRef } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
// import { 
//   Room, 
//   Track,
//   VideoPresets,
//   createLocalTracks,
//   RoomEvent
// } from 'livekit-client';
// import {
//   Mic, MicOff, Video, VideoOff, Monitor, PhoneOff, Users, VolumeX, Phone, X
// } from 'lucide-react';

// const IncomingCall = ({ mentorId, incomingCall: activeCall, onCallStatusChange, showVideoCall }) => {
//   const [callState, setCallState] = useState('idle'); // idle, incoming, connecting, connected, ended, error
//   const [room, setRoom] = useState(null);
//   const [participants, setParticipants] = useState([]);
//   const [localTracks, setLocalTracks] = useState([]);
//   const [isMuted, setIsMuted] = useState(false);
//   const [isVideoOff, setIsVideoOff] = useState(false);
//   const [isScreenSharing, setIsScreenSharing] = useState(false);
//   const [callDuration, setCallDuration] = useState(0);
//   const [error, setError] = useState('');
//   const [mediaError, setMediaError] = useState('');
//   const [remoteParticipants, setRemoteParticipants] = useState([]);
//   const [localVideoTrack, setLocalVideoTrack] = useState(null);
//   const [localAudioTrack, setLocalAudioTrack] = useState(null);
//   const [isRemoteAudioMuted, setIsRemoteAudioMuted] = useState(false);
//   const [remoteVideoTrack, setRemoteVideoTrack] = useState(null);
//   const [remoteAudioTrack, setRemoteAudioTrack] = useState(null);
//   const [remoteScreenTrack, setRemoteScreenTrack] = useState(null);
//   const [remoteScreenAudioTrack, setRemoteScreenAudioTrack] = useState(null);

//   const localVideoRef = useRef(null);
//   const remoteVideoRef = useRef(null);
//   const screenShareRef = useRef(null);
//   const remoteAudioRef = useRef(null);
//   const screenAudioRef = useRef(null);
//   const roomRef = useRef(null);
//   const callTimerRef = useRef(null);
//   const tracksInitialized = useRef(false);
//   const swipeRef = useRef({ startX: 0, direction: 0 });

//   const API_BASE_URL = 'https://testing.sasthra.in';
//   const LIVEKIT_URL = 'wss://livekit.sasthra.in';

//   const startCallTimer = () => {
//     const startTime = Date.now();
//     callTimerRef.current = setInterval(() => {
//       setCallDuration(Math.floor((Date.now() - startTime) / 1000));
//     }, 1000);
//   };

//   const stopCallTimer = () => {
//     if (callTimerRef.current) clearInterval(callTimerRef.current);
//     callTimerRef.current = null;
//   };

//   const formatDuration = (seconds) => {
//     const mins = Math.floor(seconds / 60);
//     const secs = seconds % 60;
//     return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
//   };

//   const cleanup = () => {
//     console.log('Cleaning up resources...');
//     if (roomRef.current) {
//       roomRef.current.disconnect();
//       roomRef.current = null;
//     }
//     setRoom(null);
//     localTracks.forEach(track => track.stop());
//     setLocalTracks([]);
//     setLocalVideoTrack(null);
//     setLocalAudioTrack(null);
//     setParticipants([]);
//     setRemoteParticipants([]);
//     setIsScreenSharing(false);
//     setIsMuted(false);
//     setIsVideoOff(false);
//     setError('');
//     setMediaError('');
//     setRemoteVideoTrack(null);
//     setRemoteAudioTrack(null);
//     setRemoteScreenTrack(null);
//     setRemoteScreenAudioTrack(null);
//     tracksInitialized.current = false;
//     stopCallTimer();
//   };
  
//   const endCall = async () => {
//     try {
//       if (activeCall?.room_name) {
//         await fetch(`${API_BASE_URL}/call/end`, {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({ room_name: activeCall.room_name })
//         });
//       }
//     } catch (error) {
//       console.error('Error sending end call request:', error);
//     }
//     cleanup();
//     setCallState('ended');
//     onCallStatusChange?.('ended');
//   };

//   const initializeLocalTracks = async () => {
//     if (tracksInitialized.current) return localTracks;
//     try {
//       setMediaError('');
//       const tracks = await createLocalTracks({
//         audio: true,
//         video: { resolution: VideoPresets.h720.resolution },
//       });
      
//       const videoTrack = tracks.find(t => t.kind === Track.Kind.Video);
//       const audioTrack = tracks.find(t => t.kind === Track.Kind.Audio);
      
//       setLocalTracks(tracks);
//       setLocalVideoTrack(videoTrack);
//       setLocalAudioTrack(audioTrack);

//       if (videoTrack && localVideoRef.current) {
//         videoTrack.attach(localVideoRef.current);
//       }
//       tracksInitialized.current = true;
//       return tracks;
//     } catch (err) {
//       console.error('Error initializing local tracks:', err);
//       let errorMessage = 'Failed to access camera/microphone. ';
//       if (err.name === 'NotAllowedError') errorMessage += 'Please grant permissions in your browser.';
//       else if (err.name === 'NotFoundError') errorMessage += 'No camera or microphone found.';
//       else errorMessage += err.message;
//       setMediaError(errorMessage);
//       setCallState('error');
//       onCallStatusChange?.('error');
//       throw err;
//     }
//   };

//   const publishLocalTracks = async (roomInstance) => {
//     try {
//       console.log('Publishing local tracks...');
//       let tracksToPublish = localTracks;
//       if (tracksToPublish.length === 0) {
//         console.log('No existing tracks, creating new ones...');
//         tracksToPublish = await createLocalTracks({
//           audio: true,
//           video: { resolution: VideoPresets.h720.resolution },
//         });
//         setLocalTracks(tracksToPublish);
//         const videoTrack = tracksToPublish.find(t => t.kind === Track.Kind.Video);
//         const audioTrack = tracksToPublish.find(t => t.kind === Track.Kind.Audio);
//         setLocalVideoTrack(videoTrack);
//         setLocalAudioTrack(audioTrack);
//         if (videoTrack && localVideoRef.current) {
//           videoTrack.attach(localVideoRef.current);
//         }
//       }
//       for (const track of tracksToPublish) {
//         console.log(`Publishing ${track.kind} track...`);
//         const publication = await roomInstance.localParticipant.publishTrack(track);
//         console.log(`Successfully published ${track.kind} track:`, publication.trackSid);
//       }
//       console.log('Local tracks publishing completed');
//     } catch (error) {
//       console.error('Error in publishLocalTracks:', error);
//     }
//   };
  
//   // Connect to the room when activeCall prop is provided
//   useEffect(() => {
//     if (!activeCall || !activeCall.token) {
//       if (callState !== 'idle' && callState !== 'incoming') {
//         cleanup();
//         setCallState('idle');
//       }
//       return;
//     }

//     // Only show incoming state when call is first received
//     if (callState === 'idle') {
//       setCallState('incoming');
//     }

//     if (callState === 'connecting' || callState === 'connected') {
//       const joinRoom = async (token) => {
//         try {
//           await initializeLocalTracks();
          
//           const room = new Room({
//             adaptiveStream: true,
//             dynacast: true,
//             autoSubscribe: true,
//           });
//           roomRef.current = room;
          
//           room
//             .on(RoomEvent.Connected, async () => {
//               console.log('Connected to room');
//               setCallState('connected');
//               startCallTimer();
//               onCallStatusChange?.('connected');
//               // Publish local tracks after connecting
//               await publishLocalTracks(room);
//               // Handle existing remote participants and their tracks
//               const remoteParts = Array.from(room.remoteParticipants.values());
//               setParticipants(remoteParts);
//               setRemoteParticipants(remoteParts);
//               remoteParts.forEach(participant => {
//                 participant.trackPublications.forEach(publication => {
//                   if (publication.isSubscribed && publication.track) {
//                     const track = publication.track;
//                     console.log('Handling existing track:', track.kind, track.source);
//                     if (track.kind === Track.Kind.Video) {
//                       if (track.source === Track.Source.Camera) {
//                         setRemoteVideoTrack(track);
//                       } else if (track.source === Track.Source.ScreenShare) {
//                         setRemoteScreenTrack(track);
//                         setIsScreenSharing(true);
//                       }
//                     } else if (track.kind === Track.Kind.Audio) {
//                       if (track.source === Track.Source.Microphone) {
//                         setRemoteAudioTrack(track);
//                       } else if (track.source === Track.Source.ScreenShareAudio) {
//                         setRemoteScreenAudioTrack(track);
//                       }
//                     }
//                   }
//                 });
//               });
//             })
//             .on(RoomEvent.Disconnected, () => {
//               console.log('Disconnected from room');
//               endCall();
//             })
//             .on(RoomEvent.ParticipantConnected, (participant) => {
//               console.log('Participant connected:', participant.identity);
//               setParticipants(prev => [...prev, participant]);
//               setRemoteParticipants(prev => [...prev, participant]);
//             })
//             .on(RoomEvent.ParticipantDisconnected, (participant) => {
//               console.log('Participant disconnected:', participant.identity);
//               setParticipants(prev => prev.filter(p => p.sid !== participant.sid));
//               setRemoteParticipants(prev => prev.filter(p => p.sid !== participant.sid));
//             })
//             .on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
//               console.log('Track subscribed:', track.kind, track.source);
//               if (track.kind === Track.Kind.Video) {
//                 if (track.source === Track.Source.Camera) {
//                   setRemoteVideoTrack(track);
//                 } else if (track.source === Track.Source.ScreenShare) {
//                   setRemoteScreenTrack(track);
//                   setIsScreenSharing(true);
//                 }
//               } else if (track.kind === Track.Kind.Audio) {
//                 if (track.source === Track.Source.Microphone) {
//                   setRemoteAudioTrack(track);
//                 } else if (track.source === Track.Source.ScreenShareAudio) {
//                   setRemoteScreenAudioTrack(track);
//                 }
//               }
//             })
//             .on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
//               console.log('Track unsubscribed:', track.kind, track.source);
//               track.detach();
//               if (track.kind === Track.Kind.Video) {
//                 if (track.source === Track.Source.Camera) {
//                   setRemoteVideoTrack(null);
//                 } else if (track.source === Track.Source.ScreenShare) {
//                   setRemoteScreenTrack(null);
//                   setIsScreenSharing(false);
//                 }
//               } else if (track.kind === Track.Kind.Audio) {
//                 if (track.source === Track.Source.Microphone) {
//                   setRemoteAudioTrack(null);
//                 } else if (track.source === Track.Source.ScreenShareAudio) {
//                   setRemoteScreenAudioTrack(null);
//                 }
//               }
//             })
//             .on(RoomEvent.TrackMuted, (publication, participant) => {
//               if (publication.kind === Track.Kind.Audio && participant.isRemote) {
//                 setIsRemoteAudioMuted(true);
//               }
//             })
//             .on(RoomEvent.TrackUnmuted, (publication, participant) => {
//               if (publication.kind === Track.Kind.Audio && participant.isRemote) {
//                 setIsRemoteAudioMuted(false);
//               }
//             });

//           await room.connect(LIVEKIT_URL, token);
//           setRoom(room);
//         } catch (err) {
//           console.error('Error joining room:', err);
//           setError(`Failed to connect: ${err.message}`);
//           setCallState('error');
//           onCallStatusChange?.('error');
//         }
//       };
      
//       joinRoom(activeCall.token);
//     }
    
//     // Cleanup on unmount or if activeCall changes
//     return () => {
//       cleanup();
//     };
//   }, [activeCall, callState]);

//   useEffect(() => {
//     if (localVideoTrack && localVideoRef.current) {
//       localVideoTrack.attach(localVideoRef.current);
//       return () => localVideoTrack.detach(localVideoRef.current);
//     }
//   }, [localVideoTrack]);

//   useEffect(() => {
//     if (remoteVideoTrack && remoteVideoRef.current) {
//       remoteVideoTrack.attach(remoteVideoRef.current);
//       return () => remoteVideoTrack.detach(remoteVideoRef.current);
//     }
//   }, [remoteVideoTrack, remoteVideoRef.current]);

//   useEffect(() => {
//     if (remoteAudioTrack && remoteAudioRef.current) {
//       remoteAudioTrack.attach(remoteAudioRef.current);
//       return () => remoteAudioTrack.detach(remoteAudioRef.current);
//     }
//   }, [remoteAudioTrack, remoteAudioRef.current]);

//   useEffect(() => {
//     if (remoteScreenTrack && screenShareRef.current) {
//       remoteScreenTrack.attach(screenShareRef.current);
//       return () => remoteScreenTrack.detach(screenShareRef.current);
//     }
//   }, [remoteScreenTrack, screenShareRef.current]);

//   useEffect(() => {
//     if (remoteScreenAudioTrack && screenAudioRef.current) {
//       remoteScreenAudioTrack.attach(screenAudioRef.current);
//       return () => remoteScreenAudioTrack.detach(screenAudioRef.current);
//     }
//   }, [remoteScreenAudioTrack, screenAudioRef.current]);

//   const toggleMute = async () => {
//     if (localAudioTrack) {
//       if (isMuted) {
//         await localAudioTrack.unmute();
//       } else {
//         await localAudioTrack.mute();
//       }
//       setIsMuted(!isMuted);
//     }
//   };

//   const toggleVideo = async () => {
//     if (localVideoTrack) {
//       if (isVideoOff) {
//         await localVideoTrack.unmute();
//       } else {
//         await localVideoTrack.mute();
//       }
//       setIsVideoOff(!isVideoOff);
//     }
//   };

//   const toggleScreenShare = async () => {
//     if (!room) return;
//     if (isScreenSharing) {
//       await room.localParticipant.setScreenShareEnabled(false);
//       setIsScreenSharing(false);
//     } else {
//       await room.localParticipant.setScreenShareEnabled(true);
//       setIsScreenSharing(true);
//     }
//   };

//   const handleAcceptCall = () => {
//     setCallState('connecting');
//     onCallStatusChange?.('accepted');
//   };

//   const handleDeclineCall = () => {
//     endCall();
//     onCallStatusChange?.('declined');
//   };

//   const handleSwipeStart = (e) => {
//     const touch = e.touches[0];
//     swipeRef.current.startX = touch.clientX;
//   };

//   const handleSwipeMove = (e) => {
//     if (!swipeRef.current.startX) return;
    
//     const touch = e.touches[0];
//     const diff = touch.clientX - swipeRef.current.startX;
    
//     if (diff > 50) {
//       handleAcceptCall();
//       swipeRef.current.startX = null;
//     } else if (diff < -50) {
//       handleDeclineCall();
//       swipeRef.current.startX = null;
//     }
//   };

//   const handleSwipeEnd = () => {
//     swipeRef.current.startX = null;
//   };

//   if (!showVideoCall) return null;

//   // Mobile-style incoming call screen
//   if (callState === 'incoming') {
//     return (
//       <div 
//         className="fixed inset-0 bg-gradient-to-b from-indigo-900 to-purple-900 flex flex-col items-center justify-between p-6 overflow-hidden"
//         onTouchStart={handleSwipeStart}
//         onTouchMove={handleSwipeMove}
//         onTouchEnd={handleSwipeEnd}
//       >
//         {/* Top status bar */}
//         <div className="w-full flex justify-between items-center mt-4">
//           <div className="text-white text-sm opacity-80">9:41</div>
//           <div className="flex space-x-1">
//             <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
//             <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
//             <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
//           </div>
//         </div>

//         {/* Call info */}
//         <motion.div 
//           initial={{ y: -50, opacity: 0 }}
//           animate={{ y: 0, opacity: 1 }}
//           transition={{ type: 'spring', stiffness: 100 }}
//           className="flex-1 flex flex-col items-center justify-center text-center"
//         >
//           <motion.div 
//             animate={{ 
//               scale: [1, 1.05, 1],
//               rotate: [0, 2, -2, 0]
//             }}
//             transition={{ 
//               duration: 2,
//               repeat: Infinity,
//               ease: 'easeInOut'
//             }}
//             className="w-32 h-32 rounded-full bg-white flex items-center justify-center mb-6 shadow-2xl overflow-hidden"
//           >
//             <Users size={64} className="text-indigo-600" />
//           </motion.div>
          
//           <motion.h2 
//             initial={{ opacity: 0, y: 20 }}
//             animate={{ opacity: 1, y: 0 }}
//             transition={{ delay: 0.2 }}
//             className="text-3xl font-bold text-white mb-2"
//           >
//             Student Call
//           </motion.h2>
          
//           <motion.p 
//             initial={{ opacity: 0, y: 20 }}
//             animate={{ opacity: 1, y: 0 }}
//             transition={{ delay: 0.3 }}
//             className="text-xl text-indigo-200 mb-8"
//           >
//             Student ID: {activeCall?.student_id}
//           </motion.p>
          
//           <motion.div 
//             initial={{ opacity: 0, y: 20 }}
//             animate={{ opacity: 1, y: 0 }}
//             transition={{ delay: 0.4 }}
//             className="text-white text-lg bg-indigo-600 bg-opacity-30 px-4 py-2 rounded-full"
//           >
//             <motion.span
//               animate={{ 
//                 opacity: [1, 0.5, 1],
//                 y: [0, -5, 0]
//               }}
//               transition={{ 
//                 duration: 1.5,
//                 repeat: Infinity,
//                 ease: 'easeInOut'
//               }}
//             >
//               Incoming call...
//             </motion.span>
//           </motion.div>
//         </motion.div>

//         {/* Action buttons */}
//         <motion.div 
//           initial={{ y: 50, opacity: 0 }}
//           animate={{ y: 0, opacity: 1 }}
//           transition={{ delay: 0.5 }}
//           className="w-full max-w-md mb-8"
//         >
//           <div className="flex justify-between px-8">
//             {/* Decline button */}
//             <motion.div
//               whileTap={{ scale: 0.9 }}
//               whileHover={{ scale: 1.1 }}
//             >
//               <div 
//                 className="w-16 h-16 rounded-full bg-red-500 flex items-center justify-center shadow-lg cursor-pointer"
//                 onClick={handleDeclineCall}
//               >
//                 <PhoneOff size={28} className="text-white" />
//               </div>
//               <p className="text-white text-sm mt-2 text-center">Decline</p>
//             </motion.div>

//             {/* Accept button */}
//             <motion.div
//               whileTap={{ scale: 0.9 }}
//               whileHover={{ scale: 1.1 }}
//             >
//               <div 
//                 className="w-16 h-16 rounded-full bg-green-500 flex items-center justify-center shadow-lg cursor-pointer"
//                 onClick={handleAcceptCall}
//               >
//                 <Phone size={28} className="text-white rotate-180" />
//               </div>
//               <p className="text-white text-sm mt-2 text-center">Accept</p>
//             </motion.div>
//           </div>

//           <motion.div 
//             className="mt-8 text-center text-white text-sm"
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             transition={{ delay: 1 }}
//           >
//             <p>Slide to answer</p>
//             <motion.div 
//               className="mt-2 h-1 w-24 bg-white bg-opacity-30 rounded-full mx-auto"
//               animate={{ 
//                 x: [-5, 5, -5],
//                 opacity: [0.5, 1, 0.5]
//               }}
//               transition={{ 
//                 duration: 1.5,
//                 repeat: Infinity,
//                 ease: 'easeInOut'
//               }}
//             >
//               <motion.div 
//                 className="h-1 w-8 bg-white rounded-full"
//                 animate={{ x: [0, 20, 0] }}
//                 transition={{ 
//                   duration: 1.5,
//                   repeat: Infinity,
//                   ease: 'easeInOut'
//                 }}
//               />
//             </motion.div>
//           </motion.div>
//         </motion.div>

//         {/* Decorative elements */}
//         <motion.div 
//           className="absolute bottom-0 left-0 w-32 h-32 bg-indigo-500 rounded-full opacity-20"
//           animate={{ 
//             y: [0, -20, 0],
//             scale: [1, 1.1, 1]
//           }}
//           transition={{ 
//             duration: 3,
//             repeat: Infinity,
//             ease: 'easeInOut'
//           }}
//         />
//         <motion.div 
//           className="absolute top-0 right-0 w-40 h-40 bg-purple-500 rounded-full opacity-20"
//           animate={{ 
//             y: [0, 20, 0],
//             scale: [1, 1.1, 1]
//           }}
//           transition={{ 
//             duration: 4,
//             repeat: Infinity,
//             ease: 'easeInOut',
//             delay: 0.5
//           }}
//         />
//       </div>
//     );
//   }

//   if (callState === 'connecting') {
//     return (
//       <div className="fixed inset-0 bg-gradient-to-b from-indigo-900 to-purple-900 flex items-center justify-center z-[100]">
//         <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 max-w-md w-full mx-4 text-center border border-white/20">
//           <motion.div
//             animate={{ 
//               scale: [1, 1.2, 1],
//               rotate: 360 
//             }}
//             transition={{ 
//               duration: 1.5, 
//               repeat: Infinity, 
//               ease: 'easeInOut' 
//             }}
//             className="w-16 h-16 border-4 border-white border-t-indigo-500 rounded-full mx-auto mb-6"
//           />
//           <h2 className="text-2xl font-bold text-white mb-2">Connecting...</h2>
//           <p className="text-indigo-200 mb-4">Setting up video call with student...</p>
//           {mediaError && <div className="mb-4 p-3 bg-red-500/20 text-red-200 rounded-lg text-sm">{mediaError}</div>}
//           {error && <div className="mb-4 p-3 bg-red-500/20 text-red-200 rounded-lg text-sm">{error}</div>}
//           <div className="mt-6">
//             <video 
//               ref={localVideoRef} 
//               autoPlay 
//               muted 
//               playsInline 
//               className="w-full h-32 bg-black/30 rounded-lg object-cover border border-white/10" 
//             />
//           </div>
//           <button 
//             onClick={endCall} 
//             className="mt-4 px-6 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center justify-center mx-auto"
//           >
//             <X size={18} className="mr-2" /> Cancel
//           </button>
//         </div>
//       </div>
//     );
//   }

//   if (callState === 'connected') {
//     return (
//       <div className="fixed inset-0 bg-gray-900 z-[100] flex flex-col">
//         <div className="bg-gradient-to-r from-indigo-900 to-purple-900 text-white px-6 py-4 flex justify-between items-center">
//           <div className="flex items-center space-x-2">
//             <motion.div 
//               animate={{ scale: [1, 1.2, 1] }} 
//               transition={{ duration: 2, repeat: Infinity }} 
//               className="w-3 h-3 bg-green-500 rounded-full" 
//             />
//             <span className="font-medium">Connected</span>
//             <span className="text-sm text-indigo-200">| Student ID: {activeCall?.student_id}</span>
//           </div>
//           <div className="text-sm text-white">Duration: {formatDuration(callDuration)}</div>
//         </div>
        
//         <div className="flex-1 p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
//           {/* Remote Video */}
//           <div className="relative bg-black rounded-lg overflow-hidden shadow-xl">
//             <video ref={remoteVideoRef} autoPlay playsInline className="w-full h-full object-cover" />
//             <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm flex items-center gap-2">
//               <span>Student: {activeCall?.student_id}</span>
//               {isRemoteAudioMuted && <VolumeX size={16} />}
//             </div>
//             {remoteParticipants.length === 0 && (
//               <div className="absolute inset-0 flex items-center justify-center text-white">
//                 <Users size={48} className="opacity-50" /> 
//                 <span className="ml-2">Waiting for student...</span>
//               </div>
//             )}
//           </div>
          
//           {/* Local Video */}
//           <div className="relative bg-black rounded-lg overflow-hidden shadow-xl">
//             <video ref={localVideoRef} autoPlay muted playsInline className="w-full h-full object-cover" />
//             <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">You (Mentor)</div>
//           </div>

//           {/* Screen Share (if active) */}
//           {isScreenSharing && (
//             <div className="relative bg-black rounded-lg overflow-hidden col-span-2 shadow-xl">
//               <video ref={screenShareRef} autoPlay playsInline className="w-full h-full object-cover" />
//               <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">Screen Share</div>
//             </div>
//           )}
//         </div>

//         {/* Controls */}
//         <div className="bg-gradient-to-r from-indigo-900 to-purple-900 px-6 py-4">
//           <div className="flex justify-center space-x-6">
//             <motion.button
//               whileTap={{ scale: 0.85 }}
//               onClick={toggleMute} 
//               className={`p-4 rounded-full ${isMuted ? 'bg-red-500' : 'bg-gray-700'} text-white`}
//             >
//               {isMuted ? <MicOff size={24} /> : <Mic size={24} />}
//             </motion.button>
//             <motion.button
//               whileTap={{ scale: 0.85 }}
//               onClick={toggleVideo} 
//               className={`p-4 rounded-full ${isVideoOff ? 'bg-red-500' : 'bg-gray-700'} text-white`}
//             >
//               {isVideoOff ? <VideoOff size={24} /> : <Video size={24} />}
//             </motion.button>
//             <motion.button
//               whileTap={{ scale: 0.85 }}
//               onClick={toggleScreenShare} 
//               className={`p-4 rounded-full ${isScreenSharing ? 'bg-blue-500' : 'bg-gray-700'} text-white`}
//             >
//               <Monitor size={24} />
//             </motion.button>
//             <motion.button
//               whileTap={{ scale: 1.1 }}
//               whileHover={{ scale: 1.05 }}
//               onClick={endCall} 
//               className="p-4 rounded-full bg-red-500 text-white"
//             >
//               <PhoneOff size={24} />
//             </motion.button>
//           </div>
//         </div>

//         <audio ref={remoteAudioRef} autoPlay />
//         <audio ref={screenAudioRef} autoPlay />
//       </div>
//     );
//   }

//   if (callState === 'ended' || callState === 'error') {
//     return (
//       <div className="fixed inset-0 bg-gradient-to-b from-indigo-900 to-purple-900 flex items-center justify-center z-[100]">
//         <AnimatePresence>
//           <motion.div
//             initial={{ opacity: 0, scale: 0.8, y: 20 }}
//             animate={{ opacity: 1, scale: 1, y: 0 }}
//             exit={{ opacity: 0, scale: 0.8 }}
//             transition={{ type: 'spring', damping: 20 }}
//             className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 max-w-sm w-full mx-4 text-center border border-white/20"
//           >
//             <div className="text-6xl mb-4">
//               {callState === 'ended' ? '📞' : '⚠️'}
//             </div>
//             <h2 className="text-2xl font-bold text-white mb-2">
//               {callState === 'error' ? 'Call Failed' : 'Call Ended'}
//             </h2>
//             {callState === 'ended' && (
//               <p className="text-indigo-200 mb-4">Duration: {formatDuration(callDuration)}</p>
//             )}
//             {error && <p className="text-red-200 text-sm mb-4">{error}</p>}
//             {mediaError && <p className="text-red-200 text-sm mb-4">{mediaError}</p>}
//             <button 
//               onClick={() => onCallStatusChange?.('closed')} 
//               className="px-6 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg transition-colors"
//             >
//               Close
//             </button>
//           </motion.div>
//         </AnimatePresence>
//       </div>
//     );
//   }

//   return null;
// };

// export default IncomingCall;