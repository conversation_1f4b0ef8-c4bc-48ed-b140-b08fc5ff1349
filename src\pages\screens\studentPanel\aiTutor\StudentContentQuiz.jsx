import React, { useEffect, useState, useRef } from 'react';
import { X, <PERSON>, Loader2, CheckCircle, XCircle } from 'lucide-react';

const FLASK_API_URL = `${import.meta.env.VITE_BASE_URL}`;
const DURATION_PER_OPTION_S = null; // Removed timer - wait for user selection
const QUESTION_READING_TIME_S = 15;
const ANSWER_FEEDBACK_TIME_S = 3; // Time to show answer feedback

const FinalResultsPopup = ({ results, onClose }) => {
  if (!results) return null;

  const getScoreColor = () => {
    const percentage = (results.final_score / results.total_questions) * 100;
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = () => {
    const percentage = (results.final_score / results.total_questions) * 100;
    if (percentage >= 80) return 'bg-green-50 border-green-200';
    if (percentage >= 60) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl border-2 border-gray-200 p-8 max-w-md w-full mx-4 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X size={24} />
        </button>

        <div className="text-center">
          <div className="mb-6">
            {results.quiz_completed ? (
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            ) : (
              <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            )}
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Quiz Completed!</h2>
          </div>

          <div className={`rounded-xl p-6 mb-6 border-2 ${getScoreBgColor()}`}>
            <div className="text-center">
              <div className={`text-4xl font-bold mb-2 ${getScoreColor()}`}>
                {results.final_score}/{results.total_questions*20}
              </div>
              <div className="text-lg text-gray-600">Final Score</div>
            </div>
          </div>

          <div className="space-y-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="text-sm text-gray-600 mb-1">Message</div>
              <div className="text-gray-800 font-medium">{results.message}</div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                <div className="text-xs text-gray-600 mb-1">Status</div>
                <div
                  className={`font-semibold text-sm ${
                    results.status === 'completed' ? 'text-green-600' : 'text-gray-800'
                  }`}
                >
                  {results.status}
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                <div className="text-xs text-gray-600 mb-1">Quiz Completed</div>
                <div
                  className={`font-semibold text-sm ${
                    results.quiz_completed ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {results.quiz_completed ? 'Yes' : 'No'}
                </div>
              </div>
            </div>
          </div>

          <button
            onClick={onClose}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

const LoadingNextQuestion = ({ isVisible, message }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/40 backdrop-blur-sm">
      <div className="bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 text-white px-16 py-12 rounded-3xl shadow-2xl border-4 border-white/30 backdrop-blur-sm animate-pulse">
        <div className="text-center">
          <div className="text-4xl font-bold mb-6 flex items-center justify-center gap-4">
            <Loader2 className="animate-spin" size={40} />
            {message}
          </div>
          <div className="flex justify-center space-x-2 mb-4">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="w-4 h-4 bg-white rounded-full animate-bounce"
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
          <div className="text-lg opacity-90">
            Please wait while we prepare your{' '}
            {message.toLowerCase().includes('next') ? 'next question' : 'question'}...
          </div>
        </div>
      </div>
    </div>
  );
};

const ReadingTimer = ({ timeRemaining, isActive }) => {
  if (!isActive) return null;

  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
      <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
        <div className="text-center">
          <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
            <Clock className="animate-spin" size={32} />
            Reading Time
          </div>
          <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
          <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
        </div>
      </div>
    </div>
  );
};

const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
  const circumference = 2 * Math.PI * 120;
  const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - progress;

  const getTimerColor = () => {
    const percentage = timeRemaining / totalTime;
    if (percentage > 0.6) return '#10B981';
    if (percentage > 0.3) return '#F59E0B';
    return '#EF4444';
  };

  const getScale = () => {
    const percentage = timeRemaining / totalTime;
    return 1 + (1 - percentage) * 0.2;
  };

  if (!isActive) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />
      <div className="absolute inset-0">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
            style={{
              top: `${10 + i * 7}%`,
              left: `${5 + i * 8}%`,
              animationDelay: `${i * 0.3}s`,
              animationDuration: `${3 + (i % 3)}s`
            }}
          />
        ))}
      </div>
      <div
        className="relative transition-all duration-300 ease-out"
        style={{ transform: `scale(${getScale()})` }}
      >
        <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />
        <div className="relative w-64 h-64 flex items-center justify-center">
          <svg
            className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
            viewBox="0 0 260 260"
          >
            <circle
              cx="130"
              cy="130"
              r="120"
              fill="none"
              stroke="rgba(255, 255, 255, 0.3)"
              strokeWidth="12"
              className="drop-shadow-lg"
            />
            <circle
              cx="130"
              cy="130"
              r="120"
              fill="none"
              stroke={getTimerColor()}
              strokeWidth="12"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className="transition-all duration-1000 ease-out drop-shadow-xl"
              style={{
                filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
              }}
            />
            <circle
              cx="130"
              cy="130"
              r="100"
              fill="none"
              stroke="rgba(255, 255, 255, 0.2)"
              strokeWidth="2"
              strokeDasharray="5,5"
              className="animate-spin-reverse"
            />
            <defs>
              <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" />
                <stop offset="50%" stopColor="#8B5CF6" />
                <stop offset="100%" stopColor="#EC4899" />
              </linearGradient>
              <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor="#FFFFFF" />
                <stop offset="100%" stopColor="#F8FAFC" />
              </radialGradient>
            </defs>
          </svg>
          <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
            <div
              className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
              style={{
                transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
                textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
              }}
            >
              {option}
            </div>
            <div
              className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
              style={{
                transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
              }}
            >
              {timeRemaining}s
            </div>
            <div className="flex gap-1 mt-2">
              {[...Array(totalTime)].map((_, i) => (
                <div
                  key={i}
                  className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                    i < totalTime - timeRemaining
                      ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
                      : 'bg-gray-300 scale-100'
                  }`}
                />
              ))}
            </div>
          </div>
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
                style={{
                  top: `${15 + i * 10}%`,
                  left: `${10 + i * 12}%`,
                  animationDelay: `${i * 0.3}s`,
                  animationDuration: `${2 + (i % 2)}s`
                }}
              />
            ))}
          </div>
          <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
        </div>
      </div>
    </div>
  );
};

const StudentContentQuiz = ({ processId, onClose }) => {
  const [quizId, setQuizId] = useState(null);
  const [questionData, setQuestionData] = useState(null);
  const [quizStatus, setQuizStatus] = useState('');
  const [finalResults, setFinalResults] = useState(null);
  const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
  const [currentOption, setCurrentOption] = useState('');
  const [questionTimer, setQuestionTimer] = useState(null);
  const [isReadingQuestion, setIsReadingQuestion] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);
  const [error, setError] = useState(null);
  const [studentId] = useState(sessionStorage.getItem('userId') || '');
  const [isOptionSelected, setIsOptionSelected] = useState(false);
  const [isLoadingNextQuestion, setIsLoadingNextQuestion] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Loading Question');
  const [showFinalResultsPopup, setShowFinalResultsPopup] = useState(false);
  
  // States for answer feedback
  const [showAnswerFeedback, setShowAnswerFeedback] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [correctAnswer, setCorrectAnswer] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [answerFeedbackTimer, setAnswerFeedbackTimer] = useState(null);

  // Audio refs
  const correctAudioRef = useRef(null);
  const wrongAudioRef = useRef(null);

  const timerIntervalRef = useRef(null);
  const optionTimerIntervalRef = useRef(null);
  const answerFeedbackIntervalRef = useRef(null);

  useEffect(() => {
    // Create audio elements
    correctAudioRef.current = new Audio('/src/assets/audio/correct_answer.mp3');
    wrongAudioRef.current = new Audio('/src/assets/audio/wrong_answer.mp3');
    
    // Set audio properties
    correctAudioRef.current.preload = 'auto';
    wrongAudioRef.current.preload = 'auto';
    
    if (processId && studentId) {
      startQuiz();
    }
    return () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      if (optionTimerIntervalRef.current) {
        clearInterval(optionTimerIntervalRef.current);
      }
      if (answerFeedbackIntervalRef.current) {
        clearInterval(answerFeedbackIntervalRef.current);
      }
    };
  }, [processId, studentId]);

  const playAudio = (isCorrectAnswer) => {
    try {
      if (isCorrectAnswer && correctAudioRef.current) {
        correctAudioRef.current.currentTime = 0; // Reset to beginning
        correctAudioRef.current.play().catch(e => console.log('Audio play failed:', e));
      } else if (!isCorrectAnswer && wrongAudioRef.current) {
        wrongAudioRef.current.currentTime = 0; // Reset to beginning
        wrongAudioRef.current.play().catch(e => console.log('Audio play failed:', e));
      }
    } catch (error) {
      console.log('Audio playback error:', error);
    }
  };

  const startQuiz = async () => {
    setIsLoadingNextQuestion(true); // Show loading popup for first question
    setLoadingMessage('Loading Question'); // Set message for first question

    if (!processId || !studentId) {
      setError('Missing ProcessId or StudentId');
      setIsLoadingNextQuestion(false);
      return;
    }

    try {
      const res = await fetch(`${FLASK_API_URL}/student/panel-quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ process_selector_id: processId, student_id: studentId })
      });

      const data = await res.json();

      if (!res.ok || data.error) throw new Error(data.error || `Server error ${res.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id);
      setQuizStatus('Ready to start this question.');
      setQuestionCount(1);
      setIsLoadingNextQuestion(false); // Hide loading popup
      startQuestionPhase();
    } catch (error) {
      setError(`Error starting quiz: ${error.message}`);
      setIsLoadingNextQuestion(false);
    }
  };

  const startQuestionPhase = () => {
    setIsReadingQuestion(true);
    setQuestionTimer(QUESTION_READING_TIME_S);
    setIsOptionSelected(false);
    setShowAnswerFeedback(false);
    setIsLoadingNextQuestion(false);

    timerIntervalRef.current = setInterval(() => {
      setQuestionTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerIntervalRef.current);
          setIsReadingQuestion(false);
          startOptionPhase();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const startOptionPhase = () => {
    setQuizStatus('Select an option.');
    // No timer - wait for user to select an option
  };

  const startAnswerFeedback = (selectedOption, correctOption, isAnswerCorrect) => {
    setShowAnswerFeedback(true);
    setSelectedAnswer(selectedOption);
    setCorrectAnswer(correctOption);
    setIsCorrect(isAnswerCorrect);
    setAnswerFeedbackTimer(ANSWER_FEEDBACK_TIME_S);
    
    // Play audio based on answer correctness
    playAudio(isAnswerCorrect);
    
    answerFeedbackIntervalRef.current = setInterval(() => {
      setAnswerFeedbackTimer((prev) => {
        if (prev <= 1) {
          clearInterval(answerFeedbackIntervalRef.current);
          setShowAnswerFeedback(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleOptionSelect = async (option) => {
    if (!quizId || isOptionSelected) return;

    setIsOptionSelected(true);
    setCurrentOption(option.toUpperCase());
    setQuizStatus('Processing answer...');

    try {
      const res = await fetch(`${FLASK_API_URL}/content/next_question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizId, user_answer: option })
      });

      const data = await res.json();

      // Show answer feedback first
      const isAnswerCorrect = data.result === 'correct';
      const correctOption = data.correct_answer || '';
      
      startAnswerFeedback(option.toUpperCase(), correctOption.toUpperCase(), isAnswerCorrect);
      
      // Wait for feedback timer to complete, then proceed
      setTimeout(() => {
        // Check if this is the final completion response (5th question)
        if (data.quiz_completed && data.status === 'completed' && data.final_score !== undefined) {
          setFinalResults(data);
          setShowFinalResultsPopup(true);
          setQuizStatus('Quiz completed! View your results.');
        } else if (data.overall_summary) {
          setFinalResults(data);
          setQuizStatus('Quiz completed! Thank you for participating.');
        } else if (data.next_question && data.next_question.question) {
          // Show loading popup for next question
          setIsLoadingNextQuestion(true);
          setLoadingMessage('Loading Next Question');
          setQuizStatus('Processing answer and fetching next question...');
          
          // Update question data with the next_question object
          const nextQuestionData = {
            ...data.next_question,
            sub_topic_name: questionData.sub_topic_name // Preserve the sub_topic_name from current question
          };
          setQuestionData(nextQuestionData);
          setQuizStatus('Ready to start this question.');
          setQuestionCount((prev) => prev + 1);
          setCurrentOption('');
          
          // Hide loading popup and start next question
          setTimeout(() => {
            setIsLoadingNextQuestion(false);
            startQuestionPhase();
          }, 2000);
        } else if (data.status === 'completed' || questionCount >= 5) {
          // Handle quiz completion
          setFinalResults(data);
          setQuizStatus('Quiz completed! Thank you for participating.');
        }
      }, ANSWER_FEEDBACK_TIME_S * 1000);

    } catch (error) {
      setError(`Error during quiz progression: ${error.message}`);
      setQuizStatus(`Error: ${error.message}`);
      setIsOptionSelected(false);
      setIsLoadingNextQuestion(false);
    }
  };

  const handleCloseFinalResults = () => {
    setShowFinalResultsPopup(false);
    // Close the entire quiz component after showing final results
    onClose();
  };

  const getOptionStyle = (optionLetter, optionIndex) => {
    const isSelected = selectedAnswer.toLowerCase() === optionLetter;
    const isCorrectOption = correctAnswer.toLowerCase() === optionLetter;
    
    if (showAnswerFeedback) {
      if (isSelected && isCorrect) {
        // User selected correct answer
        return 'bg-green-100 border-green-500 text-green-800 border-2';
      } else if (isSelected && !isCorrect) {
        // User selected wrong answer
        return 'bg-red-100 border-red-500 text-red-800 border-2';
      } else if (isCorrectOption && !isCorrect) {
        // Show correct answer when user was wrong
        return 'bg-green-100 border-green-500 text-green-800 border-2';
      } else {
        // Other options during feedback
        return 'bg-gray-200 border-gray-300 text-gray-500';
      }
    }
    
    // Normal state styling
    const isCurrentOption = currentOption.toLowerCase() === optionLetter;
    
    if (isCurrentOption) {
      return 'bg-blue-100 border-blue-300 text-blue-800';
    } else if (isReadingQuestion || isOptionSelected || isLoadingNextQuestion) {
      return 'bg-gray-200 border-gray-300 text-gray-500 cursor-not-allowed';
    } else {
      return 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-blue-100 hover:border-blue-300';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Student Quiz Panel</h2>
        <button onClick={onClose} className="text-gray-600 hover:text-red-500 transition-colors">
          <X size={24} />
        </button>
      </div>

      <FinalResultsPopup
        results={finalResults}
        onClose={handleCloseFinalResults}
        isVisible={showFinalResultsPopup}
      />

      <LoadingNextQuestion isVisible={isLoadingNextQuestion} message={loadingMessage} />

      {/* Answer Feedback Overlay */}
      {showAnswerFeedback && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm">
          <div className={`rounded-3xl shadow-2xl border-4 px-16 py-12 text-white text-center ${
            isCorrect 
              ? 'bg-gradient-to-br from-green-500 via-green-600 to-green-700 border-green-300' 
              : 'bg-gradient-to-br from-red-500 via-red-600 to-red-700 border-red-300'
          }`}>
            <div className="flex items-center justify-center mb-4">
              {isCorrect ? (
                <CheckCircle className="w-16 h-16 mr-4" />
              ) : (
                <XCircle className="w-16 h-16 mr-4" />
              )}
              <div className="text-4xl font-bold">
                {isCorrect ? 'Correct!' : 'Incorrect!'}
              </div>
            </div>
            
            <div className="text-xl mb-4">
              Your answer: <span className="font-bold">{selectedAnswer}</span>
            </div>
            
            {!isCorrect && (
              <div className="text-xl mb-4">
                Correct answer: <span className="font-bold text-green-200">{correctAnswer}</span>
              </div>
            )}
            
            <div className="text-lg opacity-90">
              Next question in {answerFeedbackTimer}s...
            </div>
            
            <div className="flex justify-center mt-4">
              {[...Array(ANSWER_FEEDBACK_TIME_S)].map((_, i) => (
                <div
                  key={i}
                  className={`mx-1 w-3 h-3 rounded-full transition-all duration-300 ${
                    i < ANSWER_FEEDBACK_TIME_S - answerFeedbackTimer
                      ? 'bg-white scale-125'
                      : 'bg-white/50 scale-100'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="text-center bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {questionData && (
        <div className="text-gray-800">
          <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-3 text-gray-800">
              {questionData.sub_topic_name}
            </h2>

            <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />

            <p className="text-lg mb-4 text-gray-700">
              {questionData.question_number}. {questionData.question}
            </p>

            <div className="grid gap-3 mb-4">
              {questionData.options.map((opt, idx) => {
                const optionLetter = String.fromCharCode(97 + idx);

                return (
                  <div key={idx} className="relative">
                    <button
                      onClick={() => handleOptionSelect(optionLetter)}
                      disabled={isReadingQuestion || isOptionSelected || isLoadingNextQuestion || showAnswerFeedback}
                      className={`w-full p-3 rounded-lg border text-left transition-all ${getOptionStyle(optionLetter, idx)}`}
                    >
                      <span className="font-medium">{optionLetter.toUpperCase()})</span> {opt}
                      {showAnswerFeedback && selectedAnswer.toLowerCase() === optionLetter && isCorrect && (
                        <CheckCircle className="inline-block ml-2 w-5 h-5 text-green-600" />
                      )}
                      {showAnswerFeedback && selectedAnswer.toLowerCase() === optionLetter && !isCorrect && (
                        <XCircle className="inline-block ml-2 w-5 h-5 text-red-600" />
                      )}
                      {showAnswerFeedback && correctAnswer.toLowerCase() === optionLetter && !isCorrect && selectedAnswer.toLowerCase() !== optionLetter && (
                        <CheckCircle className="inline-block ml-2 w-5 h-5 text-green-600" />
                      )}
                    </button>
                    <CircularOptionDisplay
                      option={currentOption}
                      timeRemaining={0}
                      totalTime={1}
                      isActive={false}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {finalResults && questionCount < 5 && !showFinalResultsPopup && (
        <div className="bg-gray-50 rounded-lg p-6 mt-6 border border-gray-200">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Quiz Finished!</h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="text-sm text-gray-600 mb-1">Final Score</div>
              <div className="text-2xl font-bold text-gray-800">
                {finalResults.final_score}/{finalResults.total_questions}
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="text-sm text-gray-600 mb-1">Message</div>
              <div className="text-gray-800 font-medium">{finalResults.message}</div>
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-white rounded-lg p-3 border border-gray-200">
                <div className="text-xs text-gray-600 mb-1">Status</div>
                <div
                  className={`font-semibold text-sm ${
                    finalResults.status === 'completed' ? 'text-green-600' : 'text-gray-800'
                  }`}
                >
                  {finalResults.status}
                </div>
              </div>
              <div className="bg-white rounded-lg p-3 border border-gray-200">
                <div className="text-xs text-gray-600 mb-1">Quiz Completed</div>
                <div
                  className={`font-semibold text-sm ${
                    finalResults.quiz_completed ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {finalResults.quiz_completed ? 'Yes' : 'No'}
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Close Quiz
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentContentQuiz;