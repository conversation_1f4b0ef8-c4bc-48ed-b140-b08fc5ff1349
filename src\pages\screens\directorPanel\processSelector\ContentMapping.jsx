import React, { useEffect, useState } from 'react';
import Button from '../../../../components/Field/Button';
import { useDispatch, useSelector } from 'react-redux';
import PopUp from '../../../../components/PopUp/PopUp';
import SearchableDropdown from '../../../../components/Field/SearchableDropdown';
import Input from '../../../../components/Field/Input';
import {
  setAllContentMappingData,
  setSubTopicData,
  setTopicData,
  useCreateContentServiceMutation,
  useDeleteContentServiceMutation,
  useLazyGetAllContentMappingServiceQuery,
  useLazyGetSubTopicByIdsServiceQuery,
  useLazyGetTopicByIdsServiceQuery,
  useStoreContentInS3ServiceMutation,
  useUpdateContentServiceMutation
} from './processSelector.slice';
import Toastify from '../../../../components/PopUp/Toastify';
import Table from '../../../../components/Layout/Table';
import { ContentMappingHeader } from './TableHeaderData';

const ContentMapping = () => {
  const [popUp, setPopUp] = useState(false);

  const [selectedCourse, setSelectedCourse] = useState({ id: '', name: '' });
  const [selectedSubject, setSelectedSubject] = useState({ id: '', name: '' });
  const [selectedTopic, setSelectedTopic] = useState({ id: '', name: '' });
  const [selectedSubTopic, setSelectedSubTopic] = useState({ id: '', name: '' });
  const [language, setLanguage] = useState({ id: '', name: '' });
  const [url, setUrl] = useState('');
  const [file, setFile] = useState(null);
  const [res, setRes] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [isEditId, setIsEditId] = useState('');
  const [confirmPopUp, setConfirmPopUp] = useState(false);

  const [getSubTopicDataService] = useLazyGetSubTopicByIdsServiceQuery();
  const [getTopicDataService] = useLazyGetTopicByIdsServiceQuery();
  const [createContentService] = useCreateContentServiceMutation();
  const [storeContentInS3Service] = useStoreContentInS3ServiceMutation();
  const [getAllContentMappingService] = useLazyGetAllContentMappingServiceQuery();
  const [updateContentMappingService] = useUpdateContentServiceMutation();
  const [deleteContentMappingService] = useDeleteContentServiceMutation();

  const courseData = useSelector((state) => state.processSelector.courseData);
  const subjectData = useSelector((state) => state.processSelector.subjectData);
  const topicData = useSelector((state) => state.processSelector.topicsData);
  const subTopicData = useSelector((state) => state.processSelector.subTopicData);
  const allContentMappingData = useSelector((state) => state.processSelector.allContentMappingData);
  const dispatch = useDispatch();

  const languages = [
    { id: '0', name: 'English' },
    { id: '1', name: 'Tamil' },
    { id: '2', name: 'Telugu' },
    { id: '3', name: 'Kannada' },
    { id: '4', name: 'Malayalam' },
    { id: '5', name: 'Hindi' }
  ];

  useEffect(() => {
    fetchAllContentMappingService();
  }, []);

  const fetchAllContentMappingService = async () => {
    try {
      const res = await getAllContentMappingService().unwrap();
      dispatch(setAllContentMappingData(res));
    } catch (error) {
      setRes(error);
    }
  };

  useEffect(() => {
    const fetchTopicsByIdsService = async () => {
      try {
        const res = await getSubTopicDataService({
          course_id: selectedCourse.id,
          subject_id: selectedSubject.id,
          topic_id: selectedTopic.id
        }).unwrap();

        dispatch(setSubTopicData(res));
      } catch (error) {
        setRes(error);
      }
    };

    if (selectedCourse?.id && selectedSubject?.id && selectedTopic?.id) {
      fetchTopicsByIdsService();
    }
  }, [selectedCourse, selectedSubject, selectedTopic, getSubTopicDataService, dispatch]);

  useEffect(() => {
    const fetchTopicsByIdsService = async () => {
      try {
        const res = await getTopicDataService({
          course_id: selectedCourse.id,
          subject_id: selectedSubject.id
        }).unwrap();

        dispatch(setTopicData(res));
      } catch (error) {
        setRes(error);
      }
    };

    if (selectedCourse?.id && selectedSubject?.id) {
      fetchTopicsByIdsService();
    }
  }, [selectedCourse, selectedSubject, getTopicDataService, dispatch]);

  const handleCreateContent = async () => {
    try {
      const res = await createContentService({
        course_id: selectedCourse.id,
        course_name: selectedCourse.name,
        subject_id: selectedSubject.id,
        subject_name: selectedSubject.name,
        topic_id: selectedTopic.id,
        topic_name: selectedTopic.name,
        sub_topic_id: selectedSubTopic.id,
        sub_topic_name: selectedSubTopic.name,
        process_selector_url: url,
        language: language.name
      }).unwrap();
      // dispatch(setAllContentMappingData((prev) => [...prev, res]));
      fetchAllContentMappingService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setPopUp(false);
      setSelectedCourse({ id: '', name: '' });
      setSelectedSubject({ id: '', name: '' });
      setSelectedTopic({ id: '', name: '' });
      setSelectedSubTopic({ id: '', name: '' });
      setUrl('');
      setFile(null);
    }
  };

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('subject', selectedSubject.name);

      try {
        const res = await storeContentInS3Service(formData).unwrap();
        setUrl(res.fileUrl);
        setRes(res);
      } catch (err) {
        setRes(err);
      }
    }
  };

  const handleEditContent = async (e) => {
    setPopUp(true);
    setIsEdit(true);
    setSelectedCourse({ id: e.course_id, name: e.course_name });
    setSelectedSubject({ id: e.subject_id, name: e.subject_name });
    setSelectedTopic({ id: e.topic_id, name: e.topic_name });
    setSelectedSubTopic({ id: e.sub_topic_id, name: e.sub_topic_name });
    setUrl(e.process_selector_url);
    setLanguage({ id: e.language_id, name: e.language });
    setIsEditId(e.process_selector_id);
  };

  const handleUpdateContent = async () => {
    try {
      const res = await updateContentMappingService({
        process_selector_id: isEditId,
        course_id: selectedCourse.id,
        course_name: selectedCourse.name,
        subject_id: selectedSubject.id,
        subject_name: selectedSubject.name,
        topic_id: selectedTopic.id,
        topic_name: selectedTopic.name,
        sub_topic_id: selectedSubTopic.id,
        sub_topic_name: selectedSubTopic.name,
        process_selector_url: url,
        language: language.name
      }).unwrap();
      fetchAllContentMappingService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setPopUp(false);
      setSelectedCourse({ id: '', name: '' });
      setSelectedSubject({ id: '', name: '' });
      setSelectedTopic({ id: '', name: '' });
      setSelectedSubTopic({ id: '', name: '' });
      setUrl('');
      setFile(null);
    }
  };

  const handleDeleteContent = async (e) => {
    setConfirmPopUp(true);
    setIsDelete(true);
    setIsEditId(e.process_selector_id);
    console.log('e.content_id', e);
  };

  console.log(isEditId, 'isEditId');

  const handleDeleteContentService = async () => {
    try {
      const res = await deleteContentMappingService(isEditId).unwrap();
      fetchAllContentMappingService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setConfirmPopUp(false);
      setIsDelete(false);
    }
  };

  return (
    <div>
      <Toastify res={res} resClear={() => setRes(null)} />
      {popUp && (
        <PopUp
          title={isEdit ? 'Update Content Mapping' : 'Create Content Mapping'}
          onClose={() => {
            setPopUp(false);
            setSelectedCourse({ id: '', name: '' });
            setSelectedSubject({ id: '', name: '' });
            setSelectedTopic({ id: '', name: '' });
            setSelectedSubTopic({ id: '', name: '' });
            setUrl('');
            setFile(null);
          }}
          isEdit={isEdit}
          post={isEdit ? handleUpdateContent : handleCreateContent}
          width="lg">
          <div className="grid grid-cols-2 gap-2 mb-4">
            <SearchableDropdown
              label="Course Name"
              value={selectedCourse?.id}
              placeholder="Select/Search the Course Name"
              options={courseData}
              onChange={(e) => {
                setSelectedCourse(e);
                setSelectedSubject({ id: '', name: '' });
                setSelectedTopic({ id: '', name: '' });
              }}
              required
            />

            <SearchableDropdown
              label="Subject Name"
              value={selectedSubject?.id}
              placeholder="Select/Search the Subject Name"
              options={subjectData}
              onChange={(e) => {
                setSelectedSubject(e);
                setSelectedTopic({ id: '', name: '' });
              }}
              disabled={!selectedCourse?.id}
              required
            />

            <SearchableDropdown
              label="Topic Name"
              value={selectedTopic?.id}
              placeholder="Select/Search the Topic Name"
              options={topicData}
              onChange={(e) => setSelectedTopic(e)}
              disabled={!selectedCourse?.id || !selectedSubject?.id}
              required
            />
            <SearchableDropdown
              label="Sub Topic Name"
              value={selectedSubTopic?.id}
              placeholder="Select/Search the Topic Name"
              options={subTopicData}
              onChange={(e) => setSelectedSubTopic(e)}
              disabled={!selectedCourse?.id || !selectedSubject?.id}
              required
            />
            <Input
              type="file"
              name="document"
              label="Upload File"
              accept=".pdf,.jpg,.png"
              required
              placeholder="Click to select a file"
              onChange={handleFileChange}
              error={false}
            />
            <SearchableDropdown
              label="Languages"
              value={language.id || language.name}
              placeholder="Select/Search the Language"
              options={languages}
              onChange={(e) => setLanguage(e)}
              required
            />
          </div>

          <Input
            label="Content Url"
            value={url}
            // onChange={(e) => setUrl(e.target.value)}
            className="border rounded"
            disabled
            required
          />
        </PopUp>
      )}
      {confirmPopUp && (
        <PopUp
          onClose={() => {
            setConfirmPopUp(false);
            setIsDelete(false);
          }}
          width="sm"
          isDelete={isDelete}
          title={'Delete Content Mapping'}
          post={handleDeleteContentService}>
          <p className="text-xl text-center my-8">Are you want to delete the Content Mapping?</p>
        </PopUp>
      )}
      <Table
        title="Content Mapping"
        onAddNew={() => setPopUp(true)}
        buttonName="Create Content Mapping"
        header={ContentMappingHeader}
        data={allContentMappingData}
        searchBy={['course_name', 'subject_name', 'topic_name', 'sub_topic_name', 'language']}
        searchPlaceholder="Search by Course, Subject, Topic, Sub Topic or Language Name"
        onEdit={(e) => {
          handleEditContent(e);
        }}
        onDelete={(e) => {
          handleDeleteContent(e);
        }}
      />
    </div>
  );
};

export default ContentMapping;
