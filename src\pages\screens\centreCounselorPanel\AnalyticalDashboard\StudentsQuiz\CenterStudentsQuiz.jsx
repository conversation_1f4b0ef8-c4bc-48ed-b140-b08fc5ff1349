import React, { useEffect, useMemo, useState } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, FileChartPie, Search, X } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import {
  clearCenterStudentQuizData,
  setCenterStudentQuizData,
  useLazyGetCenterStudentQuizDataQuery
} from '../AnalyticalDashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';

// --- Reusable UI Components (THEME UPDATED) ---

const FilterInput = ({ icon, ...props }) => (
  <div className="relative">
    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-amber-600">
      {icon}
    </div>
    <input
      type="text"
      {...props}
      className="bg-white/70 border border-amber-400/50 text-amber-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block w-full pl-10 p-2.5 placeholder:text-amber-600"
    />
  </div>
);

const FilterSelect = ({ options, ...props }) => (
  <div className="relative">
    <select
      {...props}
      className="appearance-none w-full bg-white/70 border border-amber-400/50 text-amber-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block p-2.5 pr-8">
      {options.map((o) => (
        <option key={o} value={o}>
          {o}
        </option>
      ))}
    </select>
    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-amber-600">
      <ChevronDown size={16} />
    </div>
  </div>
);

const PaginationButton = ({ children, ...props }) => (
  <button
    {...props}
    className="px-3 py-1.5 text-sm font-medium text-amber-800 bg-amber-50 border border-amber-300 rounded-lg hover:bg-amber-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
    {children}
  </button>
);

const QuizDataPopup = ({ title, data, onClose }) => {
  const popupVariants = {
    hidden: { opacity: 0, scale: 0.9, y: 50 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { type: 'spring', stiffness: 300, damping: 25 }
    },
    exit: { opacity: 0, scale: 0.9, y: 50, transition: { duration: 0.2 } }
  };

  const scoreColor = (score) => {
    if (score >= 80) return 'text-emerald-600';
    if (score >= 50) return 'text-sky-600';
    return 'text-red-600';
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-slate-900/40 backdrop-blur-sm"
      onClick={onClose}>
      <motion.div
        variants={popupVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className="relative w-full max-w-4xl bg-white/80 backdrop-blur-xl rounded-2xl border border-amber-300/60 shadow-2xl m-4"
        onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-amber-300/60">
          <h3 className="text-lg font-semibold text-amber-900">{title}</h3>
          <button
            onClick={onClose}
            className="p-1.5 rounded-full text-amber-600 hover:bg-amber-100 hover:text-amber-800 transition-colors">
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-1 max-h-[70vh] overflow-y-auto">
          {!data || data.length === 0 ? (
            <div className="text-center py-16 text-amber-600">
              No AI Tutor Quiz data found for this student.
            </div>
          ) : (
            <table className="w-full min-w-[700px] text-sm">
              <thead className="sticky top-0 bg-white/80 backdrop-blur-sm">
                <tr>
                  {[
                    'Subject',
                    'Topic',
                    'Sub-Topic',
                    'Score',
                    'Correct',
                    'Incorrect',
                    'Total Qs'
                  ].map((h) => (
                    <th
                      key={h}
                      className="px-4 py-3 text-left text-xs font-semibold text-amber-700 uppercase tracking-wider">
                      {h}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-amber-300/60">
                {data.map((quiz, i) => (
                  <tr key={i} className="hover:bg-amber-100/50">
                    <td className="px-4 py-3 text-amber-900 font-medium">{quiz.subject}</td>
                    <td className="px-4 py-3 text-amber-800">{quiz.topic}</td>
                    <td className="px-4 py-3 text-amber-700">{quiz.sub_topic || '–'}</td>
                    <td className={`px-4 py-3 font-bold ${scoreColor(quiz.score)}`}>
                      {quiz.score}%
                    </td>
                    <td className="px-4 py-3 text-emerald-600 font-medium">{quiz.correctCount}</td>
                    <td className="px-4 py-3 text-red-600 font-medium">{quiz.wrongCount}</td>
                    <td className="px-4 py-3 text-amber-800 font-medium">{quiz.totalQuestions}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

const CenterStudentsQuiz = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('All');
  const [selectedBatch, setSelectedBatch] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [res, setRes] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [popupData, setPopupData] = useState([]);
  const dispatch = useDispatch();

  const [getCenterStudentsQuizData, { isLoading: isPopupLoading }] =
    useLazyGetCenterStudentQuizDataQuery();

  const studentsData = useSelector((state) => state.centreCounselorDashboard.studentsData);

  // Filter options
  const courses = useMemo(
    () => ['All', ...new Set(studentsData?.map((s) => s.course).filter(Boolean))],
    [studentsData]
  );
  const batches = useMemo(
    () => ['All', 'No Batch', ...new Set(studentsData?.map((s) => s.batchname).filter(Boolean))],
    [studentsData]
  );

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCourse('All');
    setSelectedBatch('All');
  };
  const hasActiveFilters = searchTerm !== '' || selectedCourse !== 'All' || selectedBatch !== 'All';

  // Filtered and paginated students
  const filteredStudents = useMemo(() => {
    if (!Array.isArray(studentsData)) return [];
    return studentsData.filter((student) => {
      const searchMatch = student.name.toLowerCase().includes(searchTerm.toLowerCase());
      const courseMatch = selectedCourse === 'All' || student.course === selectedCourse;
      const batchMatch =
        selectedBatch === 'All'
          ? true
          : selectedBatch === 'No Batch'
            ? !student.batchname
            : student.batchname === selectedBatch;
      return searchMatch && courseMatch && batchMatch;
    });
  }, [studentsData, searchTerm, selectedCourse, selectedBatch]);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedCourse, selectedBatch]);

  const totalPages = Math.ceil(filteredStudents.length / 10);
  const paginatedStudents = useMemo(() => {
    const startIndex = (currentPage - 1) * 10;
    return filteredStudents.slice(startIndex, startIndex + 10);
  }, [filteredStudents, currentPage]);

  const handleGetCenterStudentsQuizApi = async (student) => {
    try {
      const centerCode = student.centerCode || sessionStorage.centercode; // Fallback
      const res = await getCenterStudentsQuizData({
        center_code: centerCode,
        student_id: student.id
      }).unwrap();
      dispatch(setCenterStudentQuizData(res));
      setPopupData(res);
      setIsPopupOpen(true);
    } catch (error) {
      setRes(error);
      dispatch(clearCenterStudentQuizData());
      setIsPopupOpen(false);
    }
  };

  // Color helpers from AnalyticalDashboard for consistency
  const getCourseColor = (course) => {
    switch (course) {
      case 'JEE':
        return 'bg-sky-500/10 text-sky-700 border-sky-500/20';
      case 'NEET':
        return 'bg-emerald-500/10 text-emerald-700 border-emerald-500/20';
      default:
        return 'bg-amber-500/10 text-amber-700 border-amber-500/20';
    }
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />
      <AnimatePresence>
        {isPopupOpen && (
          <QuizDataPopup
            title="AI Tutor Quiz Results"
            data={popupData}
            onClose={() => setIsPopupOpen(false)}
          />
        )}
      </AnimatePresence>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/50 backdrop-blur-md rounded-2xl border border-amber-300/60 shadow-lg overflow-hidden">
        {/* Control Bar */}
        <div className="p-4 border-b border-amber-300/60">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <h2 className="text-xl font-semibold text-amber-900">Student AI Quiz Records</h2>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap">
              <FilterInput
                icon={<Search size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search..."
              />
              <FilterSelect
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                options={courses}
              />
              <FilterSelect
                value={selectedBatch}
                onChange={(e) => setSelectedBatch(e.target.value)}
                options={batches}
              />
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="flex items-center gap-1.5 text-sm text-red-600 hover:text-red-700 font-medium p-2 rounded-lg hover:bg-red-500/10 transition-colors">
                  <X size={14} /> Clear
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <AnimatePresence mode="wait">
            {paginatedStudents.length === 0 ? (
              <motion.div
                key="no-results"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-16">
                <Search size={48} className="mx-auto text-amber-500 mb-4" />
                <h3 className="text-lg font-medium text-amber-800">No Students Found</h3>
                <p className="text-amber-600">
                  Your search and filter combination yielded no results.
                </p>
              </motion.div>
            ) : (
              <motion.table
                key="results-table"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="w-full min-w-[900px]">
                <thead className="bg-amber-200/40">
                  <tr>
                    {['Name', 'Course', 'Gender', 'Grade', 'Batch Name', 'Analytics'].map((h) => (
                      <th
                        key={h}
                        className="px-6 py-3 text-left text-xs font-semibold text-amber-700 uppercase tracking-wider">
                        {h}
                      </th>
                    ))}
                  </tr>
                </thead>
                <motion.tbody
                  variants={{ visible: { transition: { staggerChildren: 0.03 } } }}
                  initial="hidden"
                  animate="visible"
                  className="divide-y divide-amber-300/60">
                  {paginatedStudents.map((student) => (
                    <motion.tr
                      key={student.id}
                      variants={{ hidden: { opacity: 0, x: -20 }, visible: { opacity: 1, x: 0 } }}
                      exit={{ opacity: 0, x: -20 }}
                      layout
                      className="hover:bg-amber-200/50 transition-colors">
                      <td className="px-6 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <div
                            className={`w-9 h-9 rounded-full mr-3 flex-shrink-0 flex items-center justify-center font-bold text-sm bg-gradient-to-br from-amber-500 to-orange-500 text-white`}>
                            {student.name.charAt(0).toUpperCase()}
                          </div>
                          <div className="text-sm font-medium text-amber-900 capitalize">
                            {student.name.toLowerCase()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-3 whitespace-nowrap">
                        <span
                          className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full border ${getCourseColor(student.course)}`}>
                          {student.course}
                        </span>
                      </td>
                      <td className="px-6 py-3 whitespace-nowrap">
                        <span
                          className={`text-sm font-medium ${
                            student.gender === 'MALE'
                              ? 'text-sky-600'
                              : student.gender === 'FEMALE'
                                ? 'text-rose-600'
                                : 'text-amber-600'
                          }`}>
                          {student.gender || 'N/A'}
                        </span>
                      </td>
                      <td className="px-6 py-3 whitespace-nowrap">
                        {student.grade ? (
                          <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-emerald-500/10 text-emerald-600 border border-emerald-500/20">
                            {student.grade}
                          </span>
                        ) : (
                          <span className="text-sm text-amber-600">N/A</span>
                        )}
                      </td>
                      <td className="px-6 py-3 whitespace-nowrap">
                        {student.batchname ? (
                          <span className="text-sm font-mono text-amber-700">
                            {student.batchname}
                          </span>
                        ) : (
                          <span className="text-sm text-amber-600">N/A</span>
                        )}
                      </td>
                      <td className="px-6 py-3 whitespace-nowrap text-center">
                        <button
                          onClick={() => handleGetCenterStudentsQuizApi(student)}
                          disabled={isPopupLoading}
                          className="p-2 text-orange-600 hover:bg-orange-500/10 rounded-full transition-colors disabled:opacity-50 disabled:cursor-wait"
                          title="View AI Quiz Details">
                          <FileChartPie size={20} />
                        </button>
                      </td>
                    </motion.tr>
                  ))}
                </motion.tbody>
              </motion.table>
            )}
          </AnimatePresence>
        </div>

        {/* Pagination */}
        <div className="bg-amber-200/40 px-6 py-3 border-t border-amber-300/60">
          <div className="flex items-center justify-between">
            <p className="text-sm text-amber-700">
              Showing{' '}
              <span className="font-bold text-amber-900">
                {paginatedStudents.length > 0 ? (currentPage - 1) * 10 + 1 : 0}
              </span>
              -
              <span className="font-bold text-amber-900">
                {Math.min(currentPage * 10, filteredStudents.length)}
              </span>{' '}
              of <span className="font-bold text-amber-900">{filteredStudents.length}</span>
            </p>
            <div className="flex gap-2">
              <PaginationButton
                onClick={() => setCurrentPage((p) => p - 1)}
                disabled={currentPage === 1}>
                Previous
              </PaginationButton>
              <PaginationButton
                onClick={() => setCurrentPage((p) => p + 1)}
                disabled={currentPage === totalPages || totalPages === 0}>
                Next
              </PaginationButton>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default CenterStudentsQuiz;
