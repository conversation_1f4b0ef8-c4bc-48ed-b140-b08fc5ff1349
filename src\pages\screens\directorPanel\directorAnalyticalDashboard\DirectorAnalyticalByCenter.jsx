import React, { useEffect, useMemo, useState } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router';
import {
  ArrowLeft,
  ArrowRight,
  Award,
  BookOpen,
  Brain,
  Clock,
  Monitor,
  Target,
  TrendingDown,
  TrendingUp,
  Users,
  Users2,
  <PERSON><PERSON><PERSON>,
  Frown
} from 'lucide-react';
import clsx from 'clsx';
import { useDispatch, useSelector } from 'react-redux';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

import Button from '../../../../components/Field/Button';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  clearCenterBasedAnalyticsData,
  setCenterBasedAnalyticsData,
  useLazyGetCenterBasedAnalyticsServiceQuery
} from './directorAnalyticalDashboard.slice';

// --- Reusable Sub-components (No changes needed here) ---
// eslint-disable-next-line
const StatCard = ({ icon: Icon, title, value, change, trend, color, index }) => {
  // ... same as before
  const theme = {
    blue: { iconBg: 'bg-blue-100', text: 'text-blue-600' },
    green: { iconBg: 'bg-emerald-100', text: 'text-emerald-600' },
    purple: { iconBg: 'bg-purple-100', text: 'text-purple-600' },
    orange: { iconBg: 'bg-orange-100', text: 'text-orange-600' }
  };
  const trendTheme = trend === 'up' ? 'text-emerald-600' : 'text-red-500';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.08, ease: 'easeOut' }}
      className="bg-white p-6 rounded-2xl border border-slate-200/80 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
      <div className="flex items-start justify-between">
        <div
          className={`w-12 h-12 rounded-xl flex items-center justify-center ${theme[color].iconBg}`}>
          <Icon className={`w-6 h-6 ${theme[color].text}`} />
        </div>
      </div>
      <div className="mt-4">
        <h3 className="text-4xl font-bold text-slate-800">{value}</h3>
        <p className="text-sm font-medium text-slate-500 mt-1">{title}</p>
      </div>
      <div className="mt-5 flex items-center gap-1 text-sm">
        <div className={`flex items-center gap-1 font-semibold ${trendTheme}`}>
          {trend === 'up' ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
          <span>{change}</span>
        </div>
        <span className="text-slate-400">vs last month</span>
      </div>
    </motion.div>
  );
};
// eslint-disable-next-line
const TestTypeCard = ({ icon: Icon, name, stats, color, index }) => (
  // ... same as before
  <motion.button
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay: 0.2 + index * 0.1, ease: 'easeOut' }}
    whileHover={{ y: -6, scale: 1.02, boxShadow: '0 10px 20px -5px rgba(0,0,0,0.07)' }}
    className={clsx(
      'bg-white rounded-2xl shadow-sm border border-slate-200/80 text-left p-6 group w-full flex flex-col h-full',
      `border-l-4 ${color.border}`
    )}>
    <div className="flex items-center gap-4 mb-5">
      <div className={clsx('p-3 rounded-lg', color.iconBg)}>
        <Icon className={clsx('w-6 h-6', color.text)} />
      </div>
      <h3 className="text-lg font-bold text-slate-800">{name}</h3>
    </div>
    <ul className="space-y-3 text-sm mb-6">
      {stats.map((stat) => (
        <li key={stat.label} className="flex justify-between items-center text-slate-600">
          <span>{stat.label}</span>
          <span className="font-semibold text-slate-800 bg-slate-100/80 px-2 py-0.5 rounded-md">
            {stat.value}
          </span>
        </li>
      ))}
    </ul>
    <div className="mt-auto pt-4 border-t border-slate-100">
      <span className="flex items-center gap-2 text-sm font-semibold text-blue-600">
        View Details
        <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
      </span>
    </div>
  </motion.button>
);

const SectionHeader = ({ title, description, icon: Icon }) => (
  // ... same as before
  <motion.div
    initial={{ opacity: 0, y: -10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.1 }}
    className="flex items-center gap-4">
    {Icon && (
      <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
        <Icon className="w-6 h-6 text-slate-600" />
      </div>
    )}
    <div>
      <h2 className="text-2xl font-bold text-slate-800">{title}</h2>
      <p className="text-slate-500 mt-1">{description}</p>
    </div>
  </motion.div>
);

const DashboardSkeleton = () => (
  // ... same as before
  <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8 space-y-12 animate-pulse">
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-slate-200 h-48 rounded-2xl"></div>
      ))}
    </div>
    <div className="space-y-6">
      <div className="h-8 w-1/3 bg-slate-200 rounded-lg"></div>
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-slate-200 h-64 rounded-2xl"></div>
        ))}
      </div>
    </div>
    <div className="bg-slate-200 h-96 rounded-2xl"></div>
  </div>
);

const EmptyState = ({ onRetry }) => (
  // ... same as before
  <div className="text-center py-20">
    <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full mb-6">
      <Frown className="w-10 h-10 text-red-500" />
    </div>
    <h3 className="text-xl font-bold text-slate-800">No Data Found</h3>
    <p className="text-slate-500 mt-2 mb-6">We couldn't find any analytics data for this center.</p>
    <Button label="Try Again" onClick={onRetry} />
  </div>
);

// Highcharts custom theme ... same as before
const highchartsTheme = {
  colors: ['#0ea5e9', '#8b5cf6', '#10b981', '#f97316', '#ef4444', '#3b82f6'],
  chart: { backgroundColor: 'transparent', style: { fontFamily: 'Inter, sans-serif' } },
  title: { style: { color: '#1e293b', fontSize: '18px', fontWeight: 'bold' } },
  subtitle: { style: { color: '#64748b' } },
  xAxis: { labels: { style: { color: '#64748b' } }, lineColor: '#e2e8f0', tickColor: '#e2e8f0' },
  yAxis: {
    gridLineColor: '#e2e8f0',
    labels: { style: { color: '#64748b' } },
    title: { style: { color: '#475569' } }
  },
  legend: { itemStyle: { color: '#475569' }, itemHoverStyle: { color: '#1e293b' } },
  tooltip: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e2e8f0',
    borderRadius: 12,
    borderWidth: 1,
    shadow: true,
    style: { color: '#334155' }
  },
  plotOptions: {
    series: { marker: { radius: 4, symbol: 'circle' }, lineWidth: 2.5 },
    line: { dataLabels: { enabled: false }, enableMouseTracking: true }
  }
};
Highcharts.setOptions(highchartsTheme);

// --- Main Component ---
const DirectorAnalyticalByCenter = () => {
  // ... all hooks and logic remain exactly the same
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const [res, setRes] = useState(null);

  const [getCenterBasedAnalytics, { isLoading, isFetching }] =
    useLazyGetCenterBasedAnalyticsServiceQuery();
  const centerBasedAnalyticsData = useSelector(
    (state) => state.directorAnalyticsDashboard.centerBasedAnalyticsData
  );

  useEffect(() => {
    if (location.state?.center_code) {
      handleGetCenterBasedAnalyticsApi(location.state.center_code);
    } else {
      Toastify.error('Center information is missing.');
      navigate(-1);
    }
  }, [location.state?.center_code]);

  const handleGetCenterBasedAnalyticsApi = async (center_code) => {
    try {
      const result = await getCenterBasedAnalytics({ center_code }).unwrap();
      dispatch(setCenterBasedAnalyticsData(result));
    } catch (error) {
      setRes(error);
      dispatch(clearCenterBasedAnalyticsData());
    }
  };

  const cardData = useMemo(() => {
    // ... same as before
    const stats = centerBasedAnalyticsData?.dashboardStats?.overall;
    return [
      {
        title: 'Total Tests Completed',
        value: stats?.totalTests ?? 'N/A',
        change: '+12%',
        trend: 'up',
        icon: Target,
        color: 'blue'
      },
      {
        title: 'Average Score',
        value: `${stats?.avgScorePercentage?.toFixed(1) ?? 'N/A'}%`,
        change: '+3.2%',
        trend: 'up',
        icon: Award,
        color: 'green'
      },
      {
        title: 'Active Students',
        value: stats?.activeStudents ?? 'N/A',
        change: '+8%',
        trend: 'up',
        icon: Users,
        color: 'purple'
      },
      {
        title: 'Avg. Completion Time',
        value: stats?.avgCompletionTime ?? 'N/A',
        change: '-5%',
        trend: 'down',
        icon: Clock,
        color: 'orange'
      }
    ];
  }, [centerBasedAnalyticsData]);

  const testTypeData = useMemo(() => {
    // ... same as before
    const breakdown = centerBasedAnalyticsData?.dashboardStats?.breakdown;
    return [
      {
        name: 'CBT',
        icon: Monitor,
        color: { border: 'border-blue-500', iconBg: 'bg-blue-100', text: 'text-blue-600' },
        stats: [
          { label: 'Total Tests', value: breakdown?.cbt?.totalTests ?? 0 },
          { label: 'Avg. Score', value: `${breakdown?.cbt?.avgScorePercentage?.toFixed(1) ?? 0}%` },
          { label: 'Students', value: breakdown?.cbt?.activeStudents ?? 0 }
        ]
      },
      {
        name: 'Live Quiz',
        icon: Users2,
        color: { border: 'border-purple-500', iconBg: 'bg-purple-100', text: 'text-purple-600' },
        stats: [
          { label: 'Total Tests', value: breakdown?.liveQuiz?.totalTests ?? 0 },
          {
            label: 'Avg. Score',
            value: `${breakdown?.liveQuiz?.avgScorePercentage?.toFixed(1) ?? 0}%`
          },
          { label: 'Students', value: breakdown?.liveQuiz?.activeStudents ?? 0 }
        ]
      },
      {
        name: 'Student Quiz',
        icon: Brain,
        color: { border: 'border-emerald-500', iconBg: 'bg-emerald-100', text: 'text-emerald-600' },
        stats: [
          { label: 'Total Tests', value: breakdown?.studentQuiz?.totalTests ?? 0 },
          {
            label: 'Avg. Score',
            value: `${breakdown?.studentQuiz?.avgScorePercentage?.toFixed(1) ?? 0}%`
          },
          { label: 'Students', value: breakdown?.studentQuiz?.activeStudents ?? 0 }
        ]
      },
      {
        name: 'Practice Tests',
        icon: BookOpen,
        color: { border: 'border-orange-500', iconBg: 'bg-orange-100', text: 'text-orange-600' },
        stats: [
          { label: 'Total Tests', value: breakdown?.practice?.totalTests ?? 'N/A' },
          {
            label: 'Avg. Score',
            value: `${breakdown?.practice?.avgScorePercentage?.toFixed(1) ?? 'N/A'}%`
          },
          { label: 'Students', value: breakdown?.practice?.activeStudents ?? 'N/A' }
        ]
      }
    ];
  }, [centerBasedAnalyticsData]);

  const lineChartOptions = useMemo(() => {
    // ... same as before
    const trendData = centerBasedAnalyticsData?.performanceTrend;
    if (!trendData) return {};

    const months = trendData.cbt?.trend.map((item) => item.month) ?? [];
    const series = Object.keys(trendData).map((key) => ({
      name: key.charAt(0).toUpperCase() + key.slice(1).replace('Quiz', ' Quiz'),
      data: trendData[key].trend.map((item) => item.avgScorePercentage)
    }));

    return {
      title: { text: 'Monthly Average Performance' },
      subtitle: { text: 'Comparison across test types' },
      xAxis: { categories: months },
      yAxis: { title: { text: 'Average Score (%)' }, max: 100, min: 0 },
      tooltip: {
        shared: true,
        crosshairs: true,
        headerFormat: '<b>{point.key}</b><br/>',
        pointFormat:
          '<span style="color:{series.color}">●</span> {series.name}: <b>{point.y:.1f}%</b><br/>'
      },
      series
    };
  }, [centerBasedAnalyticsData]);

  const isDataLoading = isLoading || isFetching;

  return (
    <div className="min-h-screen bg-slate-50">
      <Toastify res={res} resClear={() => setRes(null)} />

      {/* --- NEW ENHANCED HEADER --- */}
      <header className="bg-gradient-to-br from-[#7d1e1c] to-[#4a0e0d] sticky top-0 z-30 shadow-2xl shadow-[#4a0e0d]/20 overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Aurora effect */}
          <div className="absolute top-0 left-0 w-96 h-96 bg-rose-400/20 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-purple-400/10 rounded-full filter blur-3xl opacity-50"></div>

          {/* Optional: Subtle pattern overlay. Replace with your own SVG pattern. */}
          {/* <div className="absolute inset-0 bg-[url('/path/to/grid-pattern.svg')] opacity-[0.02]"></div> */}
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.div whileHover={{ scale: 1.1, rotate: -6 }} whileTap={{ scale: 0.9 }}>
                <Button
                  className="bg-white/10 backdrop-blur-md border border-white/20 p-3 rounded-xl hover:bg-white/20 transition-all duration-300 shadow-lg"
                  icon={<ArrowLeft className="text-white w-5 h-5" />}
                  onClick={() => navigate(-1)}
                />
              </motion.div>
              <div>
                <motion.h1
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                  className="text-3xl font-bold text-white tracking-tight [text-shadow:0_2px_4px_rgba(0,0,0,0.2)]">
                  Center Analytics
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="text-rose-100/80 mt-1">
                  {location.state?.name} ({location.state?.center_code})
                </motion.p>
              </div>
            </div>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="hidden md:flex items-center space-x-2 bg-green-400/10 px-4 py-2 rounded-full border border-green-400/30">
              <div className="w-2.5 h-2.5 bg-green-400 rounded-full animate-pulse shadow-md shadow-green-400/50"></div>
              <span className="text-green-300 text-sm font-medium">Live Data</span>
            </motion.div>
          </div>
        </div>
      </header>

      <main>
        <AnimatePresence mode="wait">
          {isDataLoading ? (
            <motion.div key="loader" exit={{ opacity: 0 }}>
              <DashboardSkeleton />
            </motion.div>
          ) : !centerBasedAnalyticsData ? (
            <motion.div key="empty" initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <EmptyState
                onRetry={() => handleGetCenterBasedAnalyticsApi(location.state.center_code)}
              />
            </motion.div>
          ) : (
            <motion.div
              key="data"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8 space-y-12">
              <section>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                  {cardData.map((metric, index) => (
                    <StatCard key={index} {...metric} index={index} />
                  ))}
                </div>
              </section>

              <section>
                <SectionHeader
                  title="Performance by Test Type"
                  description="A comprehensive overview across all test formats."
                  icon={PieChart}
                />
                <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6">
                  {testTypeData.map((testType, index) => (
                    <TestTypeCard key={index} {...testType} index={index} />
                  ))}
                </div>
              </section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5, ease: 'easeOut' }}>
                <div className="bg-white p-4 sm:p-6 rounded-2xl border border-slate-200/80 shadow-sm">
                  <HighchartsReact highcharts={Highcharts} options={lineChartOptions} />
                </div>
              </motion.section>
            </motion.div>
          )}
        </AnimatePresence>
      </main>
    </div>
  );
};

export default DirectorAnalyticalByCenter;
