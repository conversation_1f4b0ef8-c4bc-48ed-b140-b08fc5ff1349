

// LiveStreamFeedback.jsx — Beautiful, embedded, non-modal version WITH DATE DISPLAY

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageSquare, User, BookOpen, Target, CheckCircle, 
  AlertCircle, Clock, ArrowLeft, ArrowRight, Calendar 
} from 'lucide-react';
import { useGetLivestreamFeedbackDetailsMutation, useSubmitLivestreamFeedbackMutation } from './liveStreamFeedback.slice';

// ✅ Helper to format ISO timestamp to readable date
const formatDate = (isoString) => {
  if (!isoString) return 'N/A';
  const date = new Date(isoString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  }).format(date);
};

// ✅ Memoized FeedbackCard with SOFT PASTEL BACKGROUND + DATE DISPLAY
const FeedbackCard = React.memo(({ feedbackItem, onProvideFeedback, index }) => {
  const formattedDate = formatDate(feedbackItem.timestamp);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      whileHover={{ y: -5, boxShadow: "0 10px 30px rgba(0,0,0,0.08)" }}
      className="bg-gradient-to-br from-blue-50 to-indigo-50 backdrop-blur-sm rounded-xl shadow-md p-6 border border-blue-100 hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2.5 bg-blue-200/80 rounded-xl">
            <User className="w-5 h-5 text-blue-700" />
          </div>
          <div>
            <h3 className="font-bold text-gray-800 text-lg">{feedbackItem.student_name || 'Student Name'}</h3>
            <p className="text-sm text-blue-600 font-medium">Center: {feedbackItem.center_code || 'N/A'}</p>
            <div className="flex items-center space-x-1 mt-1">
              <Calendar className="w-3.5 h-3.5 text-gray-500" />
              <p className="text-xs text-gray-600">{formattedDate}</p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2 bg-orange-100 text-orange-700 px-3 py-1.5 rounded-full text-sm font-medium border border-orange-200">
          <Clock className="w-3.5 h-3.5" />
          <span>Pending</span>
        </div>
      </div>
      
      <div className="space-y-3 mb-6">
        <div className="flex items-center space-x-3 p-3 bg-white/60 backdrop-blur rounded-lg border border-blue-100">
          <BookOpen className="w-5 h-5 text-green-600 flex-shrink-0" />
          <div>
            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Teacher Name</span>
            <p className="text-sm font-medium text-gray-800 mt-0.5">{feedbackItem.kota_teacher_name || 'N/A'}</p>
          </div>
         </div>  
           <div className="flex items-center space-x-3 p-3 bg-white/60 backdrop-blur rounded-lg border border-blue-100">
           <BookOpen className="w-5 h-5 text-green-600 flex-shrink-0" />
           <div>
            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Subject</span>
            <p className="text-sm font-medium text-gray-800 mt-0.5">{feedbackItem.subject || 'N/A'}</p>
          </div>
        </div> 
        <div className="flex items-center space-x-3 p-3 bg-white/60 backdrop-blur rounded-lg border border-blue-100">
          <Target className="w-5 h-5 text-purple-600 flex-shrink-0" />
          <div>
            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Topic</span>
            <p className="text-sm font-medium text-gray-800 mt-0.5">{feedbackItem.topic || 'N/A'}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3 p-3 bg-white/60 backdrop-blur rounded-lg border border-blue-100">
          <MessageSquare className="w-5 h-5 text-blue-600 flex-shrink-0" />
          <div>
            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Chapter</span>
            <p className="text-sm font-medium text-gray-800 mt-0.5">{feedbackItem.chapter_name || 'N/A'}</p>
          </div>
        </div>
      </div>
      
      <motion.button
        whileHover={{ scale: 1.03, boxShadow: "0 5px 15px rgba(59, 130, 246, 0.3)" }}
        whileTap={{ scale: 0.97 }}
        onClick={() => onProvideFeedback(feedbackItem)}
        className="w-full py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 flex items-center justify-center space-x-2 shadow-md"
      >
        <MessageSquare className="w-4 h-4" />
        <span>Provide Feedback</span>
        <ArrowRight className="w-4 h-4" />
      </motion.button>
    </motion.div>
  );
});

// ✅ InputField (unchanged)
const InputField = React.memo(({ name, value, onChange, label, placeholder, type = 'text', rows = null, required = false }) => {
  const Component = rows ? 'textarea' : 'input';
  
  return (
    <div className="mb-6">
      <label className="block text-sm font-semibold text-gray-700 mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <Component
        type={rows ? undefined : type}
        name={name}
        value={value || ''}
        onChange={onChange}
        rows={rows}
        placeholder={placeholder}
        className="w-full px-4 py-3.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm resize-none shadow-sm"
        required={required}
      />
    </div>
  );
});

// ✅ RadioField (unchanged)
const RadioField = React.memo(({ name, value, onChange, label, options, required = false }) => {
  return (
    <div className="mb-6">
      <label className="block text-sm font-semibold text-gray-700 mb-3">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {options.map((option) => (
          <motion.label
            key={option.value}
            whileHover={{ scale: 1.02, y: -2 }}
            className={`flex items-center justify-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 min-h-[60px] shadow-sm ${
              value === option.value
                ? 'border-blue-500 bg-blue-50 text-blue-700 shadow-md ring-2 ring-blue-200'
                : 'border-gray-200 bg-white/70 hover:border-blue-300 hover:bg-blue-50/50 backdrop-blur'
            }`}
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={onChange}
              className="sr-only"
              required={required && !value}
            />
            <span className="font-medium text-center text-gray-800">{option.label}</span>
          </motion.label>
        ))}
      </div>
    </div>
  );
});

const LivestreamFeedback = ({ onClose }) => {
  const [activeView, setActiveView] = useState('list');
  const [selectedFeedback, setSelectedFeedback] = useState(null);
  const [pendingFeedbacks, setPendingFeedbacks] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  
  const [feedback, setFeedback] = useState({
    overall_rating: '',
    teacher_clarity: '',
    teacher_engagement: '',
    lesson_pace: '',
    lesson_difficulty: '',
    material_quality: '',
    would_recommend: '',
    teacher_preparation: '',
    lesson_objectives_met: '',
    interaction_level: '',
    liked_most: '',
    suggestions: '',
  });

  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [getLivestreamFeedbackDetails] = useGetLivestreamFeedbackDetailsMutation();
  const [submitLivestreamFeedback] = useSubmitLivestreamFeedbackMutation();

  const userId = useMemo(() => {
    return sessionStorage.getItem('userId') || 'default_user';
  }, []);

  // Fetch feedback on mount
  useEffect(() => {
    const fetchFeedbackDetails = async () => {
      if (!userId || userId === 'default_user') {
        setIsError(true);
        setErrorMessage('Please log in to provide feedback');
        setIsLoading(false);
        return;
      }

      try {
        const response = await getLivestreamFeedbackDetails(userId).unwrap();
        if (response && Array.isArray(response)) {
          const pending = response.filter(item => !item.feedback_submitted && item.session_id).slice(0, 3);
          setPendingFeedbacks(pending);
          if (pending.length === 0) {
            setIsError(true);
            setErrorMessage('No pending feedbacks available');
          }
        } else {
          setIsError(true);
          setErrorMessage('No feedback details available for this user');
        }
      } catch (error) {
        setIsError(true);
        setErrorMessage(error.message || 'Failed to load feedback details.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeedbackDetails();
  }, [userId, getLivestreamFeedbackDetails]);

  const handleProvideFeedback = useCallback((feedbackItem) => {
    setSelectedFeedback(feedbackItem);
    setActiveView('form');
    setFeedback({
      overall_rating: '',
      teacher_clarity: '',
      teacher_engagement: '',
      lesson_pace: '',
      lesson_difficulty: '',
      material_quality: '',
      would_recommend: '',
      teacher_preparation: '',
      lesson_objectives_met: '',
      interaction_level: '',
      liked_most: '',
      suggestions: '',
    });
    setStatus('');
  }, []);

  // Options (unchanged)
  const ratingOptions = useMemo(() => [{value:'1-3',label:'Poor'},{value:'4-6',label:'Good'},{value:'7-8',label:'Very Good'},{value:'9-10',label:'Excellent'}], []);
  const qualityOptions = useMemo(() => [{value:'poor',label:'Poor'},{value:'fair',label:'Fair'},{value:'good',label:'Good'},{value:'excellent',label:'Excellent'}], []);
  const clarityOptions = useMemo(() => [{value:'unclear',label:'Unclear'},{value:'somewhat_clear',label:'Somewhat Clear'},{value:'clear',label:'Clear'},{value:'very_clear',label:'Very Clear'}], []);
  const engagementOptions = useMemo(() => [{value:'low',label:'Low'},{value:'moderate',label:'Moderate'},{value:'high',label:'High'},{value:'excellent',label:'Excellent'}], []);
  const paceOptions = useMemo(() => [{value:'too_slow',label:'Too Slow'},{value:'just_right',label:'Just Right'},{value:'slightly_fast',label:'Slightly Fast'},{value:'too_fast',label:'Too Fast'}], []);
  const difficultyOptions = useMemo(() => [{value:'too_easy',label:'Too Easy'},{value:'appropriate',label:'Appropriate'},{value:'challenging',label:'Challenging'},{value:'too_difficult',label:'Too Difficult'}], []);
  const yesNoOptions = useMemo(() => [{value:'no',label:'No'},{value:'partially',label:'Partially'},{value:'mostly',label:'Mostly'},{value:'yes',label:'Yes'}], []);
  const recommendOptions = useMemo(() => [{value:'no',label:'No'},{value:'maybe',label:'Maybe'},{value:'likely',label:'Likely'},{value:'definitely',label:'Definitely'}], []);
  const responsivenessOptions = useMemo(() => [{value:'slow',label:'Slow'},{value:'adequate',label:'Adequate'},{value:'good',label:'Good'},{value:'excellent',label:'Excellent'}], []);

  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    setFeedback((prev) => ({ ...prev, [name]: value || '' }));
  }, []);

  const handleSubmit = async () => {
    if (!selectedFeedback) {
      setStatus('error');
      setErrorMessage('No feedback session selected');
      return;
    }
    
    setIsSubmitting(true);
    setErrorMessage('');

    const payload = {
      student_id: userId,
      session_id: selectedFeedback.session_id,
      ...feedback,
      kota_teacher_name: selectedFeedback.kota_teacher_name,
      language: selectedFeedback.language,
      subject: selectedFeedback.subject,
      topic: selectedFeedback.topic,
      stream_id: selectedFeedback.stream_id,
      chapter_name: selectedFeedback.chapter_name,
      course_name: selectedFeedback.course_name,
      center_code: selectedFeedback.center_code,
    };

    if (!payload.student_id || !payload.session_id || !payload.overall_rating) {
      setStatus('error');
      setErrorMessage('Please fill in all required fields.');
      setIsSubmitting(false);
      return;
    }

    try {
      await submitLivestreamFeedback(payload).unwrap();
      setStatus('success');
      setPendingFeedbacks(prev => prev.filter(item => item.session_id !== selectedFeedback.session_id));
    } catch (error) {
      setStatus('error');
      setErrorMessage(
        error.message || 'Failed to submit feedback. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackToList = useCallback(() => {
    setActiveView('list');
    setStatus('');
    setSelectedFeedback(null);
  }, []);

  // ✅ LOADING STATE
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <div className="animate-spin rounded-full h-14 w-14 border-4 border-blue-600 border-t-transparent mb-5"></div>
        <p className="text-gray-700 font-medium text-xl">Loading your feedback sessions...</p>
      </div>
    );
  }

  // ✅ ERROR STATE
  if (isError && activeView === 'list') {
    return (
      <div className="bg-white rounded-2xl p-10 flex flex-col items-center space-y-5 max-w-md mx-auto my-12 shadow-xl border border-gray-100">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          <AlertCircle className="w-8 h-8 text-red-500" />
        </div>
        <h3 className="text-xl font-bold text-gray-800">Oops!</h3>
        <p className="text-center text-gray-600 px-4">{errorMessage}</p>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onClose}
          className="mt-2 bg-blue-600 text-white px-8 py-3.5 rounded-xl font-semibold hover:bg-blue-700 transition-colors w-full shadow-md"
        >
          Close
        </motion.button>
      </div>
    );
  }

  // ✅ SUCCESS STATE
  if (status === 'success') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-2xl p-10 max-w-md mx-auto my-12 text-center shadow-2xl border border-gray-100"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring' }}
          className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6"
        >
          <CheckCircle className="w-10 h-10 text-green-600" />
        </motion.div>
        <h3 className="text-2xl font-bold text-gray-800 mb-3">Thank You!</h3>
        <p className="text-gray-600 mb-8">Your feedback helps us improve.</p>
        <div className="space-y-3">
          {pendingFeedbacks.length > 0 && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleBackToList}
              className="bg-blue-600 text-white px-7 py-3.5 rounded-xl font-semibold hover:bg-blue-700 transition-colors w-full shadow-md"
            >
              View Other Feedbacks
            </motion.button>
          )}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onClose}
            className="bg-gray-200 text-gray-800 px-7 py-3.5 rounded-xl font-semibold hover:bg-gray-300 transition-colors w-full"
          >
            Close
          </motion.button>
        </div>
      </motion.div>
    );
  }

  // ✅ FEEDBACK LIST VIEW
  if (activeView === 'list') {
    return (
      <div className="w-full max-w-6xl mx-auto px-4">
        <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 p-7 rounded-2xl text-white mb-10 shadow-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                <MessageSquare className="w-7 h-7" />
              </div>
              <div>
                <h2 className="text-2xl md:text-3xl font-extrabold tracking-tight">Livestream Feedback</h2>
                <p className="text-blue-100 text-lg mt-1 opacity-90">
                  You have <span className="font-bold">{pendingFeedbacks.length}</span> pending feedback{pendingFeedbacks.length !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
          </div>
        </div>

        {pendingFeedbacks.length === 0 ? (
          <div className="text-center py-20 bg-gradient-to-br from-green-50 to-emerald-50 rounded-3xl border border-green-200">
            <div className="mx-auto w-28 h-28 bg-green-200 rounded-full flex items-center justify-center mb-6">
              <CheckCircle className="w-14 h-14 text-green-700" />
            </div>
            <h3 className="text-3xl font-bold text-gray-800 mb-4">All Done!</h3>
            <p className="text-gray-600 text-xl mb-10 max-w-md mx-auto">You've completed all your feedback. Thank you for helping us improve!</p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onClose}
              className="bg-blue-600 text-white px-10 py-4 rounded-xl font-semibold text-lg hover:bg-blue-700 transition-all shadow-lg"
            >
              Close
            </motion.button>
          </div>
        ) : (
          <>
            <div className="text-center mb-10">
              <h3 className="text-2xl font-bold text-gray-800 mb-3">Pending Feedback Sessions</h3>
              <p className="text-gray-600 text-lg max-w-2xl mx-auto">Select a session below to provide your valuable feedback and help us enhance your learning experience.</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {pendingFeedbacks.map((item, index) => (
                <FeedbackCard 
                  key={item.session_id || index} 
                  feedbackItem={item} 
                  onProvideFeedback={handleProvideFeedback}
                  index={index}
                />
              ))}
            </div>
          </>
        )}
      </div>
    );
  }

  // ✅ FEEDBACK FORM VIEW — Also show date in session details
  return (
    <div className="w-full max-w-5xl mx-auto px-4 pb-12">
      <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-blue-700 p-6 rounded-2xl text-white mb-10 shadow-xl relative">
        <div className="flex items-center justify-between">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleBackToList}
            className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2.5 rounded-xl backdrop-blur transition-all"
          >
            <ArrowLeft className="w-5 h-5" />
            <span className="font-medium">Back</span>
          </motion.button>
          <h2 className="text-2xl font-bold text-center flex-1">Provide Feedback</h2>
        </div>
        <p className="text-purple-100 text-center mt-2 text-sm">Your insights help us create better learning experiences</p>
      </div>

      {/* Session Info with Date */}
      {selectedFeedback && (
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-10 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 backdrop-blur-sm"
        >
          <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center">
            <BookOpen className="w-6 h-6 mr-3 text-blue-600" />
            Session Details
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
            {[
              { label: 'Course', value: selectedFeedback.course_name, icon: BookOpen },
              { label: 'Subject', value: selectedFeedback.subject, icon: Target },
              { label: 'Topic', value: selectedFeedback.topic, icon: MessageSquare },
              { label: 'Chapter', value: selectedFeedback.chapter_name, icon: BookOpen },
              { label: 'Teacher', value: selectedFeedback.kota_teacher_name, icon: User },
              { label: 'Center', value: selectedFeedback.center_code, icon: Target },
              { label: 'Language', value: selectedFeedback.language, icon: BookOpen },
              { label: 'Session ID', value: selectedFeedback.session_id, icon: MessageSquare },
              { label: 'Stream ID', value: selectedFeedback.stream_id, icon: MessageSquare },
              { 
                label: 'Date & Time', 
                value: formatDate(selectedFeedback.timestamp), 
                icon: Calendar 
              },
            ].map((field, index) => (
              <div
                key={field.label}
                className="bg-white/70 p-4 rounded-xl border border-gray-200 hover:shadow-md transition-shadow backdrop-blur"
              >
                <div className="flex items-center space-x-2.5 mb-1.5">
                  <field.icon className="w-4.5 h-4.5 text-blue-600" />
                  <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">{field.label}</label>
                </div>
                <p className="text-gray-800 font-medium">{field.value || 'N/A'}</p>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      <div className="space-y-10">
        {[
          {
            title: "Overall Experience",
            icon: Target,
            color: "text-purple-600",
            fields: [
              { type: "radio", name: "overall_rating", label: "How would you rate the livestream session overall?", options: ratingOptions, required: true }
            ]
          },
          {
            title: "Teacher Evaluation",
            icon: User,
            color: "text-green-600",
            fields: [
              { type: "radio", name: "teacher_clarity", label: "How clear and understandable were the teacher's explanations?", options: clarityOptions, required: true },
              { type: "radio", name: "teacher_engagement", label: "How engaging and interactive was the teacher?", options: engagementOptions, required: true },
              { type: "radio", name: "teacher_preparation", label: "How well was the teacher prepared for the session?", options: qualityOptions, required: true },
              { type: "radio", name: "interaction_level", label: "How responsive was the teacher to questions and feedback?", options: responsivenessOptions, required: true }
            ]
          },
          {
            title: "Session Content",
            icon: BookOpen,
            color: "text-blue-600",
            fields: [
              { type: "radio", name: "lesson_pace", label: "How was the pace of the livestream session?", options: paceOptions, required: true },
              { type: "radio", name: "lesson_difficulty", label: "How appropriate was the difficulty level of the content?", options: difficultyOptions, required: true },
              { type: "radio", name: "material_quality", label: "How would you rate the quality of content and examples provided?", options: qualityOptions, required: true },
              { type: "radio", name: "lesson_objectives_met", label: "Do you feel the session helped you achieve your learning objectives?", options: yesNoOptions, required: true }
            ]
          },
          {
            title: "Additional Feedback",
            icon: MessageSquare,
            color: "text-purple-600",
            fields: [
              { type: "radio", name: "would_recommend", label: "Would you recommend this livestream session to other students?", options: recommendOptions, required: true },
              { type: "input", name: "liked_most", label: "What did you like most about the session?", placeholder: "e.g., The teacher's clear explanations, engaging teaching style, or helpful examples", rows: 4 },
              { type: "input", name: "suggestions", label: "What suggestions do you have to improve the session?", placeholder: "e.g., Include more practice problems, slow down the pace, add more visual aids, or extend Q&A time", rows: 4 }
            ]
          }
        ].map((section, sIndex) => (
          <motion.div
            key={section.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * (sIndex + 1) }}
          >
            <h3 className={`text-xl font-bold text-gray-800 mb-6 flex items-center ${section.color}`}>
              <section.icon className="w-6 h-6 mr-3" />
              {section.title}
            </h3>
            <div className="space-y-8">
              {section.fields.map((field, fIndex) => (
                field.type === "radio" ? (
                  <RadioField
                    key={field.name}
                    name={field.name}
                    value={feedback[field.name]}
                    onChange={handleChange}
                    label={field.label}
                    options={field.options}
                    required={field.required}
                  />
                ) : (
                  <InputField
                    key={field.name}
                    name={field.name}
                    value={feedback[field.name]}
                    onChange={handleChange}
                    label={field.label}
                    placeholder={field.placeholder}
                    rows={field.rows}
                  />
                )
              ))}
            </div>
          </motion.div>
        ))}

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="flex flex-col sm:flex-row justify-between gap-5 pt-8 border-t border-gray-200"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleBackToList}
            className="px-6 py-4 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-100 transition-all flex items-center justify-center space-x-2 w-full sm:w-auto shadow-sm"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to List</span>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleSubmit}
            disabled={isSubmitting || !feedback.overall_rating}
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2 w-full sm:w-auto shadow-md"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                <span>Submitting...</span>
              </>
            ) : (
              <>
                <CheckCircle className="w-5 h-5" />
                <span>Submit Feedback</span>
              </>
            )}
          </motion.button>
        </motion.div>

        {status === 'error' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-5 bg-red-50 border border-red-200 rounded-xl flex items-start space-x-3"
          >
            <AlertCircle className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-800 font-semibold mb-1">Submission Failed</h4>
              <p className="text-red-700 text-sm">{errorMessage}</p>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default LivestreamFeedback;