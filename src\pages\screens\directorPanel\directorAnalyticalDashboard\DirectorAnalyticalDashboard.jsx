import React, { useEffect, useState, useMemo } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import {
  clearCenterByData,
  setCenters,
  useLazyGetAllCentersServiceQuery
} from './directorAnalyticalDashboard.slice';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import {
  Building, Search, ChevronDown, ArrowRight, Signal, University, User, Mail, Phone, MapPin
} from 'lucide-react';
import Toastify from '../../../../components/PopUp/Toastify';

// --- Loading Skeleton ---
const LoadingSkeleton = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
    {[...Array(8)].map((_, i) => (
      <div key={i} className="bg-white rounded-2xl shadow-sm border border-slate-200 overflow-hidden animate-pulse">
        <div className="h-1.5 bg-slate-200"></div>
        <div className="p-6">
          <div className="flex justify-between items-start">
            <div className="w-3/4 h-6 bg-slate-200 rounded"></div>
            <div className="w-16 h-5 bg-slate-200 rounded-full"></div>
          </div>
          <div className="w-1/2 h-4 bg-slate-200 rounded mt-2 mb-6"></div>
          <div className="space-y-3">
            <div className="h-5 bg-slate-100 rounded"></div>
            <div className="h-5 bg-slate-100 rounded w-5/6"></div>
          </div>
          <div className="mt-6 pt-4 border-t border-slate-100 flex justify-end">
            <div className="h-5 w-28 bg-slate-200 rounded"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

// --- Reusable Sub-components ---
// eslint-disable-next-line
const InfoRow = ({ icon: Icon, text }) => (
    <li className="flex items-start gap-3 text-sm text-slate-600">
        <Icon className="w-4 h-4 text-[#7d1e1c] flex-shrink-0 mt-0.5" />
        <span className="truncate">{text}</span>
    </li>
);

const StatusBadge = ({ isActive }) => (
    <div className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-semibold ${
        isActive ? 'bg-emerald-100 text-emerald-800' : 'bg-red-100 text-red-800'
    }`}>
        <span className={`w-2 h-2 rounded-full ${isActive ? 'bg-emerald-500' : 'bg-red-500'}`}></span>
        {isActive ? 'Active' : 'Inactive'}
    </div>
);

// --- Center Card ---
const CenterCard = ({ center, index }) => {
  const navigate = useNavigate();
  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.98 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { delay: index * 0.05, type: 'spring', stiffness: 100, damping: 15 } },
    exit: { opacity: 0, scale: 0.95 }
  };

  return (
    <motion.div
      variants={cardVariants}
      whileHover={{ y: -8, scale: 1.03, boxShadow: '0 20px 25px -5px rgba(125, 30, 28, 0.1), 0 10px 10px -5px rgba(125, 30, 28, 0.08)' }}
      className="bg-white rounded-2xl shadow-sm border border-slate-200 flex flex-col group cursor-pointer overflow-hidden border-t-4 "
      onClick={() => navigate(`/sasthra/director/center/${center.center_code}`, { state: center })}
    >
      <div className="p-6 flex flex-col flex-grow">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 pr-2">
            <h3 className="text-lg font-bold text-slate-800 group-hover:text-[#7d1e1c] transition-colors">{center.name}</h3>
            <p className="text-sm text-slate-500">{center.center_code}</p>
          </div>
          <StatusBadge isActive={center.is_active} />
        </div>
        {/* <ul className="space-y-3 flex-grow mb-6">
          <InfoRow icon={User} text={center.username} />
          <InfoRow icon={Mail} text={center.email} />
        </ul> */}
        <div className="mt-auto pt-4 border-t border-slate-100 flex items-center justify-end">
          <span className="flex items-center gap-2 text-sm font-semibold text-[#7d1e1c]">
            View Analytics
            <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
          </span>
        </div>
      </div>
    </motion.div>
  );
};

// --- Empty State ---
const EmptyState = ({ onRefresh }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    className="text-center py-20 col-span-full"
  >
    <div className="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4">
      <University className="w-8 h-8 text-[#b58e8d]" />
    </div>
    <h3 className="text-2xl font-bold text-slate-800 mb-2">No Centers Found</h3>
    <p className="text-slate-500 max-w-md mx-auto mb-6">
      Try adjusting your search or refresh to fetch the latest data.
    </p>
    <button
      onClick={onRefresh}
      className="px-5 py-2.5 font-semibold text-white bg-[#7d1e1c] rounded-lg shadow-sm hover:bg-[#641816] transition-colors"
    >
      Refresh Data
    </button>
  </motion.div>
);

// --- Main Dashboard Component ---
const DirectorAnalyticalDashboard = () => {
  const [res, setRes] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name_asc');

  const [getAllCenters] = useLazyGetAllCentersServiceQuery();
  const dispatch = useDispatch();
  const centerData = useSelector((state) => state.directorAnalyticsDashboard.centers);

  useEffect(() => {
    handleGetAllCentersService();
    dispatch(clearCenterByData());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleGetAllCentersService = async () => {
    try {
      setIsLoading(true);
      const centers = await getAllCenters().unwrap();
      dispatch(setCenters(centers));
    } catch (error) {
      setRes(error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredAndSortedCenters = useMemo(() => {
    if (!centerData) return [];
    const filtered = centerData.filter(
      (center) =>
        center.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        center.center_code.toLowerCase().includes(searchTerm.toLowerCase())
    );
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name_asc': return a.name.localeCompare(b.name);
        case 'name_desc': return b.name.localeCompare(a.name);
        case 'code_asc': return a.center_code.localeCompare(b.center_code);
        case 'code_desc': return b.center_code.localeCompare(a.center_code);
        default: return 0;
      }
    });
  }, [centerData, searchTerm, sortBy]);

  return (
    <div className="min-h-screen bg-slate-50 p-4 md:p-6 lg:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center gap-4">
            <div className="p-3 bg-red-100 rounded-xl text-[#7d1e1c]">
                <Building size={28} />
            </div>
            <div>
                <h1 className="text-3xl md:text-4xl font-bold text-slate-800">Centers Dashboard</h1>
                <p className="text-slate-500 mt-1">An overview of all operational centers.</p>
            </div>
        </div>
      </motion.header>

      {/* Controls: Search and Sort */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0, transition: { delay: 0.1 } }}
        className="mb-8 p-4 bg-white rounded-xl border border-slate-200 shadow-sm flex flex-col md:flex-row items-center justify-between gap-4"
      >
        <div className="relative w-full md:w-1/2 lg:w-1/3">
          <Search className="absolute left-3.5 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
          <input
            type="text"
            placeholder="Search by name or code..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-11 pr-4 py-2.5 bg-slate-50 border border-slate-200 rounded-lg focus:ring-2 focus:ring-[#7d1e1c]/50 focus:border-[#7d1e1c] transition-colors"
          />
        </div>

        <div className="flex items-center gap-4 w-full md:w-auto">
          {!isLoading && (
            <div className="hidden sm:flex items-center px-4 py-2 bg-red-50 text-[#7d1e1c] rounded-lg text-sm font-semibold border border-red-100">
              <Signal className="w-4 h-4 mr-2" />
              {filteredAndSortedCenters.length} Result{filteredAndSortedCenters.length !== 1 && 's'}
            </div>
          )}
          <div className="relative w-full md:w-auto">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="appearance-none w-full bg-white pl-4 pr-10 py-2.5 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#7d1e1c]/50 focus:border-[#7d1e1c] transition-colors"
            >
              <option value="name_asc">Sort by Name (A-Z)</option>
              <option value="name_desc">Sort by Name (Z-A)</option>
              <option value="code_asc">Sort by Code (Asc)</option>
              <option value="code_desc">Sort by Code (Desc)</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />
          </div>
        </div>
      </motion.div>

      <main>
        {isLoading ? (
          <LoadingSkeleton />
        ) : filteredAndSortedCenters.length > 0 ? (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
            initial="hidden"
            animate="visible"
          >
            <AnimatePresence>
              {filteredAndSortedCenters.map((center, i) => (
                <CenterCard key={center.center_code || i} center={center} index={i} />
              ))}
            </AnimatePresence>
          </motion.div>
        ) : (
          <EmptyState onRefresh={handleGetAllCentersService} />
        )}
      </main>
    </div>
  );
};

export default DirectorAnalyticalDashboard;