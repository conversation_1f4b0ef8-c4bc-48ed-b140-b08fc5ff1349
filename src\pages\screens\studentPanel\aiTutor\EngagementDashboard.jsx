import React, { useEffect, useState } from 'react';
import { X, BarChart3, Users, Activity, Trophy, Clock, Target, TrendingUp } from 'lucide-react';

const FLASK_API_URL = `${import.meta.env.VITE_BASE_URL}`;

const EngagementDashboard = ({ quizId, onClose }) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `${FLASK_API_URL}/content/engagement_dashboard?quiz_id=${quizId}`
        );
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || `Server error ${response.status}`);
        }

        setDashboardData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (quizId) {
      fetchDashboardData();
    }
  }, [quizId]);

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <BarChart3 size={24} />
            Engagement Dashboard
          </h2>
          <button onClick={onClose} className="text-white hover:text-blue-400">
            <X size={24} />
          </button>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-white text-lg">Loading dashboard data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-white to-gray-50 backdrop-blur-sm rounded-xl p-8 border border-green-200 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <BarChart3 size={24} />
            Engagement Dashboard
          </h2>
          <button onClick={onClose} className="text-gray-800 hover:text-green-600">
            <X size={24} />
          </button>
        </div>
        <div className="text-center text-red-600 p-4">
          <p>Error loading dashboard: {error}</p>
        </div>
      </div>
    );
  }

  const getPerformanceColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-green-500';
    if (percentage >= 40) return 'text-green-400';
    return 'text-red-600';
  };

  const getPerformanceMessage = (percentage) => {
    if (percentage >= 80) return 'Excellent';
    if (percentage >= 60) return 'Good';
    if (percentage >= 40) return 'Average';
    return 'Needs Improvement';
  };

  const getRankColor = (rank) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500';
      case 3:
        return 'bg-gradient-to-r from-yellow-600 to-yellow-800';
      default:
        return 'bg-gradient-to-r from-blue-400 to-blue-600';
    }
  };

  const getRankBadge = (rank) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `#${rank}`;
    }
  };

  return (
    <div className="bg-gradient-to-br from-white to-gray-50 backdrop-blur-sm rounded-xl p-8 border border-green-200 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
          <BarChart3 size={24} />
          Engagement Dashboard
        </h2>
        <button onClick={onClose} className="text-gray-800 hover:text-green-600">
          <X size={24} />
        </button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Users className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Active Students</h3>
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {dashboardData?.active_students || 0}
          </div>
          <div className="text-sm text-gray-600">Currently participating</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Clock className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Avg Response Time</h3>
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {dashboardData?.avg_response_time?.toFixed(1) || 0}s
          </div>
          <div className="text-sm text-gray-600">Per question</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Target className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Overall Accuracy</h3>
          </div>
          <div
            className={`text-3xl font-bold ${getPerformanceColor(dashboardData?.overall_accuracy || 0)}`}
          >
            {dashboardData?.overall_accuracy?.toFixed(1) || 0}%
          </div>
          <div className="text-sm text-gray-600">
            {getPerformanceMessage(dashboardData?.overall_accuracy || 0)}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Activity className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Total Responses</h3>
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {dashboardData?.total_answers_recorded || 0}
          </div>
          <div className="text-sm text-gray-600">Answers recorded</div>
        </div>
      </div>

      {/* Table Style Leaderboard */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-6">
            <Trophy className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">🏆 Leaderboard</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-green-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-800">Rank</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-800">Student</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-800">Score</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-800">Accuracy</th>
                </tr>
              </thead>
              <tbody>
                {dashboardData?.leaderboard?.map((student, index) => (
                  <tr
                    key={index}
                    className="border-b border-green-100 hover:bg-green-50 transition-all duration-300 animate-slideInLeft"
                    style={{
                      animationDelay: `${index * 0.3}s`,
                      animationFillMode: 'both'
                    }}
                  >
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <span
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${getRankColor(student.rank)}`}
                        >
                          {student.rank}
                        </span>
                        <span className="text-lg">{getRankBadge(student.rank)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-semibold text-gray-800">{student.student_name}</div>
                      <div className="text-sm text-gray-600">
                        {student.questions_answered} questions
                      </div>
                    </td>
                    <td className="py-4 px-4 text-center">
                      <div className="text-xl font-bold text-green-600">{student.score}</div>
                      <div className="text-sm text-gray-600">points</div>
                    </td>
                    <td className="py-4 px-4 text-center">
                      <div
                        className={`text-lg font-bold ${getPerformanceColor(student.accuracy_percentage)}`}
                      >
                        {student.accuracy_percentage.toFixed(1)}%
                      </div>
                      <div className="text-sm text-gray-600">
                        {student.correct_responses} correct
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Student Performance Details */}
        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Student Performance</h3>
          </div>
          <div className="space-y-4">
            {dashboardData?.leaderboard?.map((student, index) => (
              <div
                key={index}
                className="bg-gray-50 rounded-lg p-4 border border-green-100 transform transition-all duration-300 hover:scale-105"
              >
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-2">
                    <span
                      className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${getRankColor(student.rank)}`}
                    >
                      {student.rank}
                    </span>
                    <span className="text-gray-800 font-semibold">{student.student_name}</span>
                  </div>
                  <span className="text-gray-600">{student.questions_answered} questions</span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Correct: </span>
                    <span className="text-green-600 font-bold">{student.correct_responses}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Avg Time: </span>
                    <span className="text-green-600 font-bold">
                      {student.avg_response_time_seconds.toFixed(1)}s
                    </span>
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-xs text-gray-500">{student.performance_message}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes slideInLeft {
          0% {
            opacity: 0;
            transform: translateX(-100%);
          }
          100% {
            opacity: 1;
            transform: translateX(0);
          }
        }

        .animate-slideInLeft {
          animation: slideInLeft 0.8s ease-out;
        }
      `}</style>
    </div>
  );
};

export default EngagementDashboard;
