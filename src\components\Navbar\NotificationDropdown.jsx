import { Link } from "react-router"
import { <PERSON>, <PERSON>, CheckCircle2 } from "lucide-react"
// eslint-disable-next-line
import { motion, AnimatePresence } from "framer-motion"

const NotificationDropdown = ({
  notifications,
  showNotifications,
  setShowNotifications,
  setIsProfileOpen,
  handleNotificationClick,
  unreadNotifications,
  setUnreadNotifications,
}) => {
  return (
    <div className="relative">
      <motion.button
        whileHover={{ scale: 1.05, rotate: [0, -10, 10, 0] }}
        whileTap={{ scale: 0.95 }}
        onClick={() => {
          setShowNotifications(!showNotifications)
          setIsProfileOpen(false)
          setUnreadNotifications(0)
        }}
        className="relative p-3 rounded-xl bg-gradient-to-br from-slate-700 to-slate-800 hover:from-slate-600 hover:to-slate-700 shadow-lg hover:shadow-xl transition-all duration-200 group cursor-pointer"
      >
        <Bell size={20} className="text-white group-hover:text-blue-200 transition-colors duration-200" />
        {unreadNotifications > 0 && (
          <motion.span
            initial={{ scale: 0, rotate: 180 }}
            animate={{ scale: 1, rotate: 0 }}
            className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center shadow-lg ring-2 ring-white"
          >
            {unreadNotifications > 99 ? "99+" : unreadNotifications}
          </motion.span>
        )}
      </motion.button>

      <AnimatePresence>
        {showNotifications && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-30"
              onClick={() => setShowNotifications(false)}
            />

            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="absolute right-0 mt-3 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden z-40"
            >
              <div className="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100/50">
                <div className="flex items-center justify-between">
                  <h3 className="font-bold text-gray-800 text-lg">Notifications</h3>
                  {unreadNotifications > 0 && (
                    <span className="px-3 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full">
                      {unreadNotifications} new
                    </span>
                  )}
                </div>
              </div>

              <div className="max-h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
                {notifications.length > 0 ? (
                  <div className="divide-y divide-gray-100/50">
                    {notifications.map((notification, index) => (
                      <motion.div
                        key={notification.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        whileHover={{ backgroundColor: "rgba(59, 130, 246, 0.05)" }}
                        onClick={() => handleNotificationClick(notification.id)}
                        className={`px-6 py-4 cursor-pointer transition-all duration-200 relative group ${
                          !notification.read
                            ? "bg-gradient-to-r from-blue-50/50 to-transparent border-l-4 border-blue-400"
                            : "hover:bg-gray-50/50"
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div
                            className={`mt-1 p-1.5 rounded-full ${
                              !notification.read ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-400"
                            }`}
                          >
                            {!notification.read ? <Bell size={12} /> : <CheckCircle2 size={12} />}
                          </div>

                          <div className="flex-1 min-w-0">
                            <p
                              className={`text-sm leading-relaxed ${
                                !notification.read ? "text-gray-900 font-medium" : "text-gray-700"
                              }`}
                            >
                              {notification.text}
                            </p>
                            <div className="flex items-center gap-1 mt-2">
                              <Clock size={12} className="text-gray-400" />
                              <p className="text-xs text-gray-500">{notification.time}</p>
                            </div>
                          </div>

                          {!notification.read && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="w-2 h-2 bg-blue-500 rounded-full mt-2"
                            />
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="px-6 py-12 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                      <Bell size={24} className="text-gray-400" />
                    </div>
                    <p className="text-gray-500 text-sm font-medium">No notifications yet</p>
                    <p className="text-gray-400 text-xs mt-1">We'll notify you when something arrives</p>
                  </div>
                )}
              </div>

              <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-blue-50/30 border-t border-gray-100/50">
                <Link
                  to="/notifications"
                  className="block w-full text-center py-2.5 px-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm font-semibold rounded-xl transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]"
                >
                  View all notifications
                </Link>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}

export default NotificationDropdown