import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { setCbt, useLazyGetCbtServiceQuery } from '../dashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';
import { useDispatch, useSelector } from 'react-redux';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

// Import the module for its side effects to enable radar charts
import 'highcharts/highcharts-more';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import {
  BookA,
  ChevronDown,
  RefreshCw,
  X,
  Info,
  Eye,
  TrendingUp,
  Target,
  Award,
  BarChart3,
  Lightbulb,
  CheckCircle,
  XCircle,
  Sparkles,
  PieChart,
  GitCommit
} from 'lucide-react';

// --- Constants ---
const ALL_SUBJECTS = 'allsubjects';
const PRIMARY_GRADIENT = 'bg-gradient-to-br from-blue-500 to-indigo-600';

// --- Highcharts Global Config with Accessibility Warning Disabled ---
Highcharts.setOptions({
  chart: { backgroundColor: 'transparent', style: { fontFamily: 'Inter, sans-serif' } },
  credits: { enabled: false },
  title: { text: null },
  legend: { itemStyle: { color: '#64748b' }, itemHoverStyle: { color: '#1e293b' } },
  xAxis: { labels: { style: { color: '#64748b' } }, lineColor: '#e2e8f0', tickColor: '#e2e8f0' },
  yAxis: {
    gridLineColor: '#f1f5f9',
    labels: { style: { color: '#94a3b8' } },
    title: { style: { color: '#94a3b8' } }
  },
  tooltip: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#e2e8f0',
    style: { color: '#334155' },
    headerFormat: '<span style="font-size: 10px">{point.key}</span><br/>',
    shadow: true
  },

  // --- THIS IS THE FIX ---
  // Disables the accessibility module and removes the warning from the console.
  accessibility: {
    enabled: false
  }
});

// --- Helper & State Components (No changes needed here) ---
const CbtSkeleton = () => (
  <div className="animate-pulse space-y-6">
    <div className="flex items-center justify-between">
      <div className="h-10 w-48 bg-slate-200 rounded-lg"></div>
      <div className="h-10 w-64 bg-slate-200 rounded-lg"></div>
    </div>
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-28 bg-slate-200 rounded-xl"></div>
      ))}
    </div>
    <div className="h-80 bg-slate-200 rounded-xl"></div>
    <div className="h-64 bg-slate-200 rounded-xl"></div>
  </div>
);
const ErrorDisplay = ({ error, onRetry }) => (
  <div className="flex flex-col items-center justify-center text-center p-8 bg-red-50/50 border border-red-200 rounded-xl">
    <div className="w-14 h-14 flex items-center justify-center bg-red-100 rounded-full mb-4">
      <X className="w-8 h-8 text-red-600" />
    </div>
    <h3 className="text-xl font-semibold text-red-800">Failed to Load Data</h3>
    <p className="text-red-600 mt-2 mb-4 max-w-md">
      {error?.data?.message || 'An unexpected error occurred.'}
    </p>
    <button
      onClick={onRetry}
      className="flex items-center px-4 py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 transition-colors shadow-sm">
      <RefreshCw className="w-4 h-4 mr-2" /> Try Again
    </button>
  </div>
);
const EmptyState = ({ message }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    className="text-center p-12 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/30 shadow-lg shadow-blue-500/10">
    <div className="w-14 h-14 flex items-center justify-center bg-slate-100 rounded-full mb-4 mx-auto">
      <Info className="w-8 h-8 text-slate-400" />
    </div>
    <h3 className="text-xl font-semibold text-slate-700">No Tests Found</h3>
    <p className="text-slate-500 mt-2">{message}</p>
  </motion.div>
);
const TestReviewPopup = ({ testData, onClose }) => {
  const summary = testData?.evaluation_results?.score_summary;
  const feedback = testData?.evaluation_results;
  const InsightCard = ({ icon, title, items, colorClass }) => (
    <div className="bg-slate-100/70 p-4 rounded-xl border border-slate-200/50">
      <div className={`flex items-center gap-3 mb-3`}>
        <div className={`p-1.5 rounded-full bg-white shadow-sm ${colorClass.text}`}>{icon}</div>
        <h4 className="font-bold text-slate-800">{title}</h4>
      </div>
      <ul className="space-y-2 text-sm text-slate-600 list-disc pl-5">
        {items?.map((item, index) => (
          <li key={index}>{item}</li>
        ))}
      </ul>
    </div>
  );
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-md p-4"
      onClick={onClose}>
      <motion.div
        initial={{ scale: 0.95, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.95, y: 20 }}
        transition={{ type: 'spring', stiffness: 300, damping: 25 }}
        className="relative w-full max-w-4xl max-h-[90vh] bg-white/80 backdrop-blur-2xl rounded-2xl shadow-xl border border-white/30 flex flex-col"
        onClick={(e) => e.stopPropagation()}>
        <div className="p-5 border-b border-slate-900/10 sticky top-0 z-10 rounded-t-2xl">
          <h3 className="text-xl font-bold text-slate-800">{testData.unit_name}</h3>
          <p className="text-sm text-slate-500">
            {testData.subject_name} - {testData.sub_topics}
          </p>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-full text-slate-400 hover:bg-slate-900/10 transition-colors">
            <X size={20} />
          </button>
        </div>
        <div className="overflow-y-auto p-6 space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-white/70 border border-slate-200/80 rounded-lg shadow-sm">
              <p className="text-sm font-medium text-slate-500">Your Score</p>
              <p className="text-3xl font-extrabold text-blue-600">
                {summary?.score}{' '}
                <span className="text-xl font-semibold text-slate-400">
                  / {summary?.total_questions}
                </span>
              </p>
            </div>
            <div className="p-4 bg-white/70 border border-slate-200/80 rounded-lg shadow-sm">
              <p className="text-sm font-medium text-slate-500">Correct</p>
              <p className="text-3xl font-extrabold text-emerald-500">{summary?.num_correct}</p>
            </div>
            <div className="p-4 bg-white/70 border border-slate-200/80 rounded-lg shadow-sm">
              <p className="text-sm font-medium text-slate-500">Incorrect</p>
              <p className="text-3xl font-extrabold text-red-500">{summary?.num_incorrect}</p>
            </div>
          </div>
          <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 rounded-r-lg">
            <h4 className="font-bold text-blue-800 mb-1 flex items-center gap-2">
              <Sparkles size={18} /> AI Overall Assessment
            </h4>
            <p className="text-blue-700/90">{feedback?.overall_assessment}</p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <InsightCard
              icon={<TrendingUp size={20} />}
              title="Strengths"
              items={feedback?.topic_strengths}
              colorClass={{ text: 'text-emerald-500' }}
            />
            <InsightCard
              icon={<Target size={20} />}
              title="Areas for Improvement"
              items={feedback?.ai_feedback?.areas_for_improvement}
              colorClass={{ text: 'text-amber-500' }}
            />
            <InsightCard
              icon={<Lightbulb size={20} />}
              title="General Study Tips"
              items={feedback?.general_study_tips}
              colorClass={{ text: 'text-blue-500' }}
            />
          </div>
          <div>
            <h3 className="text-lg font-bold text-slate-800 mb-4">Question Breakdown</h3>
            <div className="space-y-4">
              {feedback?.detailed_question_feedback?.map((qf) => {
                const question = testData.questions_data.find((q) => q.id === qf.question_id);
                if (!question) return null;
                return (
                  <div
                    key={qf.question_id}
                    className={`p-4 border rounded-lg ${qf.validation ? 'bg-emerald-50/70 border-emerald-200/80' : 'bg-red-50/70 border-red-200/80'}`}>
                    <p className="font-semibold text-slate-700 mb-2">{question.question_text}</p>
                    <div className="flex flex-col sm:flex-row gap-4 text-sm">
                      <div className="flex-1 space-y-1">
                        <p className="font-medium text-slate-500">Your Answer:</p>
                        <p
                          className={`font-bold ${qf.validation ? 'text-emerald-700' : 'text-red-700'}`}>
                          {question.options[qf.user_answer] || 'Not Answered'}
                        </p>
                      </div>
                      {!qf.validation && (
                        <div className="flex-1 space-y-1">
                          <p className="font-medium text-slate-500">Correct Answer:</p>
                          <p className="font-bold text-emerald-700">
                            {question.options[qf.correct_answer]}
                          </p>
                        </div>
                      )}
                    </div>
                    <div className="mt-3 pt-3 border-t border-slate-900/10 text-sm">
                      <p className="flex items-start gap-2">
                        {qf.validation ? (
                          <CheckCircle className="w-4 h-4 text-emerald-500 mt-0.5 shrink-0" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500 mt-0.5 shrink-0" />
                        )}
                        <span className="text-slate-600">{qf.feedback_on_answer}</span>
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

// --- Enhanced Glassmorphic Components ---
const GlassCard = ({ children, className = '' }) => (
  <div
    className={`bg-white/60 backdrop-blur-xl border border-white/30 rounded-2xl shadow-lg shadow-blue-500/10 ring-1 ring-black/5 ${className}`}>
    {children}
  </div>
);
const CbtHeader = ({ subjectOptions, selectedSubject, onSubjectChange }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
          CBT Analytics
        </h1>
        <p className="text-slate-500 mt-1">Your performance overview in Computer Based Tests.</p>
      </div>
      <div className="relative w-full sm:w-64">
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="flex items-center justify-between w-full px-4 py-2.5 bg-white/70 backdrop-blur-lg border border-white/30 rounded-full shadow-md shadow-blue-500/10 ring-1 ring-black/5 text-slate-700 font-medium hover:bg-white/90 transition-colors">
          <span>
            {subjectOptions.find((opt) => opt.value === selectedSubject)?.label || 'Select Subject'}
          </span>
          <motion.div animate={{ rotate: isDropdownOpen ? 180 : 0 }}>
            <ChevronDown className="h-5 w-5 text-slate-400" />
          </motion.div>
        </button>
        <AnimatePresence>
          {isDropdownOpen && (
            <motion.ul
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute z-20 w-full mt-2 bg-white/80 backdrop-blur-xl border border-white/30 rounded-xl shadow-lg ring-1 ring-black/5">
              {subjectOptions.map((opt) => (
                <li key={opt.value}>
                  <button
                    onClick={() => {
                      onSubjectChange(opt.value);
                      setIsDropdownOpen(false);
                    }}
                    className="w-full text-left px-4 py-2 text-slate-700 hover:bg-blue-500/10 hover:text-blue-600 transition-colors first:rounded-t-xl last:rounded-b-xl">
                    {opt.label}
                  </button>
                </li>
              ))}
            </motion.ul>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
const StatCard = ({ icon, title, value }) => (
  <motion.div
    variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}
    className="relative p-6 bg-white rounded-2xl shadow-lg shadow-blue-500/10 border border-slate-200/50 overflow-hidden">
    <div className="flex items-center gap-4">
      <div className={`p-3 rounded-full text-white ${PRIMARY_GRADIENT}`}>{icon}</div>
      <div>
        <p className="text-sm font-medium text-slate-500">{title}</p>
        <p className="text-2xl font-bold text-slate-800">{value}</p>
      </div>
    </div>
  </motion.div>
);
const RecentTestsTable = ({ data, onView }) => (
  <GlassCard className="overflow-hidden">
    <h3 className="p-4 text-lg font-bold text-slate-700 border-b border-slate-900/10">
      Recent Tests
    </h3>
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead className="bg-slate-50/50">
          <tr>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Unit Name</th>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Subject</th>
            <th className="px-6 py-3 text-left font-semibold text-slate-600">Date</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Score</th>
            <th className="px-6 py-3 text-center font-semibold text-slate-600">Review</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-slate-900/5">
          {data.slice(0, 5).map((test, index) => (
            <motion.tr
              key={test._id || index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              className="hover:bg-blue-500/10 transition-colors">
              <td className="px-6 py-4 font-medium text-slate-800 max-w-xs truncate">
                {test.unit_name}
              </td>
              <td className="px-6 py-4">
                <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  {test.subject_name}
                </span>
              </td>
              <td className="px-6 py-4 text-slate-500">
                {new Date(test.date).toLocaleDateString()}
              </td>
              <td className="px-6 py-4 text-center font-bold text-indigo-600 text-base">
                {test.score} / {test.evaluation_results?.score_summary?.total_questions}
              </td>
              <td className="px-6 py-4 text-center">
                <button
                  onClick={() => onView(test)}
                  className="flex items-center gap-1.5 mx-auto px-3 py-1.5 bg-white text-slate-600 border border-slate-300 rounded-md hover:bg-slate-100 hover:border-slate-400 transition-all text-xs font-semibold shadow-sm">
                  <Eye size={14} /> View
                </button>
              </td>
            </motion.tr>
          ))}
        </tbody>
      </table>
    </div>
  </GlassCard>
);

// --- Chart Components ---
const PerformanceTrendChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      xAxis: { type: 'datetime', labels: { format: '{value:%b %d}' } },
      yAxis: { title: { text: 'Score' } },
      tooltip: { pointFormat: 'Score: <b>{point.y}</b>' },
      series: [
        {
          name: 'Score',
          data: data,
          color: '#4f46e5',
          fillColor: {
            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
            stops: [
              [0, 'rgba(99, 102, 241, 0.4)'],
              [1, 'rgba(99, 102, 241, 0.05)']
            ]
          },
          marker: {
            enabled: true,
            radius: 4,
            symbol: 'circle',
            fillColor: '#fff',
            lineColor: '#4f46e5',
            lineWidth: 2
          },
          lineWidth: 2
        }
      ]
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <TrendingUp className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Performance Over Time</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const SubjectBreakdownChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { type: 'bar' },
      xAxis: { categories: data.map((d) => d.name), labels: { style: { color: '#64748b' } } },
      yAxis: { title: { text: 'Average Score' } },
      legend: { enabled: false },
      tooltip: { pointFormat: 'Avg Score: <b>{point.y:.1f}</b>' },
      series: [{ name: 'Average Score', data: data.map((d) => ({ y: d.avg, color: d.color })) }],
      plotOptions: {
        bar: {
          borderRadius: 5,
          dataLabels: { enabled: true, style: { color: '#334155', textOutline: 'none' } }
        }
      }
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <BarChart3 className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Average Score by Subject</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const AccuracyDonutChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { type: 'pie' },
      title: {
        text: `${data.overallAccuracy.toFixed(1)}%`,
        align: 'center',
        verticalAlign: 'middle',
        y: 10,
        style: { fontSize: '24px', fontWeight: 'bold', color: '#334155' }
      },
      subtitle: {
        text: 'Accuracy',
        align: 'center',
        verticalAlign: 'middle',
        y: 30,
        style: { fontSize: '14px', color: '#64748b' }
      },
      tooltip: { pointFormat: '<b>{point.y}</b> questions ({point.percentage:.1f}%)' },
      plotOptions: {
        pie: {
          innerSize: '70%',
          dataLabels: { enabled: false },
          showInLegend: true,
          borderWidth: 3
        }
      },
      series: [
        {
          name: 'Questions',
          data: [
            { name: 'Correct', y: data.correct, color: '#10b981' },
            { name: 'Incorrect', y: data.incorrect, color: '#ef4444' },
            { name: 'Unattempted', y: data.unattempted, color: '#94a3b8' }
          ]
        }
      ]
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <PieChart className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Overall Accuracy</h3>
      </div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </GlassCard>
  );
};
const TopicRadarChart = ({ data }) => {
  const chartOptions = useMemo(
    () => ({
      chart: { polar: true, type: 'area' },
      pane: { size: '85%' },
      xAxis: { categories: data.map((d) => d.name), tickmarkPlacement: 'on', lineWidth: 0 },
      yAxis: {
        gridLineInterpolation: 'polygon',
        lineWidth: 0,
        min: 0,
        max: 100,
        labels: {
          formatter: function () {
            return this.value + '%';
          }
        }
      },
      tooltip: {
        pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.0f}%</b><br/>'
      },
      legend: { enabled: false },
      series: [
        {
          name: 'Average Score',
          data: data.map((d) => d.avg),
          pointPlacement: 'on',
          color: '#4f46e5',
          fillColor: 'rgba(99, 102, 241, 0.25)'
        }
      ]
    }),
    [data]
  );
  return (
    <GlassCard className="p-4 md:p-6">
      <div className="flex items-center gap-2 mb-4">
        <GitCommit className="text-indigo-500" />
        <h3 className="text-lg font-bold text-slate-800">Topic Performance</h3>
      </div>
      {data.length > 2 ? (
        <HighchartsReact highcharts={Highcharts} options={chartOptions} />
      ) : (
        <div className="h-64 flex items-center justify-center text-center text-slate-500">
          <p>Need at least 3 topics with test data to display this chart.</p>
        </div>
      )}
    </GlassCard>
  );
};

// --- Main CBT Component ---
const Cbt = () => {
  const [openPopup, setOpenPopup] = useState(false);
  const [selectedTest, setSelectedTest] = useState({});
  const [selectedSubject, setSelectedSubject] = useState(ALL_SUBJECTS);
  const [toastError, setToastError] = useState(null);
  const [trigger, { isLoading, isError, error }] = useLazyGetCbtServiceQuery();
  const dispatch = useDispatch();
  const cbtData = useSelector((state) => state.studentDashboard.cbt);

  const getCbtData = useCallback(async () => {
    try {
      const res = await trigger({ userId: sessionStorage.userId }).unwrap();
      dispatch(setCbt(res));
    } catch (err) {
      setToastError(err);
    }
  }, [dispatch, trigger]);
  useEffect(() => {
    getCbtData();
  }, [getCbtData]);

  // Data Processing Hooks
  const combinedData = useMemo(() => {
    if (!cbtData) return [];
    const allTests = Object.keys(cbtData).reduce((acc, subject) => {
      if (!cbtData[subject] || cbtData[subject].length === 0) return acc;
      const subjectData = cbtData[subject].map((test) => ({
        ...test,
        score: test.evaluation_results?.score_summary?.score ?? 0,
        subject_name: subject.charAt(0).toUpperCase() + subject.slice(1),
        date: new Date(test.start_time).getTime()
      }));
      return [...acc, ...subjectData];
    }, []);
    return allTests.sort((a, b) => b.date - a.date);
  }, [cbtData]);
  const filteredData = useMemo(() => {
    if (selectedSubject === ALL_SUBJECTS) return combinedData;
    return combinedData.filter((test) => test.subject_name.toLowerCase() === selectedSubject);
  }, [combinedData, selectedSubject]);
  const subjectOptions = useMemo(() => {
    if (!cbtData) return [];
    return [
      { value: ALL_SUBJECTS, label: 'All Subjects' },
      ...Object.keys(cbtData).map((subject) => ({
        value: subject,
        label: subject.charAt(0).toUpperCase() + subject.slice(1)
      }))
    ];
  }, [cbtData]);
  const performanceStats = useMemo(() => {
    const count = filteredData.length;
    if (count === 0) return { total: 0, avg: 'N/A', best: 'N/A' };
    const totalScore = filteredData.reduce((sum, test) => sum + test.score, 0);
    const bestScore = Math.max(...filteredData.map((test) => test.score));
    return { total: count, avg: (totalScore / count).toFixed(1), best: bestScore };
  }, [filteredData]);
  const trendChartData = useMemo(
    () => filteredData.map((d) => [d.date, d.score]).sort((a, b) => a[0] - b[0]),
    [filteredData]
  );
  const subjectBreakdownData = useMemo(() => {
    if (!cbtData) return [];
    const colors = ['#4f46e5', '#10b981', '#f97316', '#8b5cf6', '#ec4899'];
    return Object.keys(cbtData)
      .map((subject, i) => {
        const tests = cbtData[subject] || [];
        if (tests.length === 0) return null;
        const totalScore = tests.reduce(
          (sum, t) => sum + (t.evaluation_results?.score_summary?.score ?? 0),
          0
        );
        const avg = totalScore / tests.length;
        return {
          name: subject.charAt(0).toUpperCase() + subject.slice(1),
          avg: avg,
          color: colors[i % colors.length]
        };
      })
      .filter(Boolean);
  }, [cbtData]);
  const accuracyStats = useMemo(() => {
    const totals = filteredData.reduce(
      (acc, test) => {
        const summary = test.evaluation_results?.score_summary;
        if (summary) {
          acc.correct += summary.num_correct || 0;
          acc.incorrect += summary.num_incorrect || 0;
          acc.unattempted += summary.num_unattempted || 0;
          acc.total += summary.total_questions || 0;
        }
        return acc;
      },
      { correct: 0, incorrect: 0, unattempted: 0, total: 0 }
    );
    const overallAccuracy = totals.total > 0 ? (totals.correct / totals.total) * 100 : 0;
    return { ...totals, overallAccuracy };
  }, [filteredData]);
  const topicPerformanceData = useMemo(() => {
    const topicMap = new Map();
    filteredData.forEach((test) => {
      const topicName = test.sub_topics || 'General';
      const summary = test.evaluation_results?.score_summary;
      if (!summary || !summary.total_questions) return;
      const percentage = (summary.score / summary.total_questions) * 100;
      if (!topicMap.has(topicName)) {
        topicMap.set(topicName, { totalPercent: 0, count: 0 });
      }
      const current = topicMap.get(topicName);
      current.totalPercent += percentage;
      current.count += 1;
    });
    return Array.from(topicMap.entries()).map(([name, data]) => ({
      name,
      avg: data.totalPercent / data.count
    }));
  }, [filteredData]);

  const handleViewCbtData = (testData) => {
    setSelectedTest(testData);
    setOpenPopup(true);
  };

  const renderContent = () => {
    if (isLoading) return <CbtSkeleton />;
    if (isError && !cbtData) return <ErrorDisplay error={error} onRetry={getCbtData} />;
    return (
      <>
        <CbtHeader
          subjectOptions={subjectOptions}
          selectedSubject={selectedSubject}
          onSubjectChange={setSelectedSubject}
        />
        {filteredData.length > 0 ? (
          <div className="space-y-6">
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
              initial="hidden"
              animate="visible"
              variants={{ visible: { transition: { staggerChildren: 0.1 } } }}>
              <StatCard
                icon={<BarChart3 />}
                title="Total Tests Taken"
                value={performanceStats.total}
              />
              <StatCard icon={<Target />} title="Average Score" value={performanceStats.avg} />
              <StatCard icon={<Award />} title="Best Score" value={performanceStats.best} />
            </motion.div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}>
                <PerformanceTrendChart data={trendChartData} />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}>
                <AccuracyDonutChart data={accuracyStats} />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}>
                <TopicRadarChart data={topicPerformanceData} />
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}>
                <SubjectBreakdownChart data={subjectBreakdownData} />
              </motion.div>
            </div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}>
              <RecentTestsTable data={filteredData} onView={handleViewCbtData} />
            </motion.div>
          </div>
        ) : (
          <EmptyState
            message={`There are no CBT records available for ${selectedSubject === ALL_SUBJECTS ? 'any subject' : selectedSubject}.`}
          />
        )}
      </>
    );
  };

  return (
    <div className="p-0">
      <AnimatePresence>
        {openPopup && (
          <TestReviewPopup testData={selectedTest} onClose={() => setOpenPopup(false)} />
        )}
      </AnimatePresence>
      <Toastify res={toastError} resClear={() => setToastError(null)} />
      {renderContent()}
    </div>
  );
};

export default Cbt;
