import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLazyGetEbookCentreQuery } from './ebookCentre.slice';
import {
  BookOpen,
  ChevronDown,
  Eye,
  FileText,
  Frown,
  Grid3x3,
  List,
  Loader2,
  Search,
  X,
  Download,
  Library,
  Filter,
  ArrowRight
} from 'lucide-react';

const MaterialCard = ({ material, onView }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="relative overflow-hidden mt-5 rounded-lg border border-gray-200 bg-white shadow-sm transition-all hover:shadow-md"
      whileHover={{ y: -5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}>
      <div className="p-5">
        <div className="flex items-start gap-4">
          <div className="p-3 rounded-lg bg-[var(--color-student)] text-white flex-shrink-0 shadow-md">
            <BookOpen size={20} />
          </div>
          <div className="flex-1">
            <h3 className="font-bold text-gray-900 mb-1 line-clamp-2">
              {material?.name || 'Untitled Resource'}
            </h3>
            {material?.description && (
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{material.description}</p>
            )}
            <div className="flex flex-wrap gap-2">
              <span className="px-2 py-1 text-xs font-medium rounded-full text-white bg-[var(--color-counselor)]">
                {material?.file_type?.toUpperCase() || 'PDF'}
              </span>
              {material?.file_size && (
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                  {material.file_size}
                </span>
              )}
            </div>
          </div>
        </div>

        <motion.div
          className="flex justify-end gap-2 mt-4"
          animate={{ opacity: isHovered ? 1 : 0.8 }}>
          <motion.button
            onClick={() => onView(material)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-3 py-1.5 rounded-lg text-sm font-medium flex items-center bg-[var(--color-counselor)] text-white hover:from-amber-700 hover:to-amber-600 shadow-md">
            <Eye size={16} className="mr-2" />
            View
          </motion.button>
        
        </motion.div>
      </div>

      <motion.div
        className="absolute bottom-0 left-0 right-0 h-1 bg-[var(--color-student)]"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: isHovered ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
};

const EBookCentre = () => {
  const [selectedMaterial, setSelectedMaterial] = useState(null);
  const [viewMode, setViewMode] = useState('grid');
  const [expandedSubjects, setExpandedSubjects] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState([]);

  const storedCourse = sessionStorage.getItem('course');
  const storedRole = sessionStorage.getItem('role');
  const storedStudentId = sessionStorage.getItem('userId');

  const [triggerGetContent, { data, isLoading, isError }] = useLazyGetEbookCentreQuery();

  useEffect(() => {
    if (storedCourse && storedRole) {
      triggerGetContent({
        course: storedCourse,
        role: storedRole,
        studentId: storedStudentId
      });
    }
  }, [storedCourse, storedRole, storedStudentId, triggerGetContent]);

  const toggleSubject = (subject) => {
    setExpandedSubjects((prev) => ({
      ...prev,
      [subject]: !prev[subject]
    }));
    setSearchTerm('');
  };

  const filteredMaterials = (materials) => {
    let result = materials;

    if (searchTerm) {
      result = result.filter(
        (material) =>
          material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          material.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (activeFilters.length > 0) {
      result = result.filter((material) =>
        activeFilters.includes(material.file_type?.toLowerCase())
      );
    }

    return result;
  };

  const toggleFilter = (filter) => {
    setActiveFilters((prev) =>
      prev.includes(filter) ? prev.filter((f) => f !== filter) : [...prev, filter]
    );
  };

  if (isLoading)
    return (
      <div className="min-h-screen mt-5 flex flex-col items-center justify-center bg-gradient-to-br from-amber-50 to-amber-100">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="mb-6 text-amber-600">
          <Loader2 size={48} />
        </motion.div>
        <motion.h1
          className="text-2xl font-bold text-gray-800 mb-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}>
          Loading Your Library
        </motion.h1>
        <motion.p
          className="text-gray-600"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}>
          Curating your educational resources...
        </motion.p>
      </div>
    );

  if (isError)
    return (
      <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-amber-50 to-amber-100">
        <motion.div
          className="bg-white p-8 rounded-xl shadow-lg max-w-md w-full text-center border-l-4 border-amber-500"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}>
          <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-100 rounded-full mb-4">
            <Frown size={32} className="text-amber-500" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Connection Error</h2>
          <p className="text-gray-600 mb-6">
            We couldn't connect to the knowledge repository. Please check your connection and try
            again.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-2 rounded-lg font-medium text-white bg-gradient-to-br from-amber-600 to-amber-500 hover:from-amber-700 hover:to-amber-600">
            Retry Connection
          </button>
        </motion.div>
      </div>
    );

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-amber-100">
      {/* Header */}
      <header className="relative py-16 px-4 sm:px-6 lg:px-8 overflow-hidden bg-gradient-to-br from-gray-900 to-gray-800">
        {/* Decorative book stack illustration */}
        <div className="absolute left-0 right-0 bottom-0 h-40 overflow-hidden z-0">
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-4xl h-full flex justify-center">
            {[1, 2, 3, 4, 5].map((i) => (
              <motion.div
                key={i}
                className="absolute bottom-0 h-32 w-24 bg-gradient-to-b from-gray-700 to-gray-600 rounded-sm shadow-lg border-r-2 border-gray-500"
                style={{
                  left: `${50 + (i - 3) * 6}%`,
                  height: `${100 - i * 8}%`,
                  zIndex: 5 - i
                }}
                initial={{ y: 100, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: i * 0.1, type: 'spring' }}>
                <div className="absolute top-2 left-0 right-0 h-1 bg-gray-500/30"></div>
                <div className="absolute top-4 left-0 right-0 h-1 bg-gray-500/30"></div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Floating page particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute bg-white/5 rounded-sm z-0"
            style={{
              width: `${Math.random() * 60 + 40}px`,
              height: `${Math.random() * 30 + 20}px`,
              top: `${Math.random() * 60 + 10}%`,
              left: `${Math.random() * 100}%`,
              rotate: `${Math.random() * 60 - 30}deg`
            }}
            animate={{
              y: [0, Math.random() * 40 - 20],
              x: [0, Math.random() * 40 - 20],
              opacity: [0.3, 0.8, 0.3]
            }}
            transition={{
              duration: Math.random() * 15 + 10,
              repeat: Infinity,
              repeatType: 'reverse'
            }}
          />
        ))}

        <div className="max-w-7xl mx-auto relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center text-center">
            {/* Animated book opening effect */}
            <motion.div
              className="relative mb-8"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring' }}>
              <div className="relative w-32 h-32">
                <motion.div
                  className="absolute top-0 left-0 w-full h-full bg-[var(--color-counselor)] rounded-lg shadow-xl flex items-center justify-center"
                  initial={{ rotateY: 90 }}
                  animate={{ rotateY: 0 }}
                  transition={{ delay: 0.3, duration: 0.8 }}>
                  <BookOpen size={48} className="text-white" />
                </motion.div>
                <motion.div
                  className="absolute top-0 left-0 w-full h-full bg-[var(--color-student)] rounded-lg shadow-xl origin-left"
                  initial={{ rotateY: 0 }}
                  animate={{ rotateY: -120 }}
                  transition={{ delay: 0.5, duration: 0.6 }}>
                  <div className="absolute inset-0 flex items-center justify-end pr-4">
                    <div className="w-1 h-8 bg-amber-800/50 rounded-full"></div>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Title with page curl effect */}
            <motion.div className="relative inline-block mb-6">
              <motion.h1
                className="text-5xl md:text-6xl font-bold text-white mb-0"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}>
                E-Books Library
              </motion.h1>
              <motion.div
                className="absolute -bottom-6 left-0 h-2 w-full bg-[var(--color-counselor)]  rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
              />
            </motion.div>

            {/* Subtitle with typing effect */}
            <motion.p
              className="text-xl md:text-2xl text-gray-300 max-w-2xl mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}>
              {storedRole === 'student'
                ? 'Explore our collection of digital knowledge'
                : 'Manage and distribute learning materials'}
            </motion.p>

            {/* Interactive search element */}
            <motion.div
              className="relative w-full max-w-md"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.4 }}>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search e-books, guides, and resources..."
                  className="w-full pl-12 pr-6 py-4 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl focus:ring-2 focus:ring-amber-400 focus:outline-none text-white placeholder-gray-400"
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search
                  size={24}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <motion.div
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 px-2 py-1 bg-amber-500/10 border border-amber-400/30 rounded-lg text-xs text-amber-300"
                  animate={{ opacity: [0.6, 1, 0.6] }}
                  transition={{ duration: 2, repeat: Infinity }}>
                  ⌘K
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Animated page corners */}
        <div className="absolute top-0 right-0 w-32 h-32 overflow-hidden">
          <motion.div
            className="absolute -top-16 -right-16 w-32 h-32 bg-[var(--color-counselor)] rotate-45 origin-center"
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 8, repeat: Infinity }}
          />
        </div>
        <div className="absolute bottom-0 left-0 w-32 h-32 overflow-hidden">
          <motion.div
            className="absolute -bottom-16 -left-16 w-32 h-32 bg-[var(--color-counselor)] rotate-45 origin-center"

            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 8, repeat: Infinity, delay: 2 }}
          />
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 -mt-6">
        {/* Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm p-3 mb-6 flex flex-wrap items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <div className="relative group">
              <button className="flex items-center gap-2 px-3 py-1.5 bg-amber-50 hover:bg-amber-100 rounded-md text-amber-800 text-sm">
                <Filter size={18} />
                <span>Filters</span>
              </button>
              <div className="absolute left-0 mt-1 w-48 bg-white rounded-lg shadow-lg p-2 z-10 hidden group-hover:block border border-amber-100">
                <p className="text-xs font-medium text-amber-600 px-2 py-1">File Types</p>
                {['pdf', 'doc', 'ppt'].map((type) => (
                  <button
                    key={type}
                    onClick={() => toggleFilter(type)}
                    className={`w-full text-left px-2 py-1.5 rounded text-sm flex items-center ${
                      activeFilters.includes(type)
                        ? 'bg-amber-100 text-amber-800'
                        : 'hover:bg-amber-50'
                    }`}>
                    {activeFilters.includes(type) && (
                      <span className="w-2 h-2 rounded-full bg-amber-600 mr-2"></span>
                    )}
                    {type.toUpperCase()}
                  </button>
                ))}
              </div>
            </div>

            {activeFilters.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {activeFilters.map((filter) => (
                  <div
                    key={filter}
                    className="px-2 py-1 rounded-full text-xs flex items-center bg-amber-100 text-amber-800">
                    {filter.toUpperCase()}
                    <button
                      onClick={() => toggleFilter(filter)}
                      className="ml-1 text-amber-600 hover:text-amber-800">
                      <X size={14} />
                    </button>
                  </div>
                ))}
                <button
                  onClick={() => setActiveFilters([])}
                  className="text-xs text-amber-600 hover:text-amber-800 flex items-center">
                  Clear all
                </button>
              </div>
            )}
          </div>

          <div className="flex bg-amber-50 rounded-md p-0.5">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-1.5 rounded-md ${
                viewMode === 'grid'
                  ? 'bg-amber-600 text-white shadow-md'
                  : 'text-amber-600 hover:bg-amber-100'
              }`}>
              <Grid3x3 size={18} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-1.5 rounded-md ${
                viewMode === 'list'
                  ? 'bg-amber-600 text-white shadow-md'
                  : 'text-amber-600 hover:bg-amber-100'
              }`}>
              <List size={18} />
            </button>
          </div>
        </motion.div>

        {/* Subjects */}
        <div className="space-y-4">
          {data?.content &&
            Object.entries(data.content).map(([subject, files]) => (
              <motion.div
                key={subject}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ type: 'spring' }}
                className="bg-white rounded-lg shadow-sm overflow-hidden border border-amber-100">
                <button
                  onClick={() => toggleSubject(subject)}
                  className={`w-full flex justify-between items-center p-4 hover:bg-amber-50 transition-colors ${
                    expandedSubjects[subject] ? 'border-b border-amber-100' : ''
                  }`}>
                  <div className="flex items-center">
                    <motion.div
                      animate={{ rotate: expandedSubjects[subject] ? 180 : 0 }}
                      transition={{ type: 'spring' }}
                      className="mr-3 text-amber-600">
                      <ChevronDown size={20} />
                    </motion.div>
                    <div className="text-left">
                      <h2 className="font-semibold text-gray-900">{subject}</h2>
                      <p className="text-sm text-amber-600">
                        {files?.length || 0} resource{files?.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  <div className="px-2 py-1 text-xs font-medium rounded-full flex items-center bg-amber-100 text-amber-800">
                    {expandedSubjects[subject] ? 'Collapse' : 'Expand'}
                    <ArrowRight
                      size={14}
                      className={`ml-1 transition-transform ${
                        expandedSubjects[subject] ? 'rotate-90' : ''
                      }`}
                    />
                  </div>
                </button>

                <AnimatePresence>
                  {expandedSubjects[subject] && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="overflow-hidden">
                      <div className="p-4 pt-0">
                        {filteredMaterials(files)?.length > 0 ? (
                          <div
                            className={
                              viewMode === 'grid'
                                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                                : 'space-y-4'
                            }>
                            {filteredMaterials(files).map((material, idx) => (
                              <MaterialCard
                                key={idx}
                                material={material}
                                onView={setSelectedMaterial}
                              />
                            ))}
                          </div>
                        ) : (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="py-8 text-center">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-100 rounded-full mb-4">
                              <Frown size={24} className="text-amber-500" />
                            </div>
                            <h3 className="text-base font-medium text-gray-700 mb-1">
                              No matching materials
                            </h3>
                            <p className="text-amber-600 text-sm">
                              Try adjusting your filters or search term
                            </p>
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
        </div>
      </main>

      {/* Material Viewer Modal */}
      <AnimatePresence>
        {selectedMaterial && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedMaterial(null)}>
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] flex flex-col overflow-hidden relative"
              onClick={(e) => e.stopPropagation()}
              style={{
                borderTop: `4px solid #f59e0b` // amber-500
              }}>
              <div className="flex items-center justify-between p-4 border-b border-amber-100">
                <h2 className="font-bold text-lg text-gray-900">
                  {selectedMaterial?.name || 'Resource Viewer'}
                </h2>
                <button
                  onClick={() => setSelectedMaterial(null)}
                  className="p-2 rounded-full text-gray-500 hover:text-gray-900 hover:bg-amber-50">
                  <X size={20} />
                </button>
              </div>

              <div className="flex-1 overflow-auto">
                {selectedMaterial?.url ? (
                  <iframe
                    src={selectedMaterial.url}
                    className="w-full h-full min-h-[70vh] border-0"
                    title={selectedMaterial?.name || 'Resource'}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-amber-100 rounded-full mb-6">
                      <FileText size={32} className="text-amber-500" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-900 mb-2">Preview Unavailable</h3>
                    <p className="text-amber-600 mb-6">
                      This resource cannot be displayed in the viewer.
                    </p>
                    {selectedMaterial?.url && (
                      <a
                        href={selectedMaterial.url}
                        download
                        className="px-5 py-2 rounded-lg font-medium text-white flex items-center bg-gradient-to-br from-amber-600 to-amber-500 hover:from-amber-700 hover:to-amber-600 shadow-md">
                        <Download size={18} className="mr-2" />
                        Download Resource
                      </a>
                    )}
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EBookCentre;
