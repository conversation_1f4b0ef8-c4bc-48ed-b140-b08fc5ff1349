import { createSlice } from '@reduxjs/toolkit';
import { AnalyticsApi } from '../../../../redux/api/api';

const initialState = {
  studentsData: null,
  studentsAnalyticsData: null,
  leaderBoardData: null,
  centerLiveQuizData: null,
  centerStudentQuizData: null,
  centerOcrData: null,
  centerOmrData: null
};

export const centreCounselorDashboardApiSlice = AnalyticsApi.injectEndpoints({
  endpoints: (builder) => ({
    getStudentsDataByCenterId: builder.query({
      query: (query) => {
        return `/analytics/center/${query.center_code}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getStudentsAnalyticsDataByCenterId: builder.query({
      query: (query) => {
        return `/analytics/center/cbt/${query.studentId}/${query.course}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentAnalyticsDashboard']
    }),
    getLeaderBoardDataByCenterId: builder.query({
      query: (query) => {
        return `/analytics/center/leaderboard/${query.center_code}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentAnalyticsDashboard']
    }),
    getCenterLiveQuizData: builder.query({
      query: (query) => {
        return `/analytics/center/live-quiz/${query.center_code}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentAnalyticsDashboard']
    }),
    getCenterStudentQuizData: builder.query({
      query: (query) => {
        return `/analytics/center/students-quiz/${query.center_code}/${query.student_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentAnalyticsDashboard']
    }),
    getCenterOcrData: builder.query({
      query: (query) => {
        return `/analytics/ocr/center-counselor/${query.center_code}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentAnalyticsDashboard']
    }),
    getCenterOmrData: builder.query({
      query: (query) => {
        return `/analytics/omr/center-counselor/${query.center_code}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentAnalyticsDashboard']
    })
  })
});

const centreCounselorDashboardSlice = createSlice({
  name: 'centreCounselorDashboard',
  initialState,
  reducers: {
    setStudentsData: (state, action) => {
      state.studentsData = action.payload;
    },
    clearStudentsData: (state) => {
      state.studentsData = null;
    },
    setStudentsAnalyticsData: (state, action) => {
      state.studentsAnalyticsData = action.payload;
    },
    clearStudentsAnalyticsData: (state) => {
      state.studentsAnalyticsData = null;
    },
    setLeaderBoardData: (state, action) => {
      state.leaderBoardData = action.payload;
    },
    clearLeaderBoardData: (state) => {
      state.leaderBoardData = null;
    },
    setCenterLiveQuizData: (state, action) => {
      state.centerLiveQuizData = action.payload;
    },
    clearCenterLiveQuizData: (state) => {
      state.centerLiveQuizData = null;
    },
    setCenterStudentQuizData: (state, action) => {
      state.centerStudentQuizData = action.payload;
    },
    clearCenterStudentQuizData: (state) => {
      state.centerStudentQuizData = null;
    },
    setCenterOcrData: (state, action) => {
      state.centerOcrData = action.payload;
    },
    clearCenterOcrData: (state) => {
      state.centerOcrData = null;
    },
    setCenterOmrData: (state, action) => {
      state.centerOmrData = action.payload;
    },
    clearCenterOmrData: (state) => {
      state.centerOmrData = null;
    }
  }
});

export const {
  useLazyGetStudentsDataByCenterIdQuery,
  useLazyGetStudentsAnalyticsDataByCenterIdQuery,
  useLazyGetLeaderBoardDataByCenterIdQuery,
  useLazyGetCenterLiveQuizDataQuery,
  useLazyGetCenterStudentQuizDataQuery,
  useLazyGetCenterOcrDataQuery,
  useLazyGetCenterOmrDataQuery
} = centreCounselorDashboardApiSlice;
export const {
  setStudentsData,
  clearStudentsData,
  setStudentsAnalyticsData,
  clearStudentsAnalyticsData,
  setLeaderBoardData,
  clearLeaderBoardData,
  setCenterLiveQuizData,
  clearCenterLiveQuizData,
  setCenterStudentQuizData,
  clearCenterStudentQuizData,
  setCenterOcrData,
  clearCenterOcrData,
  setCenterOmrData,
  clearCenterOmrData
} = centreCounselorDashboardSlice.actions;
export const selectCenterCounselorAnalytics = (state) =>
  state.centreCounselorDashboard.studentsData;
export default centreCounselorDashboardSlice.reducer;
