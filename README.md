
## 🚀 Deployment & CI/CD Workflow

This project follows a standard Dev → Test → Deploy process.

### 🔨 1. Development Stage
- Developers push code to **feature branches**.
- Code is peer-reviewed through **merge requests**.

### 🧪 2. Testing Stage
- After merging into the `develop` branch:
  - Unit tests and integration tests run automatically using GitLab CI.
  - Artifacts may be built and verified in this stage.

### 🚀 3. Deployment Stage
- When code is merged into the `main` branch:
  - A CI/CD pipeline triggers Docker image builds.
  - Images are pushed to the GitLab container registry.
  - Containers are deployed using `docker-compose` or Kubernetes.
  - Logs and metrics are captured for monitoring.

> ⚙️ Auto-deployments can be paused using branch protection or manual approvals.

---
