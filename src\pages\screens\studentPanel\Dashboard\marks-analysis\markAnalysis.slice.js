// src/features/markAnalysis/markAnalysis.slice.js
import { MarkAnalysisApi } from "../../../../../redux/api/api";

export const markAnalysisSlice = MarkAnalysisApi.injectEndpoints({
  endpoints: (builder) => ({
    getTutorDashboard: builder.query({
      query: (studentId) => ({
        url: `/get-tutor-dashboard/${studentId}`,
        method: 'GET',
        responseHandler: async (res) => res.json(),
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data,
      }),
      providesTags: ['MarkAnalysis'],
    }),
    askQuery: builder.mutation({
      query: ({ studentId, query }) => ({
        url: '/ask',
        method: 'POST',
        body: { student_id: studentId, query },
        headers: {
          'Content-Type': 'application/json',
        },
        responseHandler: async (res) => res.json(),
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data,
      }),
      invalidatesTags: ['MarkAnalysis'],
    }),
  }),
});

export const {
  useGetTutorDashboardQuery,
  useAskQueryMutation,
} = markAnalysisSlice;