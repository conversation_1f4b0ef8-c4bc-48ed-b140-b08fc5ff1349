import React, { useEffect, useC<PERSON>back, useMemo, useState } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  BookOpen,
  Target,
  Award,
  Zap,
  TrendingUp,
  BarChart4,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  MinusCircle,
  ChevronLeft,
  ChevronRight,
  LineChart as LineChartIcon,
  Star
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { useLocation, useNavigate } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import {
  setStudentsAnalyticsData,
  useLazyGetStudentsAnalyticsDataByCenterIdQuery
} from '../AnalyticalDashboard.slice';

// --- CONSTANTS & HELPERS (Theme Updated) ---
const CONSTANTS = {
  ITEMS_PER_PAGE: 10
};

// --- Skeleton Loader (Copied from AnalyticalDashboard for consistency) ---
const SkeletonLoader = ({ count = 5, type = 'list' }) => {
  if (type === 'card') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="rounded-2xl bg-amber-200/50 h-36 animate-pulse"></div>
        <div className="rounded-2xl bg-amber-200/50 h-36 animate-pulse"></div>
        <div className="rounded-2xl bg-amber-200/50 h-36 animate-pulse"></div>
        <div className="rounded-2xl bg-amber-200/50 h-36 animate-pulse"></div>
      </div>
    );
  }
  if (type === 'chart') {
    return <div className="rounded-2xl bg-amber-200/50 h-96 animate-pulse"></div>;
  }
  return (
    <div className="space-y-3">
      {[...Array(count)].map((_, i) => (
        <div key={i} className="flex items-center p-2 h-12">
          <div className="flex-grow h-4 bg-amber-300/50 rounded" />
          <div className="w-24 h-4 bg-amber-300/50 rounded ml-4" />
          <div className="w-24 h-4 bg-amber-300/50 rounded ml-4" />
          <div className="w-32 h-4 bg-amber-300/50 rounded ml-4" />
        </div>
      ))}
    </div>
  );
};

// --- DATA PROCESSING HOOK (Helpers Themed for Light Mode) ---
const useStudentAnalytics = (studentsData) => {
  const calculateOverallStats = useCallback((data) => {
    if (!data || Object.keys(data).length === 0)
      return {
        totalTests: 0,
        totalQuestions: 0,
        totalCorrect: 0,
        accuracy: 0,
        averageScore: 0
      };
    const allTests = Object.values(data).flat();
    const totalTests = allTests.length;
    if (totalTests === 0)
      return {
        totalTests: 0,
        totalQuestions: 0,
        totalCorrect: 0,
        accuracy: 0,
        averageScore: 0
      };
    const totals = allTests.reduce(
      (acc, test) => {
        const { evaluation_results: res } = test;
        acc.questions += res.total_questions;
        acc.correct += res.num_correct;
        acc.incorrect += res.num_incorrect;
        acc.score += res.score;
        return acc;
      },
      { questions: 0, correct: 0, incorrect: 0, score: 0 }
    );
    const numAttempted = totals.correct + totals.incorrect;
    const accuracy = numAttempted > 0 ? (totals.correct / numAttempted) * 100 : 0;
    const averageScore = totalTests > 0 ? totals.score / totalTests : 0;
    return {
      totalTests,
      totalQuestions: totals.questions,
      totalCorrect: totals.correct,
      accuracy: accuracy.toFixed(1),
      averageScore: averageScore.toFixed(1)
    };
  }, []);

  const calculateSubjectStats = useCallback((subjectData) => {
    if (!subjectData || subjectData.length === 0) return null;
    const totals = subjectData.reduce(
      (acc, test) => {
        const { evaluation_results: res } = test;
        acc.correct += res.num_correct;
        acc.incorrect += res.num_incorrect;
        acc.score += res.score;
        return acc;
      },
      { correct: 0, incorrect: 0, score: 0 }
    );
    const numAttempted = totals.correct + totals.incorrect;
    const accuracy = numAttempted > 0 ? (totals.correct / numAttempted) * 100 : 0;
    const averageScore = subjectData.length > 0 ? totals.score / subjectData.length : 0;
    return {
      testsCount: subjectData.length,
      accuracy: accuracy.toFixed(1),
      averageScore: averageScore.toFixed(1)
    };
  }, []);

  const overallStats = useMemo(
    () => calculateOverallStats(studentsData),
    [studentsData, calculateOverallStats]
  );

  const subjectStats = useMemo(() => {
    if (!studentsData) return {};
    return Object.fromEntries(
      Object.entries(studentsData)
        .map(([subject, tests]) => [subject, calculateSubjectStats(tests)])
        .filter(([, stats]) => stats !== null)
    );
  }, [studentsData, calculateSubjectStats]);

  const chartData = useMemo(() => {
    if (!studentsData) return null;
    const allTests = Object.entries(studentsData)
      .flatMap(([subject, tests]) => tests.map((test) => ({ ...test, subject })))
      .sort(
        (a, b) => new Date(a.start_time.split(' & ')[0]) - new Date(b.start_time.split(' & ')[0])
      );
    if (allTests.length === 0) return null;
    const recentTests = allTests.slice(-10);
    const performanceTrend = recentTests.map((test, index) => ({
      test: `Test ${allTests.length - recentTests.length + index + 1}`,
      Correct: test.evaluation_results.num_correct,
      Incorrect: test.evaluation_results.num_incorrect,
      Unattempted: test.evaluation_results.num_unattempted
    }));
    return { performanceTrend };
  }, [studentsData]);

  const allTestsForHistory = useMemo(() => {
    if (!studentsData) return [];
    return Object.entries(studentsData)
      .flatMap(([subject, tests]) => tests.map((test) => ({ ...test, subject })))
      .sort(
        (a, b) => new Date(b.start_time.split(' & ')[0]) - new Date(a.start_time.split(' & ')[0])
      );
  }, [studentsData]);

  // *** THEME UPDATED HELPER FUNCTIONS ***
  const helpers = useMemo(
    () => ({
      getAccuracyColor: (accuracy) => {
        if (accuracy >= 80) return 'text-emerald-600';
        if (accuracy >= 60) return 'text-sky-600';
        if (accuracy >= 40) return 'text-amber-600';
        return 'text-red-600';
      },
      getPerformanceGrade: (accuracy) => {
        if (accuracy >= 90)
          return {
            grade: 'A+',
            color: 'bg-emerald-500/10 text-emerald-700 border-emerald-500/20'
          };
        if (accuracy >= 80)
          return { grade: 'A', color: 'bg-sky-500/10 text-sky-700 border-sky-500/20' };
        if (accuracy >= 70)
          return { grade: 'B', color: 'bg-amber-500/10 text-amber-700 border-amber-500/20' };
        if (accuracy >= 60)
          return { grade: 'C', color: 'bg-orange-500/10 text-orange-700 border-orange-500/20' };
        return { grade: 'D', color: 'bg-red-500/10 text-red-700 border-red-500/20' };
      }
    }),
    []
  );

  return { overallStats, subjectStats, chartData, allTestsForHistory, helpers };
};

// --- UI COMPONENTS (THEME UPDATED) ---

const Header = React.memo(({ profile, overallStats, onBack, helpers }) => (
  <motion.div
    initial={{ opacity: 0, y: -20 }}
    animate={{ opacity: 1, y: 0 }}
    className="flex items-center justify-between flex-wrap gap-4 mb-8">
    <div className="flex items-center gap-4">
      <button
        onClick={onBack}
        className="p-2 text-amber-700 hover:bg-amber-500/10 rounded-full transition-colors"
        title="Go Back">
        <ArrowLeft size={22} />
      </button>
      <div className="flex items-center gap-4">
        <div className="w-14 h-14 rounded-full bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center text-white font-bold text-2xl shadow-md">
          {profile.name?.charAt(0).toUpperCase() || 'S'}
        </div>
        <div>
          <h1 className="text-3xl font-bold text-amber-900">{profile.name}</h1>
          <p className="text-amber-700">Student Performance Details</p>
        </div>
      </div>
    </div>
    {overallStats && overallStats.totalTests > 0 && (
      <div
        className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-semibold rounded-full border ${helpers.getPerformanceGrade(overallStats.accuracy).color}`}>
        <Star size={16} />
        <span>Overall Grade: {helpers.getPerformanceGrade(overallStats.accuracy).grade}</span>
      </div>
    )}
  </motion.div>
));

// eslint-disable-next-line
const StatCard = React.memo(({ title, value, subtitle, icon: Icon, colorClass, delay = 0 }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay }}
    whileHover={{ y: -6, scale: 1.03 }}
    className="group cursor-pointer">
    <div className="bg-white/50 backdrop-blur-md rounded-2xl shadow-lg border border-amber-300/60 p-6 h-full transition-all duration-300 group-hover:border-amber-500/50 group-hover:shadow-amber-500/10">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-amber-800">{title}</h3>
        <div className={`p-3 bg-amber-100 rounded-xl ${colorClass}`}>{<Icon size={24} />}</div>
      </div>
      <p
        className={`text-4xl font-bold mb-1 bg-clip-text text-transparent bg-gradient-to-r ${colorClass.includes('emerald') ? 'from-emerald-500 to-green-600' : colorClass.includes('sky') ? 'from-sky-500 to-blue-600' : 'from-amber-500 to-orange-600'}`}>
        {value}
      </p>
      {subtitle && <p className="text-sm text-amber-700">{subtitle}</p>}
    </div>
  </motion.div>
));

// eslint-disable-next-line
const Section = ({ title, icon: Icon, children, delay = 0 }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay }}
    className="mt-8 bg-white/50 backdrop-blur-md rounded-2xl border border-amber-300/60 shadow-lg overflow-hidden">
    <div className="p-4 border-b border-amber-300/60">
      <div className="flex items-center gap-3">
        <div className="p-2 bg-amber-200/50 text-amber-700 rounded-lg">{<Icon size={20} />}</div>
        <h2 className="text-xl font-semibold text-amber-900">{title}</h2>
      </div>
    </div>
    <div className="p-4 sm:p-6">{children}</div>
  </motion.div>
);

const AnalyticsCharts = React.memo(({ chartData }) => (
  <div className="h-96 w-full">
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={chartData.performanceTrend}
        margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#fcd34d" /> {/* amber-300 */}
        <XAxis
          dataKey="test"
          stroke="#92400e" /* amber-800 */
          fontSize={12}
          tick={{ fill: '#92400e' }}
        />
        <YAxis stroke="#92400e" fontSize={12} tick={{ fill: '#92400e' }} />
        <Tooltip
          contentStyle={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            border: '1px solid #fcd34d',
            borderRadius: '12px',
            color: '#78350f'
          }}
          cursor={{ fill: 'rgba(251, 191, 36, 0.1)' }}
        />
        <Legend wrapperStyle={{ color: '#92400e', paddingTop: '10px' }} />
        <Line
          type="monotone"
          dataKey="Correct"
          stroke="#10b981" // emerald-500
          strokeWidth={2}
          dot={{ r: 4, fill: '#10b981' }}
          activeDot={{ r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="Incorrect"
          stroke="#ef4444" // red-500
          strokeWidth={2}
          dot={{ r: 4, fill: '#ef4444' }}
          activeDot={{ r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="Unattempted"
          stroke="#a3a3a3" // neutral-400
          strokeWidth={2}
          dot={{ r: 4, fill: '#a3a3a3' }}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  </div>
));

const SubjectPerformanceGrid = React.memo(({ subjectStats, helpers }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Object.entries(subjectStats).map(([subject, stats], index) => (
      <motion.div
        key={subject}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 + index * 0.05 }}
        whileHover={{ y: -8, scale: 1.05, boxShadow: '0 25px 50px -12px rgba(0,0,0,0.1)' }}
        className="bg-white/40 backdrop-blur-md rounded-2xl shadow-lg overflow-hidden border border-amber-300/50 group cursor-pointer">
        <div className="p-5">
          <h3 className="text-lg font-bold capitalize text-amber-800 mb-4">{subject}</h3>
          {stats ? (
            <div className="space-y-3">
              <div className="text-center p-3 bg-amber-100/50 rounded-lg">
                <p className={`text-3xl font-bold ${helpers.getAccuracyColor(stats.accuracy)}`}>
                  {stats.accuracy}%
                </p>
                <p className="text-xs text-amber-700 font-medium">Accuracy</p>
              </div>
              <div className="grid grid-cols-2 gap-3 text-center">
                <div className="p-2 bg-amber-100/50 rounded-lg">
                  <p className="text-xl font-bold text-amber-900">{stats.testsCount}</p>
                  <p className="text-xs text-amber-700">Tests</p>
                </div>
                <div className="p-2 bg-amber-100/50 rounded-lg">
                  <p className="text-xl font-bold text-amber-900">{stats.averageScore}</p>
                  <p className="text-xs text-amber-700">Avg. Score</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-amber-600">No tests taken.</div>
          )}
        </div>
      </motion.div>
    ))}
  </div>
));

const TestHistoryTable = React.memo(
  ({ tests, subjects, activeFilter, onFilterChange, currentPage, totalPages, onPageChange }) => (
    <div>
      {subjects.length > 0 && (
        <div className="mb-4 flex items-center gap-2 flex-wrap">
          {['all', ...subjects].map((subject) => (
            <button
              key={subject}
              onClick={() => onFilterChange(subject)}
              className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors ${activeFilter === subject ? 'bg-amber-500 text-white shadow-sm' : 'text-amber-700 hover:bg-amber-100'}`}>
              <span className="capitalize">{subject}</span>
            </button>
          ))}
        </div>
      )}
      <div className="overflow-x-auto">
        <AnimatePresence mode="wait">
          {tests.length > 0 ? (
            <motion.table
              key="history-table"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="w-full min-w-[900px]">
              <thead className="bg-amber-200/40">
                <tr>
                  {['Test Details', 'Subject', 'Date', 'Score', 'Accuracy', 'Stats (C/I/U)'].map(
                    (h) => (
                      <th
                        key={h}
                        className="px-6 py-3 text-left text-xs font-semibold text-amber-700 uppercase tracking-wider">
                        {h}
                      </th>
                    )
                  )}
                </tr>
              </thead>
              <motion.tbody
                variants={{ visible: { transition: { staggerChildren: 0.03 } } }}
                initial="hidden"
                animate="visible"
                className="divide-y divide-amber-300/60">
                {tests.map((test) => {
                  const { num_correct, num_incorrect, num_unattempted, score } =
                    test.evaluation_results;
                  const attempted = num_correct + num_incorrect;
                  const accuracy = attempted > 0 ? (num_correct / attempted) * 100 : 0;
                  const progressBarColor =
                    accuracy >= 80
                      ? 'bg-emerald-500'
                      : accuracy >= 50
                        ? 'bg-sky-500'
                        : 'bg-red-500';
                  return (
                    <motion.tr
                      key={test.unit_name + test.start_time}
                      variants={{ hidden: { opacity: 0, x: -10 }, visible: { opacity: 1, x: 0 } }}
                      layout
                      className="hover:bg-amber-200/50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="font-semibold text-amber-900">{test.unit_name}</div>
                        <div className="text-xs text-amber-600 max-w-xs truncate">
                          {test.sub_topics}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="capitalize font-medium text-amber-800">
                          {test.subject}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-amber-700">
                        {new Date(test.start_time.split(' & ')[0]).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4">
                        <span className="font-bold text-lg text-amber-800">
                          {score?.toFixed(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <span className="font-semibold text-amber-800 w-14">
                            {accuracy.toFixed(1)}%
                          </span>
                          <div className="w-full bg-amber-100 rounded-full h-1.5">
                            <div
                              className={`h-1.5 rounded-full ${progressBarColor}`}
                              style={{ width: `${accuracy}%` }}></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3 font-medium">
                          <span className="text-emerald-600 flex items-center gap-1">
                            <CheckCircle size={16} /> {num_correct}
                          </span>
                          <span className="text-red-600 flex items-center gap-1">
                            <XCircle size={16} /> {num_incorrect}
                          </span>
                          <span className="text-gray-500 flex items-center gap-1">
                            <MinusCircle size={16} /> {num_unattempted}
                          </span>
                        </div>
                      </td>
                    </motion.tr>
                  );
                })}
              </motion.tbody>
            </motion.table>
          ) : (
            <motion.div
              key="no-history"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-16 text-amber-600">
              No tests match the current filter.
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {totalPages > 1 && (
        <div className="bg-amber-200/40 px-6 py-3 border-t border-amber-300/60 mt-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-amber-700">
              Page <span className="font-bold text-amber-900">{currentPage}</span> of{' '}
              <span className="font-bold text-amber-900">{totalPages}</span>
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1.5 text-sm font-medium text-amber-800 bg-amber-50 border border-amber-300 rounded-lg hover:bg-amber-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                <ChevronLeft size={16} />
              </button>
              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1.5 text-sm font-medium text-amber-800 bg-amber-50 border border-amber-300 rounded-lg hover:bg-amber-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
);

// --- MAIN COMPONENT ---
const AnalyticalDetails = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [profile] = useState(() => location.state || null);
  const [getStudentsExamDataById, { isLoading: isApiLoading }] =
    useLazyGetStudentsAnalyticsDataByCenterIdQuery();
  const studentsData = useSelector((state) => state.centreCounselorDashboard.studentsAnalyticsData);

  const { overallStats, subjectStats, chartData, allTestsForHistory, helpers } =
    useStudentAnalytics(studentsData);

  const hasTestData = useMemo(() => {
    if (!studentsData || Object.keys(studentsData).length === 0) return false;
    return Object.values(studentsData).some(
      (subjectTests) => Array.isArray(subjectTests) && subjectTests.length > 0
    );
  }, [studentsData]);

  const [filterSubject, setFilterSubject] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  const filteredTests = useMemo(() => {
    if (filterSubject === 'all') return allTestsForHistory;
    return allTestsForHistory.filter(
      (test) => test.subject.toLowerCase() === filterSubject.toLowerCase()
    );
  }, [allTestsForHistory, filterSubject]);

  const totalPages = useMemo(
    () => Math.ceil(filteredTests.length / CONSTANTS.ITEMS_PER_PAGE),
    [filteredTests]
  );

  const paginatedTests = useMemo(() => {
    const startIndex = (currentPage - 1) * CONSTANTS.ITEMS_PER_PAGE;
    return filteredTests.slice(startIndex, startIndex + CONSTANTS.ITEMS_PER_PAGE);
  }, [filteredTests, currentPage]);

  useEffect(() => {
    setCurrentPage(1);
  }, [filterSubject]);

  useEffect(() => {
    if (!profile) {
      navigate('/sasthra/center-counselor/dashboard');
      return;
    }
    const fetchStudentExamData = async () => {
      try {
        const res = await getStudentsExamDataById({
          studentId: profile.id,
          course: profile.course
        }).unwrap();
        dispatch(setStudentsAnalyticsData(res.data));
      } catch (error) {
        console.error('Error fetching data:', error);
        dispatch(setStudentsAnalyticsData(null));
      }
    };
    fetchStudentExamData();
    return () => {
      dispatch(setStudentsAnalyticsData(null));
    };
  }, [profile, navigate, getStudentsExamDataById, dispatch]);

  const handleBack = useCallback(() => navigate(-1), [navigate]);
  const handleFilterChange = useCallback((subject) => setFilterSubject(subject), []);
  const handlePageChange = useCallback(
    (page) => {
      if (page > 0 && page <= totalPages) {
        setCurrentPage(page);
      }
    },
    [totalPages]
  );

  if (!profile)
    return (
      <div className="min-h-screen flex items-center justify-center text-orange-600 p-8">
        <AlertCircle className="w-8 h-8 mr-2" />
        Profile Not Found, Redirecting...
      </div>
    );

  const isLoading = isApiLoading || (!studentsData && !hasTestData);

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <Header profile={profile} overallStats={overallStats} onBack={handleBack} helpers={helpers} />

      {isLoading ? (
        <div className="space-y-8 mt-8">
          <SkeletonLoader type="card" />
          <SkeletonLoader type="chart" />
        </div>
      ) : !hasTestData ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center p-8 mt-12 bg-white/50 backdrop-blur-md rounded-2xl border border-amber-300/60 shadow-lg">
          <BookOpen size={48} className="mx-auto text-amber-500 mb-4" />
          <h2 className="text-2xl font-bold text-amber-800 mb-2">No Test Data Available</h2>
          <p className="text-amber-600 max-w-md mx-auto">
            {profile.name} hasn't completed any tests yet. Performance analytics will appear here
            once they do.
          </p>
        </motion.div>
      ) : (
        <>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
            initial="hidden"
            animate="visible">
            <StatCard
              title="Total Tests"
              value={overallStats.totalTests}
              icon={BookOpen}
              colorClass="text-amber-600"
              delay={0.1}
            />
            <StatCard
              title="Overall Accuracy"
              value={`${overallStats.accuracy}%`}
              icon={Target}
              colorClass={helpers.getAccuracyColor(overallStats.accuracy)}
              delay={0.2}
            />
            <StatCard
              title="Average Score"
              value={overallStats.averageScore}
              icon={Award}
              colorClass="text-sky-600"
              delay={0.3}
            />
            <StatCard
              title="Questions Solved"
              value={overallStats.totalCorrect}
              subtitle={`of ${overallStats.totalQuestions}`}
              icon={Zap}
              colorClass="text-emerald-600"
              delay={0.4}
            />
          </motion.div>

          <Section title="Performance Analytics" icon={BarChart4} delay={0.2}>
            {chartData && <AnalyticsCharts chartData={chartData} />}
          </Section>

          <Section title="Subject-wise Performance" icon={TrendingUp} delay={0.3}>
            <SubjectPerformanceGrid subjectStats={subjectStats} helpers={helpers} />
          </Section>

          <Section title="Detailed Test History" icon={Clock} delay={0.4}>
            <TestHistoryTable
              tests={paginatedTests}
              subjects={Object.keys(subjectStats)}
              activeFilter={filterSubject}
              onFilterChange={handleFilterChange}
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </Section>
        </>
      )}
    </div>
  );
};

export default AnalyticalDetails;
