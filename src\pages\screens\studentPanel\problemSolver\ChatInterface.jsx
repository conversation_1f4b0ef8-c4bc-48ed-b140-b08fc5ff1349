
import { motion, AnimatePresence } from 'framer-motion';
import { MathJax, MathJaxContext } from 'better-react-mathjax';
import YouTubeVideos from './YouTubeVidoes';
import WebSearchResults from './WebSearchResults';
import MediaInput from './MediaInput';
import { FaVolumeUp, FaPlay, FaStop, FaExpand, FaArrowLeft, FaYoutube, FaGlobe } from 'react-icons/fa';
import { GiFairyWand } from 'react-icons/gi';
import { useState ,useCallback} from 'react';

// It's good practice to define the MathJax configuration outside the component
// to prevent it from being recreated on every render.
const mathJaxConfig = {
  loader: { load: ['[tex]/ams', '[tex]/bbox'] },
  tex: {
    packages: { '[+]': ['ams', 'bbox'] },
    inlineMath: [['$', '$'], ['\\(', '\\)']],
    displayMath: [['$$', '$$'], ['\\[', '\\]']]
  },
  svg: {
    fontCache: 'global'
  }
};

const ChatInterface = ({
  response,
  youtubeResults,
  previousYoutubeResults,
  isYoutubeLoading,
  isYouTubeMaximized,
  setIsYouTubeMaximized,
  getYouTubeVideoId,
  handleGenerateAudio,
  isAudioLoading,
  audioUrl,
  isAudioPlaying,
  setIsAudioPlaying,
  audioRef,
  aiThinking,
  chatContainerRef,
  robotImg,
  text,
  setText,
  audio,
  image,
  mode,
  setMode,
  language,
  setLanguage,
  isRecording,
  recordingTime,
  activeInput,
  showMediaOptions,
  setShowMediaOptions,
  isDoubtSolverLoading,
  handleSubmit,
  handleRecord,
  handleAudioUpload,
  handleImageUpload,
  handleDeleteAudio,
  handleDeleteImage,
  audioInputRef,
  imageInputRef,
  showCropModal,
  setShowCropModal,
  imageSrc,
  crop,
  setCrop,
  imageRef,
  croppedImage,
  handleCropComplete,
  showWebcamModal,
  setShowWebcamModal,
  videoRef,
  startWebcam,
  handleCaptureImage,
  stopWebcam,
  formatTime,
  userId,
  webSearchResults,
  isWebSearchLoading,
  handleWebSearch,
  lastText,
  lastAudio,
  lastImage
}) => {
  const [activeContentTab, setActiveContentTab] = useState('youtube'); // 'youtube' or 'websearch'

  const parseMarkdownToJSX = (markdown) => {
    if (!markdown) return [];
    const lines = markdown.split('\n').map((line) => line.trimEnd());
    const elements = [];
    let inList = false;
    let currentList = [];

    const endList = () => {
      if (inList) {
        elements.push({ type: 'ul', content: [...currentList] });
        inList = false;
        currentList = [];
      }
    };

    lines.forEach((line) => {
      let trimmedLine = line.trim();
      if (trimmedLine.startsWith('## ')) {
        endList();
        elements.push({ type: 'h2', content: trimmedLine.substring(3) });
      } else if (trimmedLine.startsWith('### ') || trimmedLine.startsWith('**Step')) {
        endList();
        if (trimmedLine.startsWith('### ')) {
          elements.push({ type: 'h3', content: trimmedLine.substring(4) });
        } else {
          const boldedHeading = trimmedLine.replace(/\*\*(.*?)\*\*/g, '$1');
          elements.push({ type: 'h3', content: boldedHeading });
        }
      } else if (trimmedLine.startsWith('- ')) {
        if (!inList) {
          inList = true;
        }
        currentList.push(line.substring(line.indexOf('- ') + 2).trimStart());
      } else if (trimmedLine.startsWith('Answer:')) {
        endList();
        let answerContent = trimmedLine.replace('Answer:', '').trim();
        answerContent = answerContent.replace(/\*\*(.*?)\*\*/g, '$1');
        elements.push({ type: 'answer', content: answerContent });
      } else if (line.trim().length > 0) {
        endList();
        elements.push({ type: 'p', content: line });
      }
    });

    endList(); // Ensure the last list is pushed
    return elements;
  };

  const renderTextWithMarkdown = (text) => {
    // This check prevents the error. If the input isn't a string, it can't be split.
    if (typeof text !== 'string') return text;
    
    return text.split(/(\*\*.*?\*\*|\*.*?\*)/g).map((segment, i) => {
      if (segment.startsWith('**') && segment.endsWith('**')) {
        return <strong key={i}>{segment.slice(2, -2)}</strong>;
      }
      if (segment.startsWith('*') && segment.endsWith('*')) {
        return <em key={i}>{segment.slice(1, -1)}</em>;
      }
      return <span key={i}>{segment}</span>;
    });
  };



const renderMessageText = useCallback((content) => {
  const mathJaxRegex = /(\$\$[\s\S]*?\$\$|\\\[[\s\S]*?\\\]|\$[\s\S]*?\$|\\\(.*?\\\))/g;

  // Handle conversation mode (array of dialogues)
  if (Array.isArray(content)) {
    return (
      <div className="space-y-4">
        {content.map((dialogue, index) => (
          <div key={index} className="flex items-start space-x-4" role="dialog" aria-label={`${dialogue.speaker} message`}>
            <div className="w-24 font-bold text-gray-900 shrink-0">
              {dialogue.speaker}:
            </div>
            <div className="flex-1">
              <MathJax dynamic hideUntilTypeset="first">
                {dialogue.utterance.split(mathJaxRegex).map((part, partIndex) => {
                  if (!part) return null;
                  if (mathJaxRegex.test(part)) {
                    return <span key={`${index}-${partIndex}`}>{part}</span>;
                  }
                  const jsxElements = parseMarkdownToJSX(part);
                  return jsxElements.map((element, i) => {
                    const uniqueKey = `${index}-${partIndex}-${i}`;
                    switch (element.type) {
                      case 'h2':
                        return <h2 key={uniqueKey} className="text-2xl font-bold mt-8 mb-4">{renderTextWithMarkdown(element.content)}</h2>;
                      case 'h3':
                        return <h3 key={uniqueKey} className="text-xl font-semibold mt-4 mb-2 pb-2">{renderTextWithMarkdown(element.content)}</h3>;
                      case 'answer':
                        return (
                          <div key={uniqueKey} className="rounded-lg p-4 my-4 bg-blue-50 border border-blue-200">
                            <p className="font-bold text-gray-900">Answer: {renderTextWithMarkdown(element.content)}</p>
                          </div>
                        );
                      case 'ul':
                        return (
                          <ul key={uniqueKey} className="list-disc list-inside pl-4 mb-2 space-y-1">
                            {element.content.map((li, liIndex) => (
                              <li key={liIndex} className="text-gray-900">
                                {renderTextWithMarkdown(li)}
                              </li>
                            ))}
                          </ul>
                        );
                      case 'p':
                        return <p key={uniqueKey} className="mb-2">{renderTextWithMarkdown(element.content)}</p>;
                      default:
                        return <p key={uniqueKey} className="mb-2">{renderTextWithMarkdown(element.content)}</p>;
                    }
                  });
                })}
              </MathJax>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Handle tutor/text mode (string content)
  if (typeof content !== 'string') return content;

  const parts = content.split(mathJaxRegex);

  return (
    <div
      className="
        text-gray-900 
        leading-relaxed
        [&_mjx-chtml[display='true']]:block
        [&_mjx-chtml[display='true']]:overflow-x-auto
        [&_mjx-chtml[display='true']]:bg-gray-50
        [&_mjx-chtml[display='true']]:border
        [&_mjx-chtml[display='true']]:border-gray-200
        [&_mjx-chtml[display='true']]:rounded-lg
        [&_mjx-chtml[display='true']]:p-4
        [&_mjx-chtml[display='true']]:my-3
        [&_mjx-chtml[display='true']]:shadow-sm
        [&_mjx-chtml[display='false']]:bg-blue-50
        [&_mjx-chtml[display='false']]:text-blue-900
        [&_mjx-chtml[display='false']]:rounde
        [&_mjx-chtml[display='false']]:px-1.5
        [&_mjx-chtml[display='false']]:py-0.5
        [&_mjx-chtml[display='false']]:mx-1
      "
    >
      <MathJax dynamic hideUntilTypeset="first">
        {parts.map((part, index) => {
          if (!part) return null;
          if (mathJaxRegex.test(part)) {
            return <span key={index}>{part}</span>;
          }
          const jsxElements = parseMarkdownToJSX(part);
          return jsxElements.map((element, i) => {
            const uniqueKey = `${index}-${i}`;
            switch (element.type) {
              case 'h2':
                return <h2 key={uniqueKey} className="text-2xl font-bold mt-8 mb-4">{renderTextWithMarkdown(element.content)}</h2>;
              case 'h3':
                return <h3 key={uniqueKey} className="text-xl font-semibold mt-4 mb-2 pb-2">{renderTextWithMarkdown(element.content)}</h3>;
              case 'answer':
                return (
                  <div key={uniqueKey} className="rounded-lg p-4 my-4 bg-blue-50 border border-blue-200">
                    <p className="font-bold text-gray-900">Answer: {renderTextWithMarkdown(element.content)}</p>
                  </div>
                );
              case 'ul':
                return (
                  <ul key={uniqueKey} className="list-disc list-inside pl-4 mb-2 space-y-1">
                    {element.content.map((li, liIndex) => (
                      <li key={liIndex} className="text-gray-900">
                        {renderTextWithMarkdown(li)}
                      </li>
                    ))}
                  </ul>
                );
              case 'p':
                return <p key={uniqueKey} className="mb-2">{renderTextWithMarkdown(element.content)}</p>;
              default:
                return <p key={uniqueKey} className="mb-2">{renderTextWithMarkdown(element.content)}</p>;
            }
          });
        })}
      </MathJax>
    </div>
  );
}, []);

  const formatMessage = (message, index) => {
    if (message.type === 'user') {
      return (
        <motion.div
          key={message.id || index}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex justify-end mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-3xl rounded-br-lg px-6 py-4 max-w-xs shadow-xl">
            <p className="text-sm font-medium">{message.content}</p>
          </div>
        </motion.div>
      );
    }
    return (
      <motion.div
        key={message.id || index}
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex justify-start mb-6">
        <div className="flex items-start space-x-4">
          <div className="bg-white border border-gray-100 rounded-3xl rounded-bl-lg px-6 py-4 shadow-xl max-w-[600px] overflow-hidden">
            {renderMessageText(message.content)}
            {index === response.length - 1 && (
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleGenerateAudio(message.content)}
                disabled={isAudioLoading}
                className="mt-3 px-4 py-2 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full text-white text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
                {isAudioLoading ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                    className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  />
                ) : (
                  <>
                    <FaVolumeUp className="text-sm" />
                    <span>Generate Audio</span>
                  </>
                )}
              </motion.button>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  const renderContentArea = () => {
    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={activeContentTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="h-full overflow-y-auto overflow-x-hidden"
        >
          {activeContentTab === 'websearch' ? (
            <WebSearchResults webSearchResults={webSearchResults} isWebSearchLoading={isWebSearchLoading} />
          ) : (
            <YouTubeVideos
              youtubeResults={youtubeResults.length > 0 ? youtubeResults : previousYoutubeResults}
              isYoutubeLoading={isYoutubeLoading}
              getYouTubeVideoId={getYouTubeVideoId}
            />
          )}
        </motion.div>
      </AnimatePresence>
    );
  };

  return (
    <MathJaxContext config={mathJaxConfig}>
      <div className="pt-20 pb-6 px-4 h-screen flex flex-col">
        <AnimatePresence>
          {!isYouTubeMaximized ? (
            <div className="flex w-full h-full">
              <div className="w-[60%] flex flex-col z-10">
                <div
                  ref={chatContainerRef}
                  className="flex-1 overflow-y-auto overflow-x-hidden px-4 py-6 space-y-4"
                  style={{ maxHeight: 'calc(100vh - 180px)' }}>
                  {response.length === 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 50 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex flex-col items-center justify-center h-full text-center space-y-6">
                      <div className="space-y-3">
                        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          SPARKIT BOT
                        </h2>
                        <p className="text-gray-600 text-lg max-w-md">
                          Your intelligent learning companion is ready to help! Ask me anything
                          about your studies.
                        </p>
                      </div>
                    </motion.div>
                  )}
                  <AnimatePresence mode="popLayout">
                    {response.map((message, index) => (
                      <div key={message.id || index}>
                        {formatMessage(message, index)}
                        {message.type === 'bot' && index === response.length - 1 && audioUrl && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex justify-start ml-16 space-x-3 mb-6">
                            <motion.button
                              whileHover={{ scale: 1.05, y: -2 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => {
                                if (audioRef.current) {
                                  if (isAudioPlaying) {
                                    audioRef.current.pause();
                                    setIsAudioPlaying(false);
                                  } else {
                                    audioRef.current.currentTime = 0;
                                    audioRef.current.play().then(() => setIsAudioPlaying(true)).catch(err => console.error('Audio playback failed:', err));
                                  }
                                }
                              }}
                              className="px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full text-white text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
                              {isAudioPlaying ? <FaStop className="text-sm" /> : <FaPlay className="text-sm" />}
                              <span>{isAudioPlaying ? 'Stop Audio' : 'Play Audio'}</span>
                            </motion.button>
                          </motion.div>
                        )}
                      </div>
                    ))}
                  </AnimatePresence>
                  {aiThinking && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="flex justify-start mb-6">
                      <div className="flex items-start space-x-4">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1], rotate: [0, 360] }}
                          transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                          className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                          <GiFairyWand className="text-white text-lg" />
                        </motion.div>
                        <div className="bg-white border border-gray-100 rounded-3xl rounded-bl-lg px-6 py-4 shadow-xl">
                          <div className="flex items-center space-x-3">
                            <span className="text-gray-600 font-medium">SPARKIT is thinking</span>
                            <div className="flex space-x-1">
                              {[0, 1, 2].map((i) => (
                                <motion.div
                                  key={i}
                                  animate={{ scale: [1, 1.5, 1], opacity: [0.3, 1, 0.3] }}
                                  transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY, delay: i * 0.2 }}
                                  className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full"
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </div>
                <MediaInput
                  text={text} setText={setText} audio={audio} image={image} mode={mode} setMode={setMode}
                  language={language} setLanguage={setLanguage} isRecording={isRecording} recordingTime={recordingTime}
                  activeInput={activeInput} showMediaOptions={showMediaOptions} setShowMediaOptions={setShowMediaOptions}
                  isDoubtSolverLoading={isDoubtSolverLoading} isYoutubeLoading={isYoutubeLoading}
                  handleSubmit={handleSubmit} handleRecord={handleRecord} handleAudioUpload={handleAudioUpload}
                  handleImageUpload={handleImageUpload} handleDeleteAudio={handleDeleteAudio} handleDeleteImage={handleDeleteImage}
                  audioInputRef={audioInputRef} imageInputRef={imageInputRef} showCropModal={showCropModal}
                  setShowCropModal={setShowCropModal} imageSrc={imageSrc} crop={crop} setCrop={setCrop}
                  imageRef={imageRef} croppedImage={croppedImage} handleCropComplete={handleCropComplete}
                  showWebcamModal={showWebcamModal} setShowWebcamModal={setShowWebcamModal} videoRef={videoRef}
                  startWebcam={startWebcam} handleCaptureImage={handleCaptureImage} stopWebcam={stopWebcam} formatTime={formatTime}
                />
              </div>
              {!showCropModal && !showWebcamModal && (
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  className="w-[40%] bg-white border-l border-gray-200 flex flex-col z-10">
                  <div className="border-b border-gray-200">
                    <div className="flex items-center justify-between p-4 pb-2">
                      <h3 className="text-xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">Related Content</h3>
                      <motion.button
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => setIsYouTubeMaximized(true)}
                        className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-300">
                        <FaExpand className="text-sm" />
                      </motion.button>
                    </div>
                    <div className="flex px-4 pb-2">
                      <div className="flex bg-gray-100 rounded-lg p-1 w-full">
                        <motion.button
                          whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} onClick={() => setActiveContentTab('youtube')}
                          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all duration-300 ${activeContentTab === 'youtube' ? 'bg-white text-red-600 shadow-sm' : 'text-gray-600 hover:text-red-500'}`}>
                          <FaYoutube className="text-lg" />
                          <span>Videos</span>
                          {isYoutubeLoading && <motion.div animate={{ rotate: 360 }} transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }} className="w-3 h-3 border border-red-500 border-t-transparent rounded-full" />}
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}
                          onClick={() => {
                            setActiveContentTab('websearch');
                            if (webSearchResults.length === 0 && !isWebSearchLoading) { handleWebSearch(); }
                          }}
                          className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all duration-300 ${activeContentTab === 'websearch' ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-green-500'}`}>
                          <FaGlobe className="text-lg" />
                          <span>Web</span>
                          {isWebSearchLoading && <motion.div animate={{ rotate: 360 }} transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }} className="w-3 h-3 border border-green-500 border-t-transparent rounded-full" />}
                        </motion.button>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 overflow-y-auto overflow-x-hidden p-4">{renderContentArea()}</div>
                </motion.div>
              )}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="fixed inset-0 z-50 bg-white flex flex-col">
              <div className="border-b border-gray-200">
                <div className="flex items-center justify-between p-4 pb-2">
                  <h3 className="text-xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">Related Content</h3>
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setIsYouTubeMaximized(false)}
                    className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-gradient-to-r from-red-100 to-pink-100 hover:text-red-600">
                    <FaArrowLeft className="text-lg" />
                  </motion.button>
                </div>
                <div className="flex px-4 pb-4">
                  <div className="flex bg-gray-100 rounded-lg p-1 w-full max-w-md">
                    <motion.button
                      whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} onClick={() => setActiveContentTab('youtube')}
                      className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-base font-medium transition-all duration-300 ${activeContentTab === 'youtube' ? 'bg-white text-red-600 shadow-sm' : 'text-gray-600 hover:text-red-500'}`}>
                      <FaYoutube className="text-xl" />
                      <span>Videos</span>
                      {isYoutubeLoading && <motion.div animate={{ rotate: 360 }} transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }} className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full" />}
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        setActiveContentTab('websearch');
                        if (webSearchResults.length === 0 && !isWebSearchLoading) { handleWebSearch(); }
                      }}
                      className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-base font-medium transition-all duration-300 ${activeContentTab === 'websearch' ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-green-500'}`}>
                      <FaGlobe className="text-xl" />
                      <span>Web Search</span>
                      {isWebSearchLoading && <motion.div animate={{ rotate: 360 }} transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }} className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full" />}
                    </motion.button>
                  </div>
                </div>
              </div>
              <div className="flex-1 overflow-y-auto overflow-x-hidden p-6">{renderContentArea()}</div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </MathJaxContext>
  );
};

export default ChatInterface;