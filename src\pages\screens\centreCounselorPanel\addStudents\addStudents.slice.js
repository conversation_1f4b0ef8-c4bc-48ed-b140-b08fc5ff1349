import { centerCounselorPanelApi } from '../../../../redux/api/api';
import { faceRegisterApi } from '../../../../redux/api/api';

export const addStudentsSlice = centerCounselorPanelApi.injectEndpoints({
  endpoints: (builder) => ({
    addStudents: builder.mutation({
      query: (body) => ({
        url: '/submit-student-request',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    }),
    checkDocumentUpload: builder.query({
      query: (requestId) => ({
        url: `/check-document-upload/${requestId}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Check Document Upload Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    }),
    centerListCourses: builder.query({
      query: () => ({
        url: '/center_list-courses',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Courses Response:', response);

        // Handle different possible response structures
        if (response && response.data && Array.isArray(response.data.courses)) {
          return response;
        }

        if (response && Array.isArray(response.courses)) {
          return {
            success: true,
            data: { courses: response.courses }
          };
        }

        if (Array.isArray(response)) {
          return {
            success: true,
            data: { courses: response }
          };
        }

        // Default fallback if none of the expected structures match
        return {
          success: true,
          data: { courses: [] }
        };
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    }),
    centerListBatches: builder.query({
      query: () => ({
        url: '/center_list-batches',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Batches Response:', response);

        // Handle different possible response structures
        if (response && response.data && Array.isArray(response.data.batches)) {
          return response;
        }

        if (response && Array.isArray(response.batches)) {
          return {
            success: true,
            data: { batches: response.batches }
          };
        }

        if (Array.isArray(response)) {
          return {
            success: true,
            data: { batches: response }
          };
        }

        // Default fallback if none of the expected structures match
        return {
          success: true,
          data: { batches: [] }
        };
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    }),
    studentDocument: builder.query({
      query: ({ documentType, documentId }) => ({
        url: `/student/${documentId}/document/${documentType}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Student Document Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    })
  })
});

export const {
  useAddStudentsMutation,
  useLazyCheckDocumentUploadQuery,
  useLazyCenterListCoursesQuery,
  useLazyCenterListBatchesQuery,
  useLazyStudentDocumentQuery
} = addStudentsSlice;

export const uploadApplicationForm = faceRegisterApi.injectEndpoints({
  endpoints: (builder) => ({
    uploadApplicationForm: builder.mutation({
      query: (formData) => ({
        url: '/upload-application-form',
        method: 'POST',
        body: formData,
        formData: true
      }),
      transformResponse: (response) => {
        console.log('Raw uploadApplicationForm response:', response);
        if (response.success) {
          const result = {
            ...response,
            form_id: response.form_id,
            confidence: response.confidence,
            comments: response.comments || [],
            extracted_data: response.extracted_data || response.editable_form || {}
          };
          console.log('Transformed response:', result);
          return result;
        }
        return response;
      },
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.message || 'Error uploading application form'
      })
    }),
    captureFaceImages: builder.mutation({
      query: (body) => ({
        url: '/capture-face-images',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Capture Face Images Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['FaceRegister']
    }),
    getFaceEmbeddings: builder.query({
      query: (formId) => ({
        url: `/get-face-embeddings/${formId}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Get Face Embeddings Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['FaceRegister']
    }),
    // slice.ts
    recaptureImage: builder.mutation({
      query: ({ studentId, frontFace, leftFace, rightFace }) => {
        const formData = new FormData();
        formData.append('front_face', frontFace, 'front_face.jpg');
        formData.append('left_face', leftFace, 'left_face.jpg');
        formData.append('right_face', rightFace, 'right_face.jpg');
        formData.append('student_id', studentId); // Assuming studentId is required

        return {
          url: `/student/${studentId}/face-embeddings`,
          method: 'PUT',
          body: formData,
          formData: true
        };
      },
      transformResponse: (response) => {
        console.log('Recapture Image Response:', response);
        return {
          success: response.success,
          message: response.message || 'Face embeddings updated successfully'
        };
      },
      transformErrorResponse: ({ status, data }) => ({
        status,
        message: data?.detail || 'Error recapturing face embeddings'
      }),
      invalidatesTags: ['FaceVerification', 'FaceRegister']
    })
  })
});

export const {
  useUploadApplicationFormMutation,
  useCaptureFaceImagesMutation,
  useLazyGetFaceEmbeddingsQuery,
  useRecaptureImageMutation
} = uploadApplicationForm;
