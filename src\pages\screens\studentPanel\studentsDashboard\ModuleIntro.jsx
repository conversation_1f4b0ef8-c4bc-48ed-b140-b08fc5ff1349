import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faVideo,
  faPenToSquare,
  faVials,
  faCirclePlay,
  faBolt,
  faBookOpen,
  faUsers,
  faRobot,
  faChalkboardUser,
  faMicroscope,
  faQuestionCircle,
  faChartPie,
  faStar,
  faFileLines,
  faArrowRight,
  faAtom,
  faPlay,
  faPlus,
  faFlask,
  faLightbulb,
  faComments,
  faCogs,
  faFile
} from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from 'react-router';
import { useGetStudentsQuery } from './students.Slice';

const modules = [
  {
    name: 'Live Streaming',
    icon: faVideo,
    desc: 'Interactive sessions with experts',
    color: 'var(--color-student)',
    link: '/sasthra/student/live-streaming',
    details: 'Join real-time classes with live streaming. Recordings available after sessions.',
    category: 'Interactive',
    actionText: 'Join Live Session',
    actionIcon: faPlay
  },
  {
    name: 'Create Test',
    icon: faPenToSquare,
    desc: 'Build your own assessments',
    color: 'var(--color-counselor)',
    link: '/sasthra/student/create-your-own-test',
    details: 'Customize tests with your own questions or from our question bank.',
    category: 'Assessment',
    actionText: 'Design Your Test',
    actionIcon: faPlus
  },
  {
    name: 'Mock Tests',
    icon: faVials,
    desc: 'Practice with exam simulators',
    color: 'var(--color-student)',
    link: '/sasthra/student/mock-test-simulation',
    details:
      'Full-length simulated exams with detailed analytics And also Attend subject-wise mock tests.',
    category: 'Assessment',
    actionText: 'Start Simulation',
    actionIcon: faPenToSquare
  },
  {
    name: 'Video Library',
    icon: faCirclePlay,
    desc: 'On-demand recorded content',
    color: 'var(--color-counselor)',
    link: '/sasthra/student/recorded-videos',
    details: 'Recorded video available for the live streaming classes.',
    category: 'Resources',
    actionText: 'Browse Videos',
    actionIcon: faPlay
  },
  {
    name: 'Booster Modules',
    icon: faBolt,
    desc: 'Quick revision boosters',
    color: 'var(--color-student)',
    link: '/sasthra/student/booster-module',
    details: 'Condensed revision packages with key concepts.',
    category: 'Resources',
    actionText: 'Boost Learning',
    actionIcon: faBolt
  },
  {
    name: 'E-Books',
    icon: faBookOpen,
    desc: 'Access digital books & resources',
    color: 'var(--color-counselor)',
    link: '/sasthra/student/ebook-centre',
    details: 'Comprehensive digital textbooks with interactive elements.',
    category: 'Resources',
    actionText: 'Open Library',
    actionIcon: faBookOpen
  },
  {
    name: 'Community',
    icon: faUsers,
    desc: 'Learn together with peers',
    color: 'var(--color-student)',
    link: '/sasthra/student/student-community',
    details: 'Discussion forums and peer-to-peer learning.',
    category: 'Community',
    actionText: 'Join Community',
    actionIcon: faComments
  },
  {
    name: 'AI Tutor',
    icon: faRobot,
    desc: 'Get smart guidance instantly',
    color: 'var(--color-counselor)',
    link: '/sasthra/student/ai-tutor',
    details: '24/7 AI assistant that adapts to your learning style.',
    category: 'AI Tools',
    actionText: 'Ask AI Tutor',
    actionIcon: faRobot
  },
  {
    name: 'Virtual Labs',
    icon: faMicroscope,
    desc: 'Run experiments virtually',
    color: 'var(--color-student)',
    link: '/sasthra/student/virtual-labs',
    details: 'Interactive lab simulations with real time interaction.',
    category: 'Interactive',
    actionText: 'Enter Lab',
    actionIcon: faFlask
  },
  {
    name: 'Problem Solver',
    icon: faQuestionCircle,
    desc: 'Step-by-step solutions',
    color: 'var(--color-counselor)',
    link: '/sasthra/student/problem-solver',
    details: 'Detailed explanations for complex problems.',
    category: 'AI Tools',
    actionText: 'Solve Problems',
    actionIcon: faLightbulb
  },
  {
    name: 'Dashboard',
    icon: faChartPie,
    desc: 'Track your performance',
    color: 'var(--color-student)',
    link: '/sasthra/student/dashboard',
    details: 'Comprehensive analytics with progress tracking.',
    category: 'Resources',
    actionText: 'View Analytics',
    actionIcon: faChartPie
  },
  {
    name: 'Smart Suggestions',
    icon: faStar,
    desc: 'Recommendations tailored to you',
    color: 'var(--color-counselor)',
    link: '/sasthra/student/recommendation',
    details: 'AI-powered content recommendations.',
    category: 'AI Tools',
    actionText: 'Get Suggestions',
    actionIcon: faStar
  },
  {
    name: 'Question Generator',
    icon: faFileLines,
    desc: 'Auto-generate practice sets',
    color: 'var(--color-student)',
    link: '/sasthra/student/question-generator',
    details: 'Upload content to generate questions automatically.',
    category: 'Assessment',
    actionText: 'Generate Questions',
    actionIcon: faFileLines
  },
  {
    name: 'Sasthra Tools',
    icon: faChalkboardUser,
    desc: 'Advanced learning tools',
    color: 'var(--color-counselor)',
    link: '/sasthra/student/sasthra-tools',
    details: 'Specialized chatbots for each subject.',
    category: 'Resources',
    actionText: 'Access Tools',
    actionIcon: faCogs
  },
  {
    name: 'Material Upload',
    icon: faFile,
    desc: 'Upload your materials and Access the available files',
    color: 'var(--color-student)',
    link: '/sasthra/student/material-upload',
    details: 'Upload your materials and Access the available files.',
    category: 'Resources',
    actionText: 'Access Gallery',
    actionIcon: faFile
  }
];

const categories = ['All', 'Interactive', 'Assessment', 'Resources', 'Community', 'AI Tools'];

const ModuleIntro = () => {
  const [activeCategory, setActiveCategory] = useState('All');
  const navigate = useNavigate();
  const studentId = sessionStorage.getItem('userId');
  const {
    data: studentsData,
    isLoading,
    isError,
    error
  } = useGetStudentsQuery(undefined, {
    skip: !studentId
  });
  const user = studentsData?.student || {
    username: sessionStorage.getItem('name') || 'Unknown',
    center_code: sessionStorage.getItem('centercode') || 'Unknown',
    phone: sessionStorage.getItem('phone') || 'Unknown',
    course: 'Unknown'
  };
  const filteredModules =
    activeCategory === 'All' ? modules : modules.filter((mod) => mod.category === activeCategory);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-4 sm:mb-6 md:mb-8 relative px-4 sm:px-6 md:px-8">
          {/* Welcome text with smooth gradient animation */}
          <motion.div
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, ease: [0.16, 1, 0.3, 1] }}
            className="mb-2 sm:mb-4 md:mb-6">
            <h1
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold"
              style={{ color: 'var(--color-student)' }}>
              Welcome back,{' '}
              <span className="text-[var(--color-counselor)]">
                {user.first_name || user.username}{' '}
              </span>{' '}
              !
            </h1>
          </motion.div>

          {/* Subtitle with staggered word animation */}
          <motion.div className="overflow-hidden">
            <motion.p
              className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl mx-auto relative z-10"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6, ease: [0.16, 1, 0.3, 1] }}>
              <motion.span
                className="inline-block mr-1 sm:mr-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}>
                Discover{' '}
              </motion.span>
              <motion.span
                className="inline-block font-medium mr-1 sm:mr-2"
                style={{ color: 'var(--color-counselor)' }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}>
                powerful tools{' '}
              </motion.span>
              <motion.span
                className="inline-block"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}>
                to enhance your learning
              </motion.span>
            </motion.p>
          </motion.div>
        </motion.div>
        <motion.div
          className="relative overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}>
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12 relative"></motion.div>

          {/* Revolutionary category selector */}
          <motion.div
            className="relative z-10 pb-8 sm:pb-12 md:pb-16 lg:pb-20"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}>
            <div className="flex justify-center px-4 sm:px-6 md:px-8">
              <div className="relative w-full max-w-sm sm:max-w-md md:max-w-2xl lg:max-w-4xl">
                {/* Connecting line */}
                <motion.div
                  className="absolute h-0.5 sm:h-1 top-1/2 left-0 right-0 bg-gray-200"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ delay: 0.9, duration: 0.8 }}
                />

                <div className="flex justify-between relative">
                  {categories.map((category, i) => {
                    const isStudentColor = i % 2 === 0;
                    const color = isStudentColor
                      ? 'var(--color-student)'
                      : 'var(--color-counselor)';
                    const isActive = activeCategory === category;

                    return (
                      <motion.div
                        key={category}
                        className="relative"
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.9 + i * 0.1 }}>
                        <button
                          onClick={() => setActiveCategory(category)}
                          className={`relative z-10 w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center text-white transition-all ${
                            isActive ? 'scale-110' : 'scale-100'
                          }`}
                          style={{
                            background: color,
                            boxShadow: isActive
                              ? `0 10px 20px -5px ${color}80`
                              : '0 5px 15px rgba(0,0,0,0.1)'
                          }}>
                          {category === 'All' ? (
                            <FontAwesomeIcon
                              icon={faAtom}
                              className="text-base sm:text-lg md:text-xl"
                            />
                          ) : (
                            <span className="text-xs sm:text-sm md:text-base font-bold">
                              {category.charAt(0)}
                            </span>
                          )}
                        </button>

                        {/* Label */}
                        <motion.div
                          className={`absolute left-1/2 transform -translate-x-1/2 mt-1 sm:mt-2 md:mt-3 lg:mt-4 text-xs sm:text-sm md:text-base lg:text-lg font-medium whitespace-nowrap ${
                            isActive ? 'text-gray-900 font-bold' : 'text-gray-500'
                          }`}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                         >
                          {category}
                        </motion.div>

                        {/* Active indicator */}
                        {isActive && (
                          <motion.div
                            className="absolute -bottom-1 sm:-bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-3 sm:w-4 sm:h-4 rotate-45"
                            style={{ background: color }}
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: 'spring', stiffness: 500 }}
                          />
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Modules Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {filteredModules.map((module, index) => (
            <motion.div
              key={module.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="h-80 perspective-1000 group"
              whileHover={{ scale: 1.03 }}>
              {/* Flip Card Container */}
              <div className="relative w-full h-full transition-transform duration-700 transform-style-preserve-3d group-hover:rotate-y-180">
                {/* Front of Card - Creative Design */}
                <div className="absolute w-full h-full backface-hidden rounded-2xl overflow-hidden bg-white border-2 border-gray-200 shadow-lg">
                  <div className="h-full flex flex-col p-6 items-center justify-center text-center relative">
                    {/* Decorative elements */}
                    <div
                      className="absolute top-0 left-0 w-full h-2"
                      style={{ backgroundColor: module.color }}
                    />
                    <div
                      className="absolute -top-4 -right-4 w-20 h-20 rounded-full opacity-10"
                      style={{ backgroundColor: module.color }}
                    />

                    {/* Icon with creative background */}
                    <div className="relative z-10 mb-6">
                      <div
                        className="w-20 h-20 rounded-2xl flex items-center justify-center transform rotate-45 overflow-hidden"
                        style={{ backgroundColor: module.color }}>
                        <motion.div className="transform -rotate-45" whileHover={{ scale: 1.1 }}>
                          <FontAwesomeIcon icon={module.icon} className="text-white text-3xl" />
                        </motion.div>
                      </div>
                    </div>

                    {/* Content */}
                    <h3 className="text-xl font-bold mb-2 text-black relative z-10">
                      {module.name}
                    </h3>
                    <p className="text-gray-600 mb-4 relative z-10">{module.desc}</p>

                    {/* Category badge with animation */}
                    <motion.div
                      className="text-xs font-medium px-3 py-1 rounded-full relative z-10"
                      style={{
                        backgroundColor: module.color,
                        color: 'white'
                      }}
                      whileHover={{ scale: 1.05 }}>
                      {module.category}
                    </motion.div>

                    {/* Hover hint */}
                    <div className="absolute bottom-4 text-xs text-gray-400 group-hover:opacity-0 transition-opacity">
                      Hover to explore
                    </div>
                  </div>
                </div>

                {/* Back of Card - Enhanced Creative Design */}
                <div
                  className="absolute w-full h-full backface-hidden rounded-2xl overflow-hidden rotate-y-180 shadow-xl"
                  style={{
                    backgroundColor: module.color,
                    border: `2px solid ${module.color}`,
                    boxShadow: `0 10px 25px -5px ${module.color}80`
                  }}>
                  <div className="h-full flex flex-col p-6 text-white relative">
                    {/* Decorative corner elements */}
                    <div className="absolute top-0 left-0 w-16 h-16 border-t-2 border-l-2 border-white opacity-20 rounded-tl-2xl" />
                    <div className="absolute bottom-0 right-0 w-16 h-16 border-b-2 border-r-2 border-white opacity-20 rounded-br-2xl" />

                    {/* Module icon in corner */}
                    <div className="absolute top-4 right-4 w-10 h-10 flex items-center justify-center opacity-20">
                      <FontAwesomeIcon icon={module.icon} className="text-white text-xl" />
                    </div>

                    <h3 className="text-xl font-bold mb-3 relative z-10">{module.name}</h3>
                    <p className="text-sm mb-6 flex-grow relative z-10 opacity-90">
                      {module.details}
                    </p>

                    {/* Unique action button for each module */}
                    <motion.button
                      className="w-full py-3 px-4 bg-white rounded-lg font-medium flex items-center justify-center gap-2 mt-auto relative z-10"
                      style={{
                        color: module.color,
                        boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
                      }}
                      whileHover={{
                        scale: 1.02,
                        boxShadow: '0 6px 12px rgba(0,0,0,0.15)'
                      }}
                      whileTap={{ scale: 0.98 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(module.link);
                      }}>
                      <FontAwesomeIcon icon={module.actionIcon} />
                      {module.actionText}
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Global Styles for Flip Effect */}
      <style jsx global>{`
        .perspective-1000 {
          perspective: 1000px;
        }
        .transform-style-preserve-3d {
          transform-style: preserve-3d;
        }
        .backface-hidden {
          backface-visibility: hidden;
        }
        .rotate-y-180 {
          transform: rotateY(180deg);
        }
      `}</style>
    </div>
  );
};

export default ModuleIntro;
