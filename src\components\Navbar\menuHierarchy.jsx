import { ChartNoAxesCombined, Folders, LayoutDashboard } from 'lucide-react';
import { FiBookOpen } from 'react-icons/fi';
import { MdOutlineAssessment } from 'react-icons/md';
import { RiUserCommunityLine } from 'react-icons/ri';

export const menuHierarchy = [
  {
    uuid: 'overview',
    name: 'Overview',
    icon: <LayoutDashboard size={24} />,
    roles: [
      'director',
      'center_counselor',
      'student',
      'faculty',
      'kota_teacher',
      'parent',
      'mendor'
    ]
  },

  {
    uuid: 'learning',
    name: 'Learning Resources',
    icon: <FiBookOpen size={24} />,
    children: [
      { name: 'Live Streaming', roles: ['kota_teacher', 'student'] },
      { name: 'Recorded Video', roles: ['student'] },
      { name: 'E Book Center', roles: ['student'] },
      { name: 'Virtual labs', roles: ['student'] },
      { name: 'Material Upload', roles: ['kota_teacher', 'student', 'faculty'] },
      { name: 'Study Plan', roles: ['director'] },
      { name: 'Courses', roles: ['director'] },
      { name: 'Subjects', roles: ['director'] },
      { name: 'Live Viewer', roles: ['faculty'] }
    ]
  },
  {
    uuid: 'assessment',
    name: 'Assessment & Practice',
    icon: <MdOutlineAssessment size={24} />,
    children: [
      { name: 'Create Your Own Test', roles: ['student'] },
      { name: 'Mock Test Simulation', roles: ['student'] },
      { name: 'Booster Module', roles: ['student'] },
      { name: 'Problem Solver', roles: ['student'] },
      { name: 'Question Generator', roles: ['student'] },
      { name: 'Paper Based Test', roles: ['faculty'] },
      { name: 'OMR Sheet Evaluation', roles: ['faculty'] },
      { name: 'Upload Question Paper', roles: ['director'] },
      { name: 'Paper Based Evaluator', roles: ['faculty'] }
    ]
  },
  {
    uuid: 'analytics',
    name: 'Analytics',
    icon: <ChartNoAxesCombined size={24} />,
    children: [
      { name: 'Dashboard', roles: ['director', 'student', 'center_counselor', 'parent'] },
      // { name: 'Mentor Dashboard', roles: ['mendor'] },
      { name: 'AI - Tutor', roles: ['student'] },
      { name: 'AI Tutor', roles: ['faculty'] },
      { name: 'Recommendation', roles: ['student'] },
      { name: 'Attendance', roles: ['parent'] },
      { name: 'Evaluator Result', roles: ['faculty'] },
      { name: 'PBE Result', roles: ['parent'] },
      { name: 'OMR Evaluation Result', roles: ['faculty'] }
    ]
  },
  {
    uuid: 'community',
    name: 'Community & Tools',
    icon: <RiUserCommunityLine size={24} />,
    children: [
      { name: 'Student Community', roles: ['student'] },
      { name: 'Mentor Community', roles: ['mendor'] },
      { name: 'Feedback', roles: ['student'] },
      { name: 'Events', roles: ['director'] },
      { name: 'Schedule Events', roles: ['kota_teacher'] },
      { name: 'Inbox', roles: ['director'] },
      { name: 'Sasthra Tools', roles: ['student'] }
    ]
  },

  {
    uuid: 'admin',
    name: 'Management',
    icon: <Folders size={24} />,
    children: [
      { name: 'Add Center', roles: ['director'] },
      { name: 'List Centers', roles: ['director'] },
      { name: 'Center Info', roles: ['parent'] },
      { name: 'Mapping Centers', roles: ['kota_teacher'] },
      { name: 'Add Students', roles: ['center_counselor'] },
      { name: 'List Students', roles: ['center_counselor'] },
      { name: 'Student Info', roles: ['parent'] },
      { name: 'Add Teachers', roles: ['director'] },
      { name: 'List Teachers', roles: ['director', 'center_counselor'] },
      { name: 'Add Faculty', roles: ['center_counselor'] },
      { name: 'List Faculty', roles: ['center_counselor'] },
      { name: 'Add Mentors', roles: ['director'] },
      { name: 'List Mentors', roles: ['director'] },
      { name: 'Batches', roles: ['director'] },
      { name: 'Process Selector', roles: ['director'] }
    ]
  }
];
