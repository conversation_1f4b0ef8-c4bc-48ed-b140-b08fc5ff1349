// // import React, { useState, useEffect, useRef } from 'react';
// // import { motion, AnimatePresence } from 'framer-motion';
// // import { toast } from 'react-toastify';
// // import {
// //   Room,
// //   RoomEvent,
// //   createLocalVideoTrack,
// //   createLocalAudioTrack,
// //   createLocalScreenTracks
// // } from 'livekit-client';
// // import {
// //   FaVideo,
// //   FaVideoSlash,
// //   FaMicrophone,
// //   FaMicrophoneSlash,
// //   FaShareSquare,
// //   FaStopCircle,
// //   FaPhoneSlash,
// //   FaUser
// // } from 'react-icons/fa';

// // // Glass morphism styles (replicated for consistency)
// // const glassStyle = {
// //   background: 'rgba(255, 255, 255, 0.1)',
// //   backdropFilter: 'blur(16px)',
// //   border: '1px solid rgba(255, 255, 255, 0.2)',
// //   boxShadow: '0 8px 32px rgba(31, 38, 135, 0.2)'
// // };

// // // Animation variants for video call overlay
// // const videoCallOverlayVariants = {
// //   hidden: { opacity: 0, scale: 0.9, y: 50 },
// //   visible: {
// //     opacity: 1,
// //     scale: 1,
// //     y: 0,
// //     transition: { type: 'spring', damping: 30, stiffness: 400, delay: 0.1 }
// //   },
// //   exit: { opacity: 0, scale: 0.9, y: 50, transition: { duration: 0.3 } }
// // };

// // const videoPanelVariants = {
// //   hidden: { opacity: 0, scale: 0.9 },
// //   visible: { opacity: 1, scale: 1, transition: { duration: 0.5, ease: 'easeOut' } },
// // };

// // const controlButtonVariants = {
// //   initial: { scale: 1 },
// //   hover: { scale: 1.1, backgroundColor: 'rgba(255, 255, 255, 0.2)' },
// //   tap: { scale: 0.9 }
// // };

// // const VideocallStudents = ({ isOpen, onCloseCall, roomName: initialRoomName, livekitToken: initialLivekitToken, livekitUrl: initialLivekitUrl }) => {
// //   const [room, setRoom] = useState(null);
// //   const [localVideoTrack, setLocalVideoTrack] = useState(null);
// //   const [localAudioTrack, setLocalAudioTrack] = useState(null);
// //   const [screenTrack, setScreenTrack] = useState(null);
// //   const [isCameraOn, setIsCameraOn] = useState(true);
// //   const [isMicOn, setIsMicOn] = useState(true);
// //   const [isScreenSharing, setIsScreenSharing] = useState(false);
// //   const [connectionStatus, setConnectionStatus] = useState('connecting'); // connecting, connected, disconnected, failed

// //   // Refs for video elements
// //   const localVideoRef = useRef(null);
// //   const remoteVideoRef = useRef(null);
// //   const remoteAudioRef = useRef(null);

// //   useEffect(() => {
// //     // Cleanup function for Livekit resources
// //     const cleanupLivekit = () => {
// //       console.log('Student: Cleaning up Livekit resources...');
// //       if (room) {
// //         console.log('Student: Disconnecting from Livekit room.');
// //         room.disconnect();
// //         setRoom(null);
// //       }
// //       if (localVideoTrack) {
// //         console.log('Student: Stopping and detaching local video track.');
// //         localVideoTrack.stop();
// //         localVideoTrack.detach();
// //         setLocalVideoTrack(null);
// //       }
// //       if (localAudioTrack) {
// //         console.log('Student: Stopping and detaching local audio track.');
// //         localAudioTrack.stop();
// //         localAudioTrack.detach();
// //         setLocalAudioTrack(null);
// //       }
// //       if (screenTrack) {
// //         console.log('Student: Stopping and detaching screen track.');
// //         screenTrack.stop();
// //         screenTrack.detach();
// //         setScreenTrack(null);
// //       }
// //       // Reset call state variables
// //       setIsCameraOn(true);
// //       setIsMicOn(true);
// //       setIsScreenSharing(false);
// //       setConnectionStatus('disconnected');
// //       console.log('Student: Livekit cleanup complete.');
// //     };

// //     if (!isOpen || !initialLivekitUrl || !initialLivekitToken || !initialRoomName) {
// //       // If the overlay is hidden or essential Livekit info is missing, clean up
// //       if (room || localVideoTrack || localAudioTrack || screenTrack) {
// //         cleanupLivekit();
// //       }
// //       setConnectionStatus('disconnected'); // Ensure status is correct when modal is closed or not ready
// //       return;
// //     }

// //     const initRoom = async () => {
// //       console.log('Student: Initializing Livekit room connection...');
// //       setConnectionStatus('connecting');
// //       const newRoom = new Room();
// //       setRoom(newRoom); // Set room early to indicate connection attempt is ongoing

// //       try {
// //         await newRoom.connect(initialLivekitUrl, initialLivekitToken);
// //         toast.success('Connected to call!', { icon: '📞' });
// //         console.log('Student: Successfully connected to Livekit room.');
// //         setConnectionStatus('connected');

// //         // Publish local tracks
// //         console.log('Student: Creating local video and audio tracks...');
// //         const videoTrack = await createLocalVideoTrack();
// //         const audioTrack = await createLocalAudioTrack();

// //         await newRoom.localParticipant.publishTrack(videoTrack);
// //         await newRoom.localParticipant.publishTrack(audioTrack);
// //         console.log('Student: Published local video and audio tracks.');

// //         setLocalVideoTrack(videoTrack);
// //         setLocalAudioTrack(audioTrack);

// //         // Attach local tracks directly using refs
// //         if (localVideoRef.current) {
// //           videoTrack.attach(localVideoRef.current);
// //           console.log('Student: Local video track attached to ref.');
// //         } else {
// //           console.warn('Student: Local video ref not available when attaching track.');
// //         }

// //         // Event listeners for remote tracks (mentor's tracks)
// //         newRoom.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
// //           console.log(`Student: Track subscribed: Kind=${track.kind}, Participant=${participant.identity}, TrackName=${publication.trackName}`);
// //           if (track.kind === 'video' || track.kind === 'screen_video') {
// //             if (remoteVideoRef.current) {
// //               track.attach(remoteVideoRef.current);
// //               console.log('Student: Remote video/screen track attached to remote video ref.');
// //             } else {
// //               console.warn('Student: Remote video ref not available when attaching remote track.');
// //             }
// //           } else if (track.kind === 'audio') {
// //             if (remoteAudioRef.current) {
// //               track.attach(remoteAudioRef.current);
// //               console.log('Student: Remote audio track attached to remote audio ref.');
// //             } else {
// //               console.warn('Student: Remote audio ref not available when attaching remote audio track.');
// //             }
// //           }
// //         });

// //         newRoom.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
// //           console.log(`Student: Track unsubscribed: Kind=${track.kind}, Participant=${participant.identity}, TrackName=${publication.trackName}`);
// //           track.detach(); // Explicitly detach on unsubscribe
// //           // Optionally, handle if the only video track (main camera) is removed
// //           if (track.kind === 'video' && remoteVideoRef.current) {
// //             // Clear the srcObject if the main remote video track is removed
// //             if (remoteVideoRef.current.srcObject === track.mediaStream) {
// //               remoteVideoRef.current.srcObject = null;
// //             }
// //           }
// //         });

// //         newRoom.on(RoomEvent.Disconnected, (reason) => {
// //           toast.info(`Disconnected from call. Reason: ${reason}`);
// //           console.log(`Student: Livekit room disconnected. Reason: ${reason}`);
// //           cleanupLivekit(); // Full cleanup on disconnection
// //           onCloseCall(); // Notify parent to close modal
// //         });

// //         newRoom.on(RoomEvent.ParticipantConnected, (participant) => {
// //           console.log(`Student: Participant connected: ${participant.identity}`);
// //         });

// //         newRoom.on(RoomEvent.ParticipantDisconnected, (participant) => {
// //           console.log(`Student: Participant disconnected: ${participant.identity}`);
// //           toast.info(`${participant.identity} has left the call.`);
// //           endCall(false); // Do not send end signal to backend as it's a remote disconnection
// //         });

// //       } catch (error) {
// //         console.error("Student: Livekit connection error:", error);
// //         toast.error('Failed to connect to video call. Please check your network.', { icon: '❌' });
// //         setConnectionStatus('failed');
// //         // Clean up and hide overlay on connection failure
// //         cleanupLivekit();
// //         onCloseCall(); // Notify parent to close modal
// //       }
// //     };

// //     if (isOpen && initialLivekitUrl && initialLivekitToken && initialRoomName) {
// //       initRoom();
// //     }

// //     // Cleanup when component unmounts or dependencies change (causing re-run)
// //     return cleanupLivekit;
// //   }, [isOpen, initialLivekitUrl, initialLivekitToken, initialRoomName]); // Depend on relevant props and isOpen

// //   const toggleCamera = async () => {
// //     if (!localVideoTrack) {
// //       toast.warn('Camera track not available yet.', { icon: '⚠️' });
// //       return;
// //     }
// //     const newCameraState = !isCameraOn;
// //     await localVideoTrack.mute(!newCameraState); // Mute if turning off, unmute if turning on
// //     setIsCameraOn(newCameraState);
// //     console.log(`Student: Camera toggled: ${newCameraState ? 'On' : 'Off'}`);
// //   };

// //   const toggleMic = async () => {
// //     if (!localAudioTrack) {
// //       toast.warn('Microphone track not available yet.', { icon: '⚠️' });
// //       return;
// //     }
// //     const newMicState = !isMicOn;
// //     await localAudioTrack.mute(!newMicState); // Mute if turning off, unmute if turning on
// //     setIsMicOn(newMicState);
// //     console.log(`Student: Microphone toggled: ${newMicState ? 'On' : 'Off'}`);
// //   };

// //   const toggleScreenShare = async () => {
// //     if (!room || !room.localParticipant) {
// //       toast.error("Not connected to a call.", { icon: '❌' });
// //       console.error('Student: Attempted screen share without room or local participant.');
// //       return;
// //     }

// //     if (isScreenSharing) {
// //       if (screenTrack) {
// //         console.log('Student: Stopping screen share...');
// //         await room.localParticipant.unpublishTrack(screenTrack);
// //         screenTrack.stop();
// //         screenTrack.detach();
// //         setScreenTrack(null);
// //       }
// //       setIsScreenSharing(false);
// //       toast.info('Screen sharing stopped.');
// //       console.log('Student: Screen sharing stopped.');
// //     } else {
// //       try {
// //         console.log('Student: Starting screen share...');
// //         const [newScreenTrack] = await createLocalScreenTracks({ audio: true });
// //         await room.localParticipant.publishTrack(newScreenTrack);
// //         setScreenTrack(newScreenTrack);
// //         setIsScreenSharing(true);
// //         toast.success('Screen sharing started!', { icon: '🖥️' });
// //         console.log('Student: Screen sharing started.');
// //       } catch (e) {
// //         console.error("Student: Failed to start screen share:", e);
// //         toast.error('Failed to start screen share. Please check browser permissions or try again.', { icon: '🚫' });
// //       }
// //     }
// //   };

// //   const endCall = async (sendEndSignal = true) => {
// //     console.log('Student: Ending call...');
// //     // Send signal to backend only if initiated by user and roomName exists
// //     if (sendEndSignal && initialRoomName) {
// //       try {
// //         console.log(`Student: Sending end call signal to backend for room: ${initialRoomName}`);
// //         await fetch('https://testing.sasthra.in/call/end', {
// //           method: 'POST',
// //           headers: { 'Content-Type': 'application/json' },
// //           body: JSON.stringify({ room_name: initialRoomName }),
// //         });
// //         toast.info('Call ended successfully.');
// //       } catch (err) {
// //         toast.error('Failed to send end call signal to server.');
// //         console.error('Student: Error ending call on backend:', err);
// //       }
// //     }

// //     // Perform Livekit specific cleanup
// //     if (room) {
// //       console.log('Student: Disconnecting from Livekit room...');
// //       room.disconnect(); // Disconnect from Livekit room
// //     }

// //     // Ensure all local tracks are stopped and detached
// //     if (localVideoTrack) {
// //       localVideoTrack.stop();
// //       localVideoTrack.detach();
// //     }
// //     if (localAudioTrack) {
// //       localAudioTrack.stop();
// //       localAudioTrack.detach();
// //     }
// //     if (screenTrack) {
// //       screenTrack.stop();
// //       screenTrack.detach();
// //     }

// //     // Reset all call-related states and notify parent
// //     setRoom(null);
// //     setLocalVideoTrack(null);
// //     setLocalAudioTrack(null);
// //     setScreenTrack(null);
// //     setIsCameraOn(true);
// //     setIsMicOn(true);
// //     setIsScreenSharing(false);
// //     setConnectionStatus('disconnected');
// //     onCloseCall(); // This will close the modal and reset parent states
// //     console.log('Student: Call ended and states reset.');
// //   };

// //   return (
// //     <AnimatePresence>
// //       {isOpen && (
// //         <motion.div
// //           variants={videoCallOverlayVariants}
// //           initial="hidden"
// //           animate="visible"
// //           exit="exit"
// //           className="fixed inset-0 bg-black/90 flex flex-col items-center justify-center z-60 p-4"
// //         >
// //           {connectionStatus === 'connecting' && (
// //             <div className="flex flex-col items-center text-white">
// //               <motion.div
// //                 className="w-16 h-16 border-4 border-white border-t-transparent rounded-full"
// //                 animate={{ rotate: 360 }}
// //                 transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
// //               />
// //               <p className="mt-4 text-xl font-semibold">Connecting to mentor...</p>
// //               <p className="text-sm text-gray-300">Please wait, setting up your call.</p>
// //             </div>
// //           )}

// //           {connectionStatus === 'failed' && (
// //             <div className="flex flex-col items-center text-red-400">
// //               <FaTimes className="text-5xl mb-2" />
// //               <p className="mt-4 text-xl font-semibold">Connection Failed</p>
// //               <p className="text-sm text-red-300">Could not connect to the call. Please try again.</p>
// //               <motion.button
// //                 whileHover={{ scale: 1.05 }}
// //                 whileTap={{ scale: 0.95 }}
// //                 onClick={() => onCloseCall()} // Close the video call overlay
// //                 className="mt-4 px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md">
// //                 Close Call
// //               </motion.button>
// //             </div>
// //           )}

// //           {connectionStatus === 'connected' && (
// //             <motion.div
// //               variants={videoPanelVariants}
// //               initial="hidden"
// //               animate="visible"
// //               className="flex flex-col w-full max-w-5xl h-full max-h-[calc(100vh-80px)] bg-gray-900 rounded-2xl overflow-hidden shadow-2xl relative"
// //             >
// //               {/* Video Streams Container */}
// //               <div className="flex-1 flex flex-col lg:flex-row gap-4 p-4 items-center justify-center">
// //                 <motion.div
// //                   className="relative w-full h-1/2 lg:h-full lg:w-1/2 rounded-xl overflow-hidden bg-gray-800 flex items-center justify-center shadow-lg border border-gray-700"
// //                   initial={{ opacity: 0, scale: 0.8 }}
// //                   animate={{ opacity: 1, scale: 1 }}
// //                   transition={{ duration: 0.5, delay: 0.2 }}
// //                 >
// //                   <video
// //                     id="local-video"
// //                     ref={localVideoRef}
// //                     autoPlay
// //                     playsInline
// //                     muted // Mute local video to prevent echo
// //                     className="w-full h-full object-cover"
// //                   />
// //                   <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-md">
// //                     You
// //                   </div>
// //                 </motion.div>

// //                 <motion.div
// //                   className="relative w-full h-1/2 lg:h-full lg:w-1/2 rounded-xl overflow-hidden bg-gray-800 flex items-center justify-center shadow-lg border border-gray-700"
// //                   initial={{ opacity: 0, scale: 0.8 }}
// //                   animate={{ opacity: 1, scale: 1 }}
// //                   transition={{ duration: 0.5, delay: 0.3 }}
// //                 >
// //                   <video
// //                     id="remote-video"
// //                     ref={remoteVideoRef}
// //                     autoPlay
// //                     playsInline
// //                     className="w-full h-full object-cover"
// //                   />
// //                   <audio id="remote-audio" ref={remoteAudioRef} autoPlay />
// //                   <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-md">
// //                     Mentor
// //                   </div>
// //                   {/* Placeholder for mentor if no remote video stream is attached */}
// //                   {!remoteVideoRef.current?.srcObject && (
// //                     <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-400 text-lg p-4 text-center">
// //                       <FaUser className="text-5xl mb-2" />
// //                       <span>Waiting for mentor to join or enable camera...</span>
// //                     </div>
// //                   )}
// //                 </motion.div>
// //               </div>

// //               {/* Call Controls */}
// //               <motion.div
// //                 style={glassStyle}
// //                 className="p-4 flex justify-center space-x-6 rounded-b-2xl border-t border-gray-700/50"
// //                 initial={{ y: 100, opacity: 0 }}
// //                 animate={{ y: 0, opacity: 1 }}
// //                 transition={{ type: 'spring', damping: 20, stiffness: 200, delay: 0.5 }}
// //               >
// //                 <motion.button
// //                   variants={controlButtonVariants}
// //                   whileHover="hover"
// //                   whileTap="tap"
// //                   onClick={toggleCamera}
// //                   className={`p-3 rounded-full transition-colors duration-200 ${
// //                     isCameraOn ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-gray-700 hover:bg-gray-600'
// //                   } text-white text-xl flex items-center justify-center`}
// //                   title={isCameraOn ? 'Turn Off Camera' : 'Turn On Camera'}
// //                 >
// //                   {isCameraOn ? <FaVideo /> : <FaVideoSlash />}
// //                 </motion.button>

// //                 <motion.button
// //                   variants={controlButtonVariants}
// //                   whileHover="hover"
// //                   whileTap="tap"
// //                   onClick={toggleMic}
// //                   className={`p-3 rounded-full transition-colors duration-200 ${
// //                     isMicOn ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-gray-700 hover:bg-gray-600'
// //                   } text-white text-xl flex items-center justify-center`}
// //                   title={isMicOn ? 'Mute Microphone' : 'Unmute Microphone'}
// //                 >
// //                   {isMicOn ? <FaMicrophone /> : <FaMicrophoneSlash />}
// //                 </motion.button>

// //                 <motion.button
// //                   variants={controlButtonVariants}
// //                   whileHover="hover"
// //                   whileTap="tap"
// //                   onClick={toggleScreenShare}
// //                   className={`p-3 rounded-full transition-colors duration-200 ${
// //                     isScreenSharing ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-gray-700 hover:bg-gray-600'
// //                   } text-white text-xl flex items-center justify-center`}
// //                   title={isScreenSharing ? 'Stop Screen Share' : 'Share Screen'}
// //                 >
// //                   {isScreenSharing ? <FaStopCircle /> : <FaShareSquare />}
// //                 </motion.button>

// //                 <motion.button
// //                   variants={controlButtonVariants}
// //                   whileHover="hover"
// //                   whileTap="tap"
// //                   onClick={() => endCall(true)}
// //                   className="p-3 rounded-full bg-red-600 hover:bg-red-700 text-white text-xl transition-colors duration-200 flex items-center justify-center"
// //                   title="End Call"
// //                 >
// //                   <FaPhoneSlash />
// //                 </motion.button>
// //               </motion.div>
// //             </motion.div>
// //           )}
// //         </motion.div>
// //       )}
// //     </AnimatePresence>
// //   );
// // };

// // export default VideocallStudents;






// import React, { useState, useEffect, useRef } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
// import { toast } from 'react-toastify';
// import {
//   Room,
//   RoomEvent,
//   createLocalVideoTrack,
//   createLocalAudioTrack,
//   createLocalScreenTracks
// } from 'livekit-client';
// import {
//   FaVideo,
//   FaVideoSlash,
//   FaMicrophone,
//   FaMicrophoneSlash,
//   FaShareSquare,
//   FaStopCircle,
//   FaPhoneSlash,
//   FaUser
// } from 'react-icons/fa';

// const glassStyle = {
//   background: 'rgba(255, 255, 255, 0.1)',
//   backdropFilter: 'blur(16px)',
//   border: '1px solid rgba(255, 255, 255, 0.2)',
//   boxShadow: '0 8px 32px rgba(31, 38, 135, 0.2)'
// };

// const videoCallOverlayVariants = {
//   hidden: { opacity: 0, scale: 0.9, y: 50 },
//   visible: {
//     opacity: 1,
//     scale: 1,
//     y: 0,
//     transition: { type: 'spring', damping: 30, stiffness: 400, delay: 0.1 }
//   },
//   exit: { opacity: 0, scale: 0.9, y: 50, transition: { duration: 0.3 } }
// };

// const videoPanelVariants = {
//   hidden: { opacity: 0, scale: 0.9 },
//   visible: { opacity: 1, scale: 1, transition: { duration: 0.5, ease: 'easeOut' } },
// };

// const controlButtonVariants = {
//   initial: { scale: 1 },
//   hover: { scale: 1.1, backgroundColor: 'rgba(255, 255, 255, 0.2)' },
//   tap: { scale: 0.9 }
// };

// const VideocallStudents = ({ isOpen, onCloseCall, roomName: initialRoomName, livekitToken: initialLivekitToken, livekitUrl: initialLivekitUrl }) => {
//   const [room, setRoom] = useState(null);
//   const [localVideoTrack, setLocalVideoTrack] = useState(null);
//   const [localAudioTrack, setLocalAudioTrack] = useState(null);
//   const [screenTrack, setScreenTrack] = useState(null);
//   const [isCameraOn, setIsCameraOn] = useState(true);
//   const [isMicOn, setIsMicOn] = useState(true);
//   const [isScreenSharing, setIsScreenSharing] = useState(false);
//   const [connectionStatus, setConnectionStatus] = useState('connecting');

//   const localVideoRef = useRef(null);
//   const remoteVideoRef = useRef(null);
//   const remoteAudioRef = useRef(null);

//   useEffect(() => {
//     const cleanupLivekit = () => {
//       console.log('Student: Cleaning up Livekit resources...');
//       if (room) {
//         console.log('Student: Disconnecting from Livekit room.');
//         room.disconnect();
//         setRoom(null);
//       }
//       if (localVideoTrack) {
//         console.log('Student: Stopping and detaching local video track.');
//         localVideoTrack.stop();
//         localVideoTrack.detach();
//         setLocalVideoTrack(null);
//       }
//       if (localAudioTrack) {
//         console.log('Student: Stopping and detaching local audio track.');
//         localAudioTrack.stop();
//         localAudioTrack.detach();
//         setLocalAudioTrack(null);
//       }
//       if (screenTrack) {
//         console.log('Student: Stopping and detaching screen track.');
//         screenTrack.stop();
//         screenTrack.detach();
//         setScreenTrack(null);
//       }
//       setIsCameraOn(true);
//       setIsMicOn(true);
//       setIsScreenSharing(false);
//       setConnectionStatus('disconnected');
//       console.log('Student: Livekit cleanup complete.');
//     };

//     if (!isOpen || !initialLivekitUrl || !initialLivekitToken || !initialRoomName) {
//       if (room || localVideoTrack || localAudioTrack || screenTrack) {
//         cleanupLivekit();
//       }
//       setConnectionStatus('disconnected');
//       return;
//     }

//     const initRoom = async () => {
//       console.log('Student: Initializing Livekit room connection...');
//       setConnectionStatus('connecting');
//       const newRoom = new Room();
//       setRoom(newRoom);

//       try {
//         await newRoom.connect(initialLivekitUrl, initialLivekitToken);
//         toast.success('Connected to call!', { icon: '📞' });
//         console.log('Student: Successfully connected to Livekit room.');
//         setConnectionStatus('connected');

//         console.log('Student: Creating local video and audio tracks...');
//         const videoTrack = await createLocalVideoTrack({ facingMode: 'user' });
//         const audioTrack = await createLocalAudioTrack();

//         await newRoom.localParticipant.publishTrack(videoTrack);
//         await newRoom.localParticipant.publishTrack(audioTrack);
//         console.log('Student: Published local video and audio tracks.');

//         setLocalVideoTrack(videoTrack);
//         setLocalAudioTrack(audioTrack);

//         newRoom.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
//           console.log(`Student: Track subscribed: Kind=${track.kind}, Participant=${participant.identity}, TrackName=${publication.trackName}`);
//           if (track.kind === 'video' && remoteVideoRef.current) {
//             track.attach(remoteVideoRef.current);
//             console.log('Student: Remote video track attached to remote video ref.');
//           } else if (track.kind === 'audio' && remoteAudioRef.current) {
//             track.attach(remoteAudioRef.current);
//             console.log('Student: Remote audio track attached to remote audio ref.');
//           }
//         });

//         newRoom.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
//           console.log(`Student: Track unsubscribed: Kind=${track.kind}, Participant=${participant.identity}, TrackName=${publication.trackName}`);
//           track.detach();
//           if (track.kind === 'video' && remoteVideoRef.current) {
//             if (remoteVideoRef.current.srcObject === track.mediaStream) {
//               remoteVideoRef.current.srcObject = null;
//             }
//           }
//         });

//         newRoom.on(RoomEvent.Disconnected, (reason) => {
//           toast.info(`Disconnected from call. Reason: ${reason}`);
//           console.log(`Student: Livekit room disconnected. Reason: ${reason}`);
//           cleanupLivekit();
//           onCloseCall();
//         });

//         newRoom.on(RoomEvent.ParticipantConnected, (participant) => {
//           console.log(`Student: Participant connected: ${participant.identity}`);
//         });

//         newRoom.on(RoomEvent.ParticipantDisconnected, (participant) => {
//           console.log(`Student: Participant disconnected: ${participant.identity}`);
//           toast.info(`${participant.identity} has left the call.`);
//           endCall(false);
//         });

//       } catch (error) {
//         console.error("Student: Livekit connection error:", error);
//         toast.error('Failed to connect to video call. Please check your network.', { icon: '❌' });
//         setConnectionStatus('failed');
//         cleanupLivekit();
//         onCloseCall();
//       }
//     };

//     if (isOpen && initialLivekitUrl && initialLivekitToken && initialRoomName) {
//       initRoom();
//     }

//     return cleanupLivekit;
//   }, [isOpen, initialLivekitUrl, initialLivekitToken, initialRoomName]);

//   useEffect(() => {
//     if (localVideoTrack && localVideoRef.current) {
//       localVideoTrack.attach(localVideoRef.current);
//       console.log('Student: Local video track attached to ref.');
//     }
//   }, [localVideoTrack]);

//   const toggleCamera = async () => {
//     if (!localVideoTrack) {
//       toast.warn('Camera track not available yet.', { icon: '⚠️' });
//       return;
//     }
//     const newCameraState = !isCameraOn;
//     await localVideoTrack.mute(!newCameraState);
//     setIsCameraOn(newCameraState);
//     console.log(`Student: Camera toggled: ${newCameraState ? 'On' : 'Off'}`);
//   };

//   const toggleMic = async () => {
//     if (!localAudioTrack) {
//       toast.warn('Microphone track not available yet.', { icon: '⚠️' });
//       return;
//     }
//     const newMicState = !isMicOn;
//     await localAudioTrack.mute(!newMicState);
//     setIsMicOn(newMicState);
//     console.log(`Student: Microphone toggled: ${newMicState ? 'On' : 'Off'}`);
//   };

//   const toggleScreenShare = async () => {
//     if (!room || !room.localParticipant) {
//       toast.error("Not connected to a call.", { icon: '❌' });
//       console.error('Student: Attempted screen share without room or local participant.');
//       return;
//     }

//     if (isScreenSharing) {
//       if (screenTrack) {
//         console.log('Student: Stopping screen share...');
//         await room.localParticipant.unpublishTrack(screenTrack);
//         screenTrack.stop();
//         screenTrack.detach();
//         setScreenTrack(null);
//       }
//       setIsScreenSharing(false);
//       toast.info('Screen sharing stopped.');
//       console.log('Student: Screen sharing stopped.');
//     } else {
//       try {
//         console.log('Student: Starting screen share...');
//         const [newScreenTrack] = await createLocalScreenTracks({ audio: true });
//         await room.localParticipant.publishTrack(newScreenTrack);
//         setScreenTrack(newScreenTrack);
//         setIsScreenSharing(true);
//         toast.success('Screen sharing started!', { icon: '🖥️' });
//         console.log('Student: Screen sharing started.');
//       } catch (e) {
//         console.error("Student: Failed to start screen share:", e);
//         toast.error('Failed to start screen share. Please check browser permissions or try again.', { icon: '🚫' });
//       }
//     }
//   };

//   const endCall = async (sendEndSignal = true) => {
//     console.log('Student: Ending call...');
//     if (sendEndSignal && initialRoomName) {
//       try {
//         console.log(`Student: Sending end call signal to backend for room: ${initialRoomName}`);
//         await fetch('https://testing.sasthra.in/call/end', {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({ room_name: initialRoomName }),
//         });
//         toast.info('Call ended successfully.');
//       } catch (err) {
//         toast.error('Failed to send end call signal to server.');
//         console.error('Student: Error ending call on backend:', err);
//       }
//     }

//     if (room) {
//       console.log('Student: Disconnecting from Livekit room...');
//       room.disconnect();
//     }

//     if (localVideoTrack) {
//       localVideoTrack.stop();
//       localVideoTrack.detach();
//     }
//     if (localAudioTrack) {
//       localAudioTrack.stop();
//       localAudioTrack.detach();
//     }
//     if (screenTrack) {
//       screenTrack.stop();
//       screenTrack.detach();
//     }

//     setRoom(null);
//     setLocalVideoTrack(null);
//     setLocalAudioTrack(null);
//     setScreenTrack(null);
//     setIsCameraOn(true);
//     setIsMicOn(true);
//     setIsScreenSharing(false);
//     setConnectionStatus('disconnected');
//     onCloseCall();
//     console.log('Student: Call ended and states reset.');
//   };

//   return (
//     <AnimatePresence>
//       {isOpen && (
//         <motion.div
//           key="video-call"
//           variants={videoCallOverlayVariants}
//           initial="hidden"
//           animate="visible"
//           exit="exit"
//           className="fixed inset-0 bg-black/90 flex flex-col items-center justify-center z-60 p-4"
//         >
//           {connectionStatus === 'connecting' && (
//             <div className="flex flex-col items-center text-white">
//               <motion.div
//                 className="w-16 h-16 border-4 border-white border-t-transparent rounded-full"
//                 animate={{ rotate: 360 }}
//                 transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
//               />
//               <p className="mt-4 text-xl font-semibold">Connecting to mentor...</p>
//               <p className="text-sm text-gray-300">Please wait, setting up your call.</p>
//             </div>
//           )}

//           {connectionStatus === 'failed' && (
//             <div className="flex flex-col items-center text-red-400">
//               <FaTimes className="text-5xl mb-2" />
//               <p className="mt-4 text-xl font-semibold">Connection Failed</p>
//               <p className="text-sm text-red-300">Could not connect to the call. Please try again.</p>
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={() => onCloseCall()}
//                 className="mt-4 px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md">
//                 Close Call
//               </motion.button>
//             </div>
//           )}

//           {connectionStatus === 'connected' && (
//             <motion.div
//               variants={videoPanelVariants}
//               initial="hidden"
//               animate="visible"
//               className="flex flex-col w-full max-w-5xl h-full max-h-[calc(100vh-80px)] bg-gray-900 rounded-2xl overflow-hidden shadow-2xl relative"
//             >
//               <div className="flex-1 flex flex-col lg:flex-row gap-4 p-4 items-center justify-center">
//                 <motion.div
//                   className="relative w-full h-1/2 lg:h-full lg:w-1/2 rounded-xl overflow-hidden bg-gray-800 flex items-center justify-center shadow-lg border border-gray-700"
//                   initial={{ opacity: 0, scale: 0.8 }}
//                   animate={{ opacity: 1, scale: 1 }}
//                   transition={{ duration: 0.5, delay: 0.2 }}
//                 >
//                   <video
//                     id="local-video"
//                     ref={localVideoRef}
//                     autoPlay
//                     playsInline
//                     muted
//                     className="w-full h-full object-cover"
//                   />
//                   <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-md">
//                     You
//                   </div>
//                 </motion.div>

//                 <motion.div
//                   className="relative w-full h-1/2 lg:h-full lg:w-1/2 rounded-xl overflow-hidden bg-gray-800 flex items-center justify-center shadow-lg border border-gray-700"
//                   initial={{ opacity: 0, scale: 0.8 }}
//                   animate={{ opacity: 1, scale: 1 }}
//                   transition={{ duration: 0.5, delay: 0.3 }}
//                 >
//                   <video
//                     id="remote-video"
//                     ref={remoteVideoRef}
//                     autoPlay
//                     playsInline
//                     className="w-full h-full object-cover"
//                   />
//                   <audio id="remote-audio" ref={remoteAudioRef} autoPlay />
//                   <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-md">
//                     Mentor
//                   </div>
//                   {!remoteVideoRef.current?.srcObject && (
//                     <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-400 text-lg p-4 text-center">
//                       <FaUser className="text-5xl mb-2" />
//                       <span>Waiting for mentor to join or enable camera...</span>
//                     </div>
//                   )}
//                 </motion.div>
//               </div>

//               <motion.div
//                 style={glassStyle}
//                 className="p-4 flex justify-center space-x-6 rounded-b-2xl border-t border-gray-700/50"
//                 initial={{ y: 100, opacity: 0 }}
//                 animate={{ y: 0, opacity: 1 }}
//                 transition={{ type: 'spring', damping: 20, stiffness: 200, delay: 0.5 }}
//               >
//                 <motion.button
//                   variants={controlButtonVariants}
//                   whileHover="hover"
//                   whileTap="tap"
//                   onClick={toggleCamera}
//                   className={`p-3 rounded-full transition-colors duration-200 ${
//                     isCameraOn ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-gray-700 hover:bg-gray-600'
//                   } text-white text-xl flex items-center justify-center`}
//                   title={isCameraOn ? 'Turn Off Camera' : 'Turn On Camera'}
//                 >
//                   {isCameraOn ? <FaVideo /> : <FaVideoSlash />}
//                 </motion.button>

//                 <motion.button
//                   variants={controlButtonVariants}
//                   whileHover="hover"
//                   whileTap="tap"
//                   onClick={toggleMic}
//                   className={`p-3 rounded-full transition-colors duration-200 ${
//                     isMicOn ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-gray-700 hover:bg-gray-600'
//                   } text-white text-xl flex items-center justify-center`}
//                   title={isMicOn ? 'Mute Microphone' : 'Unmute Microphone'}
//                 >
//                   {isMicOn ? <FaMicrophone /> : <FaMicrophoneSlash />}
//                 </motion.button>

//                 <motion.button
//                   variants={controlButtonVariants}
//                   whileHover="hover"
//                   whileTap="tap"
//                   onClick={toggleScreenShare}
//                   className={`p-3 rounded-full transition-colors duration-200 ${
//                     isScreenSharing ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-gray-700 hover:bg-gray-600'
//                   } text-white text-xl flex items-center justify-center`}
//                   title={isScreenSharing ? 'Stop Screen Share' : 'Share Screen'}
//                 >
//                   {isScreenSharing ? <FaStopCircle /> : <FaShareSquare />}
//                 </motion.button>

//                 <motion.button
//                   variants={controlButtonVariants}
//                   whileHover="hover"
//                   whileTap="tap"
//                   onClick={() => endCall(true)}
//                   className="p-3 rounded-full bg-red-600 hover:bg-red-700 text-white text-xl transition-colors duration-200 flex items-center justify-center"
//                   title="End Call"
//                 >
//                   <FaPhoneSlash />
//                 </motion.button>
//               </motion.div>
//             </motion.div>
//           )}
//         </motion.div>
//       )}
//     </AnimatePresence>
//   );
// };

// export default VideocallStudents;