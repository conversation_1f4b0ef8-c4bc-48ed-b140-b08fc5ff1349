import React, { useEffect, useState, useMemo, memo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import clsx from 'clsx';

// --- Live Data Integration ---
import {
  clearCenterOcrData,
  setCenterOcrData,
  useLazyGetCenterOcrDataQuery
} from '../AnalyticalDashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';

// --- Highcharts Modules ---
import 'highcharts/highcharts-more';
import 'highcharts/modules/solid-gauge';
import 'highcharts/modules/heatmap'; // Correct module for the main chart

// --- Lucide Icons ---
import {
  Users,
  BarChart2,
  CheckCircle,
  AlertTriangle,
  Target,
  Search,
  X,
  ChevronUp,
  ChevronDown,
  Eye,
  GraduationCap,
  Lightbulb,
  TrendingUp,
  Award,
  Loader2,
  CheckCircle2,
  XCircle
} from 'lucide-react';

// --- Reusable UI Components (Memoized for performance) ---

const DashboardCard = memo(({ children, className }) => (
  <motion.div
    className={clsx(
      'bg-white rounded-2xl shadow-lg border border-slate-200/60 p-6 overflow-hidden',
      className
    )}
    variants={{ hidden: { y: 20, opacity: 0 }, visible: { y: 0, opacity: 1 } }}>
    {children}
  </motion.div>
));

const StatCard = memo(({ icon, label, value, color }) => (
  <div className="flex items-center">
    <div className={`p-3 rounded-full ${color.bg}`}>
      {React.cloneElement(icon, { className: `h-7 w-7 ${color.text}` })}
    </div>
    <div className="ml-4">
      <p className="text-slate-500 text-sm font-medium">{label}</p>
      <p className="text-3xl font-bold text-slate-800">{value}</p>
    </div>
  </div>
));

const TabButton = memo(({ label, isActive, onClick }) => (
  <button
    onClick={onClick}
    className="relative px-4 py-3 text-sm font-semibold text-slate-500 transition focus:outline-none">
    {isActive && (
      <motion.div
        layoutId="counselor-modal-tab"
        className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"
      />
    )}
    <span className={clsx('transition', isActive ? 'text-blue-600' : 'hover:text-slate-800')}>
      {label}
    </span>
  </button>
));

const QuestionAccordionItem = memo(({ question }) => {
  const [isOpen, setIsOpen] = useState(false);
  const status = question.is_correct
    ? { color: 'border-green-500', icon: <CheckCircle2 className="text-green-500" /> }
    : { color: 'border-red-500', icon: <XCircle className="text-red-500" /> };
  return (
    <div
      className={`border-l-4 rounded-r-lg bg-slate-50 transition-shadow hover:shadow-md ${status.color}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-4 text-left">
        <div className="flex items-center space-x-4">
          <span className="font-bold text-slate-800">Q{question.question_number}</span>
          {status.icon}
          <span className="font-semibold text-slate-700">
            {question.feedback.core_concept_tested}
          </span>
        </div>
        <ChevronDown
          className={`transform transition-transform text-slate-500 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden">
            <div className="p-4 border-t border-slate-200 space-y-4 text-sm">
              <div className="p-4 bg-white rounded-lg border border-slate-200">
                <h5 className="font-semibold mb-2 text-slate-800">Expert Explanation</h5>
                <p className="text-slate-600 whitespace-pre-wrap leading-relaxed">
                  {question.expert_method_explanation}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

// --- Report Detail View (Inside Modal) ---

const ReportDetailView = memo(({ test }) => {
  const [activeTab, setActiveTab] = useState('Overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [questionFilter, setQuestionFilter] = useState('All');

  const { accuracy, filteredQuestions } = useMemo(() => {
    const qBreakdown = test.performance_analysis.question_by_question_breakdown;
    const accuracy = Math.round(
      (qBreakdown.filter((q) => q.is_correct).length / qBreakdown.length) * 100
    );
    const filteredQuestions = qBreakdown.filter((q) => {
      const matchesFilter =
        questionFilter === 'All' ||
        (questionFilter === 'Correct' && q.is_correct) ||
        (questionFilter === 'Incorrect' && !q.is_correct);
      const matchesSearch =
        !searchTerm ||
        q.feedback.core_concept_tested.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesFilter && matchesSearch;
    });
    return { accuracy, filteredQuestions };
  }, [test, searchTerm, questionFilter]);

  const strategy = test.performance_analysis.overall_feedback_and_strategy;

  const scoreDialOptions = useMemo(
    () => ({
      chart: { type: 'solidgauge', height: '100%' },
      pane: {
        center: ['50%', '70%'],
        size: '100%',
        startAngle: -90,
        endAngle: 90,
        background: {
          backgroundColor: '#f1f5f9',
          innerRadius: '60%',
          outerRadius: '100%',
          shape: 'arc'
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        tickAmount: 2,
        lineWidth: 0,
        minorTickInterval: null,
        labels: { y: 16 }
      },
      plotOptions: {
        solidgauge: {
          dataLabels: {
            y: -25,
            borderWidth: 0,
            useHTML: true,
            format:
              '<div class="text-center"><span class="text-5xl font-bold text-slate-800">{y}</span><span class="text-2xl text-slate-500">%</span></div>'
          }
        }
      },
      series: [
        {
          name: 'Score',
          data: [
            {
              y: accuracy,
              color: accuracy > 80 ? '#22c55e' : accuracy > 60 ? '#f59e0b' : '#ef4444'
            }
          ]
        }
      ]
    }),
    [accuracy]
  );

  return (
    <div className="h-full flex flex-col">
      <header className="p-4 border-b border-slate-200 flex-shrink-0">
        <h3 className="text-xl font-bold text-slate-900">
          {test.subject} - {test.exam} Report
        </h3>
        <p className="text-sm text-slate-500">Analyzed on {new Date(test.date).toLocaleString()}</p>
      </header>
      <div className="flex border-b border-slate-200 px-2 flex-shrink-0">
        <TabButton
          label="Overview"
          isActive={activeTab === 'Overview'}
          onClick={() => setActiveTab('Overview')}
        />
        <TabButton
          label="Question Breakdown"
          isActive={activeTab === 'Question Breakdown'}
          onClick={() => setActiveTab('Question Breakdown')}
        />
      </div>
      <div className="flex-grow p-4 overflow-y-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}>
            {activeTab === 'Overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div className="lg:col-span-1">
                  <h4 className="font-semibold text-slate-600 mb-2">Overall Score</h4>
                  <div className="h-40">
                    <HighchartsReact highcharts={Highcharts} options={scoreDialOptions} />
                  </div>
                </div>
                <div className="lg:col-span-2">
                  <h4 className="font-semibold text-slate-600 mb-2">AI Action Plan</h4>
                  <div className="space-y-3 text-sm">
                    <div>
                      <h5 className="font-semibold text-blue-600 flex items-center">
                        <TrendingUp size={16} className="mr-2" />
                        Primary Focus
                      </h5>
                      <p className="text-slate-600 pl-6 mt-1">
                        {strategy.primary_area_for_improvement}
                      </p>
                    </div>
                    <div>
                      <h5 className="font-semibold text-green-600 flex items-center">
                        <Award size={16} className="mr-2" />
                        Strengths
                      </h5>
                      <ul className="text-slate-600 list-disc list-inside pl-6 mt-1">
                        {strategy.conceptual_strengths.slice(0, 3).map((s) => (
                          <li key={s}>{s}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {activeTab === 'Question Breakdown' && (
              <div>
                <div className="flex flex-col md:flex-row justify-between items-center mb-4">
                  <h4 className="font-semibold text-slate-600 mb-2 md:mb-0">All Questions</h4>
                  <div className="flex items-center space-x-4 w-full md:w-auto">
                    <div className="relative flex-grow">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                      <input
                        type="text"
                        placeholder="Search concepts..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg"
                      />
                    </div>
                    <div className="flex items-center space-x-1 p-1 bg-slate-100 rounded-lg">
                      {['All', 'Correct', 'Incorrect'].map((f) => (
                        <button
                          key={f}
                          onClick={() => setQuestionFilter(f)}
                          className={clsx(
                            'px-3 py-1 text-sm rounded-md',
                            f === questionFilter
                              ? 'bg-white text-blue-600 shadow-sm'
                              : 'text-slate-600'
                          )}>
                          {f}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <AnimatePresence>
                    {filteredQuestions.map((q) => (
                      <QuestionAccordionItem key={q.question_number} question={q} />
                    ))}
                  </AnimatePresence>
                  {filteredQuestions.length === 0 && (
                    <p className="text-center text-slate-500 py-8">
                      No questions match your criteria.
                    </p>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
});

// --- Student Detail Modal Wrapper ---

const StudentDetailModal = memo(({ student, onClose }) => {
  const [selectedTestDate, setSelectedTestDate] = useState(null);

  useEffect(() => {
    if (student && student.tests.length > 0) {
      setSelectedTestDate(student.tests[0].date);
    }
  }, [student]);

  const selectedTest = useMemo(() => {
    return student.tests.find((t) => t.date === selectedTestDate);
  }, [student, selectedTestDate]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"
      onClick={onClose}>
      <motion.div
        initial={{ scale: 0.95, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.95, y: -20 }}
        className="relative w-full max-w-6xl bg-slate-50 rounded-2xl shadow-2xl overflow-hidden flex h-[90vh]"
        onClick={(e) => e.stopPropagation()}>
        <aside className="w-1/4 bg-white border-r border-slate-200 flex flex-col">
          <header className="p-4 border-b border-slate-200 flex-shrink-0">
            <h3 className="text-lg font-bold text-slate-900">{student.name}</h3>
            <p className="text-sm text-slate-500">Student ID: {student.id.slice(0, 8)}...</p>
          </header>
          <div className="flex-grow overflow-y-auto p-2 space-y-1">
            {student.tests.map((test) => (
              <button
                key={test.date}
                onClick={() => setSelectedTestDate(test.date)}
                className={clsx(
                  'w-full text-left p-3 rounded-lg transition-colors flex justify-between items-center',
                  selectedTestDate === test.date
                    ? 'bg-blue-100 text-blue-800'
                    : 'hover:bg-slate-100'
                )}>
                <div>
                  <span
                    className={clsx(
                      'font-semibold',
                      selectedTestDate === test.date ? 'text-blue-800' : 'text-slate-700'
                    )}>
                    {test.subject}
                  </span>
                  <p
                    className={clsx(
                      'text-xs',
                      selectedTestDate === test.date ? 'text-blue-600' : 'text-slate-500'
                    )}>
                    {new Date(test.date).toLocaleDateString()}
                  </p>
                </div>
                <span className="font-bold">{test.accuracy}%</span>
              </button>
            ))}
          </div>
        </aside>
        <main className="w-3/4 flex flex-col">
          <AnimatePresence mode="wait">
            {selectedTest ? (
              <ReportDetailView key={selectedTest.date} test={selectedTest} />
            ) : (
              <div>Loading...</div>
            )}
          </AnimatePresence>
        </main>
      </motion.div>
    </motion.div>
  );
});

// --- Main Center OCR Dashboard Component ---

const CenterOcr = () => {
  const [getCenterOcr] = useLazyGetCenterOcrDataQuery();
  const [res, setRes] = useState(null);
  const dispatch = useDispatch();
  const ocrData = useSelector((state) => state.centreCounselorDashboard.centerOcrData);

  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'avgAccuracy', direction: 'desc' });
  const [selectedStudent, setSelectedStudent] = useState(null);

  useEffect(() => {
    Highcharts.setOptions({
      chart: { style: { fontFamily: '"Inter", sans-serif' }, backgroundColor: 'transparent' },
      title: { style: { display: 'none' } },
      credits: { enabled: false },
      legend: {
        align: 'right',
        verticalAlign: 'middle',
        layout: 'vertical',
        itemStyle: { color: '#475569', fontWeight: '500' }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e2e8f0',
        borderRadius: 8,
        borderWidth: 1,
        shadow: true,
        style: { color: '#334155' }
      }
    });
  }, []);

  const handleGetCenterOcrApi = useCallback(async () => {
    setIsLoading(true);
    try {
      const ocr = await getCenterOcr({ center_code: sessionStorage.centercode }).unwrap();
      dispatch(setCenterOcrData(ocr));
    } catch (error) {
      setRes(error);
      dispatch(clearCenterOcrData());
    } finally {
      setIsLoading(false);
    }
  }, [getCenterOcr, dispatch]);

  useEffect(() => {
    handleGetCenterOcrApi();
  }, [handleGetCenterOcrApi]);

  const processedData = useMemo(() => {
    if (!ocrData || ocrData.length === 0) return null;

    const allQuestions = ocrData.flatMap(
      (test) => test.performance_analysis.question_by_question_breakdown
    );
    const totalAccuracy = Math.round(
      (allQuestions.filter((q) => q.is_correct).length / allQuestions.length) * 100
    );
    const errorTypes = allQuestions.reduce((acc, q) => {
      const type = q.is_correct
        ? 'Correct'
        : q.error_type === 'CONCEPTUAL_MISTAKE'
          ? 'Conceptual'
          : 'Calculation';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const conceptPerformance = allQuestions.reduce((acc, q) => {
      const concept = q.feedback.core_concept_tested;
      const type = q.is_correct
        ? 'Correct'
        : q.error_type === 'CONCEPTUAL_MISTAKE'
          ? 'Conceptual Error'
          : 'Calculation Error';
      if (!acc[concept])
        acc[concept] = { Correct: 0, 'Calculation Error': 0, 'Conceptual Error': 0 };
      acc[concept][type]++;
      return acc;
    }, {});

    const concepts = Object.keys(conceptPerformance);
    const performanceCategories = ['Correct', 'Calculation Error', 'Conceptual Error'];
    const heatmapData = concepts
      .flatMap((concept, y) =>
        performanceCategories.map((category, x) => [
          x,
          y,
          conceptPerformance[concept][category] || 0
        ])
      )
      .filter((point) => point[2] > 0);

    const conceptHeatmapOptions = {
      chart: { type: 'heatmap', marginTop: 40, marginBottom: 80, plotBorderWidth: 1 },
      xAxis: {
        categories: performanceCategories,
        title: null,
        labels: { style: { fontSize: '12px' } }
      },
      yAxis: {
        categories: concepts,
        title: null,
        reversed: true,
        labels: { style: { fontSize: '11px' } }
      },
      colorAxis: { min: 0, minColor: '#FFFFFF', maxColor: '#3b82f6' }, // Blue theme
      legend: {
        align: 'right',
        layout: 'vertical',
        margin: 0,
        verticalAlign: 'top',
        y: 25,
        symbolHeight: 280
      },
      tooltip: {
        formatter: function () {
          return `<b>${this.series.yAxis.categories[this.point.y]}</b><br/>${this.point.value} questions were <b>${this.series.xAxis.categories[this.point.x]}</b>`;
        }
      },
      series: [
        {
          name: 'Concept Performance',
          borderWidth: 1,
          data: heatmapData,
          dataLabels: {
            enabled: true,
            color: '#000000',
            style: { textOutline: 'none' },
            formatter: function () {
              return this.point.value > 0 ? this.point.value : null;
            }
          }
        }
      ]
    };

    const studentsMap = ocrData.reduce((acc, test) => {
      if (!acc[test.student_id])
        acc[test.student_id] = { name: test.student_name, id: test.student_id, tests: [] };
      const qBreakdown = test.performance_analysis.question_by_question_breakdown;
      const accuracy =
        qBreakdown.length > 0
          ? Math.round((qBreakdown.filter((q) => q.is_correct).length / qBreakdown.length) * 100)
          : 0;
      acc[test.student_id].tests.push({ ...test, accuracy });
      return acc;
    }, {});
    const studentList = Object.values(studentsMap).map((student) => {
      const sortedTests = [...student.tests].sort((a, b) => new Date(b.date) - new Date(a.date));
      const avgAccuracy = Math.round(
        sortedTests.reduce((acc, t) => acc + t.accuracy, 0) / sortedTests.length
      );
      return { ...student, tests: sortedTests, avgAccuracy, testCount: sortedTests.length };
    });

    return {
      totalReports: ocrData.length,
      studentCount: studentList.length,
      totalAccuracy,
      errorTypes,
      conceptHeatmapOptions,
      studentList
    };
  }, [ocrData]);

  const sortedAndFilteredStudents = useMemo(() => {
    if (!processedData) return [];
    return processedData.studentList
      .filter((s) => s.name.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) return sortConfig.direction === 'asc' ? -1 : 1;
        if (a[sortConfig.key] > b[sortConfig.key]) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
  }, [processedData, searchTerm, sortConfig]);

  const handleSort = (key) => {
    setSortConfig((current) => ({
      key,
      direction: current.key === key && current.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  if (isLoading)
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="w-12 h-12 text-blue-600 animate-spin" />
      </div>
    );
  if (!processedData)
    return (
      <div className="flex h-screen items-center justify-center bg-slate-100 text-center">
        No reports found for this center.
      </div>
    );

  const { totalReports, studentCount, totalAccuracy, conceptHeatmapOptions } = processedData;

  return (
    <div className="min-h-screen p-4 sm:p-6 lg:p-8 font-sans text-slate-800">
      <Toastify res={res} resClear={() => setRes(null)} />
      <AnimatePresence>
        {selectedStudent && (
          <StudentDetailModal student={selectedStudent} onClose={() => setSelectedStudent(null)} />
        )}
      </AnimatePresence>
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-slate-900">Center Performance Dashboard</h1>
        <p className="text-slate-500 mt-1">
          AI-powered OCR analysis for Center: {sessionStorage.centercode}
        </p>
      </header>
      <div className="space-y-6">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial="hidden"
          animate="visible"
          variants={{ visible: { transition: { staggerChildren: 0.05 } } }}>
          <StatCard
            icon={<BarChart2 />}
            label="Total Reports Analyzed"
            value={totalReports}
            color={{ bg: 'bg-blue-100', text: 'text-blue-600' }}
          />
          <StatCard
            icon={<Users />}
            label="Students with Reports"
            value={studentCount}
            color={{ bg: 'bg-indigo-100', text: 'text-indigo-600' }}
          />
          <StatCard
            icon={<Target />}
            label="Center Average Accuracy"
            value={`${totalAccuracy}%`}
            color={{ bg: 'bg-emerald-100', text: 'text-emerald-600' }}
          />
          <StatCard
            icon={<AlertTriangle />}
            label="Most Common Error"
            value={Object.keys(processedData.errorTypes).reduce((a, b) =>
              processedData.errorTypes[a] > processedData.errorTypes[b] ? a : b
            )}
            color={{ bg: 'bg-amber-100', text: 'text-amber-600' }}
          />
        </motion.div>

        <DashboardCard>
          <div className="flex items-center text-slate-600 mb-2">
            <GraduationCap className="w-6 h-6" />
            <h3 className="text-lg font-semibold ml-2">Center-Wide Concept Analysis</h3>
          </div>
          <p className="text-sm text-slate-500 mb-4">
            Instantly identify "hotspots" of conceptual or calculation errors for each topic.
          </p>
          <div className="min-h-[500px]">
            <HighchartsReact highcharts={Highcharts} options={conceptHeatmapOptions} />
          </div>
        </DashboardCard>

        <DashboardCard>
          <div className="flex flex-col md:flex-row justify-between items-center mb-4">
            <h3 className="text-xl font-bold text-slate-800 mb-3 md:mb-0">Student Performance</h3>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
              <input
                type="text"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead>
                <tr className="border-b border-slate-200 bg-slate-50">
                  {[
                    { key: 'name', label: 'Student Name' },
                    { key: 'avgAccuracy', label: 'Avg. Accuracy' },
                    { key: 'testCount', label: 'Tests Taken' }
                  ].map((h) => (
                    <th
                      key={h.key}
                      className="p-3 text-sm font-semibold text-slate-600 cursor-pointer"
                      onClick={() => handleSort(h.key)}>
                      <div className="flex items-center">
                        {h.label}
                        {sortConfig.key === h.key &&
                          (sortConfig.direction === 'asc' ? (
                            <ChevronUp className="w-4 h-4 ml-1" />
                          ) : (
                            <ChevronDown className="w-4 h-4 ml-1" />
                          ))}
                      </div>
                    </th>
                  ))}
                  <th className="p-3 text-sm font-semibold text-slate-600 text-right">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedAndFilteredStudents.map((student) => (
                  <tr key={student.id} className="border-b border-slate-100 hover:bg-slate-50">
                    <td className="p-3 font-medium text-slate-800">{student.name}</td>
                    <td className="p-3 font-semibold text-slate-700">{student.avgAccuracy}%</td>
                    <td className="p-3 text-slate-600">{student.testCount}</td>
                    <td className="p-3 text-right">
                      <button
                        onClick={() => setSelectedStudent(student)}
                        className="px-3 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full hover:bg-blue-200 flex items-center ml-auto">
                        <Eye className="w-3 h-3 mr-1.5" />
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {sortedAndFilteredStudents.length === 0 && (
              <p className="text-center text-slate-500 py-8">No students match your search.</p>
            )}
          </div>
        </DashboardCard>
      </div>
    </div>
  );
};

export default CenterOcr;
