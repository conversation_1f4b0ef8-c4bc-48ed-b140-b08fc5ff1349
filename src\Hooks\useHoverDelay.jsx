import { useState, useEffect } from 'react';

export const useHoverDelay = (delay = 150) => {
  const [isHovered, setIsHovered] = useState(false);

  let timeoutId;

  const handleMouseEnter = () => {
    clearTimeout(timeoutId);
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    timeoutId = setTimeout(() => {
      setIsHovered(false);
    }, delay);
  };

  useEffect(() => {
    return () => clearTimeout(timeoutId);
  }, []);

  return {
    isHovered,
    handleMouseEnter,
    handleMouseLeave
  };
};
