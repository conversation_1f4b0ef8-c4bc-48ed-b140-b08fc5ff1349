

// // //below is the oneside working code 
// import React, { useState, useEffect, useRef } from 'react';
// import { 
//   Room, 
//   Track,
//   VideoPresets,
//   createLocalTracks,
//   RoomEvent,
//   ParticipantEvent
// } from 'livekit-client';

// const ReferTutorModal = ({ isOpen, onClose }) => {
//   const [mentors, setMentors] = useState([]);
//   const [loading, setLoading] = useState(false);
//   const [callState, setCallState] = useState('idle'); // idle, requesting, waiting, connected, ended
//   const [selectedMentor, setSelectedMentor] = useState(null);
//   const [room, setRoom] = useState(null);
//   const [participants, setParticipants] = useState([]);
//   const [localTracks, setLocalTracks] = useState([]);
//   const [isMuted, setIsMuted] = useState(false);
//   const [isVideoOff, setIsVideoOff] = useState(false);
//   const [isScreenSharing, setIsScreenSharing] = useState(false);
//   const [connectionStatus, setConnectionStatus] = useState('disconnected');
//   const [currentRoomName, setCurrentRoomName] = useState('');
//   const [mediaError, setMediaError] = useState('');
//   const [localVideoTrack, setLocalVideoTrack] = useState(null);
//   const [localAudioTrack, setLocalAudioTrack] = useState(null);
//   const [remoteVideoTrack, setRemoteVideoTrack] = useState(null);
//   const [remoteAudioTrack, setRemoteAudioTrack] = useState(null);
//   const [remoteScreenTrack, setRemoteScreenTrack] = useState(null);
//   const [remoteScreenAudioTrack, setRemoteScreenAudioTrack] = useState(null);

//   const localVideoRef = useRef(null);
//   const remoteVideoRef = useRef(null);
//   const screenShareRef = useRef(null);
//   const remoteAudioRef = useRef(null);
//   const screenAudioRef = useRef(null);
//   const roomRef = useRef(null);
//   const tracksInitialized = useRef(false);

//   const API_BASE_URL = 'https://testing.sasthra.in';
//   const LIVEKIT_URL = 'wss://livekit.sasthra.in';

//   // Get student ID from sessionStorage
//   const getStudentId = () => {
//     const userId = sessionStorage.getItem('userId') || sessionStorage.getItem('user_id') || sessionStorage.getItem('studentId');
//     if (!userId) {
//       alert('Student ID not found in session. Please login again.');
//       throw new Error('Student ID not found');
//     }
//     return userId;
//   };

//   // Fetch available mentors
//   const fetchMentors = async () => {
//     setLoading(true);
//     try {
//       const response = await fetch(`${API_BASE_URL}/mentors/available`);
//       const data = await response.json();
//       setMentors(data.mentors || []);
//     } catch (error) {
//       console.error('Error fetching mentors:', error);
//       setMentors([]);
//     } finally {
//       setLoading(false);
//     }
//   };

//   // Initialize local media tracks
//   const initializeLocalTracks = async () => {
//     if (tracksInitialized.current) {
//       console.log('Tracks already initialized');
//       return;
//     }

//     try {
//       setMediaError('');
//       console.log('Requesting media permissions...');
      
//       // Request permissions first
//       const stream = await navigator.mediaDevices.getUserMedia({
//         video: {
//           width: { ideal: VideoPresets.h720.width },
//           height: { ideal: VideoPresets.h720.height },
//           frameRate: { ideal: 30 }
//         },
//         audio: {
//           echoCancellation: true,
//           noiseSuppression: true,
//           autoGainControl: true
//         }
//       });

//       // Stop the temporary stream
//       stream.getTracks().forEach(track => track.stop());

//       console.log('Creating LiveKit tracks...');
//       const tracks = await createLocalTracks({
//         audio: {
//           echoCancellation: true,
//           noiseSuppression: true,
//           autoGainControl: true
//         },
//         video: {
//           resolution: {
//             width: VideoPresets.h720.width,
//             height: VideoPresets.h720.height,
//             frameRate: 30
//           }
//         }
//       });

//       console.log('Created tracks:', tracks.length);
//       setLocalTracks(tracks);

//       // Separate video and audio tracks
//       const videoTrack = tracks.find(track => track.kind === Track.Kind.Video);
//       const audioTrack = tracks.find(track => track.kind === Track.Kind.Audio);

//       if (videoTrack) {
//         setLocalVideoTrack(videoTrack);
//         console.log('Video track created:', videoTrack);
//       } else {
//         console.error('No video track found');
//       }

//       if (audioTrack) {
//         setLocalAudioTrack(audioTrack);
//         console.log('Audio track created:', audioTrack);
//       } else {
//         console.error('No audio track found');
//       }

//       tracksInitialized.current = true;
//       console.log('Local tracks initialized successfully');

//     } catch (error) {
//       console.error('Error initializing local tracks:', error);
//       let errorMessage = 'Failed to access camera/microphone. ';
      
//       if (error.name === 'NotAllowedError') {
//         errorMessage += 'Please allow camera and microphone permissions.';
//       } else if (error.name === 'NotFoundError') {
//         errorMessage += 'No camera or microphone found.';
//       } else if (error.name === 'NotReadableError') {
//         errorMessage += 'Camera/microphone is being used by another application.';
//       } else {
//         errorMessage += error.message;
//       }
      
//       setMediaError(errorMessage);
//       throw error;
//     }
//   };

//   // Start call with mentor
//   const startCall = async (mentorId) => {
//     try {
//       const studentId = getStudentId();
      
//       setCallState('requesting');
//       setSelectedMentor(mentors.find(m => m.mentor_id === mentorId));

//       // Initialize tracks before starting call
//       await initializeLocalTracks();

//       const response = await fetch(`${API_BASE_URL}/call/start`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({
//           student_id: studentId,
//           mentor_id: mentorId
//         })
//       });

//       const data = await response.json();
//       if (response.ok) {
//         setCurrentRoomName(data.room_name);
//         setCallState('waiting');
//         pollForMentorAcceptance(data.room_name);
//       } else {
//         throw new Error(data.error || 'Failed to start call');
//       }
//     } catch (error) {
//       console.error('Error starting call:', error);
//       alert(`Error: ${error.message}`);
//       setCallState('idle');
//     }
//   };

//   // Poll for mentor acceptance and join call
//   const pollForMentorAcceptance = async (roomName) => {
//     const maxAttempts = 60; // 2 minutes
//     let attempts = 0;

//     const poll = async () => {
//       if (attempts >= maxAttempts || callState === 'ended') {
//         setCallState('ended');
//         alert('Mentor did not accept the call. Please try again.');
//         return;
//       }

//       try {
//         const studentId = getStudentId();
        
//         const response = await fetch(`${API_BASE_URL}/call/student/join`, {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({
//             room_name: roomName,
//             student_id: studentId
//           })
//         });

//         const data = await response.json();
//         if (response.ok && data.token) {
//           await joinRoom(data.token, LIVEKIT_URL, roomName);
//           return;
//         }
//       } catch (error) {
//         console.error('Polling error:', error);
//       }

//       attempts++;
//       setTimeout(poll, 2000);
//     };

//     poll();
//   };

//   // Join LiveKit room
//   const joinRoom = async (token, wsUrl, roomName) => {
//     try {
//       console.log('Joining room with token:', token.substring(0, 20) + '...');
      
//       const newRoom = new Room({
//         adaptiveStream: true,
//         dynacast: true,
//         autoSubscribe: true,
//         videoCaptureDefaults: {
//           resolution: {
//             width: VideoPresets.h720.width,
//             height: VideoPresets.h720.height,
//             frameRate: 30
//           }
//         },
//         publishDefaults: {
//           videoSimulcastLayers: [
//             VideoPresets.h90,
//             VideoPresets.h216,
//             VideoPresets.h540,
//           ],
//         }
//       });

//       roomRef.current = newRoom;
//       setRoom(newRoom);

//       // Room event listeners
//       newRoom.on(RoomEvent.Connected, async () => {
//         console.log('Connected to room successfully');
//         setConnectionStatus('connected');
//         setCallState('connected');
        
//         // Publish existing local tracks
//         await publishLocalTracks(newRoom);

//         // Handle existing remote participants and their tracks
//         const remoteParts = Array.from(newRoom.remoteParticipants.values());
//         setParticipants(remoteParts);
//         remoteParts.forEach(participant => {
//           setupParticipantTracks(participant);
//           participant.trackPublications.forEach(publication => {
//             if (publication.isSubscribed && publication.track) {
//               const track = publication.track;
//               console.log('Handling existing track:', track.kind, track.source);
//               if (track.kind === Track.Kind.Video) {
//                 if (track.source === Track.Source.Camera) {
//                   setRemoteVideoTrack(track);
//                 } else if (track.source === Track.Source.ScreenShare) {
//                   setRemoteScreenTrack(track);
//                   setIsScreenSharing(true);
//                 }
//               } else if (track.kind === Track.Kind.Audio) {
//                 if (track.source === Track.Source.Microphone) {
//                   setRemoteAudioTrack(track);
//                 } else if (track.source === Track.Source.ScreenShareAudio) {
//                   setRemoteScreenAudioTrack(track);
//                 }
//               }
//             }
//           });
//         });
//       });

//       newRoom.on(RoomEvent.Disconnected, (reason) => {
//         console.log('Disconnected from room:', reason);
//         setConnectionStatus('disconnected');
//         setCallState('ended');
//         cleanup();
//       });

//       newRoom.on(RoomEvent.ParticipantConnected, (participant) => {
//         console.log('Participant connected:', participant.identity);
//         setParticipants(prev => [...prev, participant]);
//         setupParticipantTracks(participant);
//       });

//       newRoom.on(RoomEvent.ParticipantDisconnected, (participant) => {
//         console.log('Participant disconnected:', participant.identity);
//         setParticipants(prev => prev.filter(p => p.sid !== participant.sid));
//       });

//       newRoom.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
//         console.log('Track subscribed:', track.kind, track.source);
//         if (track.kind === Track.Kind.Video) {
//           if (track.source === Track.Source.Camera) {
//             setRemoteVideoTrack(track);
//           } else if (track.source === Track.Source.ScreenShare) {
//             setRemoteScreenTrack(track);
//             setIsScreenSharing(true);
//           }
//         } else if (track.kind === Track.Kind.Audio) {
//           if (track.source === Track.Source.Microphone) {
//             setRemoteAudioTrack(track);
//           } else if (track.source === Track.Source.ScreenShareAudio) {
//             setRemoteScreenAudioTrack(track);
//           }
//         }
//       });

//       newRoom.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
//         console.log('Track unsubscribed:', track.kind, track.source);
//         track.detach();
//         if (track.kind === Track.Kind.Video) {
//           if (track.source === Track.Source.Camera) {
//             setRemoteVideoTrack(null);
//           } else if (track.source === Track.Source.ScreenShare) {
//             setRemoteScreenTrack(null);
//             setIsScreenSharing(false);
//           }
//         } else if (track.kind === Track.Kind.Audio) {
//           if (track.source === Track.Source.Microphone) {
//             setRemoteAudioTrack(null);
//           } else if (track.source === Track.Source.ScreenShareAudio) {
//             setRemoteScreenAudioTrack(null);
//           }
//         }
//       });

//       newRoom.on(RoomEvent.TrackPublished, (publication, participant) => {
//         console.log('Track published:', publication.kind, 'by:', participant.identity);
//       });

//       newRoom.on(RoomEvent.TrackUnpublished, (publication, participant) => {
//         console.log('Track unpublished:', publication.kind, 'by:', participant.identity);
//       });

//       // Connect to room
//       console.log('Connecting to room...');
//       await newRoom.connect(wsUrl, token);
//       console.log('Room connection established');

//     } catch (error) {
//       console.error('Error joining room:', error);
//       alert(`Failed to join call: ${error.message}`);
//       setCallState('ended');
//     }
//   };

//   // Publish local tracks to the room
//   const publishLocalTracks = async (room) => {
//     try {
//       console.log('Publishing local tracks...');
      
//       // Use existing tracks or create new ones
//       let tracksToPublish = localTracks;
      
//       if (tracksToPublish.length === 0) {
//         console.log('No existing tracks, creating new ones...');
//         tracksToPublish = await createLocalTracks({
//           audio: {
//             echoCancellation: true,
//             noiseSuppression: true,
//             autoGainControl: true
//           },
//           video: {
//             resolution: {
//               width: VideoPresets.h720.width,
//               height: VideoPresets.h720.height,
//               frameRate: 30
//             }
//           }
//         });
//         setLocalTracks(tracksToPublish);
        
//         // Update track references
//         const videoTrack = tracksToPublish.find(track => track.kind === Track.Kind.Video);
//         const audioTrack = tracksToPublish.find(track => track.kind === Track.Kind.Audio);
        
//         if (videoTrack) {
//           setLocalVideoTrack(videoTrack);
//         }
        
//         if (audioTrack) {
//           setLocalAudioTrack(audioTrack);
//         }
//       }

//       console.log('Tracks to publish:', tracksToPublish.length);
      
//       for (const track of tracksToPublish) {
//         try {
//           console.log(`Publishing ${track.kind} track...`);
//           const publication = await room.localParticipant.publishTrack(track);
//           console.log(`Successfully published ${track.kind} track:`, publication.trackSid);
//         } catch (publishError) {
//           console.error(`Error publishing ${track.kind} track:`, publishError);
//         }
//       }
      
//       console.log('Local tracks publishing completed');
//     } catch (error) {
//       console.error('Error in publishLocalTracks:', error);
//     }
//   };

//   // Setup participant tracks
//   const setupParticipantTracks = (participant) => {
//     participant.on(ParticipantEvent.TrackSubscribed, (track, publication) => {
//       console.log(`Participant ${participant.identity} track subscribed:`, track.kind);
//     });

//     participant.on(ParticipantEvent.TrackUnsubscribed, (track, publication) => {
//       console.log(`Participant ${participant.identity} track unsubscribed:`, track.kind);
//     });

//     // Subscribe to existing tracks
//     participant.trackPublications.forEach((publication) => {
//       if (publication.isSubscribed && publication.track) {
//         // The RoomEvent.TrackSubscribed will handle setting states
//       }
//     });
//   };

//   // Toggle audio mute
//   const toggleMute = async () => {
//     if (room && room.localParticipant && localAudioTrack) {
//       try {
//         await localAudioTrack.setMuted(!isMuted);
//         setIsMuted(!isMuted);
//         console.log('Audio mute toggled:', !isMuted);
//       } catch (error) {
//         console.error('Error toggling mute:', error);
//       }
//     }
//   };

//   // Toggle video
//   const toggleVideo = async () => {
//     if (room && room.localParticipant && localVideoTrack) {
//       try {
//         await localVideoTrack.setMuted(!isVideoOff);
//         setIsVideoOff(!isVideoOff);
//         console.log('Video toggled:', !isVideoOff);
//       } catch (error) {
//         console.error('Error toggling video:', error);
//       }
//     }
//   };

//   // Toggle screen share
//   const toggleScreenShare = async () => {
//     if (!room) return;

//     try {
//       if (isScreenSharing) {
//         // Stop screen sharing
//         await room.localParticipant.setScreenShareEnabled(false);
//         setIsScreenSharing(false);
//         console.log('Screen sharing stopped');
//       } else {
//         // Start screen sharing
//         await room.localParticipant.setScreenShareEnabled(true);
//         setIsScreenSharing(true);
//         console.log('Screen sharing started');
//       }
//     } catch (error) {
//       console.error('Error toggling screen share:', error);
//       alert('Failed to toggle screen share: ' + error.message);
//     }
//   };

//   // End call
//   const endCall = async () => {
//     try {
//       if (currentRoomName) {
//         await fetch(`${API_BASE_URL}/call/end`, {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({ room_name: currentRoomName })
//         });
//       }
//     } catch (error) {
//       console.error('Error ending call:', error);
//     }

//     cleanup();
//     setCallState('ended');
//   };

//   // Cleanup resources
//   const cleanup = () => {
//     console.log('Cleaning up resources...');
    
//     if (roomRef.current) {
//       roomRef.current.disconnect();
//       roomRef.current = null;
//     }
//     setRoom(null);
    
//     // Stop and cleanup local tracks
//     localTracks.forEach(track => {
//       try {
//         track.stop();
//       } catch (error) {
//         console.error('Error stopping track:', error);
//       }
//     });
    
//     setLocalTracks([]);
//     setLocalVideoTrack(null);
//     setLocalAudioTrack(null);
//     setParticipants([]);
//     setCurrentRoomName('');
//     setIsScreenSharing(false);
//     setIsMuted(false);
//     setIsVideoOff(false);
//     setConnectionStatus('disconnected');
//     setMediaError('');
//     setRemoteVideoTrack(null);
//     setRemoteAudioTrack(null);
//     setRemoteScreenTrack(null);
//     setRemoteScreenAudioTrack(null);
//     tracksInitialized.current = false;
    
//     console.log('Cleanup completed');
//   };

//   useEffect(() => {
//     if (localVideoTrack && localVideoRef.current) {
//       localVideoTrack.attach(localVideoRef.current);
//       return () => localVideoTrack.detach(localVideoRef.current);
//     }
//   }, [localVideoTrack, localVideoRef.current]);

//   useEffect(() => {
//     if (remoteVideoTrack && remoteVideoRef.current) {
//       remoteVideoTrack.attach(remoteVideoRef.current);
//       return () => remoteVideoTrack.detach(remoteVideoRef.current);
//     }
//   }, [remoteVideoTrack, remoteVideoRef.current]);

//   useEffect(() => {
//     if (remoteAudioTrack && remoteAudioRef.current) {
//       remoteAudioTrack.attach(remoteAudioRef.current);
//       return () => remoteAudioTrack.detach(remoteAudioRef.current);
//     }
//   }, [remoteAudioTrack, remoteAudioRef.current]);

//   useEffect(() => {
//     if (remoteScreenTrack && screenShareRef.current) {
//       remoteScreenTrack.attach(screenShareRef.current);
//       return () => remoteScreenTrack.detach(screenShareRef.current);
//     }
//   }, [remoteScreenTrack, screenShareRef.current]);

//   useEffect(() => {
//     if (remoteScreenAudioTrack && screenAudioRef.current) {
//       remoteScreenAudioTrack.attach(screenAudioRef.current);
//       return () => remoteScreenAudioTrack.detach(screenAudioRef.current);
//     }
//   }, [remoteScreenAudioTrack, screenAudioRef.current]);

//   // Initialize when modal opens
//   useEffect(() => {
//     if (isOpen) {
//       console.log('Modal opened, fetching mentors...');
//       fetchMentors();
//       // Don't initialize tracks here, do it when starting a call
//     } else {
//       console.log('Modal closed, cleaning up...');
//       cleanup();
//     }

//     return () => cleanup();
//   }, [isOpen]);

//   // Handle modal close
//   const handleClose = () => {
//     if (callState === 'connected' || callState === 'waiting') {
//       endCall();
//     } else {
//       cleanup();
//     }
//     onClose();
//     setCallState('idle');
//   };

//   if (!isOpen) return null;

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//       <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
//         <div className="p-6">
//           <div className="flex justify-between items-center mb-6">
//             <h2 className="text-2xl font-bold text-gray-800">
//               {callState === 'idle' ? 'Select a Mentor for Video Call' :
//                callState === 'requesting' ? 'Requesting Call...' :
//                callState === 'waiting' ? 'Waiting for Mentor...' :
//                callState === 'connected' ? 'Video Call Active' : 'Call Ended'}
//             </h2>
//             <button 
//               onClick={handleClose}
//               className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
//             >
//               ×
//             </button>
//           </div>

//           {/* Media Error Display */}
//           {mediaError && (
//             <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
//               <strong>Media Error:</strong> {mediaError}
//               <button 
//                 onClick={() => {
//                   setMediaError('');
//                   tracksInitialized.current = false;
//                 }}
//                 className="ml-4 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
//               >
//                 Retry
//               </button>
//             </div>
//           )}

//           {callState === 'idle' && (
//             <div>
//               {loading ? (
//                 <div className="text-center py-8">
//                   <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
//                   <p className="mt-4 text-gray-600">Loading mentors...</p>
//                 </div>
//               ) : mentors.length === 0 ? (
//                 <div className="text-center py-8">
//                   <p className="text-gray-600">No mentors available at the moment.</p>
//                   <button 
//                     onClick={fetchMentors}
//                     className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
//                   >
//                     Refresh
//                   </button>
//                 </div>
//               ) : (
//                 <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//                   {mentors.map((mentor) => (
//                     <div key={mentor.mentor_id} className="border rounded-lg p-4 hover:shadow-lg transition-shadow">
//                       <div className="flex items-center mb-3">
//                         <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
//                           {mentor.name ? mentor.name.charAt(0).toUpperCase() : 'M'}
//                         </div>
//                         <div className="ml-3">
//                           <h3 className="font-semibold text-gray-800">
//                             {mentor.name || `Mentor ${mentor.mentor_id}`}
//                           </h3>
//                           <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
//                             Online
//                           </span>
//                         </div>
//                       </div>
//                       {mentor.subjects && (
//                         <p className="text-sm text-gray-600 mb-3">
//                           Subjects: {mentor.subjects.join(', ')}
//                         </p>
//                       )}
//                       <button
//                         onClick={() => startCall(mentor.mentor_id)}
//                         className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
//                         disabled={!sessionStorage.getItem('userId') && !sessionStorage.getItem('user_id') && !sessionStorage.getItem('studentId')}
//                       >
//                         {(!sessionStorage.getItem('userId') && !sessionStorage.getItem('user_id') && !sessionStorage.getItem('studentId')) 
//                           ? 'Please Login First' 
//                           : 'Start Video Call'
//                         }
//                       </button>
//                     </div>
//                   ))}
//                 </div>
//               )}
//             </div>
//           )}

//           {(callState === 'requesting' || callState === 'waiting') && (
//             <div className="text-center py-8">
//               <div className="animate-pulse">
//                 <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-4">
//                   📹
//                 </div>
//               </div>
//               {selectedMentor && (
//                 <h3 className="text-lg font-semibold mb-2">
//                   Calling {selectedMentor.name || `Mentor ${selectedMentor.mentor_id}`}
//                 </h3>
//               )}
//               <p className="text-gray-600 mb-4">
//                 {callState === 'requesting' ? 'Sending call request...' : 'Waiting for mentor to accept...'}
//               </p>
//               <div className="mb-4">
//                 <video
//                   ref={localVideoRef}
//                   autoPlay
//                   muted
//                   playsInline
//                   className="w-64 h-48 bg-gray-900 rounded-lg mx-auto object-cover"
//                 />
//                 <p className="text-sm text-gray-500 mt-2">Your video preview</p>
//                 {!localVideoTrack && (
//                   <p className="text-sm text-red-500 mt-1">
//                     {mediaError ? 'Camera access failed' : 'Initializing camera...'}
//                   </p>
//                 )}
//               </div>
//               <button
//                 onClick={endCall}
//                 className="bg-red-600 text-white py-2 px-6 rounded hover:bg-red-700"
//               >
//                 Cancel Call
//               </button>
//             </div>
//           )}

//           {callState === 'connected' && (
//             <div>
//               <div className="mb-4 p-3 bg-green-100 text-green-800 rounded-lg text-center">
//                 Connected to {selectedMentor?.name || 'Mentor'} • {connectionStatus}
//               </div>
              
//               <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
//                 {/* Remote video */}
//                 <div className="relative">
//                   <video
//                     ref={remoteVideoRef}
//                     autoPlay
//                     playsInline
//                     className="w-full h-64 lg:h-80 bg-gray-900 rounded-lg object-cover"
//                   />
//                   <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
//                     {selectedMentor?.name || 'Mentor'}
//                   </div>
//                 </div>
                
//                 {/* Local video */}
//                 <div className="relative">
//                   <video
//                     ref={localVideoRef}
//                     autoPlay
//                     muted
//                     playsInline
//                     className="w-full h-64 lg:h-80 bg-gray-900 rounded-lg object-cover"
//                   />
//                   <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
//                     You {isVideoOff ? '(Video Off)' : ''}
//                   </div>
//                 </div>
//               </div>

//               {/* Screen share */}
//               {isScreenSharing && (
//                 <div className="mb-4">
//                   <video
//                     ref={screenShareRef}
//                     autoPlay
//                     playsInline
//                     className="w-full h-64 bg-gray-900 rounded-lg object-contain"
//                   />
//                   <p className="text-center text-sm text-gray-600 mt-2">Screen Share</p>
//                 </div>
//               )}

//               {/* Audio elements */}
//               <audio ref={remoteAudioRef} autoPlay />
//               <audio ref={screenAudioRef} autoPlay />

//               {/* Call controls */}
//               <div className="flex justify-center space-x-4">
//                 <button
//                   onClick={toggleMute}
//                   className={`p-3 rounded-full ${isMuted ? 'bg-red-600' : 'bg-gray-600'} text-white hover:opacity-80 transition-all`}
//                   title={isMuted ? 'Unmute' : 'Mute'}
//                 >
//                   {isMuted ? '🔇' : '🎤'}
//                 </button>
                
//                 <button
//                   onClick={toggleVideo}
//                   className={`p-3 rounded-full ${isVideoOff ? 'bg-red-600' : 'bg-gray-600'} text-white hover:opacity-80 transition-all`}
//                   title={isVideoOff ? 'Turn Video On' : 'Turn Video Off'}
//                 >
//                   {isVideoOff ? '📹' : '🎥'}
//                 </button>
                
//                 <button
//                   onClick={toggleScreenShare}
//                   className={`p-3 rounded-full ${isScreenSharing ? 'bg-blue-600' : 'bg-gray-600'} text-white hover:opacity-80 transition-all`}
//                   title={isScreenSharing ? 'Stop Screen Share' : 'Start Screen Share'}
//                 >
//                   🖥️
//                 </button>
                
//                 <button
//                   onClick={endCall}
//                   className="p-3 rounded-full bg-red-600 text-white hover:bg-red-700 transition-all"
//                   title="End Call"
//                 >
//                   📞
//                 </button>
//               </div>
              
//               {/* Connection status */}
//               <div className="mt-4 text-center text-sm text-gray-500">
//                 Video: {localVideoTrack ? (isVideoOff ? 'Off' : 'On') : 'Not Available'} | 
//                 Audio: {localAudioTrack ? (isMuted ? 'Muted' : 'On') : 'Not Available'} |
//                 Participants: {participants.length + 1}
//               </div>
//             </div>
//           )}

//           {callState === 'ended' && (
//             <div className="text-center py-8">
//               <div className="text-6xl mb-4">📞</div>
//               <h3 className="text-lg font-semibold mb-2">Call Ended</h3>
//               <p className="text-gray-600 mb-4">Thank you for using our video call service.</p>
//               <button
//                 onClick={() => setCallState('idle')}
//                 className="bg-blue-600 text-white py-2 px-6 rounded hover:bg-blue-700 mr-2"
//               >
//                 Start New Call
//               </button>
//               <button
//                 onClick={handleClose}
//                 className="bg-gray-600 text-white py-2 px-6 rounded hover:bg-gray-700"
//               >
//                 Close
//               </button>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ReferTutorModal;//ReferTutorModal modified 

import React, { useState, useEffect, useRef } from 'react';
import { 
  Room, 
  Track,
  VideoPresets,
  createLocalTracks,
  RoomEvent,
  ParticipantEvent
} from 'livekit-client';

const ReferTutorModal = ({ isOpen, onClose }) => {
  const [mentors, setMentors] = useState({
    available: [],
    away: [],
    offline: []
  });
  const [loading, setLoading] = useState(false);
  const [callState, setCallState] = useState('idle'); // idle, requesting, waiting, connected, ended
  const [selectedMentor, setSelectedMentor] = useState(null);
  const [room, setRoom] = useState(null);
  const [participants, setParticipants] = useState([]);
  const [localTracks, setLocalTracks] = useState([]);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [currentRoomName, setCurrentRoomName] = useState('');
  const [mediaError, setMediaError] = useState('');
  const [localVideoTrack, setLocalVideoTrack] = useState(null);
  const [localAudioTrack, setLocalAudioTrack] = useState(null);
  const [remoteVideoTrack, setRemoteVideoTrack] = useState(null);
  const [remoteAudioTrack, setRemoteAudioTrack] = useState(null);
  const [remoteScreenTrack, setRemoteScreenTrack] = useState(null);
  const [remoteScreenAudioTrack, setRemoteScreenAudioTrack] = useState(null);

  const localVideoRef = useRef(null);
  const remoteVideoRef = useRef(null);
  const screenShareRef = useRef(null);
  const remoteAudioRef = useRef(null);
  const screenAudioRef = useRef(null);
  const roomRef = useRef(null);
  const tracksInitialized = useRef(false);

  const API_BASE_URL = 'https://testing.sasthra.in';
  const LIVEKIT_URL = 'wss://livekit.sasthra.in';

  // Get student ID from sessionStorage
  const getStudentId = () => {
    const userId = sessionStorage.getItem('userId') || sessionStorage.getItem('user_id') || sessionStorage.getItem('studentId');
    if (!userId) {
      alert('Student ID not found in session. Please login again.');
      throw new Error('Student ID not found');
    }
    return userId;
  };

  // Fetch mentors with status (available, away, offline)
  const fetchMentors = async () => {
    setLoading(true);
    try {
      // Fetch all mentors with their status
      const response = await fetch(`${API_BASE_URL}/mentors-by-status`);
      if (!response.ok) {
        throw new Error(`Failed to fetch mentors: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Handle the specific API response structure
      if (data && Array.isArray(data.online_mentors) && Array.isArray(data.away_mentors) && Array.isArray(data.offline_mentors)) {
        console.log('API returned expected structure with online_mentors, away_mentors, offline_mentors');
        
        // Map online_mentors to available
        setMentors({
          available: data.online_mentors,
          away: data.away_mentors,
          offline: data.offline_mentors
        });
        
        return;
      }
      
      // Fallback handling for other possible structures
      let mentorsList = [];
      if (Array.isArray(data)) {
        // If data is directly an array
        mentorsList = data;
      } else if (data && Array.isArray(data.mentors)) {
        // If data has a mentors property that's an array
        mentorsList = data.mentors;
      } else if (data && data.available && data.away && data.offline) {
        // If data already has the categorized structure
        setMentors({
          available: data.available,
          away: data.away,
          offline: data.offline
        });
        return;
      } else {
        console.warn('Unexpected API response structure:', data);
        
        // Try to find any mentors array in the response
        const allMentors = Object.values(data).find(item => Array.isArray(item));
        if (allMentors) {
          mentorsList = allMentors;
        }
      }
      
      if (mentorsList.length > 0) {
        // Group mentors by status (handling both 'online' and 'available' as available)
        const available = mentorsList.filter(m => m.status === 'available' || m.status === 'online');
        const away = mentorsList.filter(m => m.status === 'away');
        const offline = mentorsList.filter(m => m.status === 'offline');
        
        setMentors({ available, away, offline });
      } else {
        console.error('No valid mentors array found in response');
        setMentors({ available: [], away: [], offline: [] });
      }
    } catch (error) {
      console.error('Error fetching mentors:', error);
      setMentors({ available: [], away: [], offline: [] });
      alert(`Error loading mentors: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Initialize local media tracks
  const initializeLocalTracks = async () => {
    if (tracksInitialized.current) {
      console.log('Tracks already initialized');
      return;
    }

    try {
      setMediaError('');
      console.log('Requesting media permissions...');
      
      // Request permissions first
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: VideoPresets.h720.width },
          height: { ideal: VideoPresets.h720.height },
          frameRate: { ideal: 30 }
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // Stop the temporary stream
      stream.getTracks().forEach(track => track.stop());

      console.log('Creating LiveKit tracks...');
      const tracks = await createLocalTracks({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        },
        video: {
          resolution: {
            width: VideoPresets.h720.width,
            height: VideoPresets.h720.height,
            frameRate: 30
          }
        }
      });

      console.log('Created tracks:', tracks.length);
      setLocalTracks(tracks);

      // Separate video and audio tracks
      const videoTrack = tracks.find(track => track.kind === Track.Kind.Video);
      const audioTrack = tracks.find(track => track.kind === Track.Kind.Audio);

      if (videoTrack) {
        setLocalVideoTrack(videoTrack);
        console.log('Video track created:', videoTrack);
      } else {
        console.error('No video track found');
      }

      if (audioTrack) {
        setLocalAudioTrack(audioTrack);
        console.log('Audio track created:', audioTrack);
      } else {
        console.error('No audio track found');
      }

      tracksInitialized.current = true;
      console.log('Local tracks initialized successfully');

    } catch (error) {
      console.error('Error initializing local tracks:', error);
      let errorMessage = 'Failed to access camera/microphone. ';
      
      if (error.name === 'NotAllowedError') {
        errorMessage += 'Please allow camera and microphone permissions.';
      } else if (error.name === 'NotFoundError') {
        errorMessage += 'No camera or microphone found.';
      } else if (error.name === 'NotReadableError') {
        errorMessage += 'Camera/microphone is being used by another application.';
      } else {
        errorMessage += error.message;
      }
      
      setMediaError(errorMessage);
      throw error;
    }
  };

  // Start call with mentor
  const startCall = async (mentorId) => {
    try {
      const studentId = getStudentId();
      
      setCallState('requesting');
      setSelectedMentor([
        ...mentors.available,
        ...mentors.away,
        ...mentors.offline
      ].find(m => m.mentor_id === mentorId));

      // Initialize tracks before starting call
      await initializeLocalTracks();

      const response = await fetch(`${API_BASE_URL}/call/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          student_id: studentId,
          mentor_id: mentorId
        })
      });

      const data = await response.json();
      if (response.ok) {
        setCurrentRoomName(data.room_name);
        setCallState('waiting');
        pollForMentorAcceptance(data.room_name);
      } else {
        throw new Error(data.error || 'Failed to start call');
      }
    } catch (error) {
      console.error('Error starting call:', error);
      alert(`Error: ${error.message}`);
      setCallState('idle');
    }
  };

  // Poll for mentor acceptance and join call
  const pollForMentorAcceptance = async (roomName) => {
    const maxAttempts = 60; // 2 minutes
    let attempts = 0;

    const poll = async () => {
      if (attempts >= maxAttempts || callState === 'ended') {
        setCallState('ended');
        alert('Mentor did not accept the call. Please try again.');
        return;
      }

      try {
        const studentId = getStudentId();
        
        const response = await fetch(`${API_BASE_URL}/call/student/join`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            room_name: roomName,
            student_id: studentId
          })
        });

        const data = await response.json();
        if (response.ok && data.token) {
          await joinRoom(data.token, LIVEKIT_URL, roomName);
          return;
        }
      } catch (error) {
        console.error('Polling error:', error);
      }

      attempts++;
      setTimeout(poll, 2000);
    };

    poll();
  };

  // Join LiveKit room
  const joinRoom = async (token, wsUrl, roomName) => {
    try {
      console.log('Joining room with token:', token.substring(0, 20) + '...');
      
      const newRoom = new Room({
        adaptiveStream: true,
        dynacast: true,
        autoSubscribe: true,
        videoCaptureDefaults: {
          resolution: {
            width: VideoPresets.h720.width,
            height: VideoPresets.h720.height,
            frameRate: 30
          }
        },
        publishDefaults: {
          videoSimulcastLayers: [
            VideoPresets.h90,
            VideoPresets.h216,
            VideoPresets.h540,
          ],
        }
      });

      roomRef.current = newRoom;
      setRoom(newRoom);

      // Room event listeners
      newRoom.on(RoomEvent.Connected, async () => {
        console.log('Connected to room successfully');
        setConnectionStatus('connected');
        setCallState('connected');
        
        // Publish existing local tracks
        await publishLocalTracks(newRoom);

        // Handle existing remote participants and their tracks
        const remoteParts = Array.from(newRoom.remoteParticipants.values());
        setParticipants(remoteParts);
        remoteParts.forEach(participant => {
          setupParticipantTracks(participant);
          participant.trackPublications.forEach(publication => {
            if (publication.isSubscribed && publication.track) {
              const track = publication.track;
              console.log('Handling existing track:', track.kind, track.source);
              if (track.kind === Track.Kind.Video) {
                if (track.source === Track.Source.Camera) {
                  setRemoteVideoTrack(track);
                } else if (track.source === Track.Source.ScreenShare) {
                  setRemoteScreenTrack(track);
                  setIsScreenSharing(true);
                }
              } else if (track.kind === Track.Kind.Audio) {
                if (track.source === Track.Source.Microphone) {
                  setRemoteAudioTrack(track);
                } else if (track.source === Track.Source.ScreenShareAudio) {
                  setRemoteScreenAudioTrack(track);
                }
              }
            }
          });
        });
      });

      newRoom.on(RoomEvent.Disconnected, (reason) => {
        console.log('Disconnected from room:', reason);
        setConnectionStatus('disconnected');
        setCallState('ended');
        cleanup();
      });

      newRoom.on(RoomEvent.ParticipantConnected, (participant) => {
        console.log('Participant connected:', participant.identity);
        setParticipants(prev => [...prev, participant]);
        setupParticipantTracks(participant);
      });

      newRoom.on(RoomEvent.ParticipantDisconnected, (participant) => {
        console.log('Participant disconnected:', participant.identity);
        setParticipants(prev => prev.filter(p => p.sid !== participant.sid));
      });

      newRoom.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
        console.log('Track subscribed:', track.kind, track.source);
        if (track.kind === Track.Kind.Video) {
          if (track.source === Track.Source.Camera) {
            setRemoteVideoTrack(track);
          } else if (track.source === Track.Source.ScreenShare) {
            setRemoteScreenTrack(track);
            setIsScreenSharing(true);
          }
        } else if (track.kind === Track.Kind.Audio) {
          if (track.source === Track.Source.Microphone) {
            setRemoteAudioTrack(track);
          } else if (track.source === Track.Source.ScreenShareAudio) {
            setRemoteScreenAudioTrack(track);
          }
        }
      });

      newRoom.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
        console.log('Track unsubscribed:', track.kind, track.source);
        track.detach();
        if (track.kind === Track.Kind.Video) {
          if (track.source === Track.Source.Camera) {
            setRemoteVideoTrack(null);
          } else if (track.source === Track.Source.ScreenShare) {
            setRemoteScreenTrack(null);
            setIsScreenSharing(false);
          }
        } else if (track.kind === Track.Kind.Audio) {
          if (track.source === Track.Source.Microphone) {
            setRemoteAudioTrack(null);
          } else if (track.source === Track.Source.ScreenShareAudio) {
            setRemoteScreenAudioTrack(null);
          }
        }
      });

      newRoom.on(RoomEvent.TrackPublished, (publication, participant) => {
        console.log('Track published:', publication.kind, 'by:', participant.identity);
      });

      newRoom.on(RoomEvent.TrackUnpublished, (publication, participant) => {
        console.log('Track unpublished:', publication.kind, 'by:', participant.identity);
      });

      // Connect to room
      console.log('Connecting to room...');
      await newRoom.connect(wsUrl, token);
      console.log('Room connection established');

    } catch (error) {
      console.error('Error joining room:', error);
      alert(`Failed to join call: ${error.message}`);
      setCallState('ended');
    }
  };

  // Publish local tracks to the room
  const publishLocalTracks = async (room) => {
    try {
      console.log('Publishing local tracks...');
      
      // Use existing tracks or create new ones
      let tracksToPublish = localTracks;
      
      if (tracksToPublish.length === 0) {
        console.log('No existing tracks, creating new ones...');
        tracksToPublish = await createLocalTracks({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          },
          video: {
            resolution: {
              width: VideoPresets.h720.width,
              height: VideoPresets.h720.height,
              frameRate: 30
            }
          }
        });
        setLocalTracks(tracksToPublish);
        
        // Update track references
        const videoTrack = tracksToPublish.find(track => track.kind === Track.Kind.Video);
        const audioTrack = tracksToPublish.find(track => track.kind === Track.Kind.Audio);
        
        if (videoTrack) {
          setLocalVideoTrack(videoTrack);
        }
        
        if (audioTrack) {
          setLocalAudioTrack(audioTrack);
        }
      }

      console.log('Tracks to publish:', tracksToPublish.length);
      
      for (const track of tracksToPublish) {
        try {
          console.log(`Publishing ${track.kind} track...`);
          const publication = await room.localParticipant.publishTrack(track);
          console.log(`Successfully published ${track.kind} track:`, publication.trackSid);
        } catch (publishError) {
          console.error(`Error publishing ${track.kind} track:`, publishError);
        }
      }
      
      console.log('Local tracks publishing completed');
    } catch (error) {
      console.error('Error in publishLocalTracks:', error);
    }
  };

  // Setup participant tracks
  const setupParticipantTracks = (participant) => {
    participant.on(ParticipantEvent.TrackSubscribed, (track, publication) => {
      console.log(`Participant ${participant.identity} track subscribed:`, track.kind);
    });

    participant.on(ParticipantEvent.TrackUnsubscribed, (track, publication) => {
      console.log(`Participant ${participant.identity} track unsubscribed:`, track.kind);
    });

    // Subscribe to existing tracks
    participant.trackPublications.forEach((publication) => {
      if (publication.isSubscribed && publication.track) {
        // The RoomEvent.TrackSubscribed will handle setting states
      }
    });
  };

  // Toggle audio mute
  const toggleMute = async () => {
    if (room && room.localParticipant && localAudioTrack) {
      try {
        await localAudioTrack.setMuted(!isMuted);
        setIsMuted(!isMuted);
        console.log('Audio mute toggled:', !isMuted);
      } catch (error) {
        console.error('Error toggling mute:', error);
      }
    }
  };

  // Toggle video
  const toggleVideo = async () => {
    if (room && room.localParticipant && localVideoTrack) {
      try {
        await localVideoTrack.setMuted(!isVideoOff);
        setIsVideoOff(!isVideoOff);
        console.log('Video toggled:', !isVideoOff);
      } catch (error) {
        console.error('Error toggling video:', error);
      }
    }
  };

  // Toggle screen share
  const toggleScreenShare = async () => {
    if (!room) return;

    try {
      if (isScreenSharing) {
        // Stop screen sharing
        await room.localParticipant.setScreenShareEnabled(false);
        setIsScreenSharing(false);
        console.log('Screen sharing stopped');
      } else {
        // Start screen sharing
        await room.localParticipant.setScreenShareEnabled(true);
        setIsScreenSharing(true);
        console.log('Screen sharing started');
      }
    } catch (error) {
      console.error('Error toggling screen share:', error);
      alert('Failed to toggle screen share: ' + error.message);
    }
  };

  // End call
  const endCall = async () => {
    try {
      if (currentRoomName) {
        await fetch(`${API_BASE_URL}/call/end`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ room_name: currentRoomName })
        });
      }
    } catch (error) {
      console.error('Error ending call:', error);
    }

    cleanup();
    setCallState('ended');
  };

  // Cleanup resources
  const cleanup = () => {
    console.log('Cleaning up resources...');
    
    if (roomRef.current) {
      roomRef.current.disconnect();
      roomRef.current = null;
    }
    setRoom(null);
    
    // Stop and cleanup local tracks
    localTracks.forEach(track => {
      try {
        track.stop();
      } catch (error) {
        console.error('Error stopping track:', error);
      }
    });
    
    setLocalTracks([]);
    setLocalVideoTrack(null);
    setLocalAudioTrack(null);
    setParticipants([]);
    setCurrentRoomName('');
    setIsScreenSharing(false);
    setIsMuted(false);
    setIsVideoOff(false);
    setConnectionStatus('disconnected');
    setMediaError('');
    setRemoteVideoTrack(null);
    setRemoteAudioTrack(null);
    setRemoteScreenTrack(null);
    setRemoteScreenAudioTrack(null);
    tracksInitialized.current = false;
    
    console.log('Cleanup completed');
  };

  useEffect(() => {
    if (localVideoTrack && localVideoRef.current) {
      localVideoTrack.attach(localVideoRef.current);
      return () => localVideoTrack.detach(localVideoRef.current);
    }
  }, [localVideoTrack, localVideoRef.current]);

  useEffect(() => {
    if (remoteVideoTrack && remoteVideoRef.current) {
      remoteVideoTrack.attach(remoteVideoRef.current);
      return () => remoteVideoTrack.detach(remoteVideoRef.current);
    }
  }, [remoteVideoTrack, remoteVideoRef.current]);

  useEffect(() => {
    if (remoteAudioTrack && remoteAudioRef.current) {
      remoteAudioTrack.attach(remoteAudioRef.current);
      return () => remoteAudioTrack.detach(remoteAudioRef.current);
    }
  }, [remoteAudioTrack, remoteAudioRef.current]);

  useEffect(() => {
    if (remoteScreenTrack && screenShareRef.current) {
      remoteScreenTrack.attach(screenShareRef.current);
      return () => remoteScreenTrack.detach(screenShareRef.current);
    }
  }, [remoteScreenTrack, screenShareRef.current]);

  useEffect(() => {
    if (remoteScreenAudioTrack && screenAudioRef.current) {
      remoteScreenAudioTrack.attach(screenAudioRef.current);
      return () => remoteScreenAudioTrack.detach(screenAudioRef.current);
    }
  }, [remoteScreenAudioTrack, screenAudioRef.current]);

  // Initialize when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('Modal opened, fetching mentors...');
      fetchMentors();
      // Don't initialize tracks here, do it when starting a call
    } else {
      console.log('Modal closed, cleaning up...');
      cleanup();
    }

    return () => cleanup();
  }, [isOpen]);

  // Handle modal close
  const handleClose = () => {
    if (callState === 'connected' || callState === 'waiting') {
      endCall();
    } else {
      cleanup();
    }
    onClose();
    setCallState('idle');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 bg-opacity-90 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto transform transition-all duration-300 hover:scale-[1.005]">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl md:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700">
              {callState === 'idle' ? 'Connect with a Mentor' :
               callState === 'requesting' ? 'Requesting Call...' :
               callState === 'waiting' ? 'Waiting for Mentor...' :
               callState === 'connected' ? 'Video Call Active' : 'Call Ended'}
            </h2>
            <button 
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 text-3xl font-bold transition-all duration-200 hover:scale-110 hover:rotate-90"
            >
              ×
            </button>
          </div>

          {/* Media Error Display */}
          {mediaError && (
            <div className="mb-6 p-5 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl shadow-sm">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-red-500 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                </div>
                <div className="ml-3 flex-1">
                  <h3 className="text-lg font-medium text-red-800">Media Error</h3>
                  <div className="mt-2 text-red-700">{mediaError}</div>
                  <div className="mt-4 flex">
                    <button 
                      onClick={() => {
                        setMediaError('');
                        tracksInitialized.current = false;
                      }}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                    >
                      <svg className="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2A8.001 8.001 0 0020.418 9m0 0h-5m-5 5v-5m-1.418 4.582a8 8 0 01-13.272-3.418m13.272 3.418L3 3m5.293 5.293A8 8 0 013 15.868V11" />
                      </svg>
                      Retry
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {callState === 'idle' && (
            <div>
              {loading ? (
                <div className="text-center py-12">
                  <div className="relative mx-auto w-20 h-20 mb-6">
                    <div className="absolute inset-0 border-4 border-blue-200 rounded-full"></div>
                    <div className="absolute inset-0 border-4 border-blue-600 rounded-full animate-spin border-t-transparent"></div>
                  </div>
                  <p className="text-xl font-medium text-gray-700">Finding the perfect mentor for you...</p>
                  <p className="text-gray-500 mt-2">This won't take long!</p>
                </div>
              ) : (
                <div>
                  {/* Available Mentors Section */}
                  {(mentors.available.length > 0 || mentors.away.length > 0 || mentors.offline.length > 0) ? (
                    <>
                      {mentors.available.length > 0 && (
                        <div className="mb-8">
                          <div className="flex items-center mb-4">
                            <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                            <h3 className="text-xl font-bold text-gray-800">Available Now</h3>
                          </div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                            {mentors.available.map((mentor) => (
                              <MentorCard 
                                key={mentor.mentor_id} 
                                mentor={mentor} 
                                status="available"
                                onStartCall={startCall}
                              />
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Away Mentors Section */}
                      {mentors.away.length > 0 && (
                        <div className="mb-8">
                          <div className="flex items-center mb-4">
                            <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2 animate-pulse"></div>
                            <h3 className="text-xl font-bold text-gray-800">Away</h3>
                          </div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                            {mentors.away.map((mentor) => (
                              <MentorCard 
                                key={mentor.mentor_id} 
                                mentor={mentor} 
                                status="away"
                                onStartCall={startCall}
                              />
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Offline Mentors Section */}
                      {mentors.offline.length > 0 && (
                        <div>
                          <div className="flex items-center mb-4">
                            <div className="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                            <h3 className="text-xl font-bold text-gray-800">Offline</h3>
                          </div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                            {mentors.offline.map((mentor) => (
                              <MentorCard 
                                key={mentor.mentor_id} 
                                mentor={mentor} 
                                status="offline"
                                onStartCall={startCall}
                              />
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-12 bg-gray-50 rounded-xl">
                      <div className="text-5xl mb-4">😔</div>
                      <h3 className="text-xl font-bold text-gray-800 mb-2">No Mentors Available</h3>
                      <p className="text-gray-600 mb-6 max-w-md mx-auto">
                        We couldn't find any mentors at the moment. Please check back later or try refreshing the page.
                      </p>
                      <button 
                        onClick={fetchMentors}
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105"
                      >
                        <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2A8.001 8.001 0 0020.418 9m0 0h-5m-5 5v-5m-1.418 4.582a8 8 0 01-13.272-3.418m13.272 3.418L3 3m5.293 5.293A8 8 0 013 15.868V11" />
                        </svg>
                        Refresh List
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {(callState === 'requesting' || callState === 'waiting') && (
            <div className="text-center py-8">
              <div className="relative mx-auto w-40 h-40 mb-6">
                <div className="absolute inset-0 border-4 border-blue-200 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-blue-500 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white text-4xl">
                    📹
                  </div>
                </div>
              </div>
              
              {selectedMentor && (
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">
                    {callState === 'requesting' ? 'Calling' : 'Waiting for'} {selectedMentor.name || `Mentor ${selectedMentor.mentor_id}`}
                  </h3>
                  <div className="flex justify-center space-x-2 mb-4">
                    {[...Array(3)].map((_, i) => (
                      <div 
                        key={i} 
                        className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      ></div>
                    ))}
                  </div>
                </div>
              )}
              
              <p className="text-gray-600 mb-6 text-lg">
                {callState === 'requesting' ? 'Sending call request...' : 'Waiting for mentor to accept...'}
              </p>
              
              <div className="mb-6 bg-gray-50 rounded-xl p-4 max-w-md mx-auto">
                <div className="mb-4">
                  <video
                    ref={localVideoRef}
                    autoPlay
                    muted
                    playsInline
                    className="w-full h-48 rounded-lg object-cover shadow-md border-2 border-white"
                  />
                </div>
                <p className="text-sm text-gray-500 mt-2 flex items-center justify-center">
                  <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                  Your video preview
                </p>
                {!localVideoTrack && (
                  <p className="text-sm text-red-500 mt-1">
                    {mediaError ? 'Camera access failed' : 'Initializing camera...'}
                  </p>
                )}
              </div>
              
              <button
                onClick={endCall}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 transform hover:scale-105"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                Cancel Call
              </button>
            </div>
          )}

          {callState === 'connected' && (
            <div>
              <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center">
                      <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                      <h3 className="text-lg font-bold text-gray-800">
                        Connected to {selectedMentor?.name || 'Mentor'}
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Your video call is active and secure
                    </p>
                  </div>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                    Connected
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                {/* Remote video */}
                <div className="relative rounded-2xl overflow-hidden shadow-xl border-2 border-white bg-gray-900">
                  <video
                    ref={remoteVideoRef}
                    autoPlay
                    playsInline
                    className="w-full h-64 lg:h-80 object-cover"
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                      <span className="text-white font-medium">
                        {selectedMentor?.name || 'Mentor'} {isVideoOff ? '(Video Off)' : ''}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Local video */}
                <div className="relative rounded-2xl overflow-hidden shadow-xl border-2 border-white bg-gray-900">
                  <video
                    ref={localVideoRef}
                    autoPlay
                    muted
                    playsInline
                    className="w-full h-64 lg:h-80 object-cover"
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                      <span className="text-white font-medium">
                        You {isVideoOff ? '(Video Off)' : ''}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Screen share */}
              {isScreenSharing && (
                <div className="mb-6 rounded-2xl overflow-hidden shadow-xl border-2 border-white bg-gray-900">
                  <video
                    ref={screenShareRef}
                    autoPlay
                    playsInline
                    className="w-full h-64 bg-gray-900 object-contain"
                  />
                  <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black to-transparent p-4">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h5.25l-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <span className="text-white font-medium">Screen Share</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Audio elements */}
              <audio ref={remoteAudioRef} autoPlay />
              <audio ref={screenAudioRef} autoPlay />

              {/* Call controls */}
              <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
                <button
                  onClick={toggleMute}
                  className={`p-4 rounded-full ${isMuted ? 'bg-red-500' : 'bg-gray-800'} text-white hover:scale-110 transition-all duration-200 shadow-lg`}
                  title={isMuted ? 'Unmute' : 'Mute'}
                >
                  {isMuted ? (
                    <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                    </svg>
                  ) : (
                    <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8a4 4 0 01-4-4V8a4 4 0 014-4h5a4 4 0 014 4v12a4 4 0 01-4 4z" />
                    </svg>
                  )}
                </button>
                
                <button
                  onClick={toggleVideo}
                  className={`p-4 rounded-full ${isVideoOff ? 'bg-red-500' : 'bg-gray-800'} text-white hover:scale-110 transition-all duration-200 shadow-lg`}
                  title={isVideoOff ? 'Turn Video On' : 'Turn Video Off'}
                >
                  {isVideoOff ? (
                    <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  ) : (
                    <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  )}
                </button>
                
                <button
                  onClick={toggleScreenShare}
                  className={`p-4 rounded-full ${isScreenSharing ? 'bg-blue-500' : 'bg-gray-800'} text-white hover:scale-110 transition-all duration-200 shadow-lg`}
                  title={isScreenSharing ? 'Stop Screen Share' : 'Start Screen Share'}
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h5.25l-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </button>
                
                <button
                  onClick={endCall}
                  className="p-4 rounded-full bg-red-600 text-white hover:bg-red-700 hover:scale-110 transition-all duration-200 shadow-lg"
                  title="End Call"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.801.129H5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2h-5.72a1 1 0 01-.948-.684L9.424 3.684A1 1 0 008.373 3H5a2 2 0 01-2-2z" />
                  </svg>
                </button>
              </div>
              
              {/* Connection status */}
              <div className="mt-4 text-center text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
                <div className="flex justify-center space-x-4">
                  <div className="flex items-center">
                    <span className={`w-2 h-2 rounded-full mr-1 ${localVideoTrack && !isVideoOff ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>
                    Video: {localVideoTrack ? (isVideoOff ? 'Off' : 'On') : 'Not Available'}
                  </div>
                  <div className="flex items-center">
                    <span className={`w-2 h-2 rounded-full mr-1 ${localAudioTrack && !isMuted ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>
                    Audio: {localAudioTrack ? (isMuted ? 'Muted' : 'On') : 'Not Available'}
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 rounded-full mr-1 bg-blue-500"></span>
                    Participants: {participants.length + 1}
                  </div>
                </div>
              </div>
            </div>
          )}

          {callState === 'ended' && (
            <div className="text-center py-10 px-6">
              <div className="relative mx-auto w-32 h-32 mb-6">
                <div className="absolute inset-0 border-4 border-green-200 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-green-500 rounded-full"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-20 h-20 rounded-full bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center text-white text-4xl">
                    ✅
                  </div>
                </div>
              </div>
              
              <h3 className="text-2xl font-bold text-gray-800 mb-2">Call Ended Successfully</h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Thank you for using our video call service. Your session was recorded and will be available in your history.
              </p>
              
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <button
                  onClick={() => setCallState('idle')}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105"
                >
                  <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Start New Call
                </button>
                <button
                  onClick={handleClose}
                  className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
                >
                  <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Mentor Card Component
const MentorCard = ({ mentor, status, onStartCall }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'away':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'offline':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'available':
        return '🟢';
      case 'away':
        return '🟡';
      case 'offline':
        return '⚪';
      default:
        return '⚪';
    }
  };

  return (
    <div 
      className={`border-2 rounded-xl overflow-hidden bg-white shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02] ${getStatusColor(status)} relative`}
    >
      {/* Status indicator */}
      <div className="absolute top-3 right-3 z-10">
        <div className={`w-3 h-3 rounded-full ${status === 'available' ? 'bg-green-500 animate-pulse' : status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'}`}></div>
      </div>
      
      <div className="p-5">
        <div className="flex items-center mb-4">
          <div className="relative">
            <div className={`w-12 h-12 rounded-full bg-gradient-to-br ${status === 'available' ? 'from-green-400 to-emerald-500' : status === 'away' ? 'from-yellow-400 to-amber-500' : 'from-gray-400 to-gray-500'} flex items-center justify-center text-white font-bold shadow-md`}>
              {mentor.name ? mentor.name.charAt(0).toUpperCase() : 'M'}
            </div>
            {status === 'available' && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full border-2 border-green-500 flex items-center justify-center">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              </div>
            )}
          </div>
          
          <div className="ml-4">
            <h3 className="font-bold text-gray-800 text-lg">
              {mentor.name || `Mentor ${mentor.mentor_id}`}
            </h3>
            <div className="flex items-center mt-1">
              <span className={`inline-block w-2 h-2 rounded-full mr-2 ${status === 'available' ? 'bg-green-500 animate-pulse' : status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'}`}></span>
              <span className="text-sm font-medium capitalize">{status}</span>
            </div>
          </div>
        </div>
        
        {mentor.subjects && mentor.subjects.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {mentor.subjects.slice(0, 3).map((subject, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {subject}
                </span>
              ))}
              {mentor.subjects.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{mentor.subjects.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}
        
        <div className="mt-4 pt-4 border-t border-gray-100">
          <button
            onClick={() => onStartCall(mentor.mentor_id)}
            disabled={status !== 'available'}
            className={`w-full py-2.5 rounded-lg font-medium transition-all duration-200 ${
              status === 'available'
                ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 shadow-md hover:shadow-lg transform hover:scale-[1.02]'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {status === 'available' ? 'Start Video Call' : 'Not Available'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReferTutorModal;


