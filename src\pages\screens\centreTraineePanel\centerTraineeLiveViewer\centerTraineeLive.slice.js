// import { CenterliveViewerApi } from '../../../../redux/api/api';
// const initialState = {
//   centerLiveViewerData: null
// };
// export const centerTraineeLiveSlice = CenterliveViewerApi.injectEndpoints({
//   endpoints: (builder) => ({
//     getCenterLiveViewer: builder.query({
//       query: () => {
//         const timestamp = Date.now();
//         console.log('🔗 RTK Query making request at:', new Date(timestamp).toISOString());
//         return {
//           url: `/active-streams?rtk=true&t=${timestamp}`,
//           method: 'GET',
//           headers: {
//             'Cache-Control': 'no-cache',
//             'Pragma': 'no-cache',
//             'Accept': 'application/json'
//           },
//           responseHandler: async (res) => {
//             console.log('🔗 RTK Query response status:', res.status);
//             console.log('🔗 RTK Query response headers:', res.headers);
//             const data = await res.json();
//             console.log('🔗 RTK Query raw response data:', data);
//             return data;
//           }
//         };
//       },
//       transformResponse: (response) => {
//         console.log('🔍 Center Live Viewer API Response:', response);
//         console.log('🔍 Response Type:', typeof response);
//         console.log('🔍 Response Keys:', Object.keys(response || {}));
//         console.log('🔍 Active Streams Array:', response?.active_streams);
//         console.log('🔍 Active Streams Type:', typeof response?.active_streams);
//         console.log('🔍 Active Streams Length:', response?.active_streams?.length);
//         console.log('🔍 Success Status:', response?.success);
//         console.log('🔍 Timestamp:', response?.timestamp);
//         console.log('🔍 Raw Response JSON:', JSON.stringify(response, null, 2));
//         if (response?.active_streams && response.active_streams.length > 0) {
//           response.active_streams.forEach((stream, index) => {
//             console.log(`🔍 Stream ${index + 1}:`, {
//               session_id: stream.session_id,
//               teacher_id: stream.teacher_id,
//               viewer_count: stream.viewer_count,
//               quality: stream.quality
//             });
//           });
//         } else {
//           console.log('❌ No active streams found in response');
//         }
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => {
//         console.error('❌ Center Live Viewer API Error:', {
//           originalStatus,
//           status,
//           data,
//           url: '/active-streams'
//         });
//         return {
//           status: originalStatus ?? status,
//           data
//         };
//       },
//       providesTags: ['CenterLiveViewer'],
//       keepUnusedDataFor: 0
//     }),
//     joinLiveStream: builder.mutation({
//       query: (sessionData) => {
//         const userId = sessionStorage.getItem('userId');
//         const userName = sessionStorage.getItem('userName') || sessionStorage.getItem('username') || 'Unknown User';
//         const userRole = sessionStorage.getItem('userRole') || sessionStorage.getItem('role') || 'faculty';
//         const requestBody = {
//           session_id: sessionData.session_id,
//           user_id: userId,
//           user_name: userName,
//           user_role: userRole,
//           teacher_id: sessionData.teacher_id
//         };
//         console.log('Join Stream Request Body:', requestBody);
//         return {
//           url: '/api/livekit/join',
//           method: 'POST',
//           body: requestBody,
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Join Stream Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       invalidatesTags: ['CenterLiveViewer']
//     }),
//     startTranslationSession: builder.mutation({
//       query: (translationData) => {
//         console.log('Start Translation Request:', translationData);
//         return {
//           url: '/api/translate/start-session',
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json'
//           },
//           body: translationData,
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Start Translation Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       })
//     }),
//     stopTranslationSession: builder.mutation({
//       query: (sessionData) => {
//         console.log('Stop Translation Request:', sessionData);
//         return {
//           url: '/api/translate/stop-session',
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json'
//           },
//           body: sessionData,
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Stop Translation Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       })
//     }),
//     translateText: builder.mutation({
//       query: (translationData) => {
//         console.log('Translate Text Request:', translationData);
//         return {
//           url: '/api/translate/text',
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json'
//           },
//           body: {
//             session_id: translationData.session_id || 'viewer-session',
//             text: translationData.text,
//             source_language: translationData.source_language,
//             target_language: translationData.target_language
//           },
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Translate Text Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       })
//     }),
//     sendChatMessage: builder.mutation({
//       query: (messageData) => {
//         console.log('Send Chat Message Request:', messageData);
//         return {
//           url: '/api/chat/send',
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json',
//             Authorization: `Bearer ${sessionStorage.getItem('token')}`
//           },
//           body: messageData,
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Send Chat Message Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       })
//     }),
//     getChatHistory: builder.query({
//       query: (sessionId) => {
//         console.log('Get Chat History Request for session:', sessionId);
//         return {
//           url: `/api/chat/history/${sessionId}`,
//           method: 'GET',
//           headers: {
//             'Content-Type': 'application/json',
//             Authorization: `Bearer ${sessionStorage.getItem('token')}`
//           },
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Get Chat History Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: (result, error, sessionId) => [{ type: 'ChatHistory', id: sessionId }]
//     }),
//     recordLivestreamFeedback: builder.mutation({
//       query: (data) => {
//         console.log('Record Livestream Feedback Request:', data);
//         return {
//           url: `/api/feedback_livestream?session_id=${data.session_id}&event_id=${data.event_id}&center_code=${data.center_code}`,
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json'
//           },
//           body: { frames: data.frames },
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Record Livestream Feedback Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       })
//     }),
//     startQuiz: builder.mutation({
//       query: (body) => {
//         console.log('Start Quiz Request:', body);
//         // Add center_code to headers
//         const headers = {
//           'Content-Type': 'application/json'
//         };
        
//         // Get center code from session storage
//         const centerCode = sessionStorage.getItem('centercode');
//         if (centerCode) {
//           headers['X-Center-Code'] = centerCode;
//         }
        
//         return {
//           url: '/api/content/start_or_join_quiz',
//           method: 'POST',
//           headers: headers,
//           body: body,
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Start Quiz Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       invalidatesTags: ['LiveQuiz']
//     }),
//     nextQuestion: builder.mutation({
//       query: (body) => {
//         console.log('Next Question Request:', body);
//         // Add center_code to headers
//         const headers = {
//           'Content-Type': 'application/json'
//         };
        
//         // Get center code from session storage
//         const centerCode = sessionStorage.getItem('centercode');
//         if (centerCode) {
//           headers['X-Center-Code'] = centerCode;
//         }
        
//         return {
//           url: '/api/content/next_question',
//           method: 'POST',
//           headers: headers,
//           body: body,
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Next Question Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       invalidatesTags: ['LiveQuiz']
//     }),
//     finalizeSession: builder.mutation({
//       query: (body) => {
//         console.log('Finalize Session Request:', body);
//         return {
//           url: '/api/content/finalize_session',
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json'
//           },
//           body: body,
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Finalize Session Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       invalidatesTags: ['LiveQuiz']
//     }),
//     getEngagementDashboard: builder.query({
//       query: ({ sessionId, centerCode }) => {
//         console.log('Get Engagement Dashboard Request:', { sessionId, centerCode });
//         return {
//           url: `/api/content/engagement_dashboard?session_id=${sessionId}&center_code=${centerCode}`,
//           method: 'GET',
//           responseHandler: async (res) => res.json()
//         };
//       },
//       transformResponse: (response) => {
//         console.log('Get Engagement Dashboard Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['LiveQuiz']
//     })
//   })
// });
// export const {
//   useLazyGetCenterLiveViewerQuery,
//   useJoinLiveStreamMutation,
//   useStartTranslationSessionMutation,
//   useStopTranslationSessionMutation,
//   useTranslateTextMutation,
//   useSendChatMessageMutation,
//   useLazyGetChatHistoryQuery,
//   useRecordLivestreamFeedbackMutation,
//   useStartQuizMutation,
//   useNextQuestionMutation,
//   useFinalizeSessionMutation,
//   useGetEngagementDashboardQuery
// } = centerTraineeLiveSlice;

///working live code above 

import { CenterliveViewerApi } from '../../../../redux/api/api';
const initialState = {
  centerLiveViewerData: null
};
export const centerTraineeLiveSlice = CenterliveViewerApi.injectEndpoints({
  endpoints: (builder) => ({
    getCenterLiveViewer: builder.query({
      query: () => {
        const timestamp = Date.now();
        console.log('🔗 RTK Query making request at:', new Date(timestamp).toISOString());
        return {
          url: `/active-streams?rtk=true&t=${timestamp}`,
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Accept': 'application/json'
          },
          responseHandler: async (res) => {
            console.log('🔗 RTK Query response status:', res.status);
            console.log('🔗 RTK Query response headers:', res.headers);
            const data = await res.json();
            console.log('🔗 RTK Query raw response data:', data);
            return data;
          }
        };
      },
      transformResponse: (response) => {
        console.log('🔍 Center Live Viewer API Response:', response);
        console.log('🔍 Response Type:', typeof response);
        console.log('🔍 Response Keys:', Object.keys(response || {}));
        console.log('🔍 Active Streams Array:', response?.active_streams);
        console.log('🔍 Active Streams Type:', typeof response?.active_streams);
        console.log('🔍 Active Streams Length:', response?.active_streams?.length);
        console.log('🔍 Success Status:', response?.success);
        console.log('🔍 Timestamp:', response?.timestamp);
        console.log('🔍 Raw Response JSON:', JSON.stringify(response, null, 2));
        if (response?.active_streams && response.active_streams.length > 0) {
          response.active_streams.forEach((stream, index) => {
            console.log(`🔍 Stream ${index + 1}:`, {
              session_id: stream.session_id,
              teacher_id: stream.teacher_id,
              viewer_count: stream.viewer_count,
              quality: stream.quality
            });
          });
        } else {
          console.log('❌ No active streams found in response');
        }
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => {
        console.error('❌ Center Live Viewer API Error:', {
          originalStatus,
          status,
          data,
          url: '/active-streams'
        });
        return {
          status: originalStatus ?? status,
          data
        };
      },
      providesTags: ['CenterLiveViewer'],
      keepUnusedDataFor: 0
    }),
    joinLiveStream: builder.mutation({
      query: (sessionData) => {
        const userId = sessionStorage.getItem('userId');
        const userName = sessionStorage.getItem('userName') || sessionStorage.getItem('username') || 'Unknown User';
        const userRole = sessionStorage.getItem('userRole') || sessionStorage.getItem('role') || 'faculty';
        const requestBody = {
          session_id: sessionData.session_id,
          user_id: userId,
          user_name: userName,
          user_role: userRole,
          teacher_id: sessionData.teacher_id
        };
        console.log('Join Stream Request Body:', requestBody);
        return {
          url: '/api/livekit/join',
          method: 'POST',
          body: requestBody,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Join Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['CenterLiveViewer']
    }),
    startTranslationSession: builder.mutation({
      query: (translationData) => {
        console.log('Start Translation Request:', translationData);
        return {
          url: '/api/translate/start-session',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: translationData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Start Translation Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    stopTranslationSession: builder.mutation({
      query: (sessionData) => {
        console.log('Stop Translation Request:', sessionData);
        return {
          url: '/api/translate/stop-session',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: sessionData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Stop Translation Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    translateText: builder.mutation({
      query: (translationData) => {
        console.log('Translate Text Request:', translationData);
        return {
          url: '/api/translate/text',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: {
            session_id: translationData.session_id || 'viewer-session',
            text: translationData.text,
            source_language: translationData.source_language,
            target_language: translationData.target_language
          },
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Translate Text Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    sendChatMessage: builder.mutation({
      query: (messageData) => {
        console.log('Send Chat Message Request:', messageData);
        return {
          url: '/api/chat/send',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${sessionStorage.getItem('token')}`
          },
          body: messageData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Send Chat Message Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    getChatHistory: builder.query({
      query: (sessionId) => {
        console.log('Get Chat History Request for session:', sessionId);
        return {
          url: `/api/chat/history/${sessionId}`,
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${sessionStorage.getItem('token')}`
          },
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Get Chat History Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: (result, error, sessionId) => [{ type: 'ChatHistory', id: sessionId }]
    }),
    recordLivestreamFeedback: builder.mutation({
      query: (data) => {
        console.log('Record Livestream Feedback Request:', data);
        return {
          url: `/api/feedback_livestream?session_id=${data.session_id}&event_id=${data.event_id}&center_code=${data.center_code}`,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: { frames: data.frames },
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Record Livestream Feedback Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    startQuiz: builder.mutation({
      query: (body) => {
        console.log('Start Quiz Request:', body);
        // Add center_code to headers
        const headers = {
          'Content-Type': 'application/json'
        };
        
        // Get center code from session storage
        const centerCode = sessionStorage.getItem('centercode');
        if (centerCode) {
          headers['X-Center-Code'] = centerCode;
        }
        
        return {
          url: '/api/content/start_or_join_quiz',
          method: 'POST',
          headers: headers,
          body: body,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Start Quiz Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['LiveQuiz']
    }),
    nextQuestion: builder.mutation({
      query: (body) => {
        console.log('Next Question Request:', body);
        // Add center_code to headers
        const headers = {
          'Content-Type': 'application/json'
        };
        
        // Get center code from session storage
        const centerCode = sessionStorage.getItem('centercode');
        if (centerCode) {
          headers['X-Center-Code'] = centerCode;
        }
        
        return {
          url: '/api/content/next_question',
          method: 'POST',
          headers: headers,
          body: body,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Next Question Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['LiveQuiz']
    }),
    finalizeSession: builder.mutation({
      query: (body) => {
        console.log('Finalize Session Request:', body);
        return {
          url: '/api/content/finalize_session',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: body,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Finalize Session Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['LiveQuiz']
    }),
    getEngagementDashboard: builder.query({
      query: ({ sessionId, centerCode }) => {
        console.log('Get Engagement Dashboard Request:', { sessionId, centerCode });
        return {
          url: `/api/content/engagement_dashboard?session_id=${sessionId}&center_code=${centerCode}`,
          method: 'GET',
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Get Engagement Dashboard Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveQuiz']
    })
  })
});
export const {
  useLazyGetCenterLiveViewerQuery,
  useJoinLiveStreamMutation,
  useStartTranslationSessionMutation,
  useStopTranslationSessionMutation,
  useTranslateTextMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
  useRecordLivestreamFeedbackMutation,
  useStartQuizMutation,
  useNextQuestionMutation,
  useFinalizeSessionMutation,
  useGetEngagementDashboardQuery
} = centerTraineeLiveSlice;