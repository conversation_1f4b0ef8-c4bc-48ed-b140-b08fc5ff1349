# Build stage
FROM node:18 AS builder
WORKDIR /app

# Copy dependencies
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install --force

# Copy env and source
COPY . .
COPY .env.testing .env

# Build for testing
RUN npm run build

# Serve stage
FROM node:alpine
WORKDIR /app

RUN npm install -g serve

COPY --from=builder /app/dist ./dist

EXPOSE 5173
CMD ["serve", "-s", "dist", "-l", "5173"]
