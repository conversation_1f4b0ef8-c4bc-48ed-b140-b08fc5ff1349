import { createSlice } from '@reduxjs/toolkit';
import { processSelectorApi } from '../../../../redux/api/api';

const initialState = {
  courseData: null,
  subjectData: null,
  topicsData: null,
  subTopicData: null,
  allTopicsData: null,
  allSubTopicsData: null,
  allContentMappingData: null
};

export const processSelectorApiSlice = processSelectorApi.injectEndpoints({
  endpoints: (builder) => ({
    getCourseService: builder.query({
      query: () => {
        return `/get-coures`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    getSubjectService: builder.query({
      query: () => {
        return `/subject`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    getTopicByIdsService: builder.query({
      query: ({ course_id, subject_id }) => {
        return `/topic/${course_id}/${subject_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    getSubTopicByIdsService: builder.query({
      query: ({ course_id, subject_id, topic_id }) => {
        return `/subtopic/${course_id}/${subject_id}/${topic_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    getAllTopicsService: builder.query({
      query: () => {
        return `/topic`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    getAllSubTopicsService: builder.query({
      query: () => {
        return `/subtopic`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    getAllContentMappingService: builder.query({
      query: () => {
        return `/process-selector`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    createTopicService: builder.mutation({
      query: (body) => ({
        url: '/topic',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    createSubTopicService: builder.mutation({
      query: (body) => ({
        url: '/subtopic',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    createContentService: builder.mutation({
      query: (body) => ({
        url: '/process-selector',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ProcessSelector']
    }),
    storeContentInS3Service: builder.mutation({
      query: (formData) => ({
        url: '/s3',
        method: 'POST',
        body: formData, // Directly use FormData from component
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ProcessSelector']
    }),
    updateTopicService: builder.mutation({
      query: (body) => ({
        url: '/topic',
        method: 'PUT',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ProcessSelector']
    }),
    updateSubTopicService: builder.mutation({
      query: (body) => ({
        url: '/subtopic',
        method: 'PUT',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ProcessSelector']
    }),
    updateContentService: builder.mutation({
      query: (body) => ({
        url: '/process-selector',
        method: 'PUT',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ProcessSelector']
    }),
    deleteTopicService: builder.mutation({
      query: (id) => ({
        url: `/topic/${id}`,
        method: 'DELETE',
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ProcessSelector']
    }),
    deleteSubTopicService: builder.mutation({
      query: (id) => ({
        url: `/subtopic/${id}`,
        method: 'DELETE',
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ProcessSelector']
    }),
    deleteContentService: builder.mutation({
      query: (id) => ({
        url: `/process-selector/${id}`,
        method: 'DELETE',
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ProcessSelector']
    })
  })
});

const processSelectorSlice = createSlice({
  name: 'processSelector',
  initialState,
  reducers: {
    setCouresData(state, action) {
      state.courseData = action.payload;
    },
    setSubjectData(state, action) {
      state.subjectData = action.payload;
    },
    setTopicData(state, action) {
      state.topicsData = action.payload;
    },
    setSubTopicData(state, action) {
      state.subTopicData = action.payload;
    },
    setAllTopicsData(state, action) {
      state.allTopicsData = action.payload;
    },
    setAllSubTopicsData(state, action) {
      state.allSubTopicsData = action.payload;
    },
    setAllContentMappingData(state, action) {
      state.allContentMappingData = action.payload;
    }
  }
});

export const {
  useLazyGetCourseServiceQuery,
  useLazyGetSubjectServiceQuery,
  useLazyGetTopicByIdsServiceQuery,
  useLazyGetSubTopicByIdsServiceQuery,
  useCreateTopicServiceMutation,
  useCreateSubTopicServiceMutation,
  useCreateContentServiceMutation,
  useStoreContentInS3ServiceMutation,
  useLazyGetAllTopicsServiceQuery,
  useLazyGetAllSubTopicsServiceQuery,
  useLazyGetAllContentMappingServiceQuery,
  useUpdateTopicServiceMutation,
  useDeleteTopicServiceMutation,
  useUpdateSubTopicServiceMutation,
  useDeleteSubTopicServiceMutation,
  useUpdateContentServiceMutation,
  useDeleteContentServiceMutation
} = processSelectorApiSlice;
export const {
  setCouresData,
  setSubjectData,
  setTopicData,
  setSubTopicData,
  setAllTopicsData,
  setAllSubTopicsData,
  setAllContentMappingData
} = processSelectorSlice.actions;
export const selectProcessSelectorData = (state) => state.processSelector.courseData;
export default processSelectorSlice.reducer;
