import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AnimatePresence, motion } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

import Toastify from '../../../../../components/PopUp/Toastify';
import {
  clearOcrData,
  setOcrData,
  useLazyGetStudentOcrDataQuery
} from '../parentAnalyticsDashboard.slice';

// --- Reusable UI Components ---

const Icon = ({ path, className = 'w-6 h-6' }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d={path} />
  </svg>
);

const TestListItem = ({ test, isSelected, onClick }) => {
  const trend = test.trend;
  return (
    <motion.div
      layout="position"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      onClick={onClick}
      className={`p-4 rounded-xl border-l-4 transition-all duration-300 cursor-pointer ${isSelected ? 'bg-slate-700/60 border-indigo-500' : 'bg-slate-800/60 border-slate-700 hover:bg-slate-700/40'}`}>
      <div className="flex justify-between items-center">
        <div>
          <h4 className="font-bold text-white">{test.subject}</h4>
          <p className="text-xs text-slate-400">{new Date(test.date).toLocaleDateString()}</p>
        </div>
        <div className="flex items-center gap-2">
          {trend === 'up' && (
            <Icon path="m4.5 15.75 7.5-7.5 7.5 7.5" className="w-4 h-4 text-emerald-400" />
          )}
          {trend === 'down' && (
            <Icon path="m19.5 8.25-7.5 7.5-7.5-7.5" className="w-4 h-4 text-rose-400" />
          )}
          <div className="text-lg font-bold text-slate-200">{test.score}%</div>
        </div>
      </div>
    </motion.div>
  );
};

const QuestionModal = ({ question, onClose }) => {
  if (!question) return null;
  const { is_correct, feedback, question_number, error_type } = question;
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"
      onClick={onClose}>
      <motion.div
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        className="relative w-full max-w-2xl bg-slate-800 border border-slate-700 rounded-2xl shadow-2xl p-6 space-y-4"
        onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-xl font-bold text-white">Question {question_number} Analysis</h3>
            <span
              className={`px-2 py-0.5 mt-1 inline-block text-xs font-semibold rounded-full ${is_correct ? 'bg-emerald-500/20 text-emerald-300' : 'bg-rose-500/20 text-rose-300'}`}>
              {is_correct ? 'Correct' : 'Incorrect'}
            </span>
            <span className="ml-2 px-2 py-0.5 mt-1 inline-block text-xs font-semibold rounded-full bg-slate-700 text-slate-300 capitalize">
              {error_type.replace(/_/g, ' ')}
            </span>
          </div>
          <button onClick={onClose} className="text-slate-400 hover:text-white">
            &times;
          </button>
        </div>
        <div className="space-y-3 text-sm">
          <div>
            <p className="font-semibold text-slate-400 mb-1">Core Concept Tested</p>
            <p className="text-slate-200">{feedback.core_concept_tested}</p>
          </div>
          <div className="p-3 bg-slate-900/50 rounded-lg">
            <p className="font-semibold text-rose-400 mb-1">AI Error Analysis</p>
            <p className="text-slate-300">{feedback.error_analysis}</p>
          </div>
          <div className="p-3 bg-slate-900/50 rounded-lg">
            <p className="font-semibold text-sky-400 mb-1">AI Improvement Suggestion</p>
            <p className="text-slate-300">{feedback.improvement_suggestion}</p>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

const TabButton = ({ label, isActive, onClick }) => (
  <button
    onClick={onClick}
    className="relative px-4 py-2 text-sm font-semibold text-slate-300 transition focus:outline-none">
    {isActive && (
      <motion.div
        layoutId="tab-underline"
        className="absolute bottom-0 left-0 right-0 h-0.5 bg-indigo-500"
      />
    )}
    <span className={isActive ? 'text-white' : 'hover:text-white'}>{label}</span>
  </button>
);

// --- Main Detail View and Tabs ---

const ReportDetailView = ({ test, performanceTimelineData }) => {
  const [activeTab, setActiveTab] = useState('Overview');
  const [selectedQuestion, setSelectedQuestion] = useState(null);

  const highchartsDarkOptions = {
    chart: { backgroundColor: 'transparent' },
    title: { style: { color: '#E0E0E3' } },
    legend: { itemStyle: { color: '#E0E0E3' } },
    credits: { enabled: false },
    xAxis: { gridLineColor: '#707073', labels: { style: { color: '#E0E0E3' } } },
    yAxis: { gridLineColor: '#707073', labels: { style: { color: '#E0E0E3' } } }
  };

  const performanceChartOptions = useMemo(
    () => ({
      ...highchartsDarkOptions,
      chart: { ...highchartsDarkOptions.chart, type: 'area' },
      title: { text: 'Score Timeline', align: 'left' },
      xAxis: { type: 'datetime' },
      yAxis: { title: { text: null }, min: 0, max: 100, labels: { format: '{value}%' } },
      tooltip: { xDateFormat: '%b %d, %Y', pointFormat: 'Score: <b>{point.y}%</b>' },
      plotOptions: {
        area: {
          marker: { radius: 2 },
          lineWidth: 1,
          fillColor: {
            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
            stops: [
              [0, 'rgba(99, 102, 241, 0.5)'],
              [1, 'rgba(99, 102, 241, 0)']
            ]
          },
          lineColor: '#6366f1'
        }
      },
      series: [
        {
          name: 'Test Score',
          showInLegend: false,
          data: performanceTimelineData.map((d) => ({
            ...d,
            marker:
              d.id === test.id
                ? { enabled: true, radius: 5, fillColor: '#f43f5e', symbol: 'circle' }
                : { enabled: false }
          }))
        }
      ]
    }),
    [test, performanceTimelineData]
  );

  const { performance_analysis: analysis } = test;
  const questions = analysis.question_by_question_breakdown;
  const correctCount = questions.filter((q) => q.is_correct).length;
  const incorrectCount = questions.length - correctCount;

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 bg-slate-800/50 rounded-t-xl border-b border-slate-700">
        <h2 className="text-2xl font-bold text-white">
          {test.subject} - {test.exam} Report
        </h2>
        <p className="text-sm text-slate-400">Analyzed on {new Date(test.date).toLocaleString()}</p>
        <div className="mt-4 p-4 bg-indigo-900/30 border border-indigo-500/30 rounded-lg text-indigo-200 text-sm flex items-start gap-3">
          <Icon
            path="m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"
            className="w-5 h-5 flex-shrink-0 mt-0.5 text-indigo-400"
          />
          <p>
            <strong className="text-indigo-300">AI Key Insight:</strong>{' '}
            {analysis.overall_feedback_and_strategy.performance_snapshot}
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-slate-700 px-4">
        <TabButton
          label="Overview"
          isActive={activeTab === 'Overview'}
          onClick={() => setActiveTab('Overview')}
        />
        <TabButton
          label="Question Analysis"
          isActive={activeTab === 'Question Analysis'}
          onClick={() => setActiveTab('Question Analysis')}
        />
        <TabButton
          label="Conceptual Skills"
          isActive={activeTab === 'Conceptual Skills'}
          onClick={() => setActiveTab('Conceptual Skills')}
        />
      </div>

      {/* Tab Content */}
      <div className="flex-grow p-6 overflow-y-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}>
            {activeTab === 'Overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
                <div className="lg:col-span-3 space-y-6">
                  <HighchartsReact highcharts={Highcharts} options={performanceChartOptions} />
                  <div>
                    <h4 className="font-bold text-white mb-2">AI Recommended Action Plan</h4>
                    <div className="space-y-2">
                      {analysis.overall_feedback_and_strategy.strategic_action_plan.map((plan) => (
                        <div
                          key={plan.focus}
                          className="p-3 bg-slate-900/50 rounded-lg flex items-start gap-3 text-sm">
                          <Icon
                            path="M9 12.75 11.25 15 15 9.75"
                            className="w-5 h-5 text-sky-400 mt-0.5 flex-shrink-0"
                          />
                          <div>
                            <strong className="text-sky-300">{plan.focus}:</strong>{' '}
                            <span className="text-slate-300">{plan.action}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="lg:col-span-2 space-y-4">
                  <div className="p-4 bg-slate-900/50 rounded-lg text-center">
                    <div className="text-4xl font-bold text-emerald-400">{correctCount}</div>
                    <div className="text-sm text-slate-400">Correct Answers</div>
                  </div>
                  <div className="p-4 bg-slate-900/50 rounded-lg text-center">
                    <div className="text-4xl font-bold text-rose-400">{incorrectCount}</div>
                    <div className="text-sm text-slate-400">Incorrect Answers</div>
                  </div>
                  <div className="p-4 bg-slate-900/50 rounded-lg">
                    <h4 className="font-bold text-amber-400 mb-2">Most Common Error Type</h4>
                    <p className="text-lg font-semibold text-white capitalize">
                      {analysis.overall_feedback_and_strategy.primary_area_for_improvement.replace(
                        /_/g,
                        ' '
                      )}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {activeTab === 'Question Analysis' && (
              <div>
                <h3 className="font-bold text-white mb-4">Question Performance Heatmap</h3>
                <p className="text-sm text-slate-400 mb-4">
                  Click on any question to see detailed AI feedback.
                </p>
                <div className="grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 gap-2">
                  {questions.map((q) => {
                    const colorClass = q.is_correct
                      ? 'bg-emerald-500/80 hover:bg-emerald-500'
                      : q.error_type === 'CONCEPTUAL_MISTAKE'
                        ? 'bg-rose-500/80 hover:bg-rose-500'
                        : 'bg-amber-500/80 hover:bg-amber-500';
                    return (
                      <div
                        key={q.question_number}
                        onClick={() => setSelectedQuestion(q)}
                        className={`flex items-center justify-center h-12 w-12 rounded-lg cursor-pointer transition-all duration-200 text-white font-bold text-lg ${colorClass}`}>
                        {q.question_number}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            {activeTab === 'Conceptual Skills' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="font-bold text-emerald-400 flex items-center gap-2 mb-3">
                    <Icon
                      path="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      className="w-6 h-6"
                    />
                    Conceptual Strengths
                  </h3>
                  <div className="space-y-2">
                    {analysis.overall_feedback_and_strategy.conceptual_strengths.map((c) => (
                      <div
                        key={c}
                        className="p-2 bg-emerald-500/10 text-emerald-300 text-sm rounded-md">
                        {c}
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="font-bold text-rose-400 flex items-center gap-2 mb-3">
                    <Icon
                      path="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
                      className="w-6 h-6"
                    />
                    Areas for Improvement
                  </h3>
                  <div className="space-y-2">
                    {[
                      ...new Set(
                        questions
                          .filter((q) => !q.is_correct)
                          .map((q) => q.feedback.core_concept_tested)
                      )
                    ].map((c) => (
                      <div key={c} className="p-2 bg-rose-500/10 text-rose-300 text-sm rounded-md">
                        {c}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
      <AnimatePresence>
        {selectedQuestion && (
          <QuestionModal question={selectedQuestion} onClose={() => setSelectedQuestion(null)} />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// --- Main Parent Component ---
const ParentOcr = () => {
  const [getStudentOcr] = useLazyGetStudentOcrDataQuery();
  const [res, setRes] = useState(null);
  const dispatch = useDispatch();
  const ocrData = useSelector((state) => state.parentAnalyticsDashboard.studentOcrData);
  const [selectedTestId, setSelectedTestId] = useState(null);

  const processedData = useMemo(() => {
    if (!ocrData || !Array.isArray(ocrData)) return [];
    const sortedData = [...ocrData].sort((a, b) => new Date(a.date) - new Date(b.date));
    return sortedData.map((test, index) => {
      const breakdown = test.performance_analysis.question_by_question_breakdown || [];
      const totalQuestions = breakdown.length;
      const totalMarksAwarded = breakdown.reduce((sum, q) => sum + (q.marks_awarded || 0), 0);
      const totalPossibleMarks =
        breakdown.reduce((sum, q) => sum + (q.is_correct ? 2 : 0), 0) +
          breakdown.reduce((sum, q) => sum + (!q.is_correct ? 2 : 0), 0) || totalQuestions * 2;
      const score =
        totalPossibleMarks > 0 ? Math.round((totalMarksAwarded / totalPossibleMarks) * 100) : 0;

      let trend = 'same';
      if (index > 0) {
        const prevScore = sortedData[index - 1].score;
        if (score > prevScore) trend = 'up';
        if (score < prevScore) trend = 'down';
      }

      return { ...test, id: test.date + index, score, trend };
    });
  }, [ocrData]);

  const selectedTest = useMemo(
    () => processedData.find((t) => t.id === selectedTestId),
    [processedData, selectedTestId]
  );

  useEffect(() => {
    if (processedData.length > 0 && !selectedTestId) {
      setSelectedTestId(processedData[processedData.length - 1].id); // Select the most recent test by default
    }
  }, [processedData, selectedTestId]);

  useEffect(() => {
    handleGetStudentOcrApi();
  }, []);

  const handleGetStudentOcrApi = async () => {
    try {
      const ocr = await getStudentOcr({ studentId: sessionStorage.studentIdByParent }).unwrap();
      dispatch(setOcrData(ocr));
    } catch (error) {
      setRes(error);
      dispatch(clearOcrData());
    }
  };

  return (
    <div className="relative h-screen text-slate-100 p-4 md:p-6 isolate  overflow-hidden flex flex-col">
      <div className="absolute inset-x-0 top-0 -z-10 transform-gpu overflow-hidden blur-3xl">
        <div className="relative left-1/2 -z-10 aspect-[1155/678] w-[36rem] max-w-none -translate-x-1/2 rotate-[30deg] opacity-20" />
      </div>
      <Toastify res={res} resClear={() => setRes(null)} />

      <header className="flex-shrink-0 mb-4">
        <h1 className="text-3xl font-extrabold tracking-tight text-white">
          AI Performance Command Center
        </h1>
        <p className="text-sm text-slate-400">
          Student:{' '}
          <span className="font-semibold text-slate-300">
            {ocrData?.[0]?.student_name || '...'}
          </span>
        </p>
      </header>

      <div className="flex-grow grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6 min-h-0">
        <aside className="lg:col-span-1 xl:col-span-1 flex flex-col min-h-0">
          <h3 className="text-lg font-bold text-white mb-3 flex-shrink-0">Analyzed Reports</h3>
          <div className="flex-grow overflow-y-auto space-y-3 pr-2">
            <AnimatePresence>
              {processedData.map((test) => (
                <TestListItem
                  key={test.id}
                  test={test}
                  isSelected={selectedTestId === test.id}
                  onClick={() => setSelectedTestId(test.id)}
                />
              ))}
            </AnimatePresence>
          </div>
        </aside>

        <main className="lg:col-span-2 xl:col-span-3 bg-slate-800/40 rounded-xl flex flex-col min-h-0">
          <AnimatePresence mode="wait">
            {selectedTest ? (
              <ReportDetailView
                key={selectedTestId}
                test={selectedTest}
                performanceTimelineData={processedData.map((t) => ({
                  x: new Date(t.date).getTime(),
                  y: t.score,
                  id: t.id
                }))}
              />
            ) : (
              <div className="flex-grow flex items-center justify-center text-slate-500">
                Loading Report...
              </div>
            )}
          </AnimatePresence>
        </main>
      </div>
    </div>
  );
};

export default ParentOcr;
