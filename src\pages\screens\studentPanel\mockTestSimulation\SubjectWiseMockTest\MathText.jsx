import { MathJax } from 'better-react-mathjax';

export const mathJaxConfig = {
  loader: { load: ['[tex]/html'] },
  tex: {
    packages: { '[+]': ['html'] },
    inlineMath: [['$$', '$$']],
    displayMath: [['\\[', '\\]']],
    processEscapes: true,
    processEnvironments: true
  }
};

export const MathText = ({ children }) => {
  if (!children) return null;

  const text = children.toString();

  const parts = [];
  let currentIndex = 0;

  const mathRegex = /(\$\$([^$]+)\$\$|\$([^$]+)\$)/g;
  let match;

  while ((match = mathRegex.exec(text)) !== null) {
    if (match.index > currentIndex) {
      parts.push({
        type: 'text',
        content: text.substring(currentIndex, match.index)
      });
    }

    if (match[2]) {
      parts.push({
        type: 'display',
        content: `\\[${match[2].trim()}\\]`
      });
    } else if (match[3]) {
      parts.push({
        type: 'inline',
        content: `\$$${match[3].trim()}\$$`
      });
    }

    currentIndex = match.index + match[0].length;
  }

  if (currentIndex < text.length) {
    parts.push({
      type: 'text',
      content: text.substring(currentIndex)
    });
  }

  if (parts.length === 0) {
    return <span>{text}</span>;
  }

  return (
    <span>
      {parts.map((part, index) => {
        switch (part.type) {
          case 'display':
          case 'inline':
            return <MathJax key={index}>{part.content}</MathJax>;
          case 'text':
            return <span key={index}>{part.content}</span>;
          default:
            return <span key={index}>{part.content}</span>;
        }
      })}
    </span>
  );
};

export default MathText;
