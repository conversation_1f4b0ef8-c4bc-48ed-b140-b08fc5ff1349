import React, { useCallback, useEffect, useState, useMemo, useRef } from 'react';
import { setMenuData, useLazyGetRoleBasedMenuServiceQuery } from './navbar.slice';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router';
import Toastify from '../PopUp/Toastify';
import { FiMenu, FiX, FiChevronDown } from 'react-icons/fi';
import { menuHierarchy } from './menuHierarchy';

const useIsMobile = (breakpoint = 768) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < breakpoint);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoint]);

  return isMobile;
};

const SideBar = () => {
  const [getMenu] = useLazyGetRoleBasedMenuServiceQuery();
  const dispatch = useDispatch();
  const location = useLocation();
  const isMobile = useIsMobile();
  const role = sessionStorage.role;

  const flatMenus = useSelector((state) => state.menu.menuData);
  const [res, setRes] = useState(null);

  const [routeActiveItem, setRouteActiveItem] = useState(null);
  const [hoveredItem, setHoveredItem] = useState(null);
  const hoverTimeoutRef = useRef(null);

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [openMobileSubmenu, setOpenMobileSubmenu] = useState(null);

  const handleGetMenuApi = useCallback(async () => {
    try {
      const res = await getMenu(role).unwrap();
      dispatch(setMenuData(res));
    } catch (error) {
      setRes(error || { message: 'Something went wrong - Menus' });
    }
  }, [getMenu, dispatch, role]);

  useEffect(() => {
    if (!flatMenus?.length) {
      handleGetMenuApi();
    }
  }, [handleGetMenuApi, flatMenus?.length]);

  const structuredMenu = useMemo(() => {
    if (!flatMenus || !flatMenus?.length || !role) return [];

    const menuMap = new Map(flatMenus?.map((item) => [item.name.trim(), item]));

    return menuHierarchy
      ?.map((parent) => {
        if (!parent.children) {
          if (parent.roles && parent.roles.includes(role)) {
            const menuItem = menuMap.get(parent.name.trim());
            return { ...parent, href: menuItem ? menuItem.href : '/sasthra', submenu: null };
          }
          return null;
        }

        const accessibleChildren = parent.children
          .filter((child) => child.roles.includes(role))
          ?.map((child) => menuMap.get(child.name.trim()))
          .filter(Boolean);

        if (accessibleChildren?.length === 0) {
          return null;
        }

        return {
          ...parent,
          href: accessibleChildren[0].href,
          submenu: accessibleChildren
        };
      })
      .filter(Boolean);
  }, [flatMenus, role]);

  useEffect(() => {
    const currentPath = location.pathname;
    let parentToActivate = null;
    for (const parent of structuredMenu) {
      if (parent.submenu?.some((child) => currentPath.startsWith(child.href))) {
        parentToActivate = parent;
        break;
      } else if (currentPath === parent.href) {
        parentToActivate = parent;
        break;
      }
    }
    setRouteActiveItem(parentToActivate);
    setOpenMobileSubmenu(parentToActivate?.uuid || null);
  }, [location.pathname, structuredMenu]);

  useEffect(() => {
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  }, [location.pathname, isMobileMenuOpen]);

  const handleMouseEnter = (item) => {
    clearTimeout(hoverTimeoutRef.current);
    hoverTimeoutRef.current = setTimeout(() => {
      setHoveredItem(item);
    }, 100);
  };

  const handleMouseLeave = () => {
    clearTimeout(hoverTimeoutRef.current);
    setHoveredItem(null);
  };

  const handleMobileSubmenuToggle = (uuid) => {
    setOpenMobileSubmenu((prev) => (prev === uuid ? null : uuid));
  };

  const itemToShow = hoveredItem;

  const MenuContent = ({ isMobileView = false }) => (
    <div className="flex w-full flex-col items-center gap-2">
      {structuredMenu?.map((item) => {
        const isVisuallyActive =
          !isMobileView &&
          (itemToShow?.uuid === item.uuid || (!itemToShow && routeActiveItem?.uuid === item.uuid));

        if (isMobileView && item.submenu) {
          const isAccordionOpen = openMobileSubmenu === item.uuid;
          return (
            <div key={item.uuid} className="w-full">
              <button
                onClick={() => handleMobileSubmenuToggle(item.uuid)}
                className="w-full flex items-center justify-between text-left p-4 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200">
                <div className="flex items-center gap-4">
                  {item.icon}
                  <span className="font-medium">{item.name}</span>
                </div>
                <FiChevronDown
                  className={`transform transition-transform duration-300 ${isAccordionOpen ? 'rotate-180' : ''}`}
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isAccordionOpen ? 'max-h-96' : 'max-h-0'
                }`}>
                <ul className="pl-12 pt-2 pb-2 space-y-1">
                  {item.submenu?.map((subItem) => (
                    <li key={subItem.uuid}>
                      <Link
                        to={subItem.href}
                        className={`block p-2 rounded-md transition-colors ${
                          location.pathname === subItem.href
                            ? 'bg-purple-100 text-purple-700 font-medium'
                            : 'text-gray-600 hover:bg-gray-100'
                        }`}>
                        {subItem.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          );
        }

        const mobileClasses = `w-full flex items-center gap-4 p-4 rounded-lg transition-colors ${
          routeActiveItem?.uuid === item.uuid
            ? 'bg-purple-100 text-purple-700 font-medium'
            : 'text-gray-700 hover:bg-gray-200'
        }`;

        const desktopClasses = `flex flex-col items-center justify-center p-4 rounded-2xl transform transition-all duration-200 ease-in-out ${
          isVisuallyActive
            ? 'bg-white text-blue-700 scale-110 shadow-lg'
            : 'text-white hover:bg-white/20 hover:scale-110'
        }`;

        return (
          <Link
            to={item.href}
            key={item.uuid}
            onMouseEnter={!isMobileView ? () => handleMouseEnter(item) : undefined}
            className={isMobileView ? mobileClasses : desktopClasses}>
            {item.icon}
            {isMobileView && <span className="font-medium">{item.name}</span>}
          </Link>
        );
      })}
    </div>
  );

  const roleColorMap = {
    director: 'bg-director',
    student: 'bg-student',
    center_counselor: 'bg-counselor',
    kota_teacher: 'bg-teacher',
    faculty: 'bg-trainee',
    parent: 'bg-parents',
    mendor: 'bg-mentor'
  };
  const bgClass = roleColorMap[sessionStorage.role] || 'bg-gray-200';

  return (
    <>
      <Toastify res={res} resClear={() => setRes(null)} />
      <div className="fixed top-4 left-4 z-50 md:hidden">
        <button
          onClick={() => setIsMobileMenuOpen(true)}
          className="p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-md text-gray-800 transition-all duration-200 hover:bg-gray-100 active:scale-95"
          aria-label="Open menu">
          <FiMenu size={24} />
        </button>
      </div>
      <div
        className={`fixed inset-0 z-40 md:hidden transition-opacity duration-300 ease-in-out
                    ${isMobileMenuOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
        <div
          className="absolute inset-0 bg-black/60"
          onClick={() => setIsMobileMenuOpen(false)}></div>
        <div
          className={`relative w-80 max-w-[calc(100%-4rem)] h-full bg-gray-50 p-4 shadow-xl flex flex-col transition-transform duration-300 ease-in-out
                      ${isMobileMenuOpen ? 'transform-none' : '-translate-x-full'}`}>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-800">Menu</h2>
            <button onClick={() => setIsMobileMenuOpen(false)} aria-label="Close menu">
              <FiX size={24} />
            </button>
          </div>
          <div className="flex-grow overflow-y-auto">
            <MenuContent isMobileView={true} />
          </div>
        </div>
      </div>
      <div className="relative hidden h-screen font-sans md:flex" onMouseLeave={handleMouseLeave}>
        <nav className={`z-30 flex flex-col items-center justify-between p-2 ${bgClass}`}>
          <MenuContent isMobileView={false} />
          <div className="flex flex-col items-center gap-2">
            <button className="flex items-center justify-center p-3 rounded-full text-white hover:bg-white/20 transition-colors"></button>
          </div>
        </nav>
        <aside
          className={`absolute top-0 left-full h-full w-64 bg-white p-4 border-r border-gray-200 shadow-lg rounded-r-2xl z-20
                     transform transition-all duration-300 ease-in-out
                     ${
                       itemToShow?.submenu
                         ? 'opacity-100 translate-x-0'
                         : 'opacity-0 -translate-x-4 pointer-events-none'
                     }`}>
          {itemToShow?.submenu && (
            <div>
              <h2 className="text-lg font-semibold text-gray-800 px-3 py-2 mb-2">
                {itemToShow.name}
              </h2>
              <ul className="space-y-1">
                {itemToShow.submenu?.map((subItem) => (
                  <li key={subItem.uuid}>
                    <Link
                      to={subItem.href}
                      className={`block w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 ${
                        location.pathname === subItem.href
                          ? 'bg-purple-100 text-purple-700 font-semibold'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}>
                      {subItem.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </aside>
      </div>
    </>
  );
};

export default SideBar;
