import { FullLengthMockTestApi } from '../../../../../redux/api/api';

export const fullLengthMockTestSlice = FullLengthMockTestApi.injectEndpoints({
  endpoints: (builder) => ({
    getMockTestQuestions: builder.query({
      query: ({ examName }) => ({
        url: `/generate_mock?mock_type=complete&exam_name=${examName}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['FullLengthMockTest']
    }),
    submitTestResults: builder.mutation({
      query: (body) => ({  
        url: '/api/test_results',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['FullLengthMockTest']
    }),
    getSubjectMockTestQuestions: builder.query({
      query: ({ examName, subject }) => ({
        url: `/generate_mock?mock_type=subjectwise&exam_name=${examName}&subject=${subject}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['FullLengthMockTest']
    }),
    evaluateSubjectMockTest: builder.mutation({
      query: ({ examName, subject, user_id, responses }) => ({
        url: `/evaluate_subject_mock?exam_name=${encodeURIComponent(examName)}&subject=${encodeURIComponent(subject)}&user_id=${encodeURIComponent(user_id)}`,
        method: 'POST',
        body: responses,
        headers: {
          'Content-Type': 'application/json'
        }
      }),
      transformErrorResponse: (response) => response.data,
      invalidatesTags: ['FullLengthMockTest']
    })
  })
});

export const {
  useGetMockTestQuestionsQuery,
  useSubmitTestResultsMutation,
  useEvaluateSubjectMockTestMutation,
  useLazyGetSubjectMockTestQuestionsQuery
} = fullLengthMockTestSlice;
