import { faceRegisterApi } from '../../../../redux/api/api';

export const faceVerificationSlice = faceRegisterApi.injectEndpoints({
  endpoints: (builder) => ({
    verifyFace: builder.mutation({
      query: ({ userId, imageFile }) => {
        const formData = new FormData();
        formData.append('user_id', userId); // Matches backend parameter
        formData.append('image_file', imageFile, 'capture.jpg'); // Blob with filename

        return {
          url: '/verify-face',
          method: 'POST',
          body: formData,
          formData: true // Ensures correct Content-Type
        };
      },
      transformResponse: (response) => {
        console.log('Verify Face Response:', response);
        return {
          success: response.success,
          message: response.message,
          similarityScore: response.similarity_score || null
        };
      },
      transformErrorResponse: ({ status, data }) => ({
        status,
        message: data?.detail || 'Error verifying face'
      }),
      providesTags: ['FaceVerification']
    })
  })
});

export const { useVerifyFaceMutation } = faceVerificationSlice;
