import { cbtCourseBasedContentApi } from '../../../../redux/api/api';

export const cbtCourseBasedContentMappingApi = cbtCourseBasedContentApi.injectEndpoints({
  endpoints: (builder) => ({
    getCbtCourseBasedContentMapping: builder.query({
      query: ({ course, userId }) => ({
        url: `/content?course=${course}&userId=${userId}`, // Pass both course and userId
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Get CBT Course Based Content Mapping Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CbtCourseBasedContentMapping']
    })
  })
});

export const { useLazyGetCbtCourseBasedContentMappingQuery } = cbtCourseBasedContentMappingApi;
