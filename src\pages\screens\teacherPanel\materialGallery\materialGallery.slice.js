import { materialGalleryApi } from '../../../../redux/api/api';

export const materialGallerySlice = materialGalleryApi.injectEndpoints({
  endpoints: (builder) => ({
    uploadContent: builder.mutation({
      query: (body) => ({
        url: '/upload-community-gallery',
        method: 'POST',
        body: body // Expecting FormData with file, course, subject, unit, sub_unit, description, file_type
      }),
      transformResponse: (response) => ({
        success: true,
        data: response
      }),
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.detail || 'Error uploading content',
        details: error.data?.details || null
      }),
      providesTags: ['MaterialGallery']
    }),
    approveContent: builder.mutation({
      query: (body) => ({
        url: '/approve-content-community-gallery',
        method: 'POST',
        body: {
          content_id: body.content_id,
          action: body.action,
          rejection_reason: body.rejection_reason
        }
      }),
      transformResponse: (response) => ({
        success: true,
        data: response
      }),
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.detail || 'Error approving/rejecting content',
        details: error.data?.details || null
      }),
      providesTags: ['MaterialGallery']
    }),
    getApprovedContent: builder.query({
      query: () => ({
        url: '/get-approve-content-community-gallery',
        method: 'GET'
      }),
      transformResponse: (response) => ({
        success: true,
        data: response.contents || []
      }),
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.detail || 'Error fetching approved content',
        details: error.data?.details || null
      }),
      providesTags: ['MaterialGallery']
    }),
    getPendingContent: builder.query({
      query: () => ({
        url: '/content/pending-community-gallery',
        method: 'GET'
      }),
      transformResponse: (response) => ({
        success: true,
        data: response.contents || []
      }),
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.detail || 'Error fetching pending content',
        details: error.data?.details || null
      }),
      providesTags: ['MaterialGallery']
    }),
    getRejectedContent: builder.query({
      query: () => ({
        url: '/content/rejected-community-gallery',
        method: 'GET'
      }),
      transformResponse: (response) => ({
        success: true,
        data: response.contents || []
      }),
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.detail || 'Error fetching rejected content',
        details: error.data?.details || null
      }),
      providesTags: ['ContentGallery']
    }),
    getContentById: builder.query({
      query: (contentId) => ({
        url: `/content-community-gallery/${contentId}`,
        method: 'GET'
      }),
      transformResponse: (response) => ({
        success: true,
        data: response.content
      }),
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.detail || 'Error fetching content by ID',
        details: error.data?.details || null
      }),
      providesTags: ['MaterialGallery']
    })
  })
});

export const {
  useUploadContentMutation,
  useApproveContentMutation,
  useLazyGetApprovedContentQuery,
  useLazyGetPendingContentQuery,
  useLazyGetRejectedContentQuery,
  useLazyGetContentByIdQuery
} = materialGallerySlice;

export default materialGallerySlice;
