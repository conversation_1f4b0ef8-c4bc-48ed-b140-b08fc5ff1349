import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router';
import { motion, AnimatePresence } from 'framer-motion';
import OnBroadingAssessment from './OnBroadingAssessment';
import { useCheckOnBroadingAssessmentStatusMutation } from './onBroadingAssessment.slice';
import { useDispatch } from 'react-redux';
import { onBroadingAssessmentApiSlice } from './onBroadingAssessment.slice';

const AssessmentGuard = ({ children }) => {
  const [isAssessmentCompleted, setIsAssessmentCompleted] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [checkAssessmentStatus, { isLoading: isMutationLoading, error }] =
    useCheckOnBroadingAssessmentStatusMutation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  const fetchAssessmentStatus = useCallback(async () => {
    try {
      const studentId = sessionStorage.getItem('userId'); // Match OnBroadingAssessment
      if (!studentId) {
        console.warn('No userId found in sessionStorage');
        setIsLoading(false);
        setIsAssessmentCompleted(false);
        return;
      }

      const response = await checkAssessmentStatus({
        student_id: studentId
      }).unwrap();

      console.log('Assessment status response:', response); // Debug log
      const { assessment_completed } = response;
      setIsAssessmentCompleted(assessment_completed);
      setIsLoading(false);

      // Navigate only if assessment is completed and not on the target route
      if (assessment_completed && location.pathname !== '/sasthra/student/create-your-own-test') {
        navigate('/sasthra/student/create-your-own-test', { replace: true });
      }
    } catch (err) {
      console.error('Error checking assessment status:', err);
      setIsLoading(false);
      setIsAssessmentCompleted(false);
    }
  }, [checkAssessmentStatus, navigate, location.pathname]);

  useEffect(() => {
    fetchAssessmentStatus();
  }, [fetchAssessmentStatus]);

  const handleAssessmentComplete = useCallback(() => {
    try {
      console.log('Invalidating OnBroadingAssessment tag'); // Debug log
      dispatch(onBroadingAssessmentApiSlice.util.invalidateTags(['OnBroadingAssessment']));
      fetchAssessmentStatus(); // Re-fetch status immediately
    } catch (err) {
      console.error('Error handling assessment completion:', err);
    }
  }, [dispatch, fetchAssessmentStatus]);

  // Loading animation variants
  const loadingVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, ease: 'backOut' }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      transition: { duration: 0.3, ease: 'backIn' }
    }
  };

  const dotVariants = {
    initial: { y: 0 },
    animate: {
      y: [0, -15, 0],
      transition: {
        duration: 1.2,
        repeat: Infinity,
        repeatType: 'loop',
        ease: 'easeInOut'
      }
    }
  };

  if (isLoading || isMutationLoading) {
    return (
      <motion.div
        className="flex items-center justify-center h-screen bg-gradient-to-br from-indigo-50 to-blue-100"
        initial="initial"
        animate="animate"
        exit="exit"
        variants={loadingVariants}>
        <div className="text-center space-y-6">
          <motion.div className="flex justify-center space-x-3" initial="initial" animate="animate">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="w-6 h-6 bg-blue-500 rounded-full"
                variants={dotVariants}
                style={{ backgroundColor: `hsl(${210 + i * 30}, 80%, 60%)` }}
              />
            ))}
          </motion.div>
          <motion.h2
            className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}>
            Preparing Your Learning Journey
          </motion.h2>
          <motion.p
            className="text-gray-600 max-w-md mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}>
            We're getting everything ready for you...
          </motion.p>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="flex items-center justify-center h-screen bg-gradient-to-br from-red-50 to-pink-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}>
        <motion.div
          className="p-8 bg-white rounded-2xl shadow-xl max-w-md mx-4 text-center border-l-8 border-red-500"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ type: 'spring', damping: 10, stiffness: 100 }}>
          <motion.div
            className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6"
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}>
            <svg
              className="w-10 h-10 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </motion.div>
          <h2 className="text-2xl font-bold text-gray-800 mb-3">Connection Error</h2>
          <p className="text-gray-600 mb-6">
            We couldn't verify your assessment status. Please check your connection and try again.
          </p>
          <motion.button
            className="px-6 py-2 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 transition-colors flex items-center mx-auto"
            whileHover={{ scale: 1.05, boxShadow: '0 10px 20px -10px rgba(239, 68, 68, 0.6)' }}
            whileTap={{ scale: 0.95 }}
            onClick={() => window.location.reload()}>
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Retry
          </motion.button>
        </motion.div>
      </motion.div>
    );
  }

  if (isAssessmentCompleted === false) {
    return <OnBroadingAssessment isSkip={handleAssessmentComplete} />;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}>
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

export default AssessmentGuard;
