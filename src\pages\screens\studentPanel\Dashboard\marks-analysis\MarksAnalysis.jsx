import React, { useState, useEffect, useRef } from 'react';
import { Send, Volume2, Play, Pause } from "lucide-react";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import { useGetTutorDashboardQuery, useAskQueryMutation } from './markAnalysis.slice.js';

const MarkAnalysis = () => {
  const [strengths, setStrengths] = useState([
    "Strong analytical thinking",
    "Excellent problem-solving skills",
    "Consistent performance across tasks"
  ]);
  const [weaknesses, setWeaknesses] = useState([
    "Needs improvement in time management",
    "Struggles with complex problem analysis",
    "Inconsistent attention to detail"
  ]);
  const [messages, setMessages] = useState([]);
  const [userInput, setUserInput] = useState('');
  const [isReportVisible, setIsReportVisible] = useState(false);
  const [reportData, setReportData] = useState(null);
  const chatMessagesRef = useRef(null);
  const studentId = sessionStorage.getItem('userId');
  const { transcript, listening, resetTranscript } = useSpeechRecognition();
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef(null);

  const { data, error, isLoading } = useGetTutorDashboardQuery(studentId);
  const [askQuery] = useAskQueryMutation();

  useEffect(() => {
    if (data) {
      // Commenting out dynamic fetching to keep static data
      // setStrengths(data.knowledge_snapshot.strengths || []);
      // setWeaknesses(data.knowledge_snapshot.weaknesses || []);
      setMessages(prev => {
        const newMessages = [{ content: data.welcome_message, type: 'bot-message' }];
        console.log("Initial Messages:", newMessages);
        return newMessages;
      });
    }
    if (error) {
      console.error("Initialization Error:", error);
      setMessages(prev => {
        const newMessages = [{ content: "Sorry, I couldn't load your dashboard summary right now.", type: 'bot-message' }];
        console.log("Error Messages:", newMessages);
        return newMessages;
      });
    }
  }, [data, error]);

  useEffect(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    if (transcript) {
      setUserInput(transcript);
    }
  }, [transcript]);

  const constructReportText = (reportData) => {
    let text = reportData.overall_summary || '';
    if (reportData.performance_trends?.length > 0) {
      text += ' Performance Trends: ';
      reportData.performance_trends.forEach(trend => {
        text += `${trend.subject}: ${trend.trend}. Latest Score: ${trend.latest_score_percentage}%. `;
      });
    }
    if (reportData.recommendations?.primary_focus_subject) {
      text += ` Recommendations: Primary Focus: ${reportData.recommendations.primary_focus_subject}. Suggested Actions: `;
      reportData.recommendations.suggested_actions.forEach(action => {
        text += `${action}. `;
      });
    }
    return text;
  };

  const handleSendMessage = async () => {
    if (!userInput.trim()) return;

    setMessages(prev => {
      const newMessages = [...prev, { content: userInput, type: 'user-message' }];
      console.log("Messages after adding user input:", newMessages);
      return newMessages;
    });
    setUserInput('');
    resetTranscript();
    setMessages(prev => {
      const newMessages = [...prev, { content: 'Analyzing...', type: 'bot-message' }];
      console.log("Messages after adding Analyzing:", newMessages);
      return newMessages;
    });

    try {
      const result = await askQuery({ studentId, query: userInput }).unwrap();
      const responseText = result.answer.overall_summary;
      setMessages(prev => {
        const newMessages = prev.slice(0, -1).concat({ content: responseText, type: 'bot-message' });
        console.log("Messages after adding response:", newMessages);
        return newMessages;
      });
      setReportData(result.answer);
      setIsReportVisible(true);

      // Construct text from right panel content
      const reportText = constructReportText(result.answer);

      // Call TTS endpoint and play audio
      try {
        const ttsResponse = await fetch(`${import.meta.env.VITE_BASE_URL}/tts`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: reportText })
        });
        if (!ttsResponse.ok) throw new Error('Failed to fetch TTS audio');
        const ttsData = await ttsResponse.json();
        if (ttsData.audio && ttsData.mimetype === 'audio/mpeg') {
          const audio = new Audio(`data:audio/mpeg;base64,${ttsData.audio}`);
          audioRef.current = audio;
          audio.play();
          setIsPlaying(true);
        }
      } catch (ttsError) {
        console.error("TTS Error:", ttsError);
      }
    } catch (error) {
      console.error("Chat Error:", error);
      const errorMessage = 'Sorry, an error occurred while processing your request.';
      setMessages(prev => {
        const newMessages = prev.slice(0, -1).concat({ content: errorMessage, type: 'bot-message' });
        console.log("Messages after adding error message:", newMessages);
        return newMessages;
      });

      // Call TTS endpoint for error message
      try {
        const ttsResponse = await fetch('https://testing.sasthra.in/tts', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: errorMessage })
        });
        if (!ttsResponse.ok) throw new Error('Failed to fetch TTS audio');
        const ttsData = await ttsResponse.json();
        if (ttsData.audio && ttsData.mimetype === 'audio/mpeg') {
          const audio = new Audio(`data:audio/mpeg;base64,${ttsData.audio}`);
          audioRef.current = audio;
          audio.play();
          setIsPlaying(true);
        }
      } catch (ttsError) {
        console.error("TTS Error:", ttsError);
      }
    }
  };

  const handleSpeechRecognition = () => {
    if (listening) {
      SpeechRecognition.stopListening();
    } else {
      SpeechRecognition.startListening({ continuous: true });
    }
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      if (audioRef.current) audioRef.current.pause();
      setIsPlaying(false);
    } else {
      if (audioRef.current) audioRef.current.play();
      setIsPlaying(true);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 font-poppins p-6">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Panel: Chatbot */}
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col h-[500px] transition-all duration-300 hover:shadow-xl">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-5">
            <i className="fas fa-robot mr-3 text-indigo-600"></i> Mentor
          </h2>
          <div ref={chatMessagesRef} className="flex-1 overflow-y-auto p-4 bg-gray-100 rounded-lg border border-gray-200 mb-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`max-w-[85%] p-3 rounded-2xl mb-3 ${
                  msg.type === 'bot-message'
                    ? 'bg-indigo-100 text-gray-900 self-start rounded-bl-sm'
                    : 'bg-indigo-600 text-white self-end rounded-br-sm'
                }`}
              >
                {msg.content}
              </div>
            ))}
          </div>
          <div className="flex items-center">
            <input
              type="text"
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Ask me anything about your performance..."
              className="flex-1 border border-gray-300 rounded-full px-4 py-2 text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"
            />
            <button
              onClick={handleSpeechRecognition}
              className={`ml-3 bg-${listening ? 'red' : 'indigo'}-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-${listening ? 'red' : 'indigo'}-700 transition-colors`}
            >
              <Volume2 size={18} />
            </button>
            <button
              onClick={handleSendMessage}
              className="ml-3 bg-indigo-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-indigo-700 transition-colors"
            >
              <Send size={18} />
            </button>
          </div>
        </div>

        {/* Right Panel */}
        <div className="bg-white rounded-2xl shadow-lg p-6 flex flex-col h-[500px] transition-all duration-300 hover:shadow-xl overflow-y-auto">
          <div className="flex justify-between items-center mb-5">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <i className="fas fa-brain mr-3 text-indigo-600"></i> Knowledge Snapshot
            </h2>
            <button
              onClick={handlePlayPause}
              className="bg-indigo-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-indigo-700 transition-colors"
            >
              {isPlaying ? <Pause size={18} /> : <Play size={18} />}
            </button>
          </div>
          {!isReportVisible ? (
            <div className="flex-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-3">Strengths</h3>
                  <ul className="space-y-2">
                    {strengths.length > 0 ? strengths.map((item, index) => (
                      <li key={index} className="bg-green-100 text-green-900 p-3 rounded-md font-medium">
                        {item}
                      </li>
                    )) : (
                      <li className="text-gray-500">No specific strengths identified yet.</li>
                    )}
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-3">Areas for Improvement</h3>
                  <ul className="space-y-2">
                    {weaknesses.length > 0 ? weaknesses.map((item, index) => (
                      <li key={index} className="bg-red-100 text-red-900 p-3 rounded-md font-medium">
                        {item}
                      </li>
                    )) : (
                      <li className="text-gray-500">No specific weaknesses identified yet.</li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center mb-5">
                <i className="fas fa-chart-line mr-3 text-indigo-600"></i> Performance Analysis
              </h2>
              {reportData && (
                <div>
                  <h4 className="text-lg font-medium text-gray-800 mb-2">Overall Summary</h4>
                  <p className="text-gray-600 mb-4">{reportData.overall_summary}</p>
                  {reportData.performance_trends?.length > 0 && (
                    <>
                      <h4 className="text-lg font-medium text-gray-800 mb-2">Performance Trends</h4>
                      {reportData.performance_trends.map((trend, index) => (
                        <p key={index} className="text-gray-600">
                          <strong>{trend.subject}:</strong> {trend.trend} (Latest Score: {trend.latest_score_percentage}%)
                        </p>
                      ))}
                    </>
                  )}
                  {reportData.recommendations?.primary_focus_subject && (
                    <>
                      <h4 className="text-lg font-medium text-gray-800 mb-2 mt-4">Recommendations</h4>
                      <p className="text-gray-600"><strong>Primary Focus:</strong> {reportData.recommendations.primary_focus_subject}</p>
                      <ul className="list-disc pl-5 text-gray-600">
                        {reportData.recommendations.suggested_actions.map((action, index) => (
                          <li key={index}>{action}</li>
                        ))}
                      </ul>
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarkAnalysis;