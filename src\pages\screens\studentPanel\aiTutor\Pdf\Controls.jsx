// import React from 'react';
// import { useState } from 'react';
// import TTS from '../TTS';
// import ClassRoomTimer from '../Timer/ClassRoomTimer';
// import StudentPanelTimer from '../Timer/StudentPanelTimer';
// import BreakTime from '../Timer/BreakTime';
// import { Monitor } from "lucide-react"


// const Controls = ({
//   subject,
//   pageNumber,
//   numPages,
//   currentSlideIndex,
//   slideData,
//   isTransitioning,
//   isFullscreen,
//   getCurrentSlideContent,
//   goToPrevPage,
//   goToNextPage,
//   toggleFullscreen,
//   setPageNumber,
//   setCurrentSlideIndex,
//   setIsTransitioning,
//   setError,
//   setPhonemeData,
//   processSelectorId
// }) => {

//    const [showScreensaver, setShowScreensaver] = useState(false)
//    const [logs, setLogs] = useState([]);

//   const handleThreeMinutes = () => {
//     console.log("✅ Timer reached 3 minutes!");
//   };

//   const handlePlayClick = () => {
//     const logObject = {
//       process_selector_id: processSelectorId,
//       current_slide_number: currentSlideIndex + 1
//     };

//     // Push new log object to state
//     setLogs((prevLogs) => [...prevLogs, logObject]);

//     // Console log the current object
//     console.log(logs);
//   };

//   const handlePageSelect = (newPage) => {
//     setPageNumber(newPage);
//     setIsTransitioning(true);
//     setTimeout(() => {
//       setCurrentSlideIndex(newPage - 1);
//       setIsTransitioning(false);
//       console.log(`Selected page ${newPage}`);
//     }, 2000);
//   };

//   return (
//     <>
//       {/* Header Controls */}
//       <div className="bg-white px-6 py-4 border-b border-gray-200 shadow-sm">
//         <div className="flex items-center justify-between">
//           <h1 className="text-2xl font-bold text-gray-900">
//             {subject ? `Subject: ${subject}` : 'Learning Viewer'}
//           </h1>
//           <div className="flex items-center space-x-4">
//             <div className="flex items-center space-x-2">
//               <button
//                 onClick={goToPrevPage}
//                 disabled={pageNumber === 1 || isTransitioning}
//                 className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1 text-white"
//               >
//                 <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
//                   <path
//                     fillRule="evenodd"
//                     d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
//                     clipRule="evenodd"
//                   />
//                 </svg>
//                 <span>Prev</span>
//               </button>
//               <span className="text-sm text-gray-600 px-3">
//                 {currentSlideIndex + 1} / {slideData.length || 1}
//               </span>
//               <button
//                 onClick={goToNextPage}
//                 disabled={pageNumber === numPages || isTransitioning}
//                 className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1 text-white"
//               >
//                 <span>Next</span>
//                 <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
//                   <path
//                     fillRule="evenodd"
//                     d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
//                     clipRule="evenodd"
//                   />
//                 </svg>
//               </button>
//             </div>
//             <div className="flex items-center space-x-3">
//               <TTS
//                 contentToSpeak={getCurrentSlideContent()}
//                 pageNumber={pageNumber}
//                 numPages={numPages}
//                 goToNextPage={goToNextPage}
//                 setError={setError}
//                 isTransitioning={isTransitioning}
//                 setPhonemeData={setPhonemeData}
//                  onPlayClick={handlePlayClick}
//               />
                
//            {sessionStorage.getItem('role')==='student'?<StudentPanelTimer onThreeMinutes={handleThreeMinutes} />:<ClassRoomTimer onThreeMinutes={handleThreeMinutes} />}
              
//       {/* Render the BreakTime component */}
//       <BreakTime showScreensaver={showScreensaver} setShowScreensaver={setShowScreensaver} />



//         {/* Button to trigger the screensaver */}
//         <button
//           onClick={() => setShowScreensaver(true)}
//           className="mt-2 inline-flex items-center px-3 py-3 border border-purple-300 shadow-sm text-sm font-medium rounded-md text-purple-700 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
//         >
//           <Monitor className="h-4 w-4 mr-1" />
//           Break Time
//         </button>
      
  
//               <button
//                 onClick={toggleFullscreen}
//                 className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm transition-colors flex items-center space-x-1 text-white"
//               >
//                 <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
//                   {isFullscreen ? (
//                     <path
//                       fillRule="evenodd"
//                       d="M10 2L3 9l1.5 1.5L9 6v8l-4.5-4.5L3 11l7-7z"
//                       clipRule="evenodd"
//                     />
//                   ) : (
//                     <path
//                       fillRule="evenodd"
//                       d="M3 5a1 1 0 011-1h2a1 1 0 110 2H5v1a1 1 0 01-2 0V5zm14 0a1 1 0 01-1-1h-2a1 1 0 110-2h2v1a1 1 0 012 0v2zm-14 10a1 1 0 001 1h2a1 1 0 100-2H5v-1a1 1 0 00-2 0v2zm14 0a1 1 0 01-1 1h-2a1 1 0 110-2h1v-1a1 1 0 012 0v2z"
//                       clipRule="evenodd"
//                     />
//                   )}
//                 </svg>
//                 <span>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</span>
//               </button>
              
//             </div>
//           </div>
//         </div>
//       </div>
      

//       {/* Footer Page Controls */}
//       <div className="flex justify-center items-center mt-4">
//         <div className="flex items-center space-x-2">
//           <span className="text-sm text-gray-600">
//             Page {pageNumber} of {numPages || 1}
//           </span>
//           {numPages && numPages > 1 && (
//             <select
//               value={pageNumber}
//               onChange={(e) => handlePageSelect(Number(e.target.value))}
//               className="bg-white text-gray-900 text-sm rounded px-2 py-1 border border-gray-300 focus:border-blue-500 focus:outline-none"
//             >
//               {Array.from({ length: numPages }, (_, i) => i + 1).map((page) => (
//                 <option key={page} value={page}>
//                   {page}
//                 </option>
//               ))}
//             </select>
//           )}
//         </div>
//       </div>
//     </>
//   );
// };

// export default Controls;

import React, { useState } from 'react';
import TTS from '../TTS';
import ClassRoomTimer from '../Timer/ClassRoomTimer';
import StudentPanelTimer from '../Timer/StudentPanelTimer';
import BreakTime from '../Timer/BreakTime';
import { Monitor } from "lucide-react";

const Controls = ({
  subject,
  pageNumber,
  numPages,
  currentSlideIndex,
  slideData,
  isTransitioning,
  isFullscreen,
  getCurrentSlideContent,
  goToPrevPage,
  goToNextPage,
  toggleFullscreen,
  setPageNumber,
  setCurrentSlideIndex,
  setIsTransitioning,
  setError,
  setPhonemeData,
  setLogs,
  processSelectorId
}) => {
  const [showScreensaver, setShowScreensaver] = useState(false);

  const handleThreeMinutes = () => {
    console.log("✅ Timer reached 3 minutes!");
  };

  const handlePlayClick = () => {
    const logObject = {
      process_selector_id: processSelectorId,
      current_slide_number: currentSlideIndex + 1
    };

    // Update parent logs state
    setLogs((prevLogs) => {
      const newLogs = [...prevLogs, logObject];
      console.log('Logs updated:', newLogs);
      return newLogs;
    });
  };

  const handlePageSelect = (newPage) => {
    setPageNumber(newPage);
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentSlideIndex(newPage - 1);
      setIsTransitioning(false);
      console.log(`Selected page ${newPage}`);
    }, 2000);
  };

  return (
    <>
      {/* Header Controls */}
      <div className="bg-white px-6 py-4 border-b border-gray-200 shadow-sm">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">
            {subject ? `Subject: ${subject}` : 'Learning Viewer'}
          </h1>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={goToPrevPage}
                disabled={pageNumber === 1 || isTransitioning}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1 text-white"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Prev</span>
              </button>
              <span className="text-sm text-gray-600 px-3">
                {currentSlideIndex + 1} / {slideData.length || 1}
              </span>
              <button
                onClick={goToNextPage}
                disabled={pageNumber === numPages || isTransitioning}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1 text-white"
              >
                <span>Next</span>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
            <div className="flex items-center space-x-3">
              <TTS
                contentToSpeak={getCurrentSlideContent()}
                pageNumber={pageNumber}
                numPages={numPages}
                goToNextPage={goToNextPage}
                setError={setError}
                isTransitioning={isTransitioning}
                setPhonemeData={setPhonemeData}
                onPlayClick={handlePlayClick}
              />
                
              {sessionStorage.getItem('role') === 'student' ? <StudentPanelTimer onThreeMinutes={handleThreeMinutes} /> : <ClassRoomTimer onThreeMinutes={handleThreeMinutes} />}
              
              {/* Render the BreakTime component */}
              <BreakTime showScreensaver={showScreensaver} setShowScreensaver={setShowScreensaver} />

              {/* Button to trigger the screensaver */}
              <button
                onClick={() => setShowScreensaver(true)}
                className="mt-2 inline-flex items-center px-3 py-3 border border-purple-300 shadow-sm text-sm font-medium rounded-md text-purple-700 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <Monitor className="h-4 w-4 mr-1" />
                Break Time
              </button>
      
              <button
                onClick={toggleFullscreen}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm transition-colors flex items-center space-x-1 text-white"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  {isFullscreen ? (
                    <path
                      fillRule="evenodd"
                      d="M10 2L3 9l1.5 1.5L9 6v8l-4.5-4.5L3 11l7-7z"
                      clipRule="evenodd"
                    />
                  ) : (
                    <path
                      fillRule="evenodd"
                      d="M3 5a1 1 0 011-1h2a1 1 0 110 2H5v1a1 1 0 01-2 0V5zm14 0a1 1 0 01-1-1h-2a1 1 0 110-2h2v1a1 1 0 012 0v2zm-14 10a1 1 0 001 1h2a1 1 0 100-2H5v-1a1 1 0 00-2 0v2zm14 0a1 1 0 01-1 1h-2a1 1 0 110-2h1v-1a1 1 0 012 0v2z"
                      clipRule="evenodd"
                    />
                  )}
                </svg>
                <span>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</span>
              </button>
              
            </div>
          </div>
        </div>
      </div>
      

      {/* Footer Page Controls */}
      <div className="flex justify-center items-center mt-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">
            Page {pageNumber} of {numPages || 1}
          </span>
          {numPages && numPages > 1 && (
            <select
              value={pageNumber}
              onChange={(e) => handlePageSelect(Number(e.target.value))}
              className="bg-white text-gray-900 text-sm rounded px-2 py-1 border border-gray-300 focus:border-blue-500 focus:outline-none"
            >
              {Array.from({ length: numPages }, (_, i) => i + 1).map((page) => (
                <option key={page} value={page}>
                  {page}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>
    </>
  );
};

export default Controls;