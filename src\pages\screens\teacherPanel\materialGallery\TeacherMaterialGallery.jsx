import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import debounce from 'lodash/debounce';
import {
  useUploadContentMutation,
  useApproveContentMutation,
  useLazyGetApprovedContentQuery,
  useLazyGetPendingContentQuery,
  useLazyGetRejectedContentQuery
} from './materialGallery.slice';

import ContentCard from './ContentCard';
import Toastify from '../../../../components/PopUp/Toastify';

// Floating blob background component
const FloatingBlobs = () => {
  return (
    <>
      <motion.div
        className="absolute -left-20 -top-20 w-64 h-64 rounded-full bg-purple-100/40 blur-xl"
        animate={{
          x: [0, -10, 0, 10, 0],
          y: [0, 15, 0, -15, 0],
          transition: {
            duration: 25,
            repeat: Infinity,
            ease: 'easeInOut'
          }
        }}
      />
      <motion.div
        className="absolute -right-10 -bottom-10 w-80 h-80 rounded-full bg-blue-100/40 blur-xl"
        animate={{
          x: [0, 15, 0, -15, 0],
          y: [0, -10, 0, 10, 0],
          transition: {
            duration: 30,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 5
          }
        }}
      />
      <motion.div
        className="absolute right-1/4 top-1/3 w-40 h-40 rounded-full bg-indigo-100/50 blur-lg"
        animate={{
          x: [0, 20, 0, -20, 0],
          y: [0, -20, 0, 20, 0],
          transition: {
            duration: 20,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 10
          }
        }}
      />
    </>
  );
};

const TeacherMaterialGallery = () => {
  const [selectedTab, setSelectedTab] = useState('upload');
  const role = sessionStorage.getItem('role');
  const designation = sessionStorage.getItem('designation');
  const studentCourse = sessionStorage.getItem('course');
  const controls = useAnimation();

  const isHOD = role === 'kota_teacher' && designation === 'HOD';

  const [file, setFile] = useState(null);
  const [formData, setFormData] = useState({
    course: '',
    subject: '',
    unit: '',
    sub_unit: '',
    description: '',
    file_type: '',
    tags: ''
  });

  const [res, setRes] = useState(null);
  const [contentData, setContentData] = useState({
    approved: [],
    pending: [],
    rejected: []
  });

  // RTK Query hooks
  const [uploadContent, { isLoading: isUploading }] = useUploadContentMutation();
  const [approveContent, { isLoading: isApproving, error: approveError }] =
    useApproveContentMutation();
  // --- All RTK Query hooks go first ---
  const [
    triggerApprovedContent,
    { data: approvedData, isLoading: isApprovedLoading, error: approvedError }
  ] = useLazyGetApprovedContentQuery();
  const [
    triggerPendingContent,
    { data: pendingData, isLoading: isPendingLoading, error: pendingError }
  ] = useLazyGetPendingContentQuery();
  const [
    triggerRejectedContent,
    { data: rejectedData, isLoading: isRejectedLoading, error: rejectedError }
  ] = useLazyGetRejectedContentQuery();

  // --- Then your effect that sets contentData ---
  useEffect(() => {
    const filterByCourse = (data) => {
      if (!Array.isArray(data)) return [];

      // For Kota Teacher HOD: no filtering
      if (isHOD) return data;

      // For students: filter based on their course from sessionStorage
      if (role === 'student') {
        const userCourse = sessionStorage.getItem('course');
        if (userCourse) {
          return data.filter((item) => item.course?.toLowerCase() === userCourse.toLowerCase());
        }
      }

      // Default: return as is
      return data;
    };

    setContentData({
      approved: filterByCourse(approvedData?.data),
      pending: filterByCourse(pendingData?.data),
      rejected: filterByCourse(rejectedData?.data)
    });
  }, [approvedData, pendingData, rejectedData, isHOD, role]);

  useEffect(() => {
    if (
      (selectedTab === 'approved' && isHOD) ||
      (selectedTab === 'approved' && role === 'student' && studentCourse)
    ) {
      triggerApprovedContent();
    } else if (selectedTab === 'pending' && isHOD) {
      triggerPendingContent();
    } else if (selectedTab === 'rejected') {
      triggerRejectedContent();
    }
  }, [
    selectedTab,
    isHOD,
    role,
    studentCourse,
    triggerApprovedContent,
    triggerPendingContent,
    triggerRejectedContent
  ]);

  useEffect(() => {
    if (res) {
      const timer = setTimeout(() => setRes(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [res]);

  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  }, []);

  const handleFileChange = useCallback((e) => {
    const selectedFile = e.target.files[0];
    setFile(selectedFile);
    setFormData((prev) => ({
      ...prev,
      file_type: selectedFile?.name.endsWith('.pdf') ? 'pdf' : 'video'
    }));
  }, []);

  const handleUpload = useCallback(
    debounce(async (e) => {
      e.preventDefault();
      if (!file || !formData.course || !formData.subject || !formData.file_type) {
        setRes({ success: false, message: 'Please fill all required fields and select a file' });
        return;
      }
      if (file.size > 100 * 1024 * 1024) {
        setRes({ success: false, message: 'File size exceeds 100MB limit' });
        return;
      }

      const uploadFormData = new FormData();
      uploadFormData.append('file', file);
      uploadFormData.append('course', formData.course);
      uploadFormData.append('subject', formData.subject);
      uploadFormData.append('unit', formData.unit);
      uploadFormData.append('sub_unit', formData.sub_unit);
      uploadFormData.append('description', formData.description);
      uploadFormData.append('file_type', formData.file_type);
      if (formData.tags) {
        uploadFormData.append(
          'tags',
          JSON.stringify(formData.tags.split(',').map((tag) => tag.trim()))
        );
      }

      try {
        await uploadContent(uploadFormData).unwrap();
        setRes({ success: true, message: 'File uploaded successfully' });
        setFormData({
          course: '',
          subject: '',
          unit: '',
          sub_unit: '',
          description: '',
          file_type: '',
          tags: ''
        });
        setFile(null);
        await controls.start({
          scale: [1, 1.05, 1],
          transition: { duration: 0.5 }
        });
      } catch (err) {
        setRes({ success: false, message: `Upload failed: ${err.message || 'Unknown error'}` });
      }
    }, 500),
    [file, formData, uploadContent, controls]
  );

  const handleApprove = useCallback(
    async (contentId) => {
      try {
        await approveContent({ content_id: contentId, action: 'approve' }).unwrap();
        setRes({ success: true, message: 'Content approved successfully' });
        triggerPendingContent();
      } catch (err) {
        setRes({ success: false, message: `Approval failed: ${err.message || 'Unknown error'}` });
      }
    },
    [approveContent, triggerPendingContent]
  );

  const handleReject = useCallback(
    async (contentId, rejectionReasonParam) => {
      if (!rejectionReasonParam) {
        setRes({ success: false, message: 'Please provide a rejection reason' });
        return;
      }
      try {
        await approveContent({
          content_id: contentId,
          action: 'reject',
          rejection_reason: rejectionReasonParam
        }).unwrap();
        setRes({ success: true, message: 'Content rejected successfully' });
        triggerPendingContent();
      } catch (err) {
        setRes({ success: false, message: `Rejection failed: ${err.message || 'Unknown error'}` });
      }
    },
    [approveContent, triggerPendingContent]
  );

  const isUploadDisabled = useMemo(() => {
    return !formData.course || !formData.subject || !formData.description || !file;
  }, [formData.course, formData.subject, formData.description, file]);

  const memoizedContentCards = useMemo(() => {
    const content = Array.isArray(contentData[selectedTab]) ? contentData[selectedTab] : [];
    return content.length > 0 ? (
      content.map((content, index) => (
        <motion.div
          key={content.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}>
          <ContentCard
            content={content}
            isHOD={isHOD}
            selectedTab={selectedTab}
            handleApprove={handleApprove}
            handleReject={handleReject}
            isApproving={isApproving}
          />
        </motion.div>
      ))
    ) : (
      <motion.div
        className="col-span-full flex flex-col items-center justify-center p-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}>
        <svg
          className="w-16 h-6 text-gray-400 mb-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
            d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <p className="text-gray-500 text-lg font-medium">No {selectedTab} content available</p>
      </motion.div>
    );
  }, [contentData, selectedTab, isHOD, handleApprove, handleReject, isApproving]);

  const tabVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 10
      }
    },
    exit: { opacity: 0, x: 50 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/20 to-purple-50/20 p-4 md:p-8 relative overflow-hidden">
      {/* Background elements */}
      <FloatingBlobs />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-transparent via-white/10 to-transparent opacity-20 pointer-events-none"></div>

      <Toastify res={res} resClear={() => setRes(null)} />
      <div className="max-w-7xl mx-auto relative z-10">
        {/* Header with glass morphism effect */}
        <motion.div
          className="relative mb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}>
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 to-purple-50/40 rounded-2xl"></div>
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxmZUNvbXBvc2l0ZSBpZD0ibm9pc2UiIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiB4PSIwIiB5PSIwIiBvcGVyYXRvcj0ib3ZlcmxheSI+PGZlVHVyYnVsZW5jZSB0eXBlPSJmcmFjdGFsTm9pc2UiIGJhc2VGcmVxdWVuY3k9IjAuMDUiIG51bU9jdGF2ZXM9IjUiIHN0aXRjaFRpbGVzPSJzdGl0Y2giLz48L2ZlQ29tcG9zaXRlPjwvc3ZnPg==')] opacity-5 rounded-2xl"></div>
          <div className="bg-white/30 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/30 relative z-10">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <motion.h1
                  className="text-3xl md:text-4xl font-bold text-gray-800 mb-2"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, type: 'spring' }}>
                  Material Gallery
                </motion.h1>
                <motion.p
                  className="text-gray-600"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.5 }}>
                  {selectedTab === 'upload'
                    ? 'Upload new educational materials'
                    : `Viewing ${selectedTab} materials`}
                </motion.p>
              </div>

              {/* Tab Navigation with enhanced animations */}
              <motion.div
                className="flex flex-wrap gap-2 mt-4 md:mt-0"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}>
                {/* Always show Upload tab */}
                <motion.button
                  onClick={() => setSelectedTab('upload')}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`relative px-4 py-2 rounded-lg font-medium transition-colors ${
                    selectedTab === 'upload'
                      ? 'text-indigo-600'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                  }`}>
                  Upload
                  {selectedTab === 'upload' && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-indigo-500"
                      layoutId="underline"
                      transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
                    />
                  )}
                </motion.button>

                {/* Student with course: show Approved and Rejected tabs */}
                {role === 'student' && studentCourse && (
                  <>
                    <motion.button
                      onClick={() => setSelectedTab('approved')}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative px-4 py-2 rounded-lg font-medium transition-colors ${
                        selectedTab === 'approved'
                          ? 'text-green-600'
                          : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                      }`}>
                      Approved
                      {selectedTab === 'approved' && (
                        <motion.div
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-green-500"
                          layoutId="underline"
                          transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
                        />
                      )}
                    </motion.button>
                    <motion.button
                      onClick={() => setSelectedTab('rejected')}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative px-4 py-2 rounded-lg font-medium transition-colors ${
                        selectedTab === 'rejected'
                          ? 'text-red-600'
                          : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                      }`}>
                      Rejected
                      {selectedTab === 'rejected' && (
                        <motion.div
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-red-500"
                          layoutId="underline"
                          transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
                        />
                      )}
                    </motion.button>
                  </>
                )}

                {/* Kota Teacher/Faculty (not HOD): show Upload and Rejected tabs */}
                {(role === 'kota_teacher' || role === 'faculty') && designation !== 'HOD' && (
                  <motion.button
                    onClick={() => setSelectedTab('rejected')}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`relative px-4 py-2 rounded-lg font-medium transition-colors ${
                      selectedTab === 'rejected'
                        ? 'text-red-600'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                    }`}>
                    Rejected
                    {selectedTab === 'rejected' && (
                      <motion.div
                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-red-500"
                        layoutId="underline"
                        transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
                      />
                    )}
                  </motion.button>
                )}

                {/* Kota Teacher HOD: show all tabs */}
                {role === 'kota_teacher' && designation === 'HOD' && (
                  <>
                    <motion.button
                      onClick={() => setSelectedTab('approved')}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative px-4 py-2 rounded-lg font-medium transition-colors ${
                        selectedTab === 'approved'
                          ? 'text-green-600'
                          : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                      }`}>
                      Approved
                      {selectedTab === 'approved' && (
                        <motion.div
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-green-500"
                          layoutId="underline"
                          transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
                        />
                      )}
                    </motion.button>
                    <motion.button
                      onClick={() => setSelectedTab('pending')}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative px-4 py-2 rounded-lg font-medium transition-colors ${
                        selectedTab === 'pending'
                          ? 'text-amber-600'
                          : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                      }`}>
                      Pending
                      {selectedTab === 'pending' && (
                        <motion.div
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-amber-500"
                          layoutId="underline"
                          transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
                        />
                      )}
                    </motion.button>
                    <motion.button
                      onClick={() => setSelectedTab('rejected')}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`relative px-4 py-2 rounded-lg font-medium transition-colors ${
                        selectedTab === 'rejected'
                          ? 'text-red-600'
                          : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                      }`}>
                      Rejected
                      {selectedTab === 'rejected' && (
                        <motion.div
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-red-500"
                          layoutId="underline"
                          transition={{ type: 'spring', bounce: 0.3, duration: 0.6 }}
                        />
                      )}
                    </motion.button>
                  </>
                )}
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Main Content Area */}
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedTab}
            variants={tabVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="relative">
            {selectedTab === 'upload' && (
              <motion.div
                className="bg-white rounded-xl shadow-md overflow-hidden mb-6"
                whileHover={{ scale: 1.005 }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}>
                <div className="p-6">
                  <motion.h2
                    className="text-2xl font-semibold text-gray-800 mb-6 flex items-center"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}>
                    <motion.svg
                      className="w-6 h-6 text-indigo-500 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      animate={{
                        rotate: [0, 10, -10, 0],
                        transition: {
                          duration: 2,
                          repeat: Infinity,
                          repeatType: 'reverse'
                        }
                      }}>
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </motion.svg>
                    Upload New Content
                  </motion.h2>

                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {['course', 'subject', 'unit', 'sub_unit'].map((field, index) => (
                        <motion.div
                          key={field}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 + index * 0.05 }}>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {field === 'course' || field === 'subject'
                              ? `${field.charAt(0).toUpperCase() + field.slice(1)} *`
                              : `${field
                                  .split('_')
                                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                  .join(' ')} (optional)`}
                          </label>
                          <motion.input
                            type="text"
                            name={field}
                            value={formData[field]}
                            onChange={handleInputChange}
                            placeholder={
                              field === 'course'
                                ? 'e.g. NEET, JEE'
                                : field === 'subject'
                                  ? 'e.g. Physics, Chemistry, etc'
                                  : field === 'unit'
                                    ? 'e.g. Unit 1:Thermodynamics'
                                    : 'e.g. 1.1 Introduction'
                            }
                            required={field === 'course' || field === 'subject'}
                            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                            whileFocus={{ scale: 1.01 }}
                          />
                        </motion.div>
                      ))}

                      <motion.div
                        className="md:col-span-2"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description *
                        </label>
                        <motion.textarea
                          name="description"
                          value={formData.description}
                          onChange={handleInputChange}
                          rows="3"
                          required
                          placeholder="Brief description of the content"
                          className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all resize-none"
                          whileFocus={{ scale: 1.01 }}
                        />
                      </motion.div>

                      <motion.div
                        className="md:col-span-2"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.35 }}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          File *
                        </label>
                        <motion.div
                          className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg"
                          whileHover={{ borderColor: '#6366f1' }}
                          transition={{ duration: 0.3 }}>
                          <div className="space-y-1 text-center">
                            <motion.svg
                              className="mx-auto h-12 w-12 text-gray-400"
                              stroke="currentColor"
                              fill="none"
                              viewBox="0 0 48 48"
                              aria-hidden="true"
                              animate={{
                                y: [0, -5, 0],
                                transition: {
                                  duration: 3,
                                  repeat: Infinity,
                                  ease: 'easeInOut'
                                }
                              }}>
                              <path
                                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </motion.svg>
                            <div className="flex text-sm text-gray-600 justify-center">
                              <motion.label
                                htmlFor="file-upload"
                                className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}>
                                <span>Upload a file</span>
                                <input
                                  id="file-upload"
                                  name="file"
                                  type="file"
                                  accept=".pdf,.mp4,.mov,.mkv,.avi,.wmv,.flv,.webm,.mpeg,.mpg,.3gp,.ogv,.ts,.vob"
                                  onChange={handleFileChange}
                                  className="sr-only"
                                  required
                                />
                              </motion.label>
                              <p className="pl-1">or drag and drop</p>
                            </div>
                            <p className="text-xs text-gray-500">PDF, MP4, MOV up to 100MB</p>
                            {file && (
                              <motion.p
                                className="text-sm text-gray-900 mt-2"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}>
                                Selected: {file.name} ({(file.size / (1024 * 1024)).toFixed(2)} MB)
                              </motion.p>
                            )}
                          </div>
                        </motion.div>
                      </motion.div>
                    </div>

                    <div className="flex justify-end">
                      <motion.button
                        type="button"
                        onClick={handleUpload}
                        disabled={isUploadDisabled || isUploading}
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300 disabled:cursor-not-allowed transition-colors"
                        whileHover={!isUploadDisabled && !isUploading ? { scale: 1.05 } : {}}
                        whileTap={!isUploadDisabled && !isUploading ? { scale: 0.95 } : {}}
                        animate={controls}>
                        {isUploading ? (
                          <>
                            <svg
                              className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24">
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Uploading...
                          </>
                        ) : (
                          <>
                            <svg
                              className="-ml-1 mr-3 h-5 w-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                              />
                            </svg>
                            Upload Content
                          </>
                        )}
                      </motion.button>
                    </div>
                  </form>
                </div>
              </motion.div>
            )}

            {(isApprovedLoading || isPendingLoading || isRejectedLoading) && (
              <motion.div
                className="flex justify-center items-center py-12"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}>
                <div className="animate-pulse flex flex-col items-center">
                  <motion.div
                    className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4"
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 180, 360],
                      transition: {
                        duration: 2,
                        repeat: Infinity,
                        ease: 'linear'
                      }
                    }}>
                    <svg
                      className="w-8 h-8 text-indigo-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                  </motion.div>
                  <motion.p
                    className="text-gray-600"
                    animate={{
                      opacity: [0.5, 1, 0.5],
                      transition: {
                        duration: 2,
                        repeat: Infinity
                      }
                    }}>
                    Fetching content...
                  </motion.p>
                </div>
              </motion.div>
            )}

            {(approvedError || pendingError || rejectedError || approveError) && (
              <motion.div
                className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded-r-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ type: 'spring' }}>
                <div className="flex">
                  <div className="flex-shrink-0">
                    <motion.svg
                      className="h-5 w-5 text-red-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      animate={{
                        scale: [1, 1.2, 1],
                        transition: { repeat: Infinity, duration: 2 }
                      }}>
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </motion.svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">
                      {approvedError?.message ||
                        pendingError?.message ||
                        rejectedError?.message ||
                        approveError?.message ||
                        'An error occurred while fetching content'}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}

            {selectedTab !== 'upload' && (
              <motion.div
                className="mb-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}>
                <div className="flex justify-between items-center mb-4">
                  <motion.h2
                    className="text-xl font-semibold text-gray-800 capitalize"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}>
                    {selectedTab} Materials
                  </motion.h2>
                  <motion.div
                    className="text-sm text-gray-500"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}>
                    {contentData[selectedTab]?.length || 0} items
                  </motion.div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <AnimatePresence>{memoizedContentCards}</AnimatePresence>
                </div>
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Full Page Loading Overlay */}
        {isUploading && (
          <motion.div
            className="fixed inset-0 bg-indigo-50/95 backdrop-blur-md flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}>
            <div className="text-center max-w-md px-6">
              {/* Animated document stack */}
              <motion.div className="relative h-32 w-24 mx-auto mb-8">
                {/* Base document */}
                <motion.div
                  className="absolute inset-0 bg-white rounded-lg shadow-lg border-2 border-indigo-100"
                  animate={{
                    y: [0, -5, 0],
                    transition: {
                      duration: 2,
                      repeat: Infinity
                    }
                  }}>
                  <div className="absolute top-3 left-3 right-3 h-2 bg-indigo-100 rounded-full" />
                  <div className="absolute top-7 left-3 right-3 h-1 bg-indigo-50 rounded-full" />
                  <div className="absolute top-9 left-3 right-3 h-1 bg-indigo-50 rounded-full" />
                  <div className="absolute top-11 left-3 w-1/2 h-1 bg-indigo-50 rounded-full" />
                </motion.div>

                {/* Floating document */}
                <motion.div
                  className="absolute inset-0 bg-white rounded-lg shadow-xl border-2 border-indigo-200"
                  animate={{
                    y: [-20, -30, -20],
                    rotate: [-2, 2, -2],
                    transition: {
                      duration: 3,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }
                  }}>
                  <div className="absolute top-3 left-3 right-3 h-2 bg-indigo-200 rounded-full" />
                  <div className="absolute top-7 left-3 right-3 h-1 bg-indigo-100 rounded-full" />
                </motion.div>

                {/* Progress indicator */}
                <motion.div
                  className="absolute -bottom-5 left-0 right-0 h-1 bg-indigo-100 rounded-full overflow-hidden"
                  initial={{ width: 0 }}
                  animate={{
                    width: '100%',
                    transition: {
                      duration: 5,
                      repeat: Infinity
                    }
                  }}>
                  <motion.div
                    className="h-full bg-gradient-to-r from-indigo-400 to-purple-500"
                    animate={{
                      x: ['-100%', '100%'],
                      transition: {
                        duration: 1.5,
                        repeat: Infinity,
                        ease: 'linear'
                      }
                    }}
                  />
                </motion.div>
              </motion.div>

              {/* Animated text */}
              <div className="space-y-2">
                <motion.h3
                  className="text-2xl font-bold text-indigo-900"
                  animate={{
                    opacity: [0.8, 1, 0.8],
                    transition: {
                      duration: 2,
                      repeat: Infinity
                    }
                  }}>
                  Processing Your Documents
                </motion.h3>

                <motion.p
                  className="text-indigo-700"
                  animate={{
                    y: [0, -3, 0],
                    transition: {
                      duration: 3,
                      repeat: Infinity
                    }
                  }}>
                  We're giving your files the VIP treatment
                </motion.p>

                {/* Animated dots */}
                <div className="flex justify-center space-x-1 pt-4">
                  {[0, 1, 2].map((i) => (
                    <motion.div
                      key={i}
                      className="w-2 h-2 bg-indigo-400 rounded-full"
                      animate={{
                        y: [0, -5, 0],
                        opacity: [0.6, 1, 0.6],
                        transition: {
                          duration: 1.5,
                          repeat: Infinity,
                          delay: i * 0.2
                        }
                      }}
                    />
                  ))}
                </div>

                {/* Fun status messages that cycle */}
                <motion.p
                  className="text-sm text-indigo-500 mt-6 italic"
                  animate={{
                    opacity: [0, 1, 0],
                    transition: {
                      duration: 4,
                      repeat: Infinity
                    }
                  }}>
                  {
                    [
                      'Organizing pages...',
                      'Checking for paper cuts...',
                      'Adding digital confetti...',
                      'Almost there...'
                    ][Math.floor((Date.now() / 4000) % 4)]
                  }
                </motion.p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default TeacherMaterialGallery;
