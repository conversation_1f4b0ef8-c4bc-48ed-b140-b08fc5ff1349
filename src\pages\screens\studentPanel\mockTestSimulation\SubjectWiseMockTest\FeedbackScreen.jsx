import React from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faCheck,
  faTimes,
  faExclamationCircle
} from '@fortawesome/free-solid-svg-icons';
import { MathJax, MathJaxContext } from 'better-react-mathjax';

const mathJaxConfig = {
  loader: { load: ['[tex]/html'] },
  tex: {
    packages: { '[+]': ['html'] },
    inlineMath: [['$$', '$$']],
    displayMath: [['\\[', '\\]']],
    processEscapes: true,
    processEnvironments: true
  }
};

const FeedbackScreen = ({ questions, onBack, examName, subject, results }) => {
  const { total_score, max_score, correct, incorrect, unattempted, details } = results || {};

  // Calculate percentage
  const percentage = max_score > 0 ? Math.round((total_score / max_score) * 100) : 0;

  // Show loading state if results are not available
  if (!results) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  return (
    <MathJaxContext config={mathJaxConfig}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-5xl mx-auto p-6 md:p-8 lg:p-10 min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
          className="mb-10 bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-lg border border-gray-100/50">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                Review Your{' '}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-500">
                  {subject} ({examName.toUpperCase()})
                </span>{' '}
                Results
              </h1>
              <p className="text-gray-600 mt-2">Analyze your performance and improve</p>
            </div>
          
          </div>
          {/* Summary Metrics */}
          {results && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="mt-6 grid grid-cols-2 sm:grid-cols-5 gap-4">
              <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-xl shadow-sm">
                <p className="text-sm text-gray-600">Total Score</p>
                <p className="text-xl font-bold text-indigo-700">
                  {total_score}/{max_score}
                </p>
                <p className="text-xs text-indigo-600 mt-1">{percentage}%</p>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl shadow-sm">
                <p className="text-sm text-gray-600">Correct</p>
                <p className="text-xl font-bold text-green-700">{correct}</p>
              </div>
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-xl shadow-sm">
                <p className="text-sm text-gray-600">Incorrect</p>
                <p className="text-xl font-bold text-red-700">{incorrect}</p>
              </div>
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-xl shadow-sm">
                <p className="text-sm text-gray-600">Unattempted</p>
                <p className="text-xl font-bold text-gray-600">{unattempted}</p>
              </div>
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl shadow-sm">
                <p className="text-sm text-gray-600">Accuracy</p>
                <p className="text-xl font-bold text-purple-700">
                  {correct + incorrect > 0
                    ? Math.round((correct / (correct + incorrect)) * 100)
                    : 0}
                  %
                </p>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Feedback List */}
        <div className="space-y-6">
          {details && details.length > 0 ? (
            details.map((detail, index) => {
              const question = questions.find((q) => q.id === detail.question_id);
              const isCorrect = detail.status === 'correct';
              const isUnanswered = detail.status === 'unattempted';
              const isInvalid = detail.status === 'error';

              return (
                <motion.div
                  key={detail.question_id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.4 }}
                  className="border border-gray-200/50 rounded-2xl p-6 bg-white/90 backdrop-blur-sm shadow-sm hover:shadow-lg transition-shadow">
                  {/* Question Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-semibold px-3 py-1 rounded-full bg-indigo-100 text-indigo-800">
                        Question {detail.sequence}
                      </span>
                      {detail.unit && (
                        <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700">
                          {detail.unit}
                        </span>
                      )}
                      {detail.subunit && (
                        <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700">
                          {detail.subunit}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      {isInvalid ? (
                        <span className="text-sm font-medium text-orange-700 bg-orange-100 px-3 py-1 rounded-full">
                          {detail.message}
                        </span>
                      ) : isUnanswered ? (
                        <span className="text-sm font-medium text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                          Unanswered
                        </span>
                      ) : isCorrect ? (
                        <span className="text-sm font-medium text-green-700 bg-green-100 px-3 py-1 rounded-full">
                          Correct (+{detail.marks})
                        </span>
                      ) : (
                        <span className="text-sm font-medium text-red-700 bg-red-100 px-3 py-1 rounded-full">
                          Incorrect ({detail.marks})
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Question Text */}
                  <h3 className="text-lg font-semibold text-gray-800 leading-relaxed mb-5">
                    <MathJax>{detail.question_text}</MathJax>
                  </h3>

                  {/* Debug Information - Remove in production */}
                  {false && (
                    <div className="mb-4 p-3 bg-gray-100 rounded-lg text-xs">
                      <p>
                        <strong>Debug Info:</strong>
                      </p>
                      <p>Question ID: {detail.question_id}</p>
                      <p>Question Found: {question ? 'Yes' : 'No'}</p>
                      <p>Correct Answer: {detail.correct_answer}</p>
                      <p>Selected Option: {detail.selected_option}</p>
                      <p>Status: {detail.status}</p>
                      <p>Options Count: {question?.options?.length || 'N/A'}</p>
                    </div>
                  )}

                  {/* Options */}
                  {question ? (
                    <div className="space-y-3">
                      {question.options && question.options.length > 0 ? (
                        question.options.map((option, idx) => {
                          const isUserAnswer = detail.selected_option === idx + 1;
                          const isCorrectAnswer = detail.correct_answer === idx + 1;
                          const isUnanswered = detail.status === 'unattempted';
                          let bgColor = 'bg-white border-gray-200';
                          let textColor = 'text-gray-700';
                          let showIcon = false;
                          let iconType = null;

                          if (isUserAnswer && isCorrectAnswer) {
                            // User selected correct answer
                            bgColor = 'bg-green-50 border-green-300';
                            textColor = 'text-green-800';
                            showIcon = true;
                            iconType = 'correct';
                          } else if (isUserAnswer && !isCorrectAnswer) {
                            // User selected wrong answer
                            bgColor = 'bg-red-50 border-red-300';
                            textColor = 'text-red-800';
                            showIcon = true;
                            iconType = 'incorrect';
                          } else if (isCorrectAnswer) {
                            // Correct answer (show for all cases)
                            bgColor = isUnanswered
                              ? 'bg-blue-50 border-blue-300'
                              : 'bg-green-50 border-green-300';
                            textColor = isUnanswered ? 'text-blue-800' : 'text-green-800';
                            showIcon = true;
                            iconType = isUnanswered ? 'correct-answer' : 'correct';
                          }

                          return (
                            <motion.div
                              key={idx}
                              whileHover={{ scale: 1.02 }}
                              className={`p-4 rounded-xl border-2 ${bgColor} ${textColor} flex items-start transition-all`}>
                              {showIcon && (
                                <span className="w-5 h-5 mr-3 mt-0.5 flex-shrink-0">
                                  {iconType === 'incorrect' ? (
                                    <FontAwesomeIcon
                                      icon={faTimes}
                                      className="w-5 h-5 text-red-500"
                                    />
                                  ) : iconType === 'correct-answer' ? (
                                    <FontAwesomeIcon
                                      icon={faExclamationCircle}
                                      className="w-5 h-5 text-blue-500"
                                    />
                                  ) : (
                                    <FontAwesomeIcon
                                      icon={faCheck}
                                      className="w-5 h-5 text-green-500"
                                    />
                                  )}
                                </span>
                              )}
                              <span className="font-medium">
                                <MathJax>{option}</MathJax>
                              </span>
                              {isCorrectAnswer && isUnanswered && (
                                <span className="ml-auto text-xs font-semibold text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                                  Correct Answer
                                </span>
                              )}
                            </motion.div>
                          );
                        })
                      ) : (
                        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <p className="text-yellow-800 text-sm">
                            No options available for this question.
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      {detail.correct_answer && (
                        <p className="text-red-700 text-sm">
                          Correct Answer: Option {detail.correct_answer}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Explanation */}
                  {question?.explanation && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.3 }}
                      className="mt-5 p-4 bg-blue-50/80 border border-blue-200/50 rounded-xl backdrop-blur-sm">
                      <h4 className="font-semibold text-blue-800 mb-1">Explanation</h4>
                      <p className="text-blue-700 text-sm">
                        <MathJax>{question.explanation}</MathJax>
                      </p>
                    </motion.div>
                  )}

                  {/* Performance Message */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className={`mt-4 p-3 rounded-lg ${
                      isCorrect
                        ? 'bg-green-50 border border-green-200'
                        : isUnanswered
                          ? 'bg-blue-50 border border-blue-200'
                          : 'bg-red-50 border border-red-200'
                    }`}>
                    <div className="flex items-start space-x-2">
                      {isUnanswered && (
                        <FontAwesomeIcon
                          icon={faExclamationCircle}
                          className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0"
                        />
                      )}
                      <div>
                        <p
                          className={`text-sm font-medium ${
                            isCorrect
                              ? 'text-green-800'
                              : isUnanswered
                                ? 'text-blue-800'
                                : 'text-red-800'
                          }`}>
                          {isCorrect
                            ? 'Great job! You got this question right.'
                            : isUnanswered
                              ? 'This question was left unanswered. The correct answer is highlighted above for your reference.'
                              : 'This answer was incorrect. Review the correct answer and try to understand the concept.'}
                        </p>
                        {isUnanswered && (
                          <p className="text-xs text-blue-600 mt-1">
                            Tip: Always attempt all questions to maximize your score!
                          </p>
                        )}
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              );
            })
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No feedback details available.</p>
            </div>
          )}
        </div>

        {/* Final Actions */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="mt-10 flex justify-center">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onBack}
            className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition font-medium shadow-md hover:shadow-lg">
            Done Reviewing
          </motion.button>
        </motion.div>
      </motion.div>
    </MathJaxContext>
  );
};

export default FeedbackScreen;
