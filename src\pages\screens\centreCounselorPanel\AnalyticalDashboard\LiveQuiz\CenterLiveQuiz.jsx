import { useEffect, useState, useMemo } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import { useDispatch, useSelector } from 'react-redux';
import {
  setCenterLiveQuizData,
  useLazyGetCenterLiveQuizDataQuery
} from '../AnalyticalDashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';
import { Calendar, X, FileSearch, ServerCrash, FileText } from 'lucide-react';

// --- Helper Functions & Components (THEME UPDATED) ---

// A unified theme for a more cohesive and professional UI
const getSubjectTheme = () => {
  return { from: 'from-amber-500', to: 'to-orange-500', accent: 'text-amber-700' };
};

const EmptyState = ({ icon, title, message, children }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    className="flex flex-col items-center justify-center text-center py-20 bg-white/50 backdrop-blur-md rounded-2xl border border-amber-300/60 shadow-lg">
    <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-6 text-amber-500">
      {icon}
    </div>
    <h3 className="text-xl font-semibold text-amber-800 mb-2">{title}</h3>
    <p className="text-amber-600 max-w-sm">{message}</p>
    {children}
  </motion.div>
);

const SkeletonLoader = () => (
  <div className="space-y-8 animate-pulse">
    {/* Filter Bar Skeleton */}
    <div className="p-6 bg-amber-200/50 rounded-2xl h-28"></div>
    {/* Quiz Card Skeleton */}
    <div className="bg-white/50 rounded-2xl shadow-lg border border-amber-300/60 overflow-hidden flex flex-col md:flex-row min-h-[300px]">
      <div className="w-full md:w-64 p-6 bg-amber-200/50 space-y-4">
        <div className="h-7 bg-amber-300/50 rounded w-3/4"></div>
        <div className="h-4 bg-amber-300/50 rounded w-1/2"></div>
        <div className="h-6 bg-amber-300/50 rounded-full w-24 mt-4"></div>
      </div>
      <div className="flex-grow p-1">
        <div className="w-full h-full bg-amber-100/50 rounded-lg"></div>
      </div>
    </div>
  </div>
);

const QuizCard = ({ quiz, variants }) => {
  const theme = getSubjectTheme();

  return (
    <motion.div
      variants={variants}
      whileHover={{
        y: -5,
        boxShadow:
          '0 20px 25px -5px rgba(245, 158, 11, 0.1), 0 10px 10px -5px rgba(245, 158, 11, 0.04)'
      }}
      className="bg-white/50 backdrop-blur-md rounded-2xl shadow-lg border border-amber-300/60 overflow-hidden transition-all duration-300 flex flex-col md:flex-row">
      {/* Quiz Info Sidebar */}
      <div
        className={`w-full md:w-64 p-6 flex-shrink-0 bg-gradient-to-br ${theme.from} ${theme.to} flex flex-col justify-between shadow-lg`}>
        <div>
          <h2 className="text-2xl font-bold text-white mb-1 drop-shadow-sm">{quiz.subject}</h2>
        </div>
        <div>
          <p className="text-white/80 mt-2 text-sm">
            {new Date(quiz.created_at).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>
      </div>

      {/* Students Table */}
      <div className="overflow-x-auto flex-grow">
        <table className="w-full min-w-[700px] text-sm">
          <thead className="bg-amber-200/40">
            <tr>
              {[
                'Student',
                'Score',
                'Correct',
                'Incorrect',
                'Accuracy',
                'Avg Time',
                'Unattempted'
              ].map((h) => (
                <th
                  key={h}
                  className="px-4 py-3 text-xs font-semibold text-amber-700 uppercase tracking-wider text-left first:pl-6">
                  {h}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-amber-300/60">
            <AnimatePresence>
              {quiz?.students?.map((student, i) => (
                <StudentRow key={student.user_id} student={student} index={i} />
              ))}
            </AnimatePresence>
          </tbody>
        </table>
      </div>
    </motion.div>
  );
};

const StudentRow = ({ student, index }) => {
  const accuracy = student.summary.accuracy_percentage;
  const getAccuracyTheme = (acc) => {
    if (acc >= 75) return { bar: 'bg-emerald-500', text: 'text-emerald-700' };
    if (acc >= 50) return { bar: 'bg-sky-500', text: 'text-sky-700' };
    return { bar: 'bg-red-500', text: 'text-red-700' };
  };
  const accuracyTheme = getAccuracyTheme(accuracy);

  return (
    <motion.tr
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      transition={{ delay: index * 0.05 }}
      className="hover:bg-amber-200/50 transition-colors duration-200">
      <td className="px-6 py-3 whitespace-nowrap">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-amber-200 rounded-full flex items-center justify-center text-amber-700 font-bold text-xs">
            {student.name.charAt(0).toUpperCase()}
          </div>
          <div className="font-medium text-amber-900">{student.name}</div>
        </div>
      </td>
      <td className="px-4 py-3">
        <span className="inline-flex items-center justify-center w-7 h-7 bg-amber-200 text-amber-800 rounded-full font-bold text-xs">
          {student.summary.score}
        </span>
      </td>
      <td className="px-4 py-3">
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-500/10 text-emerald-700">
          {student.summary.correct_responses}
        </span>
      </td>
      <td className="px-4 py-3">
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-500/10 text-red-700">
          {student.summary.incorrect_responses}
        </span>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="flex items-center gap-2">
          <div className="w-20 h-1.5 bg-amber-100 rounded-full">
            <div
              className={`h-1.5 rounded-full ${accuracyTheme.bar}`}
              style={{ width: `${accuracy}%` }}></div>
          </div>
          <span className={`font-semibold ${accuracyTheme.text}`}>{accuracy}%</span>
        </div>
      </td>
      <td className="px-4 py-3 text-amber-700">{student.summary.avg_response_time_seconds}s</td>
      <td className="px-4 py-3">
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-amber-500/10 text-amber-700">
          {student.summary.unattempted_questions}
        </span>
      </td>
    </motion.tr>
  );
};

// --- Main Component ---
const CenterLiveQuiz = () => {
  const [toastRes, setToastRes] = useState();
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  const [getCenterLiveQuizData, { isLoading, isError }] = useLazyGetCenterLiveQuizDataQuery();
  const dispatch = useDispatch();
  const centerLiveQuizData = useSelector(
    (state) => state.centreCounselorDashboard.centerLiveQuizData
  );

  useEffect(() => {
    // Only fetch if data isn't already in the store
    if (!centerLiveQuizData) {
      handleGetCenterLiveQuizApi();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleGetCenterLiveQuizApi = async () => {
    try {
      const res = await getCenterLiveQuizData({ center_code: sessionStorage.centercode }).unwrap();
      dispatch(setCenterLiveQuizData(res));
    } catch (error) {
      setToastRes(error);
    }
  };

  const handleClearFilters = () => {
    setStartDate('');
    setEndDate('');
  };

  const filteredQuizzes = useMemo(() => {
    if (!Array.isArray(centerLiveQuizData)) return [];
    return centerLiveQuizData.filter((quiz) => {
      if (!startDate && !endDate) return true;
      const quizDate = new Date(quiz.created_at);
      const start = startDate ? new Date(`${startDate}T00:00:00`) : null;
      const end = endDate ? new Date(`${endDate}T23:59:59`) : null;
      if (start && quizDate < start) return false;
      if (end && quizDate > end) return false;
      return true;
    });
  }, [centerLiveQuizData, startDate, endDate]);

  const renderContent = () => {
    if (isLoading) return <SkeletonLoader />;

    if (isError) {
      return (
        <EmptyState
          icon={<ServerCrash size={40} />}
          title="Oops! Something went wrong."
          message="We couldn't load quiz data. Please check your connection and try again.">
          <button
            onClick={handleGetCenterLiveQuizApi}
            className="mt-6 px-5 py-2.5 bg-amber-500 text-white font-semibold rounded-lg shadow-md hover:bg-amber-600 transition-colors">
            Try Again
          </button>
        </EmptyState>
      );
    }

    if (!Array.isArray(centerLiveQuizData) || centerLiveQuizData.length === 0) {
      return (
        <EmptyState
          icon={<FileText size={40} />}
          title="No Quiz Data Available"
          message="It looks like there are no live quiz results for this center yet."
        />
      );
    }

    if (filteredQuizzes.length === 0) {
      return (
        <EmptyState
          icon={<FileSearch size={40} />}
          title="No Results Found"
          message="We couldn't find any quiz results for the selected date range. Try adjusting your filters."
        />
      );
    }

    return (
      <motion.div
        variants={{ visible: { transition: { staggerChildren: 0.15 } } }}
        initial="hidden"
        animate="visible"
        className="space-y-8">
        {filteredQuizzes.map((quiz, i) => (
          <QuizCard
            key={i}
            quiz={quiz}
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: 'easeOut' } }
            }}
          />
        ))}
      </motion.div>
    );
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <Toastify res={toastRes} resClear={() => setToastRes(null)} />

      {/* Header */}
      <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-amber-900">Center Live Quiz Results</h1>
        <p className="mt-2 text-amber-700">
          Review student performance from live quizzes conducted at this center.
        </p>
      </motion.div>

      {/* Filter Section */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="mb-8 p-4 bg-white/50 backdrop-blur-md rounded-2xl border border-amber-300/60 shadow-lg">
        <div className="flex flex-wrap items-end gap-4">
          <div className="relative flex-grow min-w-[180px]">
            <label htmlFor="startDate" className="block text-sm font-medium text-amber-800 mb-1.5">
              Start Date
            </label>
            <Calendar className="absolute left-3 top-10 w-5 h-5 text-amber-600" />
            <input
              type="date"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full pl-10 pr-3 py-2 bg-white/70 border border-amber-400/50 text-amber-900 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 sm:text-sm"
            />
          </div>
          <div className="relative flex-grow min-w-[180px]">
            <label htmlFor="endDate" className="block text-sm font-medium text-amber-800 mb-1.5">
              End Date
            </label>
            <Calendar className="absolute left-3 top-10 w-5 h-5 text-amber-600" />
            <input
              type="date"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full pl-10 pr-3 py-2 bg-white/70 border border-amber-400/50 text-amber-900 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 sm:text-sm"
            />
          </div>
          {(startDate || endDate) && (
            <div className="self-end">
              <button
                onClick={handleClearFilters}
                className="px-4 py-2 bg-amber-100 text-amber-800 rounded-lg hover:bg-amber-200 transition-colors text-sm font-medium flex items-center gap-2 border border-amber-300">
                <X size={16} /> Clear
              </button>
            </div>
          )}
        </div>
      </motion.div>

      <main>{renderContent()}</main>
    </div>
  );
};

export default CenterLiveQuiz;
