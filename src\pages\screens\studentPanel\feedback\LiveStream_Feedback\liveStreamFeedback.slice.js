
import { createSlice } from '@reduxjs/toolkit';
import { liveStreamFeedbackApi } from '../../../../../redux/api/api';

const initialState = {
  livestreamFeedbackData: null,
};

export const liveStreamFeedbackApiSlice = liveStreamFeedbackApi.injectEndpoints({
  endpoints: (builder) => ({
    getLivestreamFeedbackDetails: builder.mutation({
      query: (studentId) => ({
        url: '/api/get-livestream-feedback-details',
        method: 'POST',
        body: { student_id: studentId },
        responseHandler: async (res) => res.json(),
      }),
      transformResponse: (response) => {
        console.log('Livestream Feedback Details Response:', response);
        return response;
      },
      transformErrorResponse: (response, meta, arg) => {
        console.error('Error fetching feedback details:', response);
        return {
          status: response.status || 'UNKNOWN_ERROR',
          message: response.data?.message || 'Failed to fetch feedback details',
          data: response.data,
        };
      },
      providesTags: ['LivestreamFeedbackDetails'],
    }),
    submitLivestreamFeedback: builder.mutation({
      query: (feedbackData) => ({
        url: '/api/submit-livestream-feedback',
        method: 'POST',
        body: feedbackData,
        responseHandler: async (res) => res.json(),
      }),
      transformResponse: (response) => {
        console.log('Livestream Feedback Submission Response:', response);
        return response;
      },
      transformErrorResponse: (response, meta, arg) => {
        console.error('Error submitting feedback:', response);
        return {
          status: response.status || 'UNKNOWN_ERROR',
          message:
            response.status === 'FETCH_ERROR'
              ? 'Network error: Unable to reach the server. Please check your connection or server configuration.'
              : response.data?.message || 'Failed to submit feedback',
          data: response.data,
        };
      },
      invalidatesTags: ['LivestreamFeedbackDetails'],
    }),
  }),
});

const liveStreamFeedbackSlice = createSlice({
  name: 'livestreamFeedback',
  initialState,
  reducers: {
    setLivestreamFeedbackData: (state, action) => {
      state.livestreamFeedbackData = action.payload;
    },
  },
});

export default liveStreamFeedbackSlice.reducer;
export const { setLivestreamFeedbackData } = liveStreamFeedbackSlice.actions;
export const {
  useGetLivestreamFeedbackDetailsMutation,
  useSubmitLivestreamFeedbackMutation,
} = liveStreamFeedbackApiSlice;