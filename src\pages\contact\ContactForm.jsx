import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router';
import {
  useSubmitStudentInquiryMutation,
  useSubmitBusinessInquiryMutation,
  useSubmitSchoolHubInquiryMutation
} from './contact.slice';
import {
  Mail,
  Phone,
  User,
  BookOpen,
  Briefcase,
  Send,
  ArrowLeft,
  Check,
  Loader2,
  GraduationCap,
  Building,
  School,
  Users,
  Brain,
  Rocket,
  Target,
  BookText,
  Lightbulb,
  ChevronRight,
  Sparkles,
  Zap,
  Cpu,
  Globe,
  Database,
  Cloud,
  Code,
  MessageSquare,
  X,
  HelpCircle
} from 'lucide-react';

const ContactForm = () => {
  const navigate = useNavigate();
  const [formType, setFormType] = useState('student');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phoneNumber: '',
    classStudying: '',
    contactNumber: '',
    schoolName: '',
    message: ''
  });
  const [activeField, setActiveField] = useState(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [showHelp, setShowHelp] = useState(false);
  const formRef = useRef();

  const [submitStudentInquiry] = useSubmitStudentInquiryMutation();
  const [submitBusinessInquiry] = useSubmitBusinessInquiryMutation();
  const [submitSchoolHubInquiry] = useSubmitSchoolHubInquiryMutation();

  // Brand-themed content for Sasthra
  const content = {
    student: {
      title: 'Empower Your Learning Journey with Sasthra',
      subtitle: 'AI-powered education that adapts to your pace, style, and goals.',
      color: 'var(--color-student)',
      gradient: 'var(--color-student)',
      icon: <GraduationCap size={32} />,
      features: [
        { title: 'Personalized AI Study Plans', icon: <Brain size={18} /> },
        { title: '24/7 Expert AI Tutor Access', icon: <Users size={18} /> },
        { title: 'Progress Tracking Dashboard', icon: <Target size={18} /> },
        { title: 'Exam Prep & Practice Tests', icon: <BookText size={18} /> }
      ],
      floating: [
        <BookOpen key="1" size={20} />,
        <Zap key="2" size={20} />,
        <Cpu key="3" size={20} />
      ]
    },
    business: {
      title: 'Transform Your Workforce with Sasthra',
      subtitle: 'Scalable, measurable learning solutions for enterprise teams.',
      color: 'var(--color-counselor)',
      gradient: 'var(--color-counselor)',
      icon: <Building size={32} />,
      features: [
        { title: 'Franchise-Based Coaching Center Setup', icon: <Rocket size={18} /> },
        { title: 'Team Performance Analytics', icon: <Target size={18} /> },
        { title: 'Live Stream with Multi-Language Translation', icon: <Globe size={18} /> },
        { title: 'On-Demand Expert Sessions', icon: <Users size={18} /> }
      ],
      floating: [
        <Briefcase key="1" size={20} />,
        <Database key="2" size={20} />,
        <Globe key="3" size={20} />
      ]
    },
    schoolHub: {
      title: 'Smart School Management with Sasthra',
      subtitle: 'All-in-one platform for modern institutions and educators.',
      color: 'var(--color-teacher)',
      gradient: 'var(--color-teacher)',
      icon: <School size={32} />,
      features: [
        { title: 'Student & Teacher Portals', icon: <Users size={18} /> },
        { title: 'Automated Reporting & Insights', icon: <Target size={18} /> },
        { title: 'Curriculum Builder Tools', icon: <BookOpen size={18} /> },
        { title: 'Parent Engagement Features', icon: <Lightbulb size={18} /> }
      ],
      floating: [
        <School key="1" size={20} />,
        <Cloud key="2" size={20} />,
        <Code key="3" size={20} />
      ]
    }
  };

  const current = content[formType];

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Enforce text-only for school name
    if (name === 'schoolName') {
      const sanitized = value.replace(/[^a-zA-Z\s]/g, '');
      setFormData((prev) => ({ ...prev, [name]: sanitized }));
      return;
    }

    const maxLengths = {
      name: 80,
      email: 80,
      phoneNumber: 15,
      classStudying: 90,
      contactNumber: 15,
      schoolName: 100,
      message: 1000
    };

    if (value.length > maxLengths[name]) return;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const getCharCount = (field) => {
    const maxLengths = {
      name: 80,
      email: 80,
      phoneNumber: 15,
      classStudying: 90,
      contactNumber: 15,
      schoolName: 100,
      message: 1000
    };
    return `${formData[field]?.length || 0}/${maxLengths[field]}`;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;

    // Validate school name contains only text
    if (formType === 'schoolHub' && /\d/.test(formData.schoolName)) {
      setErrorMessage('School name should not contain numbers.');
      return;
    }

    setIsSubmitting(true);
    setErrorMessage(null);

    const payload = {
      name: formData.name,
      email: formData.email,
      description: formData.message,
      ...(formType === 'student' && {
        phoneNumber: formData.phoneNumber,
        classStudying: formData.classStudying
      }),
      ...(formType === 'business' && {
        contactNumber: formData.contactNumber
      }),
      ...(formType === 'schoolHub' && {
        schoolName: formData.schoolName,
        phoneNumber: formData.phoneNumber
      })
    };

    try {
      if (formType === 'student') {
        await submitStudentInquiry(payload).unwrap();
      } else if (formType === 'business') {
        await submitBusinessInquiry(payload).unwrap();
      } else if (formType === 'schoolHub') {
        await submitSchoolHubInquiry(payload).unwrap();
      }

      setIsSubmitted(true);
      setTimeout(() => {
        setIsSubmitted(false);
        navigate('/');
      }, 2000);
    } catch (err) {
      setErrorMessage(err?.data?.message || 'Something went wrong. Please try again.');
      setIsSubmitting(false);
    }
  };

  // Parallax effect
  const [parallax, setParallax] = useState(0);
  useEffect(() => {
    const handleScroll = () => setParallax(window.scrollY * 0.05);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex flex-col lg:flex-row relative overflow-x-hidden">
      {/* SEO-Friendly Heading */}
      <h1 className="sr-only">Contact Sasthra - AI-Powered Education Platform</h1>

      {/* Back Button */}
      <motion.button
        onClick={() => navigate('/')}
        className="absolute top-6 left-6 z-30 flex items-center gap-2 text-gray-700 hover:text-[var(--color-teacher)] transition-colors bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg border border-gray-200/50"
        whileHover={{ x: -4 }}
        whileTap={{ scale: 0.97 }}
        aria-label="Go back to home">
        <ArrowLeft size={18} />
      </motion.button>

      {/* Left Side: Sasthra Brand Story */}
      <motion.div
        className="w-full sm:h-64 md:h-80 lg:h-screen lg:w-2/5 relative flex-shrink-0 overflow-hidden"
        style={{ y: parallax }}
        aria-labelledby="sasthra-title">
        {/* Background */}
        <div
          className="absolute inset-0 transition-all duration-700"
          style={{ background: current.color }}
        />

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(30)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full hidden sm:block" // Only show particles on sm+
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${Math.random() * 8 + 2}px`,
                height: `${Math.random() * 8 + 2}px`,
                backgroundColor: '#ffffff',
                opacity: 0.12
              }}
              animate={{
                y: [0, -20, 0],
                x: [0, Math.random() * 15 - 7, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: Math.random() * 8 + 8,
                repeat: Infinity,
                delay: Math.random() * 5
              }}
            />
          ))}

          {/* Floating Icons (responsive size and visibility) */}
          {current.floating.map((Icon, i) => (
            <motion.div
              key={i}
              className="absolute text-white/20 hidden sm:block"
              style={{
                left: `${Math.random() * 90}%`,
                top: `${20 + Math.random() * 60}%`
              }}
              animate={{
                y: [0, -15, 0],
                rotate: [0, 180, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: Math.random() * 6 + 6,
                repeat: Infinity,
                ease: 'easeInOut'
              }}>
              {React.cloneElement(Icon, {
                size: window.innerWidth < 640 ? 16 : window.innerWidth < 1024 ? 18 : 20
              })}
            </motion.div>
          ))}
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center h-full p-6 sm:p-8 md:p-10 lg:p-12">
          <motion.div
            key={formType}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-5 sm:space-y-6"
            id="sasthra-title">
            {/* Icon + Title */}
            <div className="flex items-start sm:items-center gap-3 sm:gap-4">
              <motion.div
                className="p-2 sm:p-3 rounded-2xl shadow-lg flex-shrink-0"
                style={{ background: current.gradient }}
                whileHover={{ scale: 1.05, rotate: 3 }}
                transition={{ type: 'spring', stiffness: 300 }}>
                {React.cloneElement(current.icon, {
                  className: 'text-white',
                  size: window.innerWidth < 640 ? 24 : 32
                })}
              </motion.div>
              <div>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold leading-tight text-white">
                  {current.title}
                </h2>
                <p className="text-white/90 mt-1 text-sm sm:text-base">{current.subtitle}</p>
              </div>
            </div>

            {/* Features List */}
            <div>
              <h3 className="font-semibold mb-3 text-xs sm:text-sm uppercase tracking-wider text-white/70 flex items-center gap-1">
                <Sparkles size={12} /> How Sasthra Helps
              </h3>
              <ul className="space-y-2 sm:space-y-3">
                {current.features.map((item, i) => (
                  <motion.li
                    key={i}
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    transition={{ delay: 0.2 + i * 0.1 }}
                    className="overflow-hidden">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg p-2.5 sm:p-3 border border-white/10 hover:border-white/20 transition-all group cursor-pointer">
                      <div className="flex items-center gap-2.5">
                        <div
                          className="p-1.5 sm:p-2 rounded-lg flex-shrink-0 group-hover:scale-105 transition-transform"
                          style={{
                            backgroundColor: current.color,
                            boxShadow: `0 2px 6px ${current.color}30`
                          }}>
                          {React.cloneElement(item.icon, {
                            size: 16,
                            className: 'text-white'
                          })}
                        </div>
                        <span className="text-xs sm:text-sm font-medium text-white flex-1">
                          {item.title}
                        </span>
                        <ChevronRight size={14} className="text-white/50" />
                      </div>
                    </div>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Right Side: Contact Form or Loading Screen */}
      <motion.div
        className="lg:w-3/5 flex items-center justify-center p-6 lg:p-12"
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.7, delay: 0.3 }}>
        <div className="w-full max-w-xl" role="form" aria-label="Contact Sasthra">
          {/* Form Tabs */}
          <motion.div
            className="flex bg-white/80 backdrop-blur-sm p-1 rounded-2xl mb-8 shadow-lg border border-white/50"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}>
            {['student', 'business', 'schoolHub'].map((type) => (
              <button
                key={type}
                type="button"
                onClick={() => setFormType(type)}
                className={`flex-1 py-3 px-4 text-sm font-medium rounded-2xl transition-all relative ${
                  formType === type ? 'text-white' : 'text-gray-600 hover:text-gray-800'
                }`}
                style={{
                  background: formType === type ? content[type].gradient : 'transparent'
                }}
                aria-pressed={formType === type}>
                <span className="flex items-center justify-center gap-2">
                  {type === 'student' && <BookOpen size={16} />}
                  {type === 'business' && <Briefcase size={16} />}
                  {type === 'schoolHub' && <School size={16} />}
                  {type === 'student' ? 'Student' : type === 'business' ? 'Business' : 'School Hub'}
                </span>
              </button>
            ))}
          </motion.div>

          <AnimatePresence mode="wait">
            {isSubmitting ? (
              <LoadingScreen current={current} />
            ) : isSubmitted ? (
              <SuccessMessage current={current} navigate={navigate} />
            ) : (
              <motion.form
                ref={formRef}
                onSubmit={handleSubmit}
                key="form"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-6 bg-white/80 backdrop-blur-lg p-8 rounded-3xl shadow-2xl border border-white/50">
                {/* Name & Email */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <InputField
                    label="Full Name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    onFocus={() => setActiveField('name')}
                    onBlur={() => setActiveField(null)}
                    icon={<User size={18} />}
                    placeholder="Your name"
                    maxLength={80}
                    active={activeField === 'name'}
                    color={current.color}
                    charCount={getCharCount('name')}
                  />
                  <InputField
                    label="Email Address"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    onFocus={() => setActiveField('email')}
                    onBlur={() => setActiveField(null)}
                    icon={<Mail size={18} />}
                    placeholder="<EMAIL>"
                    maxLength={80}
                    active={activeField === 'email'}
                    color={current.color}
                    charCount={getCharCount('email')}
                  />
                </div>

                {/* Conditional Fields */}
                <ConditionalFields
                  formType={formType}
                  formData={formData}
                  handleChange={handleChange}
                  activeField={activeField}
                  setActiveField={setActiveField}
                  current={current}
                  getCharCount={getCharCount}
                />

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Message
                  </label>
                  <div
                    className={`flex flex-col rounded-xl px-4 py-3 border-2 transition-all ${
                      activeField === 'message'
                        ? 'border-[var(--color-teacher)] shadow-md'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      onFocus={() => setActiveField('message')}
                      onBlur={() => setActiveField(null)}
                      maxLength={1000}
                      rows={4}
                      className="w-full bg-transparent text-gray-800 placeholder-gray-400 focus:outline-none resize-none"
                      placeholder="How can Sasthra help you achieve your goals?"
                      required
                    />
                    <div className="text-right mt-1 text-xs text-gray-400">
                      {getCharCount('message')}
                    </div>
                  </div>
                </div>

                {/* Error */}
                {errorMessage && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="text-red-600 text-sm bg-red-50 p-3 rounded-lg border border-red-100 flex items-center gap-2">
                    <MessageSquare size={16} />
                    {errorMessage}
                  </motion.div>
                )}

                {/* Submit Button */}
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full py-4 rounded-xl font-medium text-white flex items-center justify-center gap-2 transition-all shadow-lg disabled:opacity-70"
                  style={{ background: current.gradient }}
                  whileHover={{
                    scale: isSubmitting ? 1 : 1.02,
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
                  }}
                  whileTap={{ scale: isSubmitting ? 1 : 0.98 }}>
                  <Send size={20} />
                  Send Message to Sasthra
                </motion.button>
              </motion.form>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Help Modal */}
      <AnimatePresence>
        {showHelp && <HelpModal setShowHelp={setShowHelp} current={current} />}
      </AnimatePresence>
    </div>
  );
};

// Loading Screen Component
const LoadingScreen = ({ current }) => (
  <motion.div
    key="loading"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center p-4">
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      className="bg-white/80 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full shadow-2xl border border-white/50 flex flex-col items-center">
      <motion.div
        className="w-20 h-20 rounded-full flex items-center justify-center mb-6 shadow-lg"
        style={{ background: current.gradient }}
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}>
        <Loader2 size={32} className="text-white" />
      </motion.div>
      <h3 className="text-xl font-semibold text-gray-800 mb-2">Submitting Your Message...</h3>
      <p className="text-gray-500 text-center">
        We're processing your inquiry. Please wait a moment.
      </p>
    </motion.div>
  </motion.div>
);

// Success Message Component
const SuccessMessage = ({ current, navigate }) => (
  <motion.div
    key="success"
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    exit={{ opacity: 0, scale: 0.9 }}
    className="text-center py-16 bg-white/80 backdrop-blur-lg rounded-3xl shadow-xl border border-white/50">
    <motion.div
      className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
      style={{ background: current.gradient }}
      animate={{ scale: [1, 1.1, 1], rotate: [0, 10, -10, 0] }}
      transition={{ repeat: Infinity, duration: 2 }}>
      <Check size={32} className="text-white" />
    </motion.div>
    <h3 className="text-2xl font-semibold text-gray-800 mb-2">Message Sent Successfully!</h3>
    <p className="text-gray-500 mb-6">
      Thank you for reaching out to <strong>Sasthra</strong>. We'll get back to you shortly.
    </p>
    <motion.button
      onClick={() => navigate('/')}
      className="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-500 text-white rounded-xl hover:from-gray-700 hover:to-gray-600 transition-colors flex items-center gap-2 mx-auto shadow-md"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}>
      Return Home <ChevronRight size={16} />
    </motion.button>
  </motion.div>
);

// Help Modal Component
const HelpModal = ({ setShowHelp, current }) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    onClick={() => setShowHelp(false)}>
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      className="bg-white rounded-2xl p-6 max-w-md w-full shadow-2xl"
      onClick={(e) => e.stopPropagation()}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Need Help?</h3>
        <button onClick={() => setShowHelp(false)} className="text-gray-500 hover:text-gray-700">
          <X size={20} />
        </button>
      </div>
      <p className="text-gray-600 mb-4">
        Fill out the form with your details and <strong>Sasthra</strong> will get back to you within
        24 hours. For urgent inquiries, please call our support line.
      </p>
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <Phone size={16} />
        <span>+****************</span>
      </div>
    </motion.div>
  </motion.div>
);

// Input Field Component
const InputField = ({ label, icon, active, color, charCount, ...props }) => (
  <div className="relative">
    <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
    <div
      className={`flex items-center rounded-xl px-4 py-3 border-2 transition-all ${
        active ? 'border-[var(--color-teacher)] shadow-md' : 'border-gray-200 hover:border-gray-300'
      }`}>
      <span className="text-gray-500 mr-3">{icon}</span>
      <input
        className="w-full bg-transparent text-gray-800 placeholder-gray-400 focus:outline-none"
        {...props}
      />
    </div>
    {charCount && (
      <div className="text-right mt-1 text-xs text-gray-400 absolute bottom-1 right-2">
        {charCount}
      </div>
    )}
  </div>
);

// Conditional Fields Component
const ConditionalFields = ({
  formType,
  formData,
  handleChange,
  activeField,
  setActiveField,
  current,
  getCharCount
}) => {
  const fields = {
    student: [
      {
        name: 'phoneNumber',
        label: 'Phone Number',
        icon: <Phone size={18} />,
        maxLength: 15,
        placeholder: 'Enter phone number'
      },
      {
        name: 'classStudying',
        label: 'Class/Grade',
        icon: <BookOpen size={18} />,
        maxLength: 90,
        placeholder: 'e.g., 10th Grade'
      }
    ],
    business: [
      {
        name: 'contactNumber',
        label: 'Contact Number',
        icon: <Phone size={18} />,
        maxLength: 15,
        placeholder: 'Enter phone number'
      }
    ],
    schoolHub: [
      {
        name: 'schoolName',
        label: 'School Name',
        icon: <School size={18} />,
        maxLength: 100,
        placeholder: 'Enter school name'
      },
      {
        name: 'phoneNumber',
        label: 'Phone Number',
        icon: <Phone size={18} />,
        maxLength: 15,
        placeholder: 'Enter phone number'
      }
    ]
  };

  const list = fields[formType];

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {list.map((field) => (
          <motion.div
            key={field.name}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">{field.label}</label>
            <div
              className={`flex items-center rounded-xl px-4 py-3 border-2 transition-all ${
                activeField === field.name
                  ? 'border-[var(--color-teacher)] shadow-md'
                  : 'border-gray-200 hover:border-gray-300'
              }`}>
              <span className="text-gray-500 mr-3">{field.icon}</span>
              <input
                type="text"
                name={field.name}
                value={formData[field.name]}
                onChange={handleChange}
                onFocus={() => setActiveField(field.name)}
                onBlur={() => setActiveField(null)}
                maxLength={field.maxLength}
                className="w-full bg-transparent text-gray-800 placeholder-gray-400 focus:outline-none"
                placeholder={field.placeholder}
                required
              />
            </div>
            <div className="text-right mt-1 text-xs text-gray-400 absolute bottom-1 right-2">
              {getCharCount(field.name)}
            </div>
          </motion.div>
        ))}
      </motion.div>
    </AnimatePresence>
  );
};

export default ContactForm;
