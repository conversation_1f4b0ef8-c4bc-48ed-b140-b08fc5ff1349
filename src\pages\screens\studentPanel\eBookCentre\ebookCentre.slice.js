import { digitalLibraryApi } from '../../../../redux/api/api';

export const ebookCentreSlice = digitalLibraryApi.injectEndpoints({
  endpoints: (builder) => ({
    getEbookCentre: builder.query({
      query: ({ course, role, studentId }) => ({
        url: `/api/content?course=${course}&role=${role}&studentId=${studentId}`,
        method: 'GET'
      }),
      providesTags: ['E-BookCentre']
    })
  })
});

export const { useLazyGetEbookCentreQuery } = ebookCentreSlice;
