import { liveStreamingApi } from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  streamingData: null,
  isStreaming: false,
  streamToken: null,
  roomName: null,
  error: null,
  chatMessages: [],
  chatLoading: false,
  chatError: null,
  isRecording: false,
  recordingData: null // This contains recording metadata and the File object (which is non-serializable)
                      // The File object is needed for upload functionality
                      // We've configured the Redux store to ignore this non-serializable value
};

export const teacherLiveStreamingSlice = liveStreamingApi.injectEndpoints({
  endpoints: (builder) => ({
    startEnhancedStream: builder.mutation({
      query: (body) => ({
        url: '/api/enhanced-stream/start',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Start Enhanced Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    stopEnhancedStream: builder.mutation({
      query: (body) => ({
        url: '/api/enhanced-stream/stop',

        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Stop Enhanced Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    sendChatMessage: builder.mutation({
      query: (body) => ({
        url: '/api/chat/send',

        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Send Chat Message Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['LiveStreaming']
    }),
    getChatHistory: builder.query({
      query: (sessionId) => `/api/chat/history/${sessionId}`,

      transformResponse: (response) => {
        console.log('Chat History Response:', response);
        return response.messages || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    uploadCbtPaper: builder.mutation({
      query: (formData) => ({
        url: '/upload_cbt_paper',

        method: 'POST',
        body: formData,
        formData: true
      }),
      transformResponse: (response) => {
        console.log('Upload CBT Paper Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    uploadRecordedVideo: builder.mutation({
      query: (formData) => ({
        url: '/api/recordings/upload',

        method: 'POST',
        body: formData,
        formData: true
      }),
      transformResponse: (response) => {
        console.log('Upload Recorded Video Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    })
  })
});

const LiveStreamingSlice = createSlice({
  name: 'liveStreaming',
  initialState,
  reducers: {
    setStreamingData: (state, action) => {
      state.streamingData = action.payload;
    },
    setIsStreaming: (state, action) => {
      state.isStreaming = action.payload;
    },
    setStreamToken: (state, action) => {
      state.streamToken = action.payload;
    },
    setRoomName: (state, action) => {
      state.roomName = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setChatMessages: (state, action) => {
      state.chatMessages = action.payload;
    },
    addChatMessage: (state, action) => {
      state.chatMessages.push(action.payload);
    },
    setChatLoading: (state, action) => {
      state.chatLoading = action.payload;
    },
    setChatError: (state, action) => {
      state.chatError = action.payload;
    },
    clearStreamingData: (state) => {
      state.streamingData = null;
      state.isStreaming = false;
      state.streamToken = null;
      state.roomName = null;
      state.error = null;
      state.chatMessages = [];
      state.chatLoading = false;
      state.chatError = null;
      state.isRecording = false;
      state.recordingData = null;
    },
    setIsRecording: (state, action) => {
      state.isRecording = action.payload;
    },
    setRecordingData: (state, action) => {
      // Store the recording data including the non-serializable File object
      // We've configured the Redux store to ignore this non-serializable value in the path liveStreaming.recordingData.file
      state.recordingData = action.payload;
      
      // Log the recording data for debugging
      console.log('📼 Setting recording data:', {
        url: action.payload?.url,
        fileName: action.payload?.file?.name,
        fileSize: action.payload?.file?.size,
        fileType: action.payload?.file?.type,
        duration: action.payload?.duration,
        timestamp: action.payload?.timestamp
      });
    }
  }
});

export const {
  setStreamingData,
  setIsStreaming,
  setStreamToken,
  setRoomName,
  setError,
  setChatMessages,
  addChatMessage,
  setChatLoading,
  setChatError,
  clearStreamingData,
  setIsRecording,
  setRecordingData
} = LiveStreamingSlice.actions;

export default LiveStreamingSlice.reducer;

export const {
  useStartEnhancedStreamMutation,
  useStopEnhancedStreamMutation,
  useSendChatMessageMutation,
  useGetChatHistoryQuery,
  useLazyGetChatHistoryQuery,
  useUploadCbtPaperMutation,
  useUploadRecordedVideoMutation
} = teacherLiveStreamingSlice;
