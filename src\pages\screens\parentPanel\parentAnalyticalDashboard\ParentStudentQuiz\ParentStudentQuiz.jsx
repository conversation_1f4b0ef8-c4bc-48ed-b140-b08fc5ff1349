import React, { useEffect, useMemo, useState } from 'react';
import {
  clearAiTutorQuizData,
  setAiTutorQuizData,
  useLazyGetStudentAiTutorQuizDataQuery
} from '../parentAnalyticsDashboard.slice';
import { useDispatch, useSelector } from 'react-redux';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import Toastify from '../../../../../components/PopUp/Toastify';

const StatCardIcon = ({ icon, color }) => {
  const icons = {
    quiz: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
        />
      </svg>
    ),
    score: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M4.26 10.147a60.437 60.437 0 0 0-.491 6.347A48.627 48.627 0 0 1 12 20.904a48.627 48.627 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.57 50.57 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0-2.172 1.909A58.21 58.21 0 0 0 5.828 15.54"
        />
      </svg>
    ),
    accuracy: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
        />
      </svg>
    )
  };
  return (
    <div className={`p-3 rounded-full ${color.bg} ${color.text}`}>
      <div className="w-6 h-6">{icons[icon]}</div>
    </div>
  );
};

const CircularProgress = ({ score, size = 80 }) => {
  const radius = size / 2 - 5;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (score / 100) * circumference;

  const barColor = (s) => {
    if (s >= 80) return 'stroke-emerald-400';
    if (s >= 60) return 'stroke-sky-400';
    if (s >= 40) return 'stroke-amber-400';
    return 'stroke-rose-400';
  };

  const textColor = (s) => {
    if (s >= 80) return 'text-emerald-400';
    if (s >= 60) return 'text-sky-400';
    if (s >= 40) return 'text-amber-400';
    return 'text-rose-400';
  };

  return (
    <div
      className="relative flex items-center justify-center"
      style={{ width: size, height: size }}>
      <svg className="absolute" width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        <circle
          className="stroke-slate-700"
          strokeWidth="6"
          fill="transparent"
          r={radius}
          cx={size / 2}
          cy={size / 2}
        />
        <motion.circle
          className={`${barColor(score)} transition-colors duration-300`}
          strokeWidth="6"
          strokeLinecap="round"
          fill="transparent"
          r={radius}
          cx={size / 2}
          cy={size / 2}
          style={{ rotate: -90, originX: '50%', originY: '50%' }}
          strokeDasharray={circumference}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset: offset }}
          transition={{ duration: 1, ease: [0.16, 1, 0.3, 1] }}
        />
      </svg>
      <div className={`text-xl font-bold ${textColor(score)}`}>
        {Math.round(score)}
        <span className="text-xs">%</span>
      </div>
    </div>
  );
};

const ParentStudentQuiz = () => {
  const [res, setRes] = useState(null);
  const [getStudentAiTutorQuizData] = useLazyGetStudentAiTutorQuizDataQuery();
  const dispatch = useDispatch();
  const studentAiTutorQuiz = useSelector((state) => state.parentAnalyticsDashboard.aiTutorQuizData);

  const quizzes = useMemo(
    () => (Array.isArray(studentAiTutorQuiz) ? studentAiTutorQuiz : []),
    [studentAiTutorQuiz]
  );

  const [subjectFilter, setSubjectFilter] = useState('All');
  const [expandedId, setExpandedId] = useState(null);

  const subjects = React.useMemo(
    () => ['All', ...Array.from(new Set(quizzes.map((q) => q?.subject).filter(Boolean)))],
    [quizzes]
  );

  const totals = React.useMemo(() => {
    const totalQuizzes = quizzes.length;
    const totalCorrect = quizzes.reduce((a, q) => a + (q?.correctCount || 0), 0);
    const totalQuestions = quizzes.reduce((a, q) => a + (q?.totalQuestions || 0), 0);
    const avgScore = totalQuizzes
      ? Math.round(quizzes.reduce((a, q) => a + (q?.score || 0), 0) / totalQuizzes)
      : 0;
    const accuracy = totalQuestions ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
    return { totalQuizzes, avgScore, accuracy };
  }, [quizzes]);

  const filtered = React.useMemo(
    () => (subjectFilter === 'All' ? quizzes : quizzes.filter((q) => q?.subject === subjectFilter)),
    [quizzes, subjectFilter]
  );

  const containerVariants = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.08 } }
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.95, y: 20 },
    show: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { type: 'spring', stiffness: 100, damping: 15 }
    }
  };

  const statusStyles = (status) => {
    switch ((status || '').toLowerCase()) {
      case 'completed':
        return 'bg-emerald-500/10 text-emerald-400';
      case 'in-progress':
        return 'bg-amber-500/10 text-amber-400';
      default:
        return 'bg-slate-500/10 text-slate-400';
    }
  };

  useEffect(() => {
    handleGetStudentAiTutorQuizApi();
  }, []);

  const handleGetStudentAiTutorQuizApi = async () => {
    try {
      const res = await getStudentAiTutorQuizData({
        user_id: sessionStorage.studentIdByParent
      }).unwrap();
      dispatch(setAiTutorQuizData(res));
    } catch (error) {
      setRes(error);
      dispatch(clearAiTutorQuizData());
    }
  };

  return (
    <div className="relative min-h-screen text-slate-100 p-4 md:p-8 isolate">
      <div
        className="absolute inset-x-0 -z-10 transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true">
        <div
          className="relative left-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-20 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
          }}
        />
      </div>
      <Toastify res={res} resClear={() => setRes(null)} />

      <header className="max-w-7xl mx-auto space-y-8">
        <div className="space-y-1">
          <h1 className="text-3xl md:text-4xl font-extrabold tracking-tight text-white text-balance">
            Quiz Performance Overview
          </h1>
          <p className="text-sm text-slate-400">
            Student:{' '}
            <span className="font-semibold text-slate-300">{quizzes[0]?.name || 'N/A'}</span> •
            Center:{' '}
            <span className="font-semibold text-slate-300">{quizzes[0]?.center_code || 'N/A'}</span>
          </p>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="show"
          className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div
            variants={itemVariants}
            className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-md shadow-lg">
            <StatCardIcon icon="quiz" color={{ bg: 'bg-indigo-500/10', text: 'text-indigo-400' }} />
            <div className="mt-4 text-4xl font-bold text-white">{totals.totalQuizzes}</div>
            <div className="text-sm font-medium text-slate-400">Total Quizzes Taken</div>
          </motion.div>
          <motion.div
            variants={itemVariants}
            className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-md shadow-lg">
            <StatCardIcon icon="score" color={{ bg: 'bg-sky-500/10', text: 'text-sky-400' }} />
            <div className="mt-4 text-4xl font-bold text-white">{totals.avgScore}%</div>
            <div className="text-sm font-medium text-slate-400">Average Score</div>
          </motion.div>
          <motion.div
            variants={itemVariants}
            className="rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-md shadow-lg">
            <StatCardIcon
              icon="accuracy"
              color={{ bg: 'bg-emerald-500/10', text: 'text-emerald-400' }}
            />
            <div className="mt-4 text-4xl font-bold text-white">{totals.accuracy}%</div>
            <div className="text-sm font-medium text-slate-400">Overall Accuracy</div>
          </motion.div>
        </motion.div>

        <div className="flex flex-wrap gap-3">
          {subjects.map((s) => {
            const active = s === subjectFilter;
            return (
              <motion.button
                key={s}
                onClick={() => setSubjectFilter(s)}
                className={`relative rounded-full px-5 py-2.5 text-sm font-semibold transition-colors duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900 focus-visible:ring-indigo-500 ${
                  active ? 'text-white' : 'text-slate-200 hover:bg-white/10'
                }`}
                aria-pressed={active}>
                {active && (
                  <motion.span
                    layoutId="filter-active"
                    className="absolute inset-0 z-0 rounded-full bg-indigo-600 shadow-md"
                    transition={{ type: 'spring', stiffness: 200, damping: 20 }}
                  />
                )}
                <span className="relative z-10">{s}</span>
              </motion.button>
            );
          })}
        </div>
      </header>

      <main className="max-w-7xl mx-auto mt-8">
        <AnimatePresence mode="wait">
          {filtered.length === 0 ? (
            <motion.div
              key="empty-state"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="rounded-2xl border-2 border-dashed border-slate-700 bg-white/5 p-12 text-center">
              <h3 className="text-lg font-semibold text-white">No Quizzes Found</h3>
              <p className="mt-1 text-sm text-slate-400">
                There are no quizzes to display
                {subjectFilter !== 'All' ? ` for the subject “${subjectFilter}”.` : '.'}
              </p>
            </motion.div>
          ) : (
            <motion.div
              key="quiz-list"
              variants={containerVariants}
              initial="hidden"
              animate="show"
              className="space-y-5">
              {filtered.map((q) => {
                const isOpen = expandedId === q.quiz_id;
                const score = Number(q?.score || 0);
                return (
                  <motion.div
                    key={q.quiz_id}
                    variants={itemVariants}
                    layout="position"
                    className="rounded-2xl border border-slate-700 bg-slate-800/50 shadow-lg overflow-hidden">
                    <div className="flex flex-col md:flex-row items-start gap-4 p-5">
                      <div className="flex-shrink-0">
                        <CircularProgress score={score} />
                      </div>
                      <div className="flex-grow space-y-3">
                        <div className="flex items-center flex-wrap gap-2">
                          <span className="inline-flex items-center rounded-md bg-slate-700 px-3 py-1 text-xs font-bold text-slate-300">
                            {q?.subject || '—'}
                          </span>
                          <span
                            className={`inline-flex items-center rounded-md px-3 py-1 text-xs font-bold ${statusStyles(q?.status)}`}>
                            {q?.status || '—'}
                          </span>
                        </div>
                        <h3 className="text-lg font-bold text-white text-pretty">
                          {q?.topic || 'Untitled Quiz'}
                          {q?.sub_topic && (
                            <span className="text-slate-400 font-normal"> • {q.sub_topic}</span>
                          )}
                        </h3>
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 text-left">
                          <StatItem label="Answered" value={q?.totalAnswered} />
                          <StatItem
                            label="Correct"
                            value={q?.correctCount}
                            color="text-emerald-400"
                          />
                          <StatItem label="Wrong" value={q?.wrongCount} color="text-rose-400" />
                          <StatItem label="Total Qs" value={q?.totalQuestions} />
                        </div>
                      </div>
                      <motion.button
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setExpandedId(isOpen ? null : q.quiz_id)}
                        className="flex-shrink-0 self-center md:self-start text-sm font-medium rounded-full bg-slate-700/50 px-4 py-2 text-slate-300 hover:bg-slate-700 transition-colors flex items-center gap-2">
                        {isOpen ? 'Hide Details' : 'View Details'}
                        <motion.div animate={{ rotate: isOpen ? 180 : 0 }}>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={2}
                            stroke="currentColor"
                            className="w-4 h-4">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="m19.5 8.25-7.5 7.5-7.5-7.5"
                            />
                          </svg>
                        </motion.div>
                      </motion.button>
                    </div>

                    <AnimatePresence>
                      {isOpen && (
                        <motion.section
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: 'easeInOut' }}
                          className="overflow-hidden">
                          <div className="border-t border-slate-700 bg-slate-900/50 p-5 text-sm">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                              <InfoItem label="Started At" value={q?.created_at} />
                              <InfoItem label="Completed At" value={q?.completed_at} />
                            </div>
                          </div>
                        </motion.section>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })}
            </motion.div>
          )}
        </AnimatePresence>
      </main>
    </div>
  );
};

const StatItem = ({ label, value, color = 'text-white' }) => (
  <div>
    <div className="text-xs text-slate-400">{label}</div>
    <div className={`text-lg font-bold ${color}`}>{value ?? '—'}</div>
  </div>
);

const InfoItem = ({ label, value, isMono = false }) => (
  <div>
    <div className="text-xs text-slate-400">{label}</div>
    <div className={`font-medium text-slate-200 ${isMono ? 'font-mono text-xs' : ''}`}>
      {value || '—'}
    </div>
  </div>
);

export default ParentStudentQuiz;
