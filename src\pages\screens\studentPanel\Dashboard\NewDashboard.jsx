import { useState } from 'react';
// eslint-disable-next-line
import { AnimatePresence, motion } from 'framer-motion';
import { LayoutDashboard } from 'lucide-react';
import Cbt from './Cbt/Cbt';
import LiveQuiz from './liveQuiz/LiveQuiz';
import StudentQuiz from './StudentQuiz/StudentQuiz';
import MarksAnalysis from './marks-analysis/MarksAnalysis';
import OpticalCharacterRecognition from './opticalCharacterRecognition/OpticalCharacterRecognition';
import OpticalMarkRecognition from './opticalMarkRecognition/OpticalMarkRecognition';

const tabItems = [
  { value: 'cbt', label: 'CBT Analytics', component: <Cbt /> },
  { value: 'livequiz', label: 'Live Quiz', component: <LiveQuiz /> },
  { value: 'studentquiz', label: 'Student Quiz', component: <StudentQuiz /> },
  { value: 'ocr', label: 'OCR', component: <OpticalCharacterRecognition /> },
  { value: 'omr', label: 'OMR', component: <OpticalMarkRecognition /> },
  { value: 'marksanalysis', label: 'Marks Analysis', component: <MarksAnalysis /> }
];

const NewDashboard = () => {
  const [activeTab, setActiveTab] = useState('cbt');

  const activeComponent = tabItems.find((tab) => tab.value === activeTab)?.component;

  return (
    <div className="min-h-screen bg-slate-50 text-slate-900 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-50 via-blue-50 to-slate-50 p-4 sm:p-6 lg:p-8">
      <div className="absolute top-0 left-0 -translate-x-1/3 -translate-y-1/3 w-[800px] h-[800px] bg-blue-400/30 rounded-full blur-3xl filter animate-blob" />
      <div className="absolute bottom-0 right-0 translate-x-1/3 translate-y-1/3 w-[800px] h-[800px] bg-indigo-400/30 rounded-full blur-3xl filter animate-blob animation-delay-2000" />
      <div className="max-w-full mx-auto">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="flex items-center gap-4 mb-8">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl text-white shadow-lg shadow-blue-500/20 border border-blue-300/50">
            <LayoutDashboard size={28} />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Dashboard Overview
            </h1>
            <p className="text-slate-500 mt-1">
              Monitor and analyze performance metrics across different views.
            </p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1, ease: 'easeOut' }}
          className="w-full flex justify-center mb-8">
          <div className="w-full sm:w-auto overflow-x-auto pb-2 -mb-2 scrollbar-hide">
            <div className="flex items-center space-x-2 bg-white/60 p-2 rounded-full border border-white/30 backdrop-blur-2xl ring-1 ring-black/5">
              {tabItems.map((tab) => (
                <button
                  key={tab.value}
                  onClick={() => setActiveTab(tab.value)}
                  className={`relative px-4 sm:px-5 py-2.5 text-sm font-semibold whitespace-nowrap rounded-full transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:ring-offset-white/80 ${
                    activeTab === tab.value
                      ? 'text-white'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                  }`}>
                  {activeTab === tab.value && (
                    <motion.div
                      layoutId="active-tab-indicator"
                      className="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full shadow-md shadow-blue-500/30"
                      transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                    />
                  )}
                  <span className="relative z-10">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        <div className="relative">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, scale: 0.98, y: 15 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.98, y: -15 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}>
              {activeComponent}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default NewDashboard;
