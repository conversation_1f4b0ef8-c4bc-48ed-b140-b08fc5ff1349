// src/redux/slices/studentPersonalDetailsSlice.js
import { studentPersonalDetailsFormApi } from '../../../../redux/api/api';

export const studentPersonalDetailsSlice = studentPersonalDetailsFormApi.injectEndpoints({
  endpoints: (builder) => ({
    addStudentPersonalDetails: builder.mutation({
      query: ({ body, token }) => ({
        url: '/student-personal-details',
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body
      }),
      transformResponse: (response) => {
        console.log('Add Student Personal Details Response:', response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error('Add Student Personal Details Error:', error);
        return error;
      },
      invalidatesTags: ['StudentPersonalDetails']
    }),
    getStudentParentDetails: builder.query({
      query: ({ token }) => ({
        url: '/student-parent-details',
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }),
      transformResponse: (response) => {
        console.log('Get Student Parent Details Response:', response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error('Get Student Parent Details Error:', error);
        return error;
      },
      providesTags: ['StudentParentDetails']
    })
  })
});

export const { useAddStudentPersonalDetailsMutation, useLazyGetStudentParentDetailsQuery } =
  studentPersonalDetailsSlice;
