import React, { useEffect, useState } from 'react';
import {
  clearLiveQuizData,
  setLiveQuizData,
  useLazyGetStudentLiveQuizDataQuery
} from '../parentAnalyticsDashboard.slice';
import { useDispatch, useSelector } from 'react-redux';
import Toastify from '../../../../../components/PopUp/Toastify';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import { RefreshCw, BarChart, CheckCircle, XCircle, Slash, Clock, Inbox } from 'lucide-react';


const StatusBadge = ({ status }) => {
  const normalized = (status || '').toLowerCase();
  const isCompleted = normalized === 'completed';
  const color = isCompleted ? 'bg-emerald-500/10 text-emerald-400' : 'bg-sky-500/10 text-sky-400';
  const label = isCompleted ? 'Completed' : status || '—';
  return (
    <span className={`inline-flex items-center rounded-md px-3 py-1 text-xs font-bold ${color}`}>
      {label}
    </span>
  );
};

const Stat = ({ label, value, icon }) => (
  <motion.div
    layout
    variants={{
      hidden: { opacity: 0, scale: 0.9 },
      show: { opacity: 1, scale: 1 }
    }}
    className="rounded-lg bg-slate-900/50 p-4 border border-slate-700/50">
    <div className="flex items-center gap-2 text-sm text-slate-400">
      {icon}
      {label}
    </div>
    <div className="mt-1 text-2xl font-bold text-white">{value}</div>
  </motion.div>
);

const SkeletonCard = () => (
  <div className="rounded-2xl border border-slate-700 bg-slate-800/50 p-6 shadow-lg animate-pulse">
    <div className="flex items-center justify-between">
      <div className="h-6 w-32 rounded bg-slate-700" />
      <div className="h-6 w-24 rounded-md bg-slate-700" />
    </div>
    <div className="mt-3 h-4 w-48 rounded bg-slate-700" />
    <div className="mt-6 grid grid-cols-2 gap-4 md:grid-cols-3">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="rounded-lg bg-slate-900/50 p-4 h-20" />
      ))}
    </div>
  </div>
);

const EmptyState = ({ onRefresh }) => (
  <div className="flex flex-col items-center justify-center rounded-2xl border-2 border-dashed border-slate-700 bg-white/5 p-12 text-center">
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ type: 'spring' }}>
      <Inbox className="mx-auto mb-4 h-16 w-16 text-slate-500" />
      <h3 className="text-xl font-bold text-white">No Live Quiz Data Found</h3>
      <p className="mx-auto mt-2 max-w-sm text-sm text-slate-400">
        When a student completes a live quiz, their results including score, accuracy, and response
        time will appear here.
      </p>
      <motion.button
        onClick={onRefresh}
        whileTap={{ scale: 0.95 }}
        className="mt-6 inline-flex items-center gap-2 justify-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-lg shadow-indigo-600/20 hover:bg-indigo-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900">
        <RefreshCw size={16} /> Check for Data
      </motion.button>
    </motion.div>
  </div>
);

const ParentLiveQuiz = () => {
  const [res, setRes] = useState(null);
  const [isLoading, setIsLoading] = useState(true); 

  const [getStudentLiveQuiz] = useLazyGetStudentLiveQuizDataQuery();
  const dispatch = useDispatch();
  const studentLiveQuizData = useSelector((state) => state.parentAnalyticsDashboard.liveQuizData);

  useEffect(() => {
    handleGetStudentLiveQuizApi();
    
  }, []);

  const handleGetStudentLiveQuizApi = async () => {
    setIsLoading(true);
    try {
      const response = await getStudentLiveQuiz({
        user_id: sessionStorage.studentIdByParent
      }).unwrap();
      dispatch(setLiveQuizData(response));
    } catch (error) {
      setRes(error);
      dispatch(clearLiveQuizData());
    } finally {
      setIsLoading(false);
    }
  };

  const hasData =
    !isLoading && Array.isArray(studentLiveQuizData) && studentLiveQuizData.length > 0;
  const showEmptyState =
    !isLoading && (!Array.isArray(studentLiveQuizData) || studentLiveQuizData.length === 0);

  return (
    <div className="relative min-h-screen text-slate-100 p-4 md:p-8 isolate">
      
      <div
        className="absolute inset-x-0 -z-10 transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true">
        <div
          className="relative left-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#14b8a6] to-[#6366f1] opacity-20 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
          }}
        />
      </div>
      <Toastify res={res} resClear={() => setRes(null)} />

      <main className="max-w-7xl mx-auto space-y-8">
       
        <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          <div>
            <h1 className="text-3xl md:text-4xl font-extrabold tracking-tight text-white text-balance">
              Live Quiz Results
            </h1>
            <p className="mt-1 text-pretty text-sm text-slate-400">
              Real-time performance summaries for the selected student.
            </p>
          </div>
          <motion.button
            onClick={handleGetStudentLiveQuizApi}
            disabled={isLoading}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center justify-center gap-2 rounded-lg bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-lg shadow-indigo-600/20 hover:bg-indigo-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900 disabled:opacity-60 disabled:cursor-not-allowed">
            <motion.div
              animate={{ rotate: isLoading ? 360 : 0 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}>
              <RefreshCw size={16} />
            </motion.div>
            {isLoading ? 'Refreshing…' : 'Refresh'}
          </motion.button>
        </div>

        
        <div className="space-y-6">
          {isLoading && <SkeletonCard />}

          {showEmptyState && <EmptyState onRefresh={handleGetStudentLiveQuizApi} />}

          {hasData && (
            <AnimatePresence>
              {studentLiveQuizData.map((item, idx) => {
                const summary = item?.student?.summary || {};
                const stats = [
                  { label: 'Score', value: summary.score ?? '—', icon: <BarChart size={16} /> },
                  {
                    label: 'Accuracy',
                    value:
                      typeof summary.accuracy_percentage === 'number'
                        ? `${summary.accuracy_percentage}%`
                        : '—',
                    icon: <BarChart size={16} />
                  },
                  {
                    label: 'Correct',
                    value: summary.correct_responses ?? '—',
                    icon: <CheckCircle size={16} className="text-emerald-400" />
                  },
                  {
                    label: 'Incorrect',
                    value: summary.incorrect_responses ?? '—',
                    icon: <XCircle size={16} className="text-rose-400" />
                  },
                  {
                    label: 'Unattempted',
                    value: summary.unattempted_questions ?? '—',
                    icon: <Slash size={16} />
                  },
                  {
                    label: 'Avg Time',
                    value:
                      typeof summary.avg_response_time_seconds === 'number'
                        ? `${summary.avg_response_time_seconds}s`
                        : '—',
                    icon: <Clock size={16} />
                  }
                ];
                return (
                  <motion.div
                    key={idx}
                    layout
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.4, type: 'spring' }}
                    className="rounded-2xl border border-slate-700 bg-slate-800/50 p-6 shadow-lg">
                    <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-start">
                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-white">{item?.subject || '—'}</h3>
                        <div className="flex flex-wrap items-center gap-3 text-sm">
                          <StatusBadge status={item?.status} />
                          <span className="text-slate-400">
                            {item?.created_at ? `Created: ${item.created_at}` : ''}
                          </span>
                        </div>
                      </div>
                      <div className="text-sm text-slate-300 sm:text-right">
                        <div>
                          <span className="text-slate-500">Center: </span>
                          <span className="font-medium">{item?.center_code || '—'}</span>
                        </div>
                        <div>
                          <span className="text-slate-500">Student: </span>
                          <span className="font-medium">{item?.student?.name || '—'}</span>
                        </div>
                      </div>
                    </div>

                    <motion.div
                      variants={{ show: { transition: { staggerChildren: 0.05 } } }}
                      initial="hidden"
                      animate="show"
                      className="mt-6 grid grid-cols-2 gap-4 md:grid-cols-3">
                      {stats.map((s, i) => (
                        <Stat key={i} label={s.label} value={s.value} icon={s.icon} />
                      ))}
                    </motion.div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          )}
        </div>
      </main>
    </div>
  );
};

export default ParentLiveQuiz;
