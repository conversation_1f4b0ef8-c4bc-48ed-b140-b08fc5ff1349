
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-toastify';
import {
  Loader2,
  Users,
  AlertCircle,
  User,
  BookOpen,
  Clock,
  BarChart2,
  MessageSquare,
  Award,
  Sparkles,
  Rocket,
  GraduationCap,
  NotebookPen,
  CalendarCheck,
  FileText,
  Mic2,
  Video,
  ChevronDown,
  Phone,
  PhoneOff,
  Smartphone,
  Signal,
  Wifi,
  Battery
} from 'lucide-react';
import {
  useDirectorMentorDashboardServiceQuery,
  useMentorSessionMutation
} from './mentorDashboard.slice';
import IncomingCall from './IncomingCall';

// Animation variants for smooth transitions
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};
const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 15
    }
  },
  hover: {
    y: -5,
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
  }
};
const statsVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
      delay: 0.2
    }
  },
  hover: { scale: 1.05 }
};
const dropdownVariants = {
  hidden: { opacity: 0, y: -10, scale: 0.95 },
  visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.2, ease: 'easeOut' } }
};

// Mobile-style Incoming call notification overlay
const IncomingCallOverlay = ({ incomingCall, onAccept, onReject, isLoading }) => {
  // Simulate time for the call interface
  const [currentTime, setCurrentTime] = useState('');
  
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      setCurrentTime(`${hours}:${minutes}`);
    };
    
    updateTime();
    const timer = setInterval(updateTime, 60000);
    return () => clearInterval(timer);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 50 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: 50 }}
      className="fixed bottom-6 right-6 w-80 bg-black rounded-3xl shadow-2xl overflow-hidden z-50"
      style={{ 
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 0.5px rgba(255, 255, 255, 0.1)'
      }}
    >
      {/* Phone Status Bar */}
      <div className="bg-black text-white px-4 py-2 flex justify-between items-center text-xs">
        <div className="flex items-center space-x-1">
          <Signal className="w-3 h-3" />
          <Wifi className="w-3 h-3" />
        </div>
        <div>{currentTime}</div>
        <Battery className="w-3 h-3" />
      </div>
      
      {/* Phone Screen */}
      <div className="bg-gradient-to-b from-indigo-900 via-purple-900 to-pink-900 min-h-96 flex flex-col items-center justify-center p-6 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-white rounded-full" />
          <div className="absolute bottom-1/4 right-1/4 w-24 h-24 border border-white rounded-full" />
        </div>
        
        {/* Vibrating Animation */}
        <motion.div
          animate={{ 
            y: [0, -5, 0, 5, 0],
            rotate: [0, 0.5, 0, -0.5, 0]
          }}
          transition={{ 
            duration: 0.8, 
            repeat: Infinity, 
            repeatType: "reverse",
            ease: "easeInOut"
          }}
          className="relative z-10 text-center"
        >
          {/* Call Icon */}
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="mb-6"
          >
            <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
              <Phone className="w-10 h-10 text-white" />
            </div>
          </motion.div>
          
          {/* Caller Info */}
          <h3 className="text-2xl font-bold text-white mb-2">Incoming Call</h3>
          <p className="text-white/80 mb-1">Student ID: {incomingCall?.student_id}</p>
          <p className="text-white/60 text-sm">Room: {incomingCall?.room_name}</p>
          
          {/* Ringing Indicator */}
          <div className="flex items-center justify-center mt-8 space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-green-400 text-sm">Ringing</span>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
          </div>
        </motion.div>
        
        {/* Call Action Buttons */}
        <div className="relative z-20 flex justify-center space-x-6 mt-12 w-full px-8">
          {/* Decline Button */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onReject}
            disabled={isLoading}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white py-4 rounded-full flex flex-col items-center justify-center gap-2 transition-all duration-200 shadow-lg disabled:opacity-50"
          >
            {isLoading ? (
              <Loader2 size={20} className="animate-spin" />
            ) : (
              <PhoneOff size={20} />
            )}
            <span className="text-sm font-medium">Decline</span>
          </motion.button>
          
          {/* Accept Button */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onAccept}
            disabled={isLoading}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white py-4 rounded-full flex flex-col items-center justify-center gap-2 transition-all duration-200 shadow-lg disabled:opacity-50"
          >
            {isLoading ? (
              <Loader2 size={20} className="animate-spin" />
            ) : (
              <Phone size={20} />
            )}
            <span className="text-sm font-medium">Accept</span>
          </motion.button>
        </div>
        
        {/* Sound Wave Animation */}
        <div className="absolute bottom-20 w-full flex justify-center space-x-1">
          {[...Array(7)].map((_, i) => (
            <motion.div
              key={i}
              className="w-1 bg-white/30 rounded-full"
              style={{ height: '4px' }}
              animate={{ 
                height: [4, Math.random() * 20 + 10, 4] 
              }}
              transition={{ 
                duration: Math.random() * 1 + 0.5, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
            />
          ))}
        </div>
      </div>
      
      {/* Phone Bottom */}
      <div className="bg-black h-4 flex justify-center">
        <div className="w-16 h-1 bg-gray-700 rounded-full mt-1"></div>
      </div>
    </motion.div>
  );
};

// Typewriter effect for loading text
const TypewriterEffect = ({ text, speed = 50, loop = false }) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayedText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, speed);
      return () => clearTimeout(timeout);
    } else if (loop) {
      const timeout = setTimeout(() => {
        setDisplayedText('');
        setCurrentIndex(0);
      }, 2000);
      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed, loop]);
  return <span>{displayedText}</span>;
};

// Star rating component
const StarIcon = ({ filled = false, half = false }) => {
  return (
    <svg
      className={`w-3 h-3 ${
        filled ? 'text-yellow-400' : half ? 'text-yellow-400/50' : 'text-gray-300'
      }`}
      fill="currentColor"
      viewBox="0 0 20 20">
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
    </svg>
  );
};

const MentorDashboard = () => {
  const { data: mentorData, isLoading, isError, error } = useDirectorMentorDashboardServiceQuery();
  const [triggerMentorSession, { isLoading: isSessionLoading }] = useMentorSessionMutation();
  const [isStatusOpen, setIsStatusOpen] = useState(false);
  const [currentStatus, setCurrentStatus] = useState('online');
  const [incomingCall, setIncomingCall] = useState(null);
  const [isCallAccepting, setIsCallAccepting] = useState(false);
  const [showIncomingCall, setShowIncomingCall] = useState(false);
  const [pollingInterval, setPollingInterval] = useState(null);
  // Create a reference to the audio element for ringtone
  const ringtoneRef = useState(() => {
    const audio = new Audio();
    audio.src = '/phone-ringtone-cabinet-356927.mp3';
    audio.loop = true;
    audio.volume = 1.0;
    return audio;
  })[0];
  const activeSessionId = sessionStorage.getItem('activeSessionId');
  // Get mentor ID from session storage (stored as user_id)
  const mentorId = sessionStorage.getItem('user_id') || sessionStorage.getItem('userId');
  const API_BASE_URL = 'https://testing.sasthra.in';
  const statusOptions = [
    {
      value: 'online',
      label: 'Online',
      color: 'bg-green-400',
      pulseColor: 'rgba(74, 222, 128, 0.7)'
    },
    {
      value: 'set_away',
      label: 'Away',
      color: 'bg-yellow-400',
      pulseColor: 'rgba(250, 204, 21, 0.7)'
    },
    {
      value: 'offline',
      label: 'Offline',
      color: 'bg-red-400',
      pulseColor: 'rgba(248, 113, 113, 0.7)'
    }
  ];
  
  // Start polling for incoming calls
  const startCallPolling = () => {
    if (pollingInterval || !mentorId) return;
    const pollForCalls = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/mentor/poll?mentor_id=${mentorId}`);
        const data = await response.json();
        console.log('Poll response:', data);
        // Check if there's an actual incoming call with call details
        if (data.incoming_call && data.room_name && data.student_id && !incomingCall && !showIncomingCall) {
          console.log('Incoming call detected:', data);
          const callData = {
            room_name: data.room_name,
            student_id: data.student_id,
            timestamp: data.timestamp
          };
          setIncomingCall(callData);
          setShowIncomingCall(true);
          // Play notification sound
          // Play ringtone
          try {
            // Reset the audio to the beginning
            ringtoneRef.currentTime = 0;
            // Play the ringtone
            const playPromise = ringtoneRef.play();
            
            if (playPromise !== undefined) {
              playPromise
                .then(() => {
                  console.log('Ringtone playing successfully');
                })
                .catch(error => {
                  console.error('Error playing ringtone:', error);
                  // Fallback to a simple beep if the ringtone fails
                  const fallbackBeep = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmMdBDuP1+3PfzAGIHPI8+KTQwsUWbXs7KZVEQc=');
                  fallbackBeep.volume = 1.0;
                  fallbackBeep.play().catch(e => console.error('Even fallback beep failed:', e));
                });
            }
          } catch (error) {
            console.error('Error setting up notification sound:', error);
          }
        }
      } catch (error) {
        console.error('Polling error:', error);
      }
    };
    const interval = setInterval(pollForCalls, 5000); // Poll every 5 seconds
    setPollingInterval(interval);
    pollForCalls(); // Initial poll
  };
  
  // Stop polling
  const stopCallPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  };
  
  // Accept incoming call
  const handleAcceptCall = async () => {
    if (!incomingCall || isCallAccepting || !mentorId) {
      console.error('Missing requirements for accepting call:', {
        incomingCall: !!incomingCall,
        isCallAccepting,
        mentorId: !!mentorId
      });
      return;
    }
    // Stop the ringtone
    ringtoneRef.pause();
    ringtoneRef.currentTime = 0;
    
    setIsCallAccepting(true);
    try {
      console.log('Accepting call with:', {
        mentor_id: mentorId,
        room_name: incomingCall.room_name
      });
      const response = await fetch(`${API_BASE_URL}/call/accept`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          mentor_id: mentorId,
          room_name: incomingCall.room_name
        })
      });
      const data = await response.json();
      if (response.ok) {
        console.log('Call accepted successfully:', data);
        // Assume data contains the LiveKit token for the mentor
        setIncomingCall({ ...incomingCall, token: data.token });
        // Hide the overlay and show the video call interface
        setShowIncomingCall(false);
        toast.success('Call accepted! Connecting to video call...', {
          position: 'top-right'
        });
        // The IncomingCall component will handle the actual video call
        // Pass the call data to it
      } else {
        throw new Error(data.error || 'Failed to accept call');
      }
    } catch (error) {
      console.error('Error accepting call:', error);
      toast.error(`Failed to accept call: ${error.message}`, {
        position: 'top-right'
      });
    } finally {
      setIsCallAccepting(false);
    }
  };
  
  // Reject incoming call
  const handleRejectCall = async () => {
    if (!incomingCall) return;
    try {
      // Stop the ringtone
      ringtoneRef.pause();
      ringtoneRef.currentTime = 0;
      
      await fetch(`${API_BASE_URL}/call/end`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ room_name: incomingCall.room_name })
      });
      toast.info('Call declined', {
        position: 'top-right'
      });
    } catch (error) {
      console.error('Error declining call:', error);
    }
    setIncomingCall(null);
    setShowIncomingCall(false);
  };
  
  // Handle call status change from IncomingCall component
  const handleCallStatusChange = (status, callData) => {
    console.log('Call status changed:', status, callData);
    if (status === 'ended' || status === 'declined') {
      setIncomingCall(null);
      setShowIncomingCall(false);
      setIsCallAccepting(false);
    }
  };
  
  const handleStatusChange = async (status) => {
    if (!activeSessionId) {
      toast.error('No active session found. Please start a session first.', {
        position: 'top-right'
      });
      setIsStatusOpen(false);
      return;
    }
    if (status !== 'online' && currentStatus !== 'online') {
      toast.error('You must be online to change to this status.', {
        position: 'top-right'
      });
      setIsStatusOpen(false);
      return;
    }
    try {
      const response = await triggerMentorSession({
        action: status,
        sessionId: activeSessionId
      }).unwrap();
      setCurrentStatus(status);
      setIsStatusOpen(false);
      toast.success(response.message || `Status updated to ${status}`, {
        position: 'top-right'
      });
      // Start/stop polling based on status
      if (status === 'online') {
        startCallPolling();
      } else {
        stopCallPolling();
      }
    } catch (error) {
      console.error('Status change error:', error);
      toast.error(
        error?.data?.error ||
          (error?.status === 401
            ? 'Unauthorized. Please log in again.'
            : 'Failed to update status. Please try again.'),
        { position: 'top-right' }
      );
    }
  };
  
  const currentStatusConfig =
    statusOptions.find((opt) => opt.value === currentStatus) || statusOptions[0];
  
  // Mock data for student progress
  const studentProgress = [
    { name: 'Alice Johnson', progress: 85, avatarColor: 'bg-pink-500' },
    { name: 'Bob Smith', progress: 72, avatarColor: 'bg-blue-500' },
    { name: 'Charlie Brown', progress: 93, avatarColor: 'bg-green-500' },
    { name: 'Diana Prince', progress: 68, avatarColor: 'bg-purple-500' }
  ];
  
  // Mock upcoming sessions
  const upcomingSessions = [
    { title: 'Advanced React Patterns', time: 'Today, 4:00 PM', status: 'upcoming' },
    { title: 'State Management Deep Dive', time: 'Wed, 4:00 PM', status: 'scheduled' },
    { title: 'Project Review Session', time: 'Fri, 4:00 PM', status: 'scheduled' }
  ];
  
  // Initialize polling when component mounts and mentor is online
  useEffect(() => {
    if (currentStatus === 'online' && mentorId) {
      startCallPolling();
    } else {
      stopCallPolling();
    }
    return () => {
      stopCallPolling();
    };
  }, [currentStatus, mentorId]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCallPolling();
    };
  }, []);
  
  // Check if mentor ID is available
  useEffect(() => {
    if (!mentorId) {
      console.warn('Mentor ID not found in session storage. Please ensure user is logged in.');
    } else {
      console.log('Mentor ID found:', mentorId);
    }
  }, [mentorId]);
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-50/30 p-4 md:p-8 overflow-hidden relative">
      {/* Floating Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-indigo-100/20 rounded-full blur-3xl"
          animate={{ x: [0, 20, 0], y: [0, 15, 0] }}
          transition={{ duration: 15, repeat: Infinity, ease: 'easeInOut' }}
        />
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-blue-100/20 rounded-full blur-3xl"
          animate={{ x: [0, -15, 0], y: [0, -10, 0] }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut', delay: 2 }}
        />
        <motion.div
          className="absolute top-1/3 right-1/3 w-40 h-40 bg-purple-100/20 rounded-full blur-3xl"
          animate={{ x: [0, 10, 0], y: [0, 20, 0] }}
          transition={{ duration: 12, repeat: Infinity, ease: 'easeInOut', delay: 4 }}
        />
      </div>
      
      {/* Floating particles */}
      {[...Array(20)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-indigo-200/30 pointer-events-none"
          style={{
            width: `${Math.random() * 10 + 5}px`,
            height: `${Math.random() * 10 + 5}px`,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`
          }}
          animate={{
            y: [0, (Math.random() - 0.5) * 100],
            x: [0, (Math.random() - 0.5) * 50],
            opacity: [0.3, 0.8, 0.3]
          }}
          transition={{
            duration: Math.random() * 20 + 10,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
        />
      ))}
      
      <motion.div
        className="max-w-7xl mx-auto relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible">
        
        {/* Header */}
        <motion.div className="flex justify-between items-center mb-8" variants={cardVariants}>
          <div>
            <motion.h1
              className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-blue-600"
              animate={{ backgroundPosition: ['0% 50%', '100% 50%'] }}
              transition={{ duration: 8, repeat: Infinity, repeatType: 'reverse', ease: 'linear' }}
              style={{ backgroundSize: '200% 200%' }}>
              Welcome, Mentor
              <Sparkles className="inline ml-2 w-6 h-6 text-yellow-400" />
            </motion.h1>
            <motion.p
              className="text-gray-500 mt-1"
              animate={{ x: [0, 2, 0] }}
              transition={{ duration: 5, repeat: Infinity, ease: 'easeInOut' }}>
              Your personalized teaching dashboard
            </motion.p>
          </div>
          
          {/* Call status indicator */}
          {currentStatus === 'online' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-lg"
            >
              <motion.div
                className="w-2 h-2 bg-green-500 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <span className="text-sm font-medium">Ready for calls</span>
            </motion.div>
          )}
        </motion.div>
        
        {/* Show warning if mentor ID is missing */}
        {!mentorId && (
          <motion.div
            className="bg-yellow-100 border border-yellow-300 text-yellow-800 px-4 py-3 rounded-lg mb-6"
            variants={cardVariants}>
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">Warning:</span>
              <span>Mentor ID not found. Please log in again to receive calls.</span>
            </div>
          </motion.div>
        )}
        
        {/* Loading State */}
        {isLoading && (
          <motion.div
            className="flex flex-col items-center justify-center h-[70vh]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}>
            <motion.div className="relative mb-8" style={{ width: 120, height: 120 }}>
              <motion.div
                className="absolute inset-0 border-4 border-[var(--color-mentor)] rounded-full"
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6 }}
              />
              <motion.div
                className="absolute top-8 left-6 w-6 h-6 bg-[var(--color-mentor)] rounded-full"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.4 }}
              />
              <motion.div
                className="absolute top-8 right-6 w-6 h-6 bg-[var(--color-mentor)] rounded-full"
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.4 }}
              />
              <motion.path
                d="M30 70 Q60 90 90 70"
                stroke="var(--color-mentor)"
                strokeWidth="4"
                fill="transparent"
                className="absolute top-14 left-6"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              />
              <motion.div
                className="absolute -top-6 left-0 right-0 mx-auto w-16 h-8 bg-[var(--color-mentor)] rounded-t-lg"
                initial={{ y: -30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.4 }}
              />
              <motion.div
                className="absolute -top-2 left-0 right-0 mx-auto w-24 h-2 bg-[var(--color-mentor)]"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 0.9, duration: 0.3 }}
              />
            </motion.div>
            <div className="w-full max-w-md px-4">
              <motion.div
                className="h-3 bg-gray-200 rounded-full overflow-hidden mb-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}>
                <motion.div
                  className="h-full bg-[var(--color-mentor)]"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: 'reverse',
                    ease: 'easeInOut'
                  }}
                />
              </motion.div>
              <div className="flex justify-between relative h-8">
                {['Profile', 'Courses', 'Students', 'Analytics'].map((item, index) => (
                  <motion.div
                    key={item}
                    className="absolute"
                    style={{ left: `${25 * index}%`, bottom: 0 }}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: [0, -15, 0], opacity: 1 }}
                    transition={{
                      delay: 0.5 + index * 0.2,
                      duration: 1.5,
                      repeat: Infinity,
                      repeatDelay: 1
                    }}>
                    <div className="flex flex-col items-center">
                      <div className="w-2 h-2 bg-[var(--color-mentor)] rounded-full mb-1" />
                      <span className="text-xs text-gray-500 whitespace-nowrap">{item}</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
            <motion.div className="mt-12 text-center">
              <motion.h3
                className="text-xl font-semibold text-gray-700 mb-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}>
                Building Your Mentor Space
              </motion.h3>
              <motion.p
                className="text-gray-500 max-w-md"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}>
                <TypewriterEffect
                  text="Loading your courses, students, and analytics..."
                  speed={50}
                  loop={true}
                />
              </motion.p>
            </motion.div>
            <motion.div
              className="mt-8 text-sm text-gray-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}>
              <motion.div
                animate={{ opacity: [0.6, 1, 0.6], y: [0, -3, 0] }}
                transition={{ duration: 8, repeat: Infinity }}>
                "Great mentors don't just teach - they inspire"
              </motion.div>
            </motion.div>
          </motion.div>
        )}
        
        {/* Error State */}
        {isError && (
          <motion.div
            className="bg-gradient-to-r from-red-100/80 to-red-50/50 backdrop-blur-md p-8 rounded-2xl shadow-lg flex flex-col items-center justify-center text-center border border-red-200/50"
            variants={statsVariants}
            initial="hidden"
            animate="visible">
            <motion.div
              className="bg-red-100 p-4 rounded-full mb-4 relative"
              animate={{ scale: [1, 1.05, 1], rotate: [0, 5, -5, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatType: 'mirror' }}>
              <AlertCircle className="h-12 w-12 text-red-600" />
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-red-300 opacity-0"
                animate={{ scale: [1, 1.8], opacity: [0.8, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
            <h3 className="text-2xl font-bold text-red-800 mb-3">Connection Error</h3>
            <p className="text-red-600 max-w-md text-lg mb-6">
              {error?.data?.error ||
                'Failed to connect to the mentor network. Please check your connection.'}
            </p>
            <motion.button
              whileHover={{ scale: 1.02, boxShadow: '0 5px 15px -5px rgba(239, 68, 68, 0.4)' }}
              whileTap={{ scale: 0.98 }}
              className="px-8 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full shadow-md font-medium flex items-center gap-2"
              onClick={() => window.location.reload()}>
              <Rocket className="w-5 h-5" />
              Retry Connection
            </motion.button>
          </motion.div>
        )}
        
        {/* Success State */}
        <AnimatePresence>
          {mentorData && !isLoading && !isError && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Profile Card */}
              <motion.div
                className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 relative w-full max-w-md mx-auto"
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-600/10 -z-10" />
                <div className="bg-[var(--color-mentor)] p-6 text-white relative">
                  <div className="absolute -right-12 -top-12 w-32 h-32 bg-white/10 rounded-full" />
                  <div className="absolute -right-8 -bottom-8 w-24 h-24 bg-white/5 rounded-full" />
                  <div className="flex flex-col sm:flex-row items-center gap-4 relative z-10">
                    <div className="relative flex-shrink-0">
                      <motion.div
                        className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-white/20 flex items-center justify-center text-2xl sm:text-3xl font-bold text-white shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.3 }}>
                        {mentorData.mendor?.first_name?.charAt(0) || 'M'}
                        {mentorData.mendor?.last_name?.charAt(0) || ''}
                      </motion.div>
                      <motion.div
                        className={`absolute bottom-0 right-0 w-5 h-5 sm:w-6 sm:h-6 ${currentStatusConfig.color} rounded-full border-2 border-white`}
                        animate={{
                          scale: [1, 1.2, 1],
                          boxShadow: [
                            `0 0 0 0 ${currentStatusConfig.pulseColor}`,
                            `0 0 0 8px ${currentStatusConfig.pulseColor.replace('0.7', '0')}`
                          ]
                        }}
                        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                      />
                    </div>
                    <div className="flex flex-col items-center sm:items-start text-center sm:text-left">
                      <h2 className="text-xl sm:text-2xl font-bold leading-tight">
                        {mentorData.mendor?.first_name || 'Mentor'}{' '}
                        {mentorData.mendor?.last_name || ''}
                      </h2>
                      <p className="text-indigo-100/90 flex items-center gap-1 mt-1 text-sm sm:text-base">
                        <GraduationCap className="w-4 h-4" />
                        Senior Mentor
                      </p>
                    </div>
                  </div>
                  {/* Status Selector */}
                  <div className="relative mt-4 w-full hover:cursor-pointer flex justify-center sm:justify-start z-30">
                    <motion.button
                      className="flex items-center gap-2 px-4 hover:cursor-pointer py-2 bg-white/20 rounded-lg text-sm font-medium text-white hover:bg-white/30 transition-colors"
                      onClick={() => setIsStatusOpen(!isStatusOpen)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      disabled={isSessionLoading}>
                      <span>{currentStatusConfig.label}</span>
                      <ChevronDown
                        className={`w-4 h-4 transition-transform ${isStatusOpen ? 'rotate-180' : ''}`}
                      />
                    </motion.button>
                    <AnimatePresence>
                      {isStatusOpen && (
                        <motion.div
                          className="absolute top-full left-0 mt-2 w-40 hover:cursor-pointer bg-white/95 backdrop-blur-md rounded-lg shadow-xl border border-gray-200/50 z-40 overflow-hidden"
                          variants={dropdownVariants}
                          initial="hidden"
                          animate="visible"
                          exit="hidden">
                          {statusOptions.map((option) => (
                            <motion.button
                              key={option.value}
                              className={`w-full flex items-center hover:cursor-pointer  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50/50 transition-colors ${
                                currentStatus === option.value ? 'bg-indigo-100/50' : ''
                              }`}
                              onClick={() => handleStatusChange(option.value)}
                              whileHover={{ x: 5 }}
                              disabled={isSessionLoading}>
                              <span className={`w-3 h-3 ${option.color} rounded-full`} />
                              {option.label}
                            </motion.button>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    <motion.div
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.3 }}>
                      <div className="p-2 bg-indigo-100/50 rounded-lg">
                        <User className="w-5 h-5 text-indigo-500" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Role</p>
                        <p className="font-medium text-gray-800">Course Mentor</p>
                      </div>
                    </motion.div>
                    <motion.div
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.3 }}>
                      <div className="p-2 bg-blue-100/50 rounded-lg">
                        <MessageSquare className="w-5 h-5 text-blue-500" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="font-medium text-gray-800">
                          {mentorData.mendor?.email || 'N/A'}
                        </p>
                      </div>
                    </motion.div>
                    <motion.div
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
                      whileHover={{ x: 5 }}
                      transition={{ duration: 0.3 }}>
                      <div className="p-2 bg-purple-100/50 rounded-lg">
                        <BookOpen className="w-5 h-5 text-purple-500" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Specialization</p>
                        <p className="font-medium text-gray-800">
                          {mentorData.mendor?.subject_name || 'N/A'}
                        </p>
                      </div>
                    </motion.div>
                  </div>
                  <motion.div
                    className="mt-6 pt-4 border-t border-gray-200/50"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  />
                </div>
              </motion.div>
              
              {/* Main Content Area */}
              <div className="lg:col-span-2 space-y-6">
                {/* Stats Grid */}
                <motion.div
                  className="grid grid-cols-2 md:grid-cols-4 gap-4"
                  variants={statsVariants}>
                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-indigo-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Students</p>
                        <p className="text-2xl font-bold text-indigo-600">42</p>
                        <motion.p
                          className="text-xs text-green-500 mt-1 flex items-center"
                          animate={{ opacity: [0.6, 1, 0.6] }}
                          transition={{ duration: 2, repeat: Infinity }}>
                          +3 this week
                        </motion.p>
                      </div>
                      <div className="p-2 bg-indigo-100/50 rounded-lg">
                        <Users className="w-6 h-6 text-indigo-600" />
                      </div>
                    </div>
                  </motion.div>
                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-blue-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Courses</p>
                        <p className="text-2xl font-bold text-blue-600">3</p>
                      </div>
                      <div className="p-2 bg-blue-100/50 rounded-lg">
                        <BookOpen className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                  </motion.div>
                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-purple-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Sessions</p>
                        <p className="text-2xl font-bold text-purple-600">18</p>
                        <p className="text-xs text-gray-400 mt-1">4 completed</p>
                      </div>
                      <div className="p-2 bg-purple-100/50 rounded-lg">
                        <Clock className="w-6 h-6 text-purple-600" />
                      </div>
                    </div>
                  </motion.div>
                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                    className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
                    <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-green-100/20 rounded-full" />
                    <div className="flex items-center justify-between relative z-10">
                      <div>
                        <p className="text-sm text-gray-500">Rating</p>
                        <p className="text-2xl font-bold text-green-600">
                          {mentorData.rating || '4.8'}
                        </p>
                        <div className="flex mt-1">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon
                              key={i}
                              filled={i < Math.floor(mentorData.rating || 4.8)}
                              half={
                                i === Math.floor(mentorData.rating || 4.8) &&
                                (mentorData.rating || 4.8) % 1 >= 0.5
                              }
                            />
                          ))}
                        </div>
                      </div>
                      <div className="p-2 bg-green-100/50 rounded-lg">
                        <Award className="w-6 h-6 text-green-600" />
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
                
                {/* Course Details Card */}
                <motion.div
                  className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 relative"
                  variants={cardVariants}
                  whileHover="hover">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-indigo-50/30 -z-10" />
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-6">
                      <h2 className="text-xl font-bold text-[var(--color-mentor)] flex items-center gap-2">
                        <NotebookPen className="text-indigo-500" />
                        Course Information
                      </h2>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                        className="px-3 py-1 bg-[var(--color-mentor)] text-white rounded-full text-xs font-medium">
                        View All
                      </motion.button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Current Course</p>
                          <p className="font-medium text-lg text-indigo-600">
                            {mentorData.mendor?.course_name || 'N/A'}
                          </p>
                        </motion.div>
                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Subject</p>
                          <p className="font-medium text-lg text-blue-600">
                            {mentorData.mendor?.subject_name || 'N/A'}
                          </p>
                        </motion.div>
                      </div>
                      <div className="space-y-4">
                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Batch Schedule</p>
                          <p className="font-medium">Mon, Wed, Fri • 4:00-6:00 PM</p>
                          <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                            <div
                              className="bg-indigo-500 h-1.5 rounded-full"
                              style={{ width: '70%' }}
                            />
                          </div>
                        </motion.div>
                        <motion.div
                          className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
                          whileHover={{ y: -3 }}>
                          <p className="text-sm text-gray-500">Next Session</p>
                          <div className="flex items-center justify-between">
                            <p className="font-medium">Tomorrow • 4:00 PM</p>
                            <motion.div
                              whileHover={{ scale: 1.1 }}
                              className="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center">
                              <CalendarCheck className="w-4 h-4 text-indigo-600" />
                            </motion.div>
                          </div>
                        </motion.div>
                      </div>
                    </div>
                    <div className="mt-6 pt-4 border-t border-gray-200/50">
                      <h3 className="text-sm font-medium text-gray-500 mb-3">Quick Actions</h3>
                      <div className="flex flex-wrap gap-3">
                        <motion.button
                          whileHover={{
                            y: -3,
                            boxShadow: '0 5px 15px -5px rgba(99, 102, 241, 0.4)'
                          }}
                          whileTap={{ scale: 0.98 }}
                          className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
                          <Mic2 className="w-4 h-4" />
                          Start Session
                        </motion.button>
                        <motion.button
                          whileHover={{
                            y: -3,
                            boxShadow: '0 5px 15px -5px rgba(59, 130, 246, 0.4)'
                          }}
                          whileTap={{ scale: 0.98 }}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          Upload Materials
                        </motion.button>
                        <motion.button
                          whileHover={{
                            y: -3,
                            boxShadow: '0 5px 15px -5px rgba(139, 92, 246, 0.4)'
                          }}
                          whileTap={{ scale: 0.98 }}
                          className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
                          <Video className="w-4 h-4" />
                          Record Lecture
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </motion.div>
                
                {/* Additional Cards Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Student Progress Card */}
                  <motion.div
                    className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 p-6"
                    variants={cardVariants}
                    whileHover="hover">
                    <h2 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <BarChart2 className="text-green-500" />
                      Student Progress
                    </h2>
                    <div className="space-y-4">
                      {studentProgress.map((student, index) => (
                        <motion.div
                          key={index}
                          className="flex items-center gap-3"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}>
                          <div
                            className={`w-10 h-10 rounded-full ${student.avatarColor} flex items-center justify-center text-white font-medium`}>
                            {student.name.charAt(0)}
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-gray-800">{student.name}</p>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full"
                                style={{ width: `${student.progress}%` }}
                              />
                            </div>
                          </div>
                          <span className="text-sm font-medium text-gray-500">
                            {student.progress}%
                          </span>
                        </motion.div>
                      ))}
                    </div>
                    <motion.button
                      whileHover={{ y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      className="mt-4 w-full py-2 text-sm font-medium text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors">
                      View All Students
                    </motion.button>
                  </motion.div>
                  
                  {/* Upcoming Sessions Card */}
                  <motion.div
                    className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 p-6"
                    variants={cardVariants}
                    whileHover="hover">
                    <h2 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Clock className="text-purple-500" />
                      Upcoming Sessions
                    </h2>
                    <div className="space-y-3">
                      {upcomingSessions.map((session, index) => (
                        <motion.div
                          key={index}
                          className={`p-3 rounded-lg border ${
                            session.status === 'upcoming'
                              ? 'bg-purple-50/50 border-purple-200'
                              : 'bg-gray-50/50 border-gray-200'
                          }`}
                          whileHover={{ scale: 1.02 }}>
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium text-gray-800">{session.title}</p>
                              <p className="text-sm text-gray-500 mt-1">{session.time}</p>
                            </div>
                            {session.status === 'upcoming' && (
                              <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">
                                Today
                              </span>
                            )}
                          </div>
                          {session.status === 'upcoming' && (
                            <motion.button
                              whileHover={{ scale: 1.03 }}
                              whileTap={{ scale: 0.98 }}
                              className="mt-2 w-full py-1.5 bg-purple-600 text-white text-sm rounded-lg font-medium">
                              Prepare Session
                            </motion.button>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          )}
        </AnimatePresence>
      </motion.div>
      
      {/* Incoming Call Overlay */}
      <AnimatePresence>
        {showIncomingCall && incomingCall && (
          <IncomingCallOverlay
            incomingCall={incomingCall}
            onAccept={handleAcceptCall}
            onReject={handleRejectCall}
            isLoading={isCallAccepting}
          />
        )}
      </AnimatePresence>
      
      {/* IncomingCall Component */}
      <IncomingCall 
        mentorId={mentorId}
        incomingCall={incomingCall}
        onCallStatusChange={handleCallStatusChange}
        showVideoCall={!showIncomingCall && incomingCall}
      />
    </div>
  );
};

export default MentorDashboard;








// import { useState, useEffect } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
// import { toast } from 'react-toastify';
// import {
//   Loader2,
//   Users,
//   AlertCircle,
//   User,
//   BookOpen,
//   Clock,
//   BarChart2,
//   MessageSquare,
//   Award,
//   Sparkles,
//   Rocket,
//   GraduationCap,
//   NotebookPen,
//   CalendarCheck,
//   FileText,
//   Mic2,
//   Video,
//   ChevronDown,
//   Phone,
//   PhoneOff
// } from 'lucide-react';
// import {
//   useDirectorMentorDashboardServiceQuery,
//   useMentorSessionMutation
// } from './mentorDashboard.slice';
// import IncomingCall from './IncomingCall';

// // Animation variants for smooth transitions
// const containerVariants = {
//   hidden: { opacity: 0 },
//   visible: {
//     opacity: 1,
//     transition: {
//       staggerChildren: 0.1,
//       delayChildren: 0.3
//     }
//   }
// };

// const cardVariants = {
//   hidden: { opacity: 0, y: 20 },
//   visible: {
//     opacity: 1,
//     y: 0,
//     transition: {
//       type: 'spring',
//       stiffness: 100,
//       damping: 15
//     }
//   },
//   hover: {
//     y: -5,
//     boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
//   }
// };

// const statsVariants = {
//   hidden: { opacity: 0, scale: 0.8 },
//   visible: {
//     opacity: 1,
//     scale: 1,
//     transition: {
//       type: 'spring',
//       stiffness: 100,
//       delay: 0.2
//     }
//   },
//   hover: { scale: 1.05 }
// };

// const dropdownVariants = {
//   hidden: { opacity: 0, y: -10, scale: 0.95 },
//   visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.2, ease: 'easeOut' } }
// };

// // Incoming call notification overlay
// const IncomingCallOverlay = ({ incomingCall, onAccept, onReject, isLoading }) => {
//   return (
//     <motion.div
//       initial={{ opacity: 0, scale: 0.8, y: 50 }}
//       animate={{ opacity: 1, scale: 1, y: 0 }}
//       exit={{ opacity: 0, scale: 0.8, y: 50 }}
//       className="fixed bottom-6 right-6 bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 z-50 max-w-sm"
//     >
//       <div className="flex items-center justify-between mb-4">
//         <h3 className="font-bold text-lg text-gray-800">Incoming Call</h3>
//         <motion.div
//           animate={{ scale: [1, 1.2, 1] }}
//           transition={{ duration: 1.5, repeat: Infinity }}
//           className="w-3 h-3 bg-green-500 rounded-full"
//         />
//       </div>
      
//       <div className="mb-4">
//         <div className="flex items-center mb-2">
//           <div className="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold">
//             S
//           </div>
//           <div className="ml-3">
//             <p className="font-medium text-gray-800">Student Call</p>
//             <p className="text-sm text-gray-500">Student ID: {incomingCall?.student_id}</p>
//           </div>
//         </div>
//         <p className="text-xs text-gray-400">Room: {incomingCall?.room_name}</p>
//       </div>

//       <div className="flex space-x-3">
//         <motion.button
//           whileHover={{ scale: 1.05 }}
//           whileTap={{ scale: 0.95 }}
//           onClick={onReject}
//           disabled={isLoading}
//           className="flex-1 bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
//         >
//           <PhoneOff size={16} />
//           Decline
//         </motion.button>
//         <motion.button
//           whileHover={{ scale: 1.05 }}
//           whileTap={{ scale: 0.95 }}
//           onClick={onAccept}
//           disabled={isLoading}
//           className="flex-1 bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
//         >
//           {isLoading ? (
//             <Loader2 size={16} className="animate-spin" />
//           ) : (
//             <Phone size={16} />
//           )}
//           Accept
//         </motion.button>
//       </div>
//     </motion.div>
//   );
// };

// // Typewriter effect for loading text
// const TypewriterEffect = ({ text, speed = 50, loop = false }) => {
//   const [displayedText, setDisplayedText] = useState('');
//   const [currentIndex, setCurrentIndex] = useState(0);

//   useEffect(() => {
//     if (currentIndex < text.length) {
//       const timeout = setTimeout(() => {
//         setDisplayedText((prev) => prev + text[currentIndex]);
//         setCurrentIndex((prev) => prev + 1);
//       }, speed);
//       return () => clearTimeout(timeout);
//     } else if (loop) {
//       const timeout = setTimeout(() => {
//         setDisplayedText('');
//         setCurrentIndex(0);
//       }, 2000);
//       return () => clearTimeout(timeout);
//     }
//   }, [currentIndex, text, speed, loop]);

//   return <span>{displayedText}</span>;
// };

// // Star rating component
// const StarIcon = ({ filled = false, half = false }) => {
//   return (
//     <svg
//       className={`w-3 h-3 ${
//         filled ? 'text-yellow-400' : half ? 'text-yellow-400/50' : 'text-gray-300'
//       }`}
//       fill="currentColor"
//       viewBox="0 0 20 20">
//       <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
//     </svg>
//   );
// };

// const MentorDashboard = () => {
//   const { data: mentorData, isLoading, isError, error } = useDirectorMentorDashboardServiceQuery();
//   const [triggerMentorSession, { isLoading: isSessionLoading }] = useMentorSessionMutation();
//   const [isStatusOpen, setIsStatusOpen] = useState(false);
//   const [currentStatus, setCurrentStatus] = useState('online');
//   const [incomingCall, setIncomingCall] = useState(null);
//   const [isCallAccepting, setIsCallAccepting] = useState(false);
//   const [showIncomingCall, setShowIncomingCall] = useState(false);
//   const [pollingInterval, setPollingInterval] = useState(null);
  
//   const activeSessionId = sessionStorage.getItem('activeSessionId');
//   // Get mentor ID from session storage (stored as user_id)
//   const mentorId = sessionStorage.getItem('user_id') || sessionStorage.getItem('userId');

//   const API_BASE_URL = 'https://testing.sasthra.in';

//   const statusOptions = [
//     {
//       value: 'online',
//       label: 'Online',
//       color: 'bg-green-400',
//       pulseColor: 'rgba(74, 222, 128, 0.7)'
//     },
//     {
//       value: 'set_away',
//       label: 'Away',
//       color: 'bg-yellow-400',
//       pulseColor: 'rgba(250, 204, 21, 0.7)'
//     },
//     {
//       value: 'offline',
//       label: 'Offline',
//       color: 'bg-red-400',
//       pulseColor: 'rgba(248, 113, 113, 0.7)'
//     }
//   ];

//   // Start polling for incoming calls
//   const startCallPolling = () => {
//     if (pollingInterval || !mentorId) return;

//     const pollForCalls = async () => {
//       try {
//         const response = await fetch(`${API_BASE_URL}/mentor/poll?mentor_id=${mentorId}`);
//         const data = await response.json();
        
//         console.log('Poll response:', data);
        
//         // Check if there's an actual incoming call with call details
//         if (data.incoming_call && data.room_name && data.student_id && !incomingCall && !showIncomingCall) {
//           console.log('Incoming call detected:', data);
//           const callData = {
//             room_name: data.room_name,
//             student_id: data.student_id,
//             timestamp: data.timestamp
//           };
          
//           setIncomingCall(callData);
//           setShowIncomingCall(true);
          
//           // Play notification sound
//           try {
//             const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmMdBDuP1+3PfzAGIHPI8+KTQwsUWbXs7KZVEQc=');
//             audio.play().catch(console.error);
//           } catch (error) {
//             console.error('Error playing notification sound:', error);
//           }
//         }
//       } catch (error) {
//         console.error('Polling error:', error);
//       }
//     };

//     const interval = setInterval(pollForCalls, 5000); // Poll every 5 seconds
//     setPollingInterval(interval);
//     pollForCalls(); // Initial poll
//   };

//   // Stop polling
//   const stopCallPolling = () => {
//     if (pollingInterval) {
//       clearInterval(pollingInterval);
//       setPollingInterval(null);
//     }
//   };

//   // Accept incoming call
//   const handleAcceptCall = async () => {
//     if (!incomingCall || isCallAccepting || !mentorId) {
//       console.error('Missing requirements for accepting call:', {
//         incomingCall: !!incomingCall,
//         isCallAccepting,
//         mentorId: !!mentorId
//       });
//       return;
//     }

//     setIsCallAccepting(true);

//     try {
//       console.log('Accepting call with:', {
//         mentor_id: mentorId,
//         room_name: incomingCall.room_name
//       });

//       const response = await fetch(`${API_BASE_URL}/call/accept`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({
//           mentor_id: mentorId,
//           room_name: incomingCall.room_name
//         })
//       });

//       const data = await response.json();
      
//       if (response.ok) {
//         console.log('Call accepted successfully:', data);
        
//         // Assume data contains the LiveKit token for the mentor
//         setIncomingCall({ ...incomingCall, token: data.token });
        
//         // Hide the overlay and show the video call interface
//         setShowIncomingCall(false);
        
//         toast.success('Call accepted! Connecting to video call...', {
//           position: 'top-right'
//         });

//         // The IncomingCall component will handle the actual video call
//         // Pass the call data to it
        
//       } else {
//         throw new Error(data.error || 'Failed to accept call');
//       }
//     } catch (error) {
//       console.error('Error accepting call:', error);
//       toast.error(`Failed to accept call: ${error.message}`, {
//         position: 'top-right'
//       });
//     } finally {
//       setIsCallAccepting(false);
//     }
//   };

//   // Reject incoming call
//   const handleRejectCall = async () => {
//     if (!incomingCall) return;

//     try {
//       await fetch(`${API_BASE_URL}/call/end`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ room_name: incomingCall.room_name })
//       });
      
//       toast.info('Call declined', {
//         position: 'top-right'
//       });
//     } catch (error) {
//       console.error('Error declining call:', error);
//     }

//     setIncomingCall(null);
//     setShowIncomingCall(false);
//   };

//   // Handle call status change from IncomingCall component
//   const handleCallStatusChange = (status, callData) => {
//     console.log('Call status changed:', status, callData);
    
//     if (status === 'ended' || status === 'declined') {
//       setIncomingCall(null);
//       setShowIncomingCall(false);
//       setIsCallAccepting(false);
//     }
//   };

//   const handleStatusChange = async (status) => {
//     if (!activeSessionId) {
//       toast.error('No active session found. Please start a session first.', {
//         position: 'top-right'
//       });
//       setIsStatusOpen(false);
//       return;
//     }

//     if (status !== 'online' && currentStatus !== 'online') {
//       toast.error('You must be online to change to this status.', {
//         position: 'top-right'
//       });
//       setIsStatusOpen(false);
//       return;
//     }

//     try {
//       const response = await triggerMentorSession({
//         action: status,
//         sessionId: activeSessionId
//       }).unwrap();
//       setCurrentStatus(status);
//       setIsStatusOpen(false);
//       toast.success(response.message || `Status updated to ${status}`, {
//         position: 'top-right'
//       });

//       // Start/stop polling based on status
//       if (status === 'online') {
//         startCallPolling();
//       } else {
//         stopCallPolling();
//       }
//     } catch (error) {
//       console.error('Status change error:', error);
//       toast.error(
//         error?.data?.error ||
//           (error?.status === 401
//             ? 'Unauthorized. Please log in again.'
//             : 'Failed to update status. Please try again.'),
//         { position: 'top-right' }
//       );
//     }
//   };

//   const currentStatusConfig =
//     statusOptions.find((opt) => opt.value === currentStatus) || statusOptions[0];

//   // Mock data for student progress
//   const studentProgress = [
//     { name: 'Alice Johnson', progress: 85, avatarColor: 'bg-pink-500' },
//     { name: 'Bob Smith', progress: 72, avatarColor: 'bg-blue-500' },
//     { name: 'Charlie Brown', progress: 93, avatarColor: 'bg-green-500' },
//     { name: 'Diana Prince', progress: 68, avatarColor: 'bg-purple-500' }
//   ];

//   // Mock upcoming sessions
//   const upcomingSessions = [
//     { title: 'Advanced React Patterns', time: 'Today, 4:00 PM', status: 'upcoming' },
//     { title: 'State Management Deep Dive', time: 'Wed, 4:00 PM', status: 'scheduled' },
//     { title: 'Project Review Session', time: 'Fri, 4:00 PM', status: 'scheduled' }
//   ];

//   // Initialize polling when component mounts and mentor is online
//   useEffect(() => {
//     if (currentStatus === 'online' && mentorId) {
//       startCallPolling();
//     } else {
//       stopCallPolling();
//     }

//     return () => {
//       stopCallPolling();
//     };
//   }, [currentStatus, mentorId]);

//   // Cleanup on unmount
//   useEffect(() => {
//     return () => {
//       stopCallPolling();
//     };
//   }, []);

//   // Check if mentor ID is available
//   useEffect(() => {
//     if (!mentorId) {
//       console.warn('Mentor ID not found in session storage. Please ensure user is logged in.');
//     } else {
//       console.log('Mentor ID found:', mentorId);
//     }
//   }, [mentorId]);

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-50/30 p-4 md:p-8 overflow-hidden relative">
//       {/* Floating Background Elements */}
//       <div className="fixed inset-0 overflow-hidden pointer-events-none">
//         <motion.div
//           className="absolute top-1/4 left-1/4 w-64 h-64 bg-indigo-100/20 rounded-full blur-3xl"
//           animate={{ x: [0, 20, 0], y: [0, 15, 0] }}
//           transition={{ duration: 15, repeat: Infinity, ease: 'easeInOut' }}
//         />
//         <motion.div
//           className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-blue-100/20 rounded-full blur-3xl"
//           animate={{ x: [0, -15, 0], y: [0, -10, 0] }}
//           transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut', delay: 2 }}
//         />
//         <motion.div
//           className="absolute top-1/3 right-1/3 w-40 h-40 bg-purple-100/20 rounded-full blur-3xl"
//           animate={{ x: [0, 10, 0], y: [0, 20, 0] }}
//           transition={{ duration: 12, repeat: Infinity, ease: 'easeInOut', delay: 4 }}
//         />
//       </div>

//       {/* Floating particles */}
//       {[...Array(20)].map((_, i) => (
//         <motion.div
//           key={i}
//           className="absolute rounded-full bg-indigo-200/30 pointer-events-none"
//           style={{
//             width: `${Math.random() * 10 + 5}px`,
//             height: `${Math.random() * 10 + 5}px`,
//             left: `${Math.random() * 100}%`,
//             top: `${Math.random() * 100}%`
//           }}
//           animate={{
//             y: [0, (Math.random() - 0.5) * 100],
//             x: [0, (Math.random() - 0.5) * 50],
//             opacity: [0.3, 0.8, 0.3]
//           }}
//           transition={{
//             duration: Math.random() * 20 + 10,
//             repeat: Infinity,
//             repeatType: 'reverse'
//           }}
//         />
//       ))}

//       <motion.div
//         className="max-w-7xl mx-auto relative z-10"
//         variants={containerVariants}
//         initial="hidden"
//         animate="visible">
//         {/* Header */}
//         <motion.div className="flex justify-between items-center mb-8" variants={cardVariants}>
//           <div>
//             <motion.h1
//               className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-blue-600"
//               animate={{ backgroundPosition: ['0% 50%', '100% 50%'] }}
//               transition={{ duration: 8, repeat: Infinity, repeatType: 'reverse', ease: 'linear' }}
//               style={{ backgroundSize: '200% 200%' }}>
//               Welcome, Mentor
//               <Sparkles className="inline ml-2 w-6 h-6 text-yellow-400" />
//             </motion.h1>
//             <motion.p
//               className="text-gray-500 mt-1"
//               animate={{ x: [0, 2, 0] }}
//               transition={{ duration: 5, repeat: Infinity, ease: 'easeInOut' }}>
//               Your personalized teaching dashboard
//             </motion.p>
//           </div>
          
//           {/* Call status indicator */}
//           {currentStatus === 'online' && (
//             <motion.div
//               initial={{ opacity: 0, scale: 0.8 }}
//               animate={{ opacity: 1, scale: 1 }}
//               className="flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-lg"
//             >
//               <motion.div
//                 className="w-2 h-2 bg-green-500 rounded-full"
//                 animate={{ scale: [1, 1.2, 1] }}
//                 transition={{ duration: 2, repeat: Infinity }}
//               />
//               <span className="text-sm font-medium">Ready for calls</span>
//             </motion.div>
//           )}
//         </motion.div>

//         {/* Show warning if mentor ID is missing */}
//         {!mentorId && (
//           <motion.div
//             className="bg-yellow-100 border border-yellow-300 text-yellow-800 px-4 py-3 rounded-lg mb-6"
//             variants={cardVariants}>
//             <div className="flex items-center gap-2">
//               <AlertCircle className="w-5 h-5" />
//               <span className="font-medium">Warning:</span>
//               <span>Mentor ID not found. Please log in again to receive calls.</span>
//             </div>
//           </motion.div>
//         )}

//         {/* Loading State */}
//         {isLoading && (
//           <motion.div
//             className="flex flex-col items-center justify-center h-[70vh]"
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             exit={{ opacity: 0 }}>
//             <motion.div className="relative mb-8" style={{ width: 120, height: 120 }}>
//               <motion.div
//                 className="absolute inset-0 border-4 border-[var(--color-mentor)] rounded-full"
//                 initial={{ scale: 0.5, opacity: 0 }}
//                 animate={{ scale: 1, opacity: 1 }}
//                 transition={{ duration: 0.6 }}
//               />
//               <motion.div
//                 className="absolute top-8 left-6 w-6 h-6 bg-[var(--color-mentor)] rounded-full"
//                 initial={{ y: -20, opacity: 0 }}
//                 animate={{ y: 0, opacity: 1 }}
//                 transition={{ delay: 0.3, duration: 0.4 }}
//               />
//               <motion.div
//                 className="absolute top-8 right-6 w-6 h-6 bg-[var(--color-mentor)] rounded-full"
//                 initial={{ y: -20, opacity: 0 }}
//                 animate={{ y: 0, opacity: 1 }}
//                 transition={{ delay: 0.4, duration: 0.4 }}
//               />
//               <motion.path
//                 d="M30 70 Q60 90 90 70"
//                 stroke="var(--color-mentor)"
//                 strokeWidth="4"
//                 fill="transparent"
//                 className="absolute top-14 left-6"
//                 initial={{ pathLength: 0 }}
//                 animate={{ pathLength: 1 }}
//                 transition={{ delay: 0.6, duration: 0.5 }}
//               />
//               <motion.div
//                 className="absolute -top-6 left-0 right-0 mx-auto w-16 h-8 bg-[var(--color-mentor)] rounded-t-lg"
//                 initial={{ y: -30, opacity: 0 }}
//                 animate={{ y: 0, opacity: 1 }}
//                 transition={{ delay: 0.8, duration: 0.4 }}
//               />
//               <motion.div
//                 className="absolute -top-2 left-0 right-0 mx-auto w-24 h-2 bg-[var(--color-mentor)]"
//                 initial={{ scaleX: 0 }}
//                 animate={{ scaleX: 1 }}
//                 transition={{ delay: 0.9, duration: 0.3 }}
//               />
//             </motion.div>

//             <div className="w-full max-w-md px-4">
//               <motion.div
//                 className="h-3 bg-gray-200 rounded-full overflow-hidden mb-4"
//                 initial={{ opacity: 0 }}
//                 animate={{ opacity: 1 }}
//                 transition={{ delay: 0.4 }}>
//                 <motion.div
//                   className="h-full bg-[var(--color-mentor)]"
//                   initial={{ width: 0 }}
//                   animate={{ width: '100%' }}
//                   transition={{
//                     duration: 2,
//                     repeat: Infinity,
//                     repeatType: 'reverse',
//                     ease: 'easeInOut'
//                   }}
//                 />
//               </motion.div>
//               <div className="flex justify-between relative h-8">
//                 {['Profile', 'Courses', 'Students', 'Analytics'].map((item, index) => (
//                   <motion.div
//                     key={item}
//                     className="absolute"
//                     style={{ left: `${25 * index}%`, bottom: 0 }}
//                     initial={{ y: 20, opacity: 0 }}
//                     animate={{ y: [0, -15, 0], opacity: 1 }}
//                     transition={{
//                       delay: 0.5 + index * 0.2,
//                       duration: 1.5,
//                       repeat: Infinity,
//                       repeatDelay: 1
//                     }}>
//                     <div className="flex flex-col items-center">
//                       <div className="w-2 h-2 bg-[var(--color-mentor)] rounded-full mb-1" />
//                       <span className="text-xs text-gray-500 whitespace-nowrap">{item}</span>
//                     </div>
//                   </motion.div>
//                 ))}
//               </div>
//             </div>

//             <motion.div className="mt-12 text-center">
//               <motion.h3
//                 className="text-xl font-semibold text-gray-700 mb-2"
//                 initial={{ opacity: 0 }}
//                 animate={{ opacity: 1 }}
//                 transition={{ delay: 0.5 }}>
//                 Building Your Mentor Space
//               </motion.h3>
//               <motion.p
//                 className="text-gray-500 max-w-md"
//                 initial={{ opacity: 0 }}
//                 animate={{ opacity: 1 }}
//                 transition={{ delay: 0.7 }}>
//                 <TypewriterEffect
//                   text="Loading your courses, students, and analytics..."
//                   speed={50}
//                   loop={true}
//                 />
//               </motion.p>
//             </motion.div>

//             <motion.div
//               className="mt-8 text-sm text-gray-400"
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               transition={{ delay: 1 }}>
//               <motion.div
//                 animate={{ opacity: [0.6, 1, 0.6], y: [0, -3, 0] }}
//                 transition={{ duration: 8, repeat: Infinity }}>
//                 "Great mentors don't just teach - they inspire"
//               </motion.div>
//             </motion.div>
//           </motion.div>
//         )}

//         {/* Error State */}
//         {isError && (
//           <motion.div
//             className="bg-gradient-to-r from-red-100/80 to-red-50/50 backdrop-blur-md p-8 rounded-2xl shadow-lg flex flex-col items-center justify-center text-center border border-red-200/50"
//             variants={statsVariants}
//             initial="hidden"
//             animate="visible">
//             <motion.div
//               className="bg-red-100 p-4 rounded-full mb-4 relative"
//               animate={{ scale: [1, 1.05, 1], rotate: [0, 5, -5, 0] }}
//               transition={{ duration: 2, repeat: Infinity, repeatType: 'mirror' }}>
//               <AlertCircle className="h-12 w-12 text-red-600" />
//               <motion.div
//                 className="absolute inset-0 rounded-full border-2 border-red-300 opacity-0"
//                 animate={{ scale: [1, 1.8], opacity: [0.8, 0] }}
//                 transition={{ duration: 2, repeat: Infinity }}
//               />
//             </motion.div>
//             <h3 className="text-2xl font-bold text-red-800 mb-3">Connection Error</h3>
//             <p className="text-red-600 max-w-md text-lg mb-6">
//               {error?.data?.error ||
//                 'Failed to connect to the mentor network. Please check your connection.'}
//             </p>
//             <motion.button
//               whileHover={{ scale: 1.02, boxShadow: '0 5px 15px -5px rgba(239, 68, 68, 0.4)' }}
//               whileTap={{ scale: 0.98 }}
//               className="px-8 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full shadow-md font-medium flex items-center gap-2"
//               onClick={() => window.location.reload()}>
//               <Rocket className="w-5 h-5" />
//               Retry Connection
//             </motion.button>
//           </motion.div>
//         )}

//         {/* Success State */}
//         <AnimatePresence>
//           {mentorData && !isLoading && !isError && (
//             <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//               {/* Profile Card */}
//               <motion.div
//                 className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 relative w-full max-w-md mx-auto"
//                 variants={cardVariants}
//                 initial="hidden"
//                 animate="visible"
//                 whileHover="hover">
//                 <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-600/10 -z-10" />
//                 <div className="bg-[var(--color-mentor)] p-6 text-white relative">
//                   <div className="absolute -right-12 -top-12 w-32 h-32 bg-white/10 rounded-full" />
//                   <div className="absolute -right-8 -bottom-8 w-24 h-24 bg-white/5 rounded-full" />
//                   <div className="flex flex-col sm:flex-row items-center gap-4 relative z-10">
//                     <div className="relative flex-shrink-0">
//                       <motion.div
//                         className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-white/20 flex items-center justify-center text-2xl sm:text-3xl font-bold text-white shadow-lg"
//                         whileHover={{ scale: 1.05 }}
//                         transition={{ duration: 0.3 }}>
//                         {mentorData.mendor?.first_name?.charAt(0) || 'M'}
//                         {mentorData.mendor?.last_name?.charAt(0) || ''}
//                       </motion.div>
//                       <motion.div
//                         className={`absolute bottom-0 right-0 w-5 h-5 sm:w-6 sm:h-6 ${currentStatusConfig.color} rounded-full border-2 border-white`}
//                         animate={{
//                           scale: [1, 1.2, 1],
//                           boxShadow: [
//                             `0 0 0 0 ${currentStatusConfig.pulseColor}`,
//                             `0 0 0 8px ${currentStatusConfig.pulseColor.replace('0.7', '0')}`
//                           ]
//                         }}
//                         transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
//                       />
//                     </div>
//                     <div className="flex flex-col items-center sm:items-start text-center sm:text-left">
//                       <h2 className="text-xl sm:text-2xl font-bold leading-tight">
//                         {mentorData.mendor?.first_name || 'Mentor'}{' '}
//                         {mentorData.mendor?.last_name || ''}
//                       </h2>
//                       <p className="text-indigo-100/90 flex items-center gap-1 mt-1 text-sm sm:text-base">
//                         <GraduationCap className="w-4 h-4" />
//                         Senior Mentor
//                       </p>
//                     </div>
//                   </div>
//                   {/* Status Selector */}
//                   <div className="relative mt-4 w-full hover:cursor-pointer flex justify-center sm:justify-start z-30">
//                     <motion.button
//                       className="flex items-center gap-2 px-4 hover:cursor-pointer py-2 bg-white/20 rounded-lg text-sm font-medium text-white hover:bg-white/30 transition-colors"
//                       onClick={() => setIsStatusOpen(!isStatusOpen)}
//                       whileHover={{ scale: 1.05 }}
//                       whileTap={{ scale: 0.95 }}
//                       disabled={isSessionLoading}>
//                       <span>{currentStatusConfig.label}</span>
//                       <ChevronDown
//                         className={`w-4 h-4 transition-transform ${isStatusOpen ? 'rotate-180' : ''}`}
//                       />
//                     </motion.button>
//                     <AnimatePresence>
//                       {isStatusOpen && (
//                         <motion.div
//                           className="absolute top-full left-0 mt-2 w-40 hover:cursor-pointer bg-white/95 backdrop-blur-md rounded-lg shadow-xl border border-gray-200/50 z-40 overflow-hidden"
//                           variants={dropdownVariants}
//                           initial="hidden"
//                           animate="visible"
//                           exit="hidden">
//                           {statusOptions.map((option) => (
//                             <motion.button
//                               key={option.value}
//                               className={`w-full flex items-center hover:cursor-pointer  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50/50 transition-colors ${
//                                 currentStatus === option.value ? 'bg-indigo-100/50' : ''
//                               }`}
//                               onClick={() => handleStatusChange(option.value)}
//                               whileHover={{ x: 5 }}
//                               disabled={isSessionLoading}>
//                               <span className={`w-3 h-3 ${option.color} rounded-full`} />
//                               {option.label}
//                             </motion.button>
//                           ))}
//                         </motion.div>
//                       )}
//                     </AnimatePresence>
//                   </div>
//                 </div>
//                 <div className="p-6">
//                   <div className="space-y-3">
//                     <motion.div
//                       className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
//                       whileHover={{ x: 5 }}
//                       transition={{ duration: 0.3 }}>
//                       <div className="p-2 bg-indigo-100/50 rounded-lg">
//                         <User className="w-5 h-5 text-indigo-500" />
//                       </div>
//                       <div>
//                         <p className="text-sm text-gray-500">Role</p>
//                         <p className="font-medium text-gray-800">Course Mentor</p>
//                       </div>
//                     </motion.div>
//                     <motion.div
//                       className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
//                       whileHover={{ x: 5 }}
//                       transition={{ duration: 0.3 }}>
//                       <div className="p-2 bg-blue-100/50 rounded-lg">
//                         <MessageSquare className="w-5 h-5 text-blue-500" />
//                       </div>
//                       <div>
//                         <p className="text-sm text-gray-500">Email</p>
//                         <p className="font-medium text-gray-800">
//                           {mentorData.mendor?.email || 'N/A'}
//                         </p>
//                       </div>
//                     </motion.div>
//                     <motion.div
//                       className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50/50 transition-colors"
//                       whileHover={{ x: 5 }}
//                       transition={{ duration: 0.3 }}>
//                       <div className="p-2 bg-purple-100/50 rounded-lg">
//                         <BookOpen className="w-5 h-5 text-purple-500" />
//                       </div>
//                       <div>
//                         <p className="text-sm text-gray-500">Specialization</p>
//                         <p className="font-medium text-gray-800">
//                           {mentorData.mendor?.subject_name || 'N/A'}
//                         </p>
//                       </div>
//                     </motion.div>
//                   </div>
//                   <motion.div
//                     className="mt-6 pt-4 border-t border-gray-200/50"
//                     initial={{ opacity: 0 }}
//                     animate={{ opacity: 1 }}
//                     transition={{ delay: 0.4 }}
//                   />
//                 </div>
//               </motion.div>

//               {/* Main Content Area */}
//               <div className="lg:col-span-2 space-y-6">
//                 {/* Stats Grid */}
//                 <motion.div
//                   className="grid grid-cols-2 md:grid-cols-4 gap-4"
//                   variants={statsVariants}>
//                   <motion.div
//                     variants={cardVariants}
//                     whileHover="hover"
//                     className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
//                     <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-indigo-100/20 rounded-full" />
//                     <div className="flex items-center justify-between relative z-10">
//                       <div>
//                         <p className="text-sm text-gray-500">Students</p>
//                         <p className="text-2xl font-bold text-indigo-600">42</p>
//                         <motion.p
//                           className="text-xs text-green-500 mt-1 flex items-center"
//                           animate={{ opacity: [0.6, 1, 0.6] }}
//                           transition={{ duration: 2, repeat: Infinity }}>
//                           +3 this week
//                         </motion.p>
//                       </div>
//                       <div className="p-2 bg-indigo-100/50 rounded-lg">
//                         <Users className="w-6 h-6 text-indigo-600" />
//                       </div>
//                     </div>
//                   </motion.div>
//                   <motion.div
//                     variants={cardVariants}
//                     whileHover="hover"
//                     className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
//                     <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-blue-100/20 rounded-full" />
//                     <div className="flex items-center justify-between relative z-10">
//                       <div>
//                         <p className="text-sm text-gray-500">Courses</p>
//                         <p className="text-2xl font-bold text-blue-600">3</p>
//                       </div>
//                       <div className="p-2 bg-blue-100/50 rounded-lg">
//                         <BookOpen className="w-6 h-6 text-blue-600" />
//                       </div>
//                     </div>
//                   </motion.div>
//                   <motion.div
//                     variants={cardVariants}
//                     whileHover="hover"
//                     className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
//                     <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-purple-100/20 rounded-full" />
//                     <div className="flex items-center justify-between relative z-10">
//                       <div>
//                         <p className="text-sm text-gray-500">Sessions</p>
//                         <p className="text-2xl font-bold text-purple-600">18</p>
//                         <p className="text-xs text-gray-400 mt-1">4 completed</p>
//                       </div>
//                       <div className="p-2 bg-purple-100/50 rounded-lg">
//                         <Clock className="w-6 h-6 text-purple-600" />
//                       </div>
//                     </div>
//                   </motion.div>
//                   <motion.div
//                     variants={cardVariants}
//                     whileHover="hover"
//                     className="bg-white/90 backdrop-blur-lg p-4 rounded-xl shadow-sm border border-gray-200/50 relative overflow-hidden">
//                     <div className="absolute -right-6 -bottom-6 w-16 h-16 bg-green-100/20 rounded-full" />
//                     <div className="flex items-center justify-between relative z-10">
//                       <div>
//                         <p className="text-sm text-gray-500">Rating</p>
//                         <p className="text-2xl font-bold text-green-600">
//                           {mentorData.rating || '4.8'}
//                         </p>
//                         <div className="flex mt-1">
//                           {[...Array(5)].map((_, i) => (
//                             <StarIcon
//                               key={i}
//                               filled={i < Math.floor(mentorData.rating || 4.8)}
//                               half={
//                                 i === Math.floor(mentorData.rating || 4.8) &&
//                                 (mentorData.rating || 4.8) % 1 >= 0.5
//                               }
//                             />
//                           ))}
//                         </div>
//                       </div>
//                       <div className="p-2 bg-green-100/50 rounded-lg">
//                         <Award className="w-6 h-6 text-green-600" />
//                       </div>
//                     </div>
//                   </motion.div>
//                 </motion.div>

//                 {/* Course Details Card */}
//                 <motion.div
//                   className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 relative"
//                   variants={cardVariants}
//                   whileHover="hover">
//                   <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-indigo-50/30 -z-10" />
//                   <div className="p-6">
//                     <div className="flex justify-between items-start mb-6">
//                       <h2 className="text-xl font-bold text-[var(--color-mentor)] flex items-center gap-2">
//                         <NotebookPen className="text-indigo-500" />
//                         Course Information
//                       </h2>
//                       <motion.button
//                         whileHover={{ scale: 1.05 }}
//                         whileTap={{ scale: 0.98 }}
//                         className="px-3 py-1 bg-[var(--color-mentor)] text-white rounded-full text-xs font-medium">
//                         View All
//                       </motion.button>
//                     </div>
//                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                       <div className="space-y-4">
//                         <motion.div
//                           className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
//                           whileHover={{ y: -3 }}>
//                           <p className="text-sm text-gray-500">Current Course</p>
//                           <p className="font-medium text-lg text-indigo-600">
//                             {mentorData.mendor?.course_name || 'N/A'}
//                           </p>
//                         </motion.div>
//                         <motion.div
//                           className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
//                           whileHover={{ y: -3 }}>
//                           <p className="text-sm text-gray-500">Subject</p>
//                           <p className="font-medium text-lg text-blue-600">
//                             {mentorData.mendor?.subject_name || 'N/A'}
//                           </p>
//                         </motion.div>
//                       </div>
//                       <div className="space-y-4">
//                         <motion.div
//                           className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
//                           whileHover={{ y: -3 }}>
//                           <p className="text-sm text-gray-500">Batch Schedule</p>
//                           <p className="font-medium">Mon, Wed, Fri • 4:00-6:00 PM</p>
//                           <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
//                             <div
//                               className="bg-indigo-500 h-1.5 rounded-full"
//                               style={{ width: '70%' }}
//                             />
//                           </div>
//                         </motion.div>
//                         <motion.div
//                           className="p-3 bg-white rounded-lg border border-gray-200/50 shadow-xs"
//                           whileHover={{ y: -3 }}>
//                           <p className="text-sm text-gray-500">Next Session</p>
//                           <div className="flex items-center justify-between">
//                             <p className="font-medium">Tomorrow • 4:00 PM</p>
//                             <motion.div
//                               whileHover={{ scale: 1.1 }}
//                               className="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center">
//                               <CalendarCheck className="w-4 h-4 text-indigo-600" />
//                             </motion.div>
//                           </div>
//                         </motion.div>
//                       </div>
//                     </div>
//                     <div className="mt-6 pt-4 border-t border-gray-200/50">
//                       <h3 className="text-sm font-medium text-gray-500 mb-3">Quick Actions</h3>
//                       <div className="flex flex-wrap gap-3">
//                         <motion.button
//                           whileHover={{
//                             y: -3,
//                             boxShadow: '0 5px 15px -5px rgba(99, 102, 241, 0.4)'
//                           }}
//                           whileTap={{ scale: 0.98 }}
//                           className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
//                           <Mic2 className="w-4 h-4" />
//                           Start Session
//                         </motion.button>
//                         <motion.button
//                           whileHover={{
//                             y: -3,
//                             boxShadow: '0 5px 15px -5px rgba(59, 130, 246, 0.4)'
//                           }}
//                           whileTap={{ scale: 0.98 }}
//                           className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
//                           <FileText className="w-4 h-4" />
//                           Upload Materials
//                         </motion.button>
//                         <motion.button
//                           whileHover={{
//                             y: -3,
//                             boxShadow: '0 5px 15px -5px rgba(139, 92, 246, 0.4)'
//                           }}
//                           whileTap={{ scale: 0.98 }}
//                           className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium flex items-center gap-2">
//                           <Video className="w-4 h-4" />
//                           Record Lecture
//                         </motion.button>
//                       </div>
//                     </div>
//                   </div>
//                 </motion.div>

//                 {/* Additional Cards Row */}
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                   {/* Student Progress Card */}
//                   <motion.div
//                     className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 p-6"
//                     variants={cardVariants}
//                     whileHover="hover">
//                     <h2 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
//                       <BarChart2 className="text-green-500" />
//                       Student Progress
//                     </h2>
//                     <div className="space-y-4">
//                       {studentProgress.map((student, index) => (
//                         <motion.div
//                           key={index}
//                           className="flex items-center gap-3"
//                           initial={{ opacity: 0, x: -10 }}
//                           animate={{ opacity: 1, x: 0 }}
//                           transition={{ delay: index * 0.1 }}>
//                           <div
//                             className={`w-10 h-10 rounded-full ${student.avatarColor} flex items-center justify-center text-white font-medium`}>
//                             {student.name.charAt(0)}
//                           </div>
//                           <div className="flex-1">
//                             <p className="font-medium text-gray-800">{student.name}</p>
//                             <div className="w-full bg-gray-200 rounded-full h-2">
//                               <div
//                                 className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full"
//                                 style={{ width: `${student.progress}%` }}
//                               />
//                             </div>
//                           </div>
//                           <span className="text-sm font-medium text-gray-500">
//                             {student.progress}%
//                           </span>
//                         </motion.div>
//                       ))}
//                     </div>
//                     <motion.button
//                       whileHover={{ y: -2 }}
//                       whileTap={{ scale: 0.98 }}
//                       className="mt-4 w-full py-2 text-sm font-medium text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors">
//                       View All Students
//                     </motion.button>
//                   </motion.div>

//                   {/* Upcoming Sessions Card */}
//                   <motion.div
//                     className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-gray-200/50 p-6"
//                     variants={cardVariants}
//                     whileHover="hover">
//                     <h2 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
//                       <Clock className="text-purple-500" />
//                       Upcoming Sessions
//                     </h2>
//                     <div className="space-y-3">
//                       {upcomingSessions.map((session, index) => (
//                         <motion.div
//                           key={index}
//                           className={`p-3 rounded-lg border ${
//                             session.status === 'upcoming'
//                               ? 'bg-purple-50/50 border-purple-200'
//                               : 'bg-gray-50/50 border-gray-200'
//                           }`}
//                           whileHover={{ scale: 1.02 }}>
//                           <div className="flex justify-between items-start">
//                             <div>
//                               <p className="font-medium text-gray-800">{session.title}</p>
//                               <p className="text-sm text-gray-500 mt-1">{session.time}</p>
//                             </div>
//                             {session.status === 'upcoming' && (
//                               <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">
//                                 Today
//                               </span>
//                             )}
//                           </div>
//                           {session.status === 'upcoming' && (
//                             <motion.button
//                               whileHover={{ scale: 1.03 }}
//                               whileTap={{ scale: 0.98 }}
//                               className="mt-2 w-full py-1.5 bg-purple-600 text-white text-sm rounded-lg font-medium">
//                               Prepare Session
//                             </motion.button>
//                           )}
//                         </motion.div>
//                       ))}
//                     </div>
//                   </motion.div>
//                 </div>
//               </div>
//             </div>
//           )}
//         </AnimatePresence>
//       </motion.div>

//       {/* Incoming Call Overlay */}
//       <AnimatePresence>
//         {showIncomingCall && incomingCall && (
//           <IncomingCallOverlay
//             incomingCall={incomingCall}
//             onAccept={handleAcceptCall}
//             onReject={handleRejectCall}
//             isLoading={isCallAccepting}
//           />
//         )}
//       </AnimatePresence>

//       {/* IncomingCall Component */}
//       <IncomingCall 
//         mentorId={mentorId}
//         incomingCall={incomingCall}
//         onCallStatusChange={handleCallStatusChange}
//         showVideoCall={!showIncomingCall && incomingCall}
//       />
//     </div>
//   );
// };

// export default MentorDashboard;