import React from 'react'; // Removed unused useState
import { useLocation, useNavigate, matchPath } from 'react-router'; // Corrected: Should be 'react-router-dom'
import SideBar from './SideBar';
import TopBar from './TopBar';
import ChatBotSystem from '../../pages/screens/studentPanel/chatSupport/ChatBotSystem';
// eslint-disable-next-line
import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';

const Navbar = ({ children }) => {

  const userRole = sessionStorage.getItem('role');
  const location = useLocation();
  const navigate = useNavigate();

  
  const hiddenNavPaths = [
    // Your paths here...
  ];

  const backButtonPaths = [
    // Your paths here...
  ];

  const isHidden = hiddenNavPaths.some((path) =>
    matchPath({ path, exact: true }, location.pathname)
  );
  const showBackButton = backButtonPaths.some((path) =>
    matchPath({ path, exact: true }, location.pathname)
  );

  const isNavVisible = !isHidden;

  

  return (
    <div className="h-screen flex bg-gray-100">
      
      {isNavVisible && <SideBar />}
      
      <div className="flex-1 flex flex-col overflow-hidden">
        
        {isNavVisible && <TopBar />}

        
        <div className="flex-grow overflow-y-auto relative">
          
          {!isNavVisible && showBackButton && (
            <motion.div
              className="absolute top-4 left-4 z-50" 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, ease: 'easeOut' }}>
              <motion.button
                onClick={() => navigate('/sasthra')}
                className="group flex items-center gap-2 px-4 py-2 bg-white rounded-lg shadow-md text-gray-700 font-semibold border-none transition-all duration-300 cursor-pointer"
                whileHover={{ scale: 1.05, backgroundColor: 'rgb(249 250 251)' }}
                whileTap={{ scale: 0.95 }}
                aria-label="Go back">
                <ArrowLeft
                  size={20}
                  className="text-gray-500 transition-transform duration-300 group-hover:-translate-x-1"
                />
                Back
              </motion.button>
            </motion.div>
          )}

          
          <main className="">{children}</main>
        </div>
      </div>
      
      {userRole === 'student' && isNavVisible && (
        <div className="fixed bottom-5 right-5 z-50">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: 'easeOut', delay: 0.1 }}>
            <ChatBotSystem />
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default Navbar;
