import { useEffect, useState, useMemo } from 'react';
// eslint-disable-next-line
import { motion, AnimatePresence } from 'framer-motion';
import { useDispatch, useSelector } from 'react-redux';
import {
  setLeaderBoardData,
  setStudentsData,
  useLazyGetLeaderBoardDataByCenterIdQuery,
  useLazyGetStudentsDataByCenterIdQuery
} from '../AnalyticalDashboard.slice';
import { useNavigate } from 'react-router';
import {
  TrendingUp,
  Users,
  X,
  Search,
  ChevronDown,
  FileChartPie,
  Crown,
  BarChart4
} from 'lucide-react';

// --- Skeleton Loaders ---
const SkeletonLoader = ({ count = 5, type = 'list' }) => {
  if (type === 'card') {
    return (
      <>
        <div className="rounded-2xl bg-amber-200/50 h-44 animate-pulse"></div>
        <div className="rounded-2xl bg-amber-200/50 h-44 animate-pulse"></div>
        <div className="lg:col-span-2 md:col-span-2 rounded-2xl bg-amber-200/50 h-44 animate-pulse"></div>
      </>
    );
  }
  return (
    <div className="space-y-3">
      {[...Array(count)].map((_, i) => (
        <div key={i} className="flex items-center p-2">
          <div className="w-8 h-8 bg-amber-300/50 rounded-md mr-4" />
          <div className="w-10 h-10 bg-amber-300/50 rounded-full mr-3" />
          <div className="flex-grow h-4 bg-amber-300/50 rounded" />
          <div className="w-16 h-8 bg-amber-300/50 rounded-full ml-4" />
        </div>
      ))}
    </div>
  );
};

// --- Main Component ---
const AnalyticalDashboard = () => {
  const [selectedCourse, setSelectedCourse] = useState('All');
  const [selectedBatch, setSelectedBatch] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [getStudentsData] = useLazyGetStudentsDataByCenterIdQuery();
  const [getLeaderBoardData] = useLazyGetLeaderBoardDataByCenterIdQuery();

  const studentsData = useSelector((state) => state.centreCounselorDashboard.studentsData);
  const leaderBoardData = useSelector((state) => state.centreCounselorDashboard.leaderBoardData);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      await Promise.all([handleGetStudentsDataByCenterId(), handleGetLeaderBoardDataByCenterId()]);
      setIsLoading(false);
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleGetStudentsDataByCenterId = async () => {
    try {
      const data = await getStudentsData({ center_code: sessionStorage.centercode }).unwrap();
      dispatch(setStudentsData(data.data));
    } catch (error) {
      console.error('Error fetching studentsData:', error);
    }
  };

  const handleGetLeaderBoardDataByCenterId = async () => {
    try {
      const data = await getLeaderBoardData({ center_code: sessionStorage.centercode }).unwrap();
      dispatch(setLeaderBoardData(data));
    } catch (error) {
      console.error('Error fetching leaderBoard data:', error);
    }
  };

  const statCards = [
    {
      icon: <TrendingUp size={24} />,
      title: 'Performance Score',
      value: '85%',
      description: 'Avg. score across all topics'
    },
    {
      icon: <Users size={24} />,
      title: 'Active Students',
      value: studentsData?.length || '0',
      description: 'Total students in this center'
    }
  ];

  // Memoized filter options & data
  const courses = useMemo(
    () => ['All', ...new Set(studentsData?.map((s) => s.course).filter(Boolean))],
    [studentsData]
  );
  const batches = useMemo(
    () => ['All', 'No Batch', ...new Set(studentsData?.map((s) => s.batchname).filter(Boolean))],
    [studentsData]
  );

  const filteredStudents = useMemo(() => {
    if (!Array.isArray(studentsData)) return [];
    return studentsData.filter((student) => {
      const searchMatch = student.name.toLowerCase().includes(searchTerm.toLowerCase());
      const courseMatch = selectedCourse === 'All' || student.course === selectedCourse;
      const batchMatch =
        selectedBatch === 'All'
          ? true
          : selectedBatch === 'No Batch'
            ? !student.batchname
            : student.batchname === selectedBatch;
      return searchMatch && courseMatch && batchMatch;
    });
  }, [studentsData, searchTerm, selectedCourse, selectedBatch]);

  useEffect(() => setCurrentPage(1), [searchTerm, selectedCourse, selectedBatch]);

  const totalPages = Math.ceil(filteredStudents.length / 10);
  const paginatedStudents = useMemo(() => {
    const startIndex = (currentPage - 1) * 10;
    return filteredStudents.slice(startIndex, startIndex + 10);
  }, [filteredStudents, currentPage]);

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCourse('All');
    setSelectedBatch('All');
  };
  const hasActiveFilters = searchTerm !== '' || selectedCourse !== 'All' || selectedBatch !== 'All';

  // --- Animation Variants ---
  const containerVariants = { hidden: {}, visible: { transition: { staggerChildren: 0.1 } } };
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.5, ease: 'easeOut' } }
  };

  return (
    // The parent component now handles the main background. This component is transparent on top.
    <div className="p-4 sm:p-6 lg:p-8">
      {/* Stats Grid & Leaderboard */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {isLoading ? (
          <SkeletonLoader type="card" />
        ) : (
          <>
            {statCards.map((item, index) => (
              <StatCard key={index} {...item} />
            ))}
            <Leaderboard topThree={leaderBoardData?.topThree} />
          </>
        )}
      </motion.div>

      {/* Filters and Table Section */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        animate="visible"
        transition={{ delay: 0.3 }}
        className="mt-12 bg-white/50 backdrop-blur-md rounded-2xl border border-amber-300/60 shadow-lg overflow-hidden">
        {/* Control Bar */}
        <div className="p-4 border-b border-amber-300/60">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <h2 className="text-xl font-semibold text-amber-900">Student Records</h2>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap">
              <FilterInput
                icon={<Search size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by Student Name"
              />
              <FilterSelect
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                options={courses}
              />
              <FilterSelect
                value={selectedBatch}
                onChange={(e) => setSelectedBatch(e.target.value)}
                options={batches}
              />
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="flex items-center gap-1.5 text-sm text-red-600 hover:text-red-700 font-medium p-2 rounded-lg hover:bg-red-500/10 transition-colors">
                  <X size={14} /> Clear
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <AnimatePresence mode="wait">
            {isLoading ? (
              <div className="p-4">
                <SkeletonLoader count={10} />
              </div>
            ) : paginatedStudents.length === 0 ? (
              <motion.div
                key="no-results"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-16">
                <Search size={48} className="mx-auto text-amber-500 mb-4" />
                <h3 className="text-lg font-medium text-amber-800">No Students Found</h3>
                <p className="text-amber-600">
                  Your search and filter combination yielded no results.
                </p>
              </motion.div>
            ) : (
              <motion.table
                key="results-table"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="w-full min-w-[900px]">
                <thead className="bg-amber-200/40">
                  <tr>
                    {['Name', 'Course', 'Gender', 'Grade', 'Batch Name', 'Analytics'].map((h) => (
                      <th
                        key={h}
                        className="px-6 py-3 text-left text-xs font-semibold text-amber-700 uppercase tracking-wider">
                        {h}
                      </th>
                    ))}
                  </tr>
                </thead>
                <motion.tbody
                  variants={{ visible: { transition: { staggerChildren: 0.03 } } }}
                  initial="hidden"
                  animate="visible"
                  className="divide-y divide-amber-300/60">
                  {paginatedStudents.map((student) => (
                    <StudentRow key={student.id} student={student} navigate={navigate} />
                  ))}
                </motion.tbody>
              </motion.table>
            )}
          </AnimatePresence>
        </div>

        {/* Pagination */}
        <div className="bg-amber-200/40 px-6 py-3 border-t border-amber-300/60">
          <div className="flex items-center justify-between">
            <p className="text-sm text-amber-700">
              Showing{' '}
              <span className="font-bold text-amber-900">
                {paginatedStudents.length > 0 ? (currentPage - 1) * 10 + 1 : 0}
              </span>
              -
              <span className="font-bold text-amber-900">
                {Math.min(currentPage * 10, filteredStudents.length)}
              </span>{' '}
              of <span className="font-bold text-amber-900">{filteredStudents.length}</span>
            </p>
            <div className="flex gap-2">
              <PaginationButton
                onClick={() => setCurrentPage((p) => p - 1)}
                disabled={currentPage === 1}>
                Previous
              </PaginationButton>
              <PaginationButton
                onClick={() => setCurrentPage((p) => p + 1)}
                disabled={currentPage === totalPages || totalPages === 0}>
                Next
              </PaginationButton>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

// --- Reusable Sub-components ---
const StatCard = ({ icon, title, value, description }) => (
  <motion.div whileHover={{ y: -6, scale: 1.03 }} className="group cursor-pointer">
    <div className="relative overflow-hidden bg-white/50 backdrop-blur-md rounded-2xl shadow-lg border border-amber-300/60 p-6 h-full transition-all duration-300 group-hover:border-amber-500/50 group-hover:shadow-amber-500/10">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-amber-800">{title}</h3>
        <motion.div
          whileHover={{ rotate: 15, scale: 1.1 }}
          className="p-3 bg-amber-100 rounded-xl text-amber-500">
          {icon}
        </motion.div>
      </div>
      <p className="text-4xl font-bold bg-gradient-to-r from-amber-500 to-orange-600 bg-clip-text text-transparent mb-2">
        {value}
      </p>
      <p className="text-sm text-amber-700">{description}</p>
    </div>
  </motion.div>
);

const Leaderboard = ({ topThree }) => (
  <motion.div className="lg:col-span-2 md:col-span-2 bg-white/50 backdrop-blur-md rounded-2xl shadow-lg border border-amber-300/60 p-6">
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-lg font-semibold text-amber-800">Top Performers</h3>
      <span className="text-sm font-semibold text-orange-800 bg-orange-500/10 px-3 py-1 rounded-full border border-orange-500/20">
        Top 3
      </span>
    </div>
    {!topThree ? (
      <SkeletonLoader count={3} />
    ) : topThree.length > 0 ? (
      <ul className="space-y-3">
        {topThree.map((student, index) => (
          <motion.li
            key={index}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 + index * 0.1 }}
            className="flex items-center p-2 rounded-lg hover:bg-amber-200/50 transition-colors">
            <div className="flex items-center w-12">
              <span
                className={`text-lg font-bold ${index === 0 ? 'text-amber-500' : index === 1 ? 'text-orange-600' : 'text-amber-700'}`}>
                {index + 1}
              </span>
              {index === 0 && (
                <Crown
                  className="ml-1.5 text-amber-500 drop-shadow-[0_0_8px_rgba(251,191,36,0.5)]"
                  size={20}
                />
              )}
            </div>
            <div className="w-9 h-9 rounded-full mr-3 flex-shrink-0 flex items-center justify-center font-bold text-sm bg-gradient-to-br from-amber-500 to-orange-500 text-white">
              {student.first_name.charAt(0).toUpperCase()}
            </div>
            <p className="flex-grow font-medium text-amber-900 capitalize text-sm">
              {student.first_name.toLowerCase()}
            </p>
            <span className="text-sm font-semibold text-amber-800 bg-amber-200 px-3 py-1 rounded-full">
              {student.total} pts
            </span>
          </motion.li>
        ))}
      </ul>
    ) : (
      <div className="text-center py-8 text-amber-600">No graded students to display.</div>
    )}
  </motion.div>
);

const StudentRow = ({ student, navigate }) => {
  const courseColors = {
    JEE: 'bg-sky-500/10 text-sky-700 border-sky-500/20',
    NEET: 'bg-emerald-500/10 text-emerald-700 border-emerald-500/20',
    default: 'bg-amber-500/10 text-amber-700 border-amber-500/20'
  };
  const genderColors = {
    MALE: 'text-sky-600',
    FEMALE: 'text-rose-600',
    default: 'text-amber-600'
  };

  return (
    <motion.tr
      variants={{ hidden: { opacity: 0, x: -20 }, visible: { opacity: 1, x: 0 } }}
      exit={{ opacity: 0, x: -20 }}
      layout
      className="hover:bg-amber-200/50 transition-colors">
      <td className="px-6 py-3 whitespace-nowrap">
        <div className="flex items-center">
          <div className="w-9 h-9 rounded-full mr-3 flex-shrink-0 flex items-center justify-center font-bold text-sm bg-gradient-to-br from-amber-500 to-orange-500 text-white">
            {student.name.charAt(0).toUpperCase()}
          </div>
          <div className="text-sm font-medium text-amber-900 capitalize">
            {student.name.toLowerCase()}
          </div>
        </div>
      </td>
      <td className="px-6 py-3 whitespace-nowrap">
        <span
          className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full border ${courseColors[student.course] || courseColors.default}`}>
          {student.course}
        </span>
      </td>
      <td className="px-6 py-3 whitespace-nowrap">
        <span
          className={`text-sm font-medium ${genderColors[student.gender] || genderColors.default}`}>
          {student.gender || 'N/A'}
        </span>
      </td>
      <td className="px-6 py-3 whitespace-nowrap">
        {student.grade ? (
          <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-emerald-500/10 text-emerald-600 border border-emerald-500/20">
            {student.grade}
          </span>
        ) : (
          <span className="text-sm text-amber-600">N/A</span>
        )}
      </td>
      <td className="px-6 py-3 whitespace-nowrap">
        {student.batchname ? (
          <span className="text-sm font-mono text-amber-700">{student.batchname}</span>
        ) : (
          <span className="text-sm text-amber-600">N/A</span>
        )}
      </td>
      <td className="px-6 py-3 whitespace-nowrap text-center">
        <button
          onClick={() =>
            navigate(`/sasthra/center-counselor/analytical-details/${student.id}`, {
              state: student
            })
          }
          className="p-2 text-orange-600 hover:bg-orange-500/10 rounded-full transition-colors"
          title="View Details">
          <FileChartPie size={20} />
        </button>
      </td>
    </motion.tr>
  );
};

const FilterInput = ({ icon, ...props }) => (
  <div className="relative">
    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-amber-600">
      {icon}
    </div>
    <input
      type="text"
      {...props}
      className="bg-white/70 border border-amber-400/50 text-amber-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block w-full pl-10 p-2.5 placeholder:text-amber-600"
    />
  </div>
);

const FilterSelect = ({ options, ...props }) => (
  <div className="relative">
    <select
      {...props}
      className="appearance-none w-full bg-white/70 border border-amber-400/50 text-amber-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block p-2.5 pr-8">
      {options.map((o) => (
        <option key={o} value={o}>
          {o}
        </option>
      ))}
    </select>
    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-amber-600">
      <ChevronDown size={16} />
    </div>
  </div>
);

const PaginationButton = ({ children, ...props }) => (
  <button
    {...props}
    className="px-3 py-1.5 text-sm font-medium text-amber-800 bg-amber-50 border border-amber-300 rounded-lg hover:bg-amber-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
    {children}
  </button>
);

export default AnalyticalDashboard;
