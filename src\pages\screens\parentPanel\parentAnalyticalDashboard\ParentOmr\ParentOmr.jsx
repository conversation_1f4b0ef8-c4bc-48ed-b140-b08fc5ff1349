import React, { useCallback, useEffect, useState, useMemo, memo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import clsx from 'clsx';

// --- Live Data Integration ---
import {
  clearStudentOmrData,
  setStudentOmrData,
  useLazyGetStudentOmrDataQuery
} from '../parentAnalyticsDashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';

// Import Highcharts modules
import 'highcharts/highcharts-more';
import 'highcharts/modules/solid-gauge';

// Lucide icons
import {
  CheckCircle2,
  XCircle,
  HelpCircle,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  TrendingUp,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Award
} from 'lucide-react';

// --- Reusable UI Components (Optimized with React.memo) ---

const DashboardCard = memo(({ children, className = '', ...props }) => (
  <motion.div
    className={clsx(
      'rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-md shadow-lg',
      className
    )}
    variants={{
      hidden: { opacity: 0, scale: 0.95, y: 20 },
      show: {
        opacity: 1,
        scale: 1,
        y: 0,
        transition: { type: 'spring', stiffness: 100, damping: 15 }
      }
    }}
    {...props}>
    {children}
  </motion.div>
));

const ChartCard = memo(({ title, icon, children, className = '' }) => (
  <DashboardCard className={clsx('flex flex-col', className)}>
    <div className="flex items-center text-slate-300 mb-4">
      {icon}
      <h3 className="text-lg font-semibold ml-2 text-white">{title}</h3>
    </div>
    <div className="flex-grow">{children}</div>
  </DashboardCard>
));

const QuestionListItem = memo(({ item, qNumber }) => {
  let status = { text: 'Unattempted', color: 'text-slate-400', icon: <HelpCircle size={20} /> };
  if (item.student_answer) {
    status = item.is_correct
      ? { text: 'Correct', color: 'text-emerald-400', icon: <CheckCircle2 size={20} /> }
      : { text: 'Incorrect', color: 'text-rose-400', icon: <XCircle size={20} /> };
  }

  return (
    <div className="flex items-center p-3 odd:bg-slate-800/30 rounded-lg">
      <div className="w-12 text-center font-bold text-slate-300">#{qNumber}</div>
      <div className="flex-1 flex justify-center">
        <span
          className={clsx(
            'w-10 h-10 flex items-center justify-center rounded-full text-sm font-semibold',
            item.student_answer === item.correct_answer
              ? 'bg-emerald-500/10 text-emerald-300'
              : 'bg-red-500/10 text-red-300'
          )}>
          {item.student_answer || '–'}
        </span>
      </div>
      <div className="flex-1 flex justify-center">
        <span className="w-10 h-10 flex items-center justify-center rounded-full text-sm font-semibold bg-emerald-500/10 text-emerald-300">
          {item.correct_answer}
        </span>
      </div>
      <div className={`w-32 flex items-center justify-end font-semibold text-sm ${status.color}`}>
        {status.icon}
        <span className="ml-2">{status.text}</span>
      </div>
    </div>
  );
});

const OverallAnalyticsCard = memo(({ analytics }) => {
  if (!analytics) return null;

  const trend = {
    Improving: {
      icon: <ArrowUpRight className="text-green-500" />,
      text: 'Improving',
      color: 'text-green-500'
    },
    Declining: {
      icon: <ArrowDownRight className="text-red-500" />,
      text: 'Declining',
      color: 'text-red-500'
    },
    Stable: { icon: <Minus className="text-slate-400" />, text: 'Stable', color: 'text-slate-400' }
  }[analytics.trend];

  return (
    <DashboardCard className="p-8">
      <h3 className="text-xl font-semibold text-white mb-4">Overall Academic Snapshot</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-indigo-500/10 rounded-full">
            <BarChart className="h-6 w-6 text-indigo-400" />
          </div>
          <div>
            <p className="text-slate-400 text-sm font-medium">Tests Taken</p>
            <p className="text-2xl font-bold text-white">{analytics.totalTests}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-sky-500/10 rounded-full">
            <Percent className="h-6 w-6 text-sky-400" />
          </div>
          <div>
            <p className="text-slate-400 text-sm font-medium">Average Score</p>
            <p className="text-2xl font-bold text-white">{analytics.average.toFixed(1)}%</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-emerald-500/10 rounded-full">
            <Award className="h-6 w-6 text-emerald-400" />
          </div>
          <div>
            <p className="text-slate-400 text-sm font-medium">Best Score</p>
            <p className="text-2xl font-bold text-white">{analytics.best}%</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className={`p-3 rounded-full ${trend.color.replace('text-', 'bg-')}/10`}>
            {trend.icon}
          </div>
          <div>
            <p className="text-slate-400 text-sm font-medium">Performance Trend</p>
            <p className={`text-2xl font-bold ${trend.color}`}>{trend.text}</p>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
});

// --- Main Parent OMR Component ---

const ParentOmr = () => {
  // --- State and Redux Hooks ---
  const [getStudentOmr] = useLazyGetStudentOmrDataQuery();
  const [res, setRes] = useState(null);
  const dispatch = useDispatch();
  const studentOmrData = useSelector((state) => state.parentAnalyticsDashboard.studentOmrData);

  const [isLoading, setIsLoading] = useState(true);
  const [selectedTestId, setSelectedTestId] = useState(null);

  // --- Highcharts Theme for Dark UI ---
  useEffect(() => {
    Highcharts.setOptions({
      chart: { style: { fontFamily: '"Inter", sans-serif' }, backgroundColor: 'transparent' },
      title: { style: { display: 'none' } },
      credits: { enabled: false },
      legend: {
        itemStyle: { color: '#a1a1aa', fontWeight: '500' },
        itemHoverStyle: { color: '#ffffff' }
      },
      xAxis: {
        labels: { style: { color: '#a1a1aa' } },
        lineColor: '#3f3f46',
        tickColor: '#3f3f46'
      },
      yAxis: {
        gridLineColor: '#3f3f46',
        labels: { style: { color: '#a1a1aa' } },
        title: { style: { color: '#a1a1aa' } }
      },
      tooltip: {
        backgroundColor: 'rgba(30, 41, 59, 0.8)',
        borderColor: '#475569',
        style: { color: '#f1f5f9' }
      }
    });
  }, []);

  // --- API Fetching Logic ---
  const handleGetStudentOmrServiceApi = useCallback(async () => {
    setIsLoading(true);
    try {
      const studentId = sessionStorage.getItem('studentIdByParent');
      if (!studentId) throw new Error('Student ID not found.');
      const omr = await getStudentOmr({ studentId }).unwrap();
      dispatch(setStudentOmrData(omr));
    } catch (error) {
      dispatch(clearStudentOmrData());
      setRes(error.data || { message: 'An error occurred while fetching data.' });
    } finally {
      setIsLoading(false);
    }
  }, [getStudentOmr, dispatch]);

  useEffect(() => {
    if (!studentOmrData || studentOmrData.length === 0) handleGetStudentOmrServiceApi();
    else setIsLoading(false);
  }, []);

  useEffect(() => {
    if (studentOmrData && studentOmrData.length > 0 && !selectedTestId) {
      setSelectedTestId(studentOmrData[0]._id);
    }
  }, [studentOmrData, selectedTestId]);

  // --- Data Processing (Memoized with Overall Analytics and Dynamic Charts) ---
  const processedData = useMemo(() => {
    if (!studentOmrData || studentOmrData.length === 0) return null;

    const sortedTests = [...studentOmrData].sort(
      (a, b) => new Date(a.evaluated_at) - new Date(b.evaluated_at)
    );

    let overallAnalytics = null;
    if (sortedTests.length > 1) {
      const percentages = sortedTests.map((t) => t.percentage);
      const totalTests = sortedTests.length;
      const average = percentages.reduce((sum, p) => sum + p, 0) / totalTests;
      const best = Math.max(...percentages);
      const latestScore = percentages[totalTests - 1];
      const secondLatestScore = percentages[totalTests - 2];

      let trend = 'Stable';
      if (latestScore > secondLatestScore + 2) trend = 'Improving';
      else if (latestScore < secondLatestScore - 2) trend = 'Declining';

      overallAnalytics = { totalTests, average, best, trend };
    }

    if (!selectedTestId) return { overallAnalytics, sortedTests };

    const currentTest = studentOmrData.find((test) => test._id === selectedTestId);
    if (!currentTest) return { overallAnalytics, sortedTests };

    const { details, total_questions, percentage } = currentTest;
    const correctCount = details.filter((q) => q.is_correct).length;
    const incorrectCount = details.filter((q) => q.student_answer && !q.is_correct).length;
    const unattemptedCount = total_questions - correctCount - incorrectCount;

    const scoreGaugeOptions = {
      chart: { type: 'solidgauge' },
      pane: {
        center: ['50%', '70%'],
        size: '100%',
        startAngle: -90,
        endAngle: 90,
        background: {
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          innerRadius: '60%',
          outerRadius: '100%',
          shape: 'arc',
          borderWidth: 0
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        tickAmount: 2,
        labels: { y: 16 },
        title: { text: null },
        lineWidth: 0,
        minorTickInterval: null
      },
      plotOptions: {
        solidgauge: {
          dataLabels: {
            y: -25,
            borderWidth: 0,
            useHTML: true,
            format:
              '<div style="text-align:center"><span style="font-size:2rem; font-weight:bold; color: #fff;">{y}%</span></div>'
          },
          cornerRadius: '50%'
        }
      },
      series: [
        {
          name: 'Score',
          data: [{ radius: '100%', innerRadius: '60%', y: percentage, color: '#6366f1' }]
        }
      ]
    };
    const answerBreakdownOptions = {
      chart: { type: 'pie' },
      colors: ['#22c55e', '#ef4444', '#9ca3af'],
      plotOptions: {
        pie: {
          innerSize: '60%',
          dataLabels: { enabled: false },
          showInLegend: true,
          borderWidth: 0
        }
      },
      series: [
        {
          name: 'Answers',
          data: [
            { name: 'Correct', y: correctCount },
            { name: 'Incorrect', y: incorrectCount },
            { name: 'Unattempted', y: unattemptedCount }
          ]
        }
      ]
    };
    const trendData = sortedTests.map((test) => ({
      x: new Date(test.evaluated_at).getTime(),
      y: test.percentage
    }));
    const trendColor =
      overallAnalytics?.trend === 'Improving'
        ? '#22c55e'
        : overallAnalytics?.trend === 'Declining'
          ? '#ef4444'
          : '#6366f1';

    // Highlight the last point in the trend data
    const highlightedTrendData = trendData.map((point, index) => {
      if (index === trendData.length - 1) {
        return {
          ...point,
          marker: { radius: 6, fillColor: '#ffffff', lineWidth: 2, lineColor: trendColor }
        };
      }
      return point;
    });

    const scoreTrendOptions = {
      chart: { type: 'areaspline' },
      xAxis: {
        type: 'datetime',
        labels: { format: '{value:%b %d}', style: { color: '#a1a1aa' } },
        gridLineWidth: 0,
        lineWidth: 0,
        tickWidth: 0
      },
      yAxis: {
        title: { text: null },
        labels: { format: '{value}%', style: { color: '#a1a1aa' } },
        min: 0,
        max: 100,
        gridLineColor: '#3f3f46',
        plotBands: [
          {
            from: overallAnalytics?.average || 0,
            to: overallAnalytics?.average || 0,
            color: '#a1a1aa',
            width: 1,
            dashStyle: 'dash',
            label: {
              text: `Avg: ${overallAnalytics?.average.toFixed(1)}%`,
              align: 'right',
              x: -5,
              style: { color: '#a1a1aa' }
            }
          }
        ]
      },
      series: [
        {
          name: 'Score',
          data: highlightedTrendData,
          color: trendColor,
          fillColor: {
            linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
            stops: [
              [0, Highcharts.color(trendColor).setOpacity(0.3).get('rgba')],
              [1, Highcharts.color(trendColor).setOpacity(0).get('rgba')]
            ]
          },
          marker: { radius: 3 },
          lineWidth: 2
        }
      ],
      legend: { enabled: false },
      tooltip: { xDateFormat: '%B %d, %Y', pointFormat: 'Score: <b>{point.y}%</b>' }
    };
    const detailedQuestions = details.map((item, index) => ({ ...item, qNumber: index + 1 }));

    return {
      currentTest,
      detailedQuestions,
      scoreGaugeOptions,
      answerBreakdownOptions,
      scoreTrendOptions,
      overallAnalytics
    };
  }, [studentOmrData, selectedTestId]);

  // --- Render Logic ---
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-xl font-semibold text-slate-400 animate-pulse">Loading Report...</p>
      </div>
    );
  }
  if (!studentOmrData || studentOmrData.length === 0) {
    return (
      <div className="flex h-screen items-center justify-center text-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-100">No OMR Reports Found</h2>
          <p className="text-slate-400 mt-2">
            Could not find any OMR test results for this student.
          </p>
          <button
            onClick={handleGetStudentOmrServiceApi}
            className="mt-6 bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-indigo-700">
            Refresh Data
          </button>
          <Toastify res={res} resClear={() => setRes(null)} />
        </div>
      </div>
    );
  }
  if (!processedData?.currentTest) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-xl font-semibold text-slate-400">Initializing...</p>
      </div>
    );
  }

  const {
    currentTest,
    detailedQuestions,
    scoreGaugeOptions,
    answerBreakdownOptions,
    scoreTrendOptions,
    overallAnalytics
  } = processedData;

  const containerVariants = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.08 } }
  };

  return (
    <div className=" min-h-screen p-4 sm:p-6 lg:p-8 font-sans text-slate-300 relative isolate">
      <div
        className="absolute inset-x-0 -z-10 transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true">
        <div
          className="relative left-1/2 -z-10 aspect-[1155/678] w-[36rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-20 sm:left-[calc(50%-40rem)] sm:w-[72rem]"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        <Toastify res={res} resClear={() => setRes(null)} />

        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">
              OMR Analysis for <span className="text-indigo-400">{currentTest.student_name}</span>
            </h1>
            <p className="text-slate-400 mt-1">
              Review your child's OMR test performance and progress over time.
            </p>
          </div>
          <div className="flex items-center space-x-4 mt-4 sm:mt-0">
            <select
              value={selectedTestId}
              onChange={(e) => setSelectedTestId(e.target.value)}
              className="bg-slate-800/50 backdrop-blur-sm border border-white/10 rounded-lg px-4 py-2 font-semibold shadow-sm focus:ring-2 focus:ring-indigo-500 focus:outline-none">
              {studentOmrData.map((test) => (
                <option key={test._id} value={test._id}>
                  {new Date(test.evaluated_at).toLocaleDateString()} - {test.subject}
                </option>
              ))}
            </select>
          </div>
        </header>

        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="show">
          {overallAnalytics && <OverallAnalyticsCard analytics={overallAnalytics} />}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <ChartCard
              title="Score Trend"
              icon={<TrendingUp className="w-6 h-6 text-indigo-400" />}
              className="lg:col-span-2">
              <div className="">
                <HighchartsReact highcharts={Highcharts} options={scoreTrendOptions} />
              </div>
            </ChartCard>
            <ChartCard
              title="Current Test Breakdown"
              icon={<PieChart className="w-6 h-6 text-indigo-400" />}>
              <div className="">
                <HighchartsReact highcharts={Highcharts} options={answerBreakdownOptions} />
              </div>
            </ChartCard>
          </div>

          <DashboardCard className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4">
              Detailed Answer Key for Selected Test
            </h3>
            <div className="space-y-1">
              <div className="flex items-center p-3 font-bold text-slate-400 text-sm">
                <div className="w-12 text-center">Q#</div>
                <div className="flex-1 text-center">Student's Answer</div>
                <div className="flex-1 text-center">Correct Answer</div>
                <div className="w-32 text-right">Result</div>
              </div>
              <div className="max-h-[400px] overflow-y-auto pr-2">
                {detailedQuestions.map((item) => (
                  <QuestionListItem key={item.qNumber} item={item} qNumber={item.qNumber} />
                ))}
              </div>
            </div>
          </DashboardCard>
        </motion.div>
      </div>
    </div>
  );
};

export default ParentOmr;
