import React, { memo, useState } from 'react';
import { motion } from 'framer-motion';
import Button from '../../../../components/Field/Button';
import Input from '../../../../components/Field/Input';

const ContentCard = memo(
  ({ content, isHOD, selectedTab, handleApprove, handleReject, isApproving }) => {
    const [localRejectionReason, setLocalRejectionReason] = useState('');
    const [showRejectionInput, setShowRejectionInput] = useState(false);

    const handleRejectClick = () => {
      if (showRejectionInput && localRejectionReason.trim()) {
        handleReject(content.id, localRejectionReason);
        setShowRejectionInput(false);
        setLocalRejectionReason(''); // Clear the local rejection reason after sending
      } else {
        setShowRejectionInput(true);
      }
    };

    const getFileTypeIcon = () => {
      const iconBase = (
        <motion.div
          initial={{ scale: 0.8, y: 10, opacity: 0 }}
          animate={{ scale: 1, y: 0, opacity: 1 }}
          whileHover={{ scale: 1.1 }}
          transition={{ type: 'spring', stiffness: 300 }}
          className="p-3 rounded-xl shadow-lg flex items-center justify-center">
          <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
              clipRule="evenodd"
            />
          </svg>
        </motion.div>
      );

      switch (content.file_type) {
        case 'pdf':
          return (
            <div className="bg-gradient-to-br from-red-400 to-pink-500 rounded-xl">{iconBase}</div>
          );
        case 'video':
          return (
            <div className="bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl">
              {iconBase}
            </div>
          );
        default:
          return (
            <div className="bg-gradient-to-br from-gray-400 to-gray-600 rounded-xl">{iconBase}</div>
          );
      }
    };

    const getStatusChip = () => {
      const baseStyles = 'px-3 py-1 rounded-full text-sm font-semibold shadow-sm';

      switch (content.status) {
        case 'approved':
          return (
            <motion.span
              className={`${baseStyles} bg-green-50 text-green-700 border border-green-200`}
              whileHover={{ scale: 1.05 }}>
              Approved
            </motion.span>
          );
        case 'pending':
          return (
            <motion.span
              className={`${baseStyles} bg-yellow-50 text-yellow-700 border border-yellow-200`}
              whileHover={{ scale: 1.05 }}>
              Pending Review
            </motion.span>
          );
        case 'rejected':
          return (
            <motion.span
              className={`${baseStyles} bg-red-50 text-red-700 border border-red-200`}
              whileHover={{ scale: 1.05 }}>
              Rejected
            </motion.span>
          );
        default:
          return (
            <span className={`${baseStyles} bg-gray-50 text-gray-700 border border-gray-200`}>
              Unknown
            </span>
          );
      }
    };

    return (
      <motion.div
        key={content.id}
        className="relative bg-white border border-gray-100 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95 }}
        whileHover={{ y: -2 }}
        layout>
        {/* Card Header */}
        <div className="flex items-start gap-4 p-5">
          <div className="flex-shrink-0">{getFileTypeIcon()}</div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between gap-2">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {content.original_filename}
              </h3>
              <div className="flex-shrink-0">{getStatusChip()}</div>
            </div>

            <div className="mt-2 flex flex-wrap gap-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {content.file_type.toUpperCase()}
              </span>
              {content.file_size && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {content.file_size}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Card Body */}
        <div className="px-5 pb-5 space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <div className="bg-gray-50 p-3 rounded-lg">
              <span className="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                Course
              </span>
              <div className="font-medium text-gray-900">{content.course || 'Not specified'}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <span className="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                Subject
              </span>
              <div className="font-medium text-gray-900">{content.subject || 'Not specified'}</div>
            </div>
            {content.unit && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <span className="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                  Unit
                </span>
                <div className="font-medium text-gray-900">{content.unit}</div>
              </div>
            )}
            {content.sub_unit && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <span className="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                  Sub-unit
                </span>
                <div className="font-medium text-gray-900">{content.sub_unit}</div>
              </div>
            )}
          </div>

          {content.description && (
            <div className="bg-gray-50 rounded-lg p-4">
              <span className="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                Description
              </span>
              <p className="text-gray-700 mt-1 text-sm">{content.description}</p>
            </div>
          )}

          {content.status === 'rejected' && content.rejection_reason && (
            <motion.div
              className="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}>
              <div className="flex">
                <svg
                  className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5"
                  viewBox="0 0 20 20"
                  fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Rejection Reason</h3>
                  <div className="mt-1 text-sm text-red-700">{content.rejection_reason}</div>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Card Footer */}
        <div className="px-5 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl space-y-3">
          {/* View Content Button - Primary Action */}
          <Button
            name="View Content"
            link={content.cloudfront_url}
            target="_blank"
            rel="noopener noreferrer"
            className="w-full justify-center bg-indigo-600 hover:bg-indigo-700 text-white py-2.5 px-4 rounded-lg font-medium text-sm shadow-sm hover:shadow-md transition-all duration-150 flex items-center"
            icon={
              <svg
                className="w-4 h-4 mr-2 -ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            }
          />

          {/* Approval section - only visible for HOD on pending tab */}
          {selectedTab === 'pending' && isHOD && (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                {/* Approve Button - Success Variant */}
                <Button
                  name="Approve"
                  onClick={() => handleApprove(content.id)}
                  disabled={isApproving}
                  className={`w-full justify-center py-2.5 px-4 rounded-lg font-medium text-sm shadow-sm hover:shadow-md transition-all duration-150 flex items-center
                    ${isApproving ? 'bg-green-400' : 'bg-green-600 hover:bg-green-700'} text-white`}
                  icon={
                    <svg
                      className="w-4 h-4 mr-2 -ml-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  }
                />

                {/* Reject Button - Danger Variant */}
                <Button
                  name={showRejectionInput ? 'Confirm Reject' : 'Reject'}
                  onClick={handleRejectClick}
                  disabled={isApproving || (showRejectionInput && !localRejectionReason.trim())}
                  className={`w-full justify-center py-2.5 px-4 rounded-lg font-medium text-sm shadow-sm hover:shadow-md transition-all duration-150 flex items-center
                    ${isApproving ? 'bg-red-400' : 'bg-red-600 hover:bg-red-700'} text-white`}
                  icon={
                    <svg
                      className="w-4 h-4 mr-2 -ml-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  }
                />
              </div>

              {/* Rejection Reason Input - Only shown when reject button is clicked */}
              {showRejectionInput && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                  className="mt-1">
                  <label htmlFor={`rejection-reason-${content.id}`} className="sr-only">
                    Rejection Reason
                  </label>
                  <Input
                    id={`rejection-reason-${content.id}`}
                    type="text"
                    name="rejectionReason"
                    value={localRejectionReason}
                    onChange={(e) => setLocalRejectionReason(e.target.value)}
                    placeholder="Reason for rejection..."
                    className="block w-full px-3 py-2 text-sm border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </motion.div>
              )}
            </div>
          )}
        </div>
      </motion.div>
    );
  }
);

export default ContentCard;
