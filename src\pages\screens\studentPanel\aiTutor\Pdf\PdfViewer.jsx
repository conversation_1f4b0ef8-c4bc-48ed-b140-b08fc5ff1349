// import React, { useState, useEffect, useRef } from 'react';
// import { Document, Page } from 'react-pdf';
// import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
// import 'react-pdf/dist/esm/Page/TextLayer.css';
// import { initializeWorker } from './WorkerSetup';
// import SlideContent from './SlideContent';
// import Controls from './Controls';
// import ErrorBoundary from './ErrorBoundary';

// function PdfViewer({ fileUrl, fileName, subject, processSelectorId }) {
//   const [file, setFile] = useState(null);
//   const [numPages, setNumPages] = useState(null);
//   const [pageNumber, setPageNumber] = useState(1);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState(null);
//   const [slideData, setSlideData] = useState([]);
//   const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
//   const [workerReady, setWorkerReady] = useState(false);
//   const [isFullscreen, setIsFullscreen] = useState(false);
//   const [isTransitioning, setIsTransitioning] = useState(false);
//   const [phonemeData, setPhonemeData] = useState('');
//   const pageRef = useRef(null);

//   // Initialize SlideContent hook
//   const slideContentHook = SlideContent({ 
//     processSelectorId, 
//     onSlideDataChange: setSlideData,
//     onCurrentSlideIndexChange: setCurrentSlideIndex
//   });

//   // Initialize PDF worker
//   useEffect(() => {
//     const init = async () => {
//       try {
//         await initializeWorker();
//         setWorkerReady(true);
//         console.log('Worker ready state set to true.');
//       } catch (err) {
//         console.error('Failed to initialize PDF worker:', err);
//         setError('Failed to initialize PDF viewer');
//       }
//     };
//     init();
//   }, []);

//   // Fullscreen change handler
//   useEffect(() => {
//     const handleFullscreenChange = () => {
//       setIsFullscreen(!!document.fullscreenElement);
//       console.log(`Fullscreen state changed: ${!!document.fullscreenElement}`);
//     };
//     document.addEventListener('fullscreenchange', handleFullscreenChange);
//     return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
//   }, []);

//   // Load PDF File
//   useEffect(() => {
//     if (fileUrl && fileName && workerReady) {
//       console.log(`Loading PDF from URL: ${fileUrl}`);
//       fetchPDF(fileUrl);
//     }
//   }, [fileUrl, fileName, workerReady]);

//   // Synchronize slide index with page number
//   useEffect(() => {
//     if (
//       slideData.length > 0 &&
//       pageNumber !== currentSlideIndex + 1 &&
//       pageNumber <= slideData.length
//     ) {
//       console.log(`Synchronizing slide index to page ${pageNumber}`);
//       setCurrentSlideIndex(pageNumber - 1);
//       console.log(`Updated slide index to ${pageNumber - 1} to match page ${pageNumber}`);
//     }
//   }, [slideData, pageNumber]);

//   // Helper function to get current slide content
//   const getCurrentSlideContent = () => {
//     const slideIndex = pageNumber - 1;
//     return slideData[slideIndex] || '';
//   };

//   const fetchPDF = async (url) => {
//     setLoading(true);
//     setError(null);
//     console.log(`Fetching PDF from: ${url}`);
//     const controller = new AbortController();
//     try {
//       const res = await fetch(url, {
//         signal: controller.signal,
//         headers: { Accept: 'application/pdf' }
//       });
//       if (!res.ok) throw new Error(`PDF fetch failed: ${res.status} ${res.statusText}`);
//       const contentType = res.headers.get('content-type');
//       if (contentType && !contentType.includes('application/pdf')) {
//         console.warn('⚠️ Response may not be a PDF file:', contentType);
//       }
//       const blob = await res.blob();
//       const objectUrl = URL.createObjectURL(blob);
//       setFile(objectUrl);
//       console.log(`PDF loaded successfully, object URL: ${objectUrl}`);
//       return () => {
//         if (objectUrl) {
//           console.log(`Revoking object URL: ${objectUrl}`);
//           URL.revokeObjectURL(objectUrl);
//         }
//         controller.abort();
//       };
//     } catch (err) {
//       if (err.name !== 'AbortError') {
//         console.error('❌ Error loading PDF:', err.message);
//         setError(`Failed to load PDF: ${err.message}`);
//       }
//     } finally {
//       setLoading(false);
//       console.log('PDF fetch complete, loading state set to false.');
//     }
//   };

//   const goToPrevPage = () => {
//     if (pageNumber > 1) {
//       const newPageNumber = pageNumber - 1;
//       setPageNumber(newPageNumber);
//       setIsTransitioning(true);
//       setTimeout(() => {
//         setCurrentSlideIndex(newPageNumber - 1);
//         setIsTransitioning(false);
//         console.log(`Navigated to previous page: ${newPageNumber}`);
//       }, 2000);
//     }
//   };

//   const goToNextPage = () => {
//     if (pageNumber < numPages) {
//       const newPageNumber = pageNumber + 1;
//       setPageNumber(newPageNumber);
//       setIsTransitioning(true);
//       setTimeout(() => {
//         setCurrentSlideIndex(newPageNumber - 1);
//         setIsTransitioning(false);
//         console.log(`Navigated to next page: ${newPageNumber}`);
//       }, 2000);
//     }
//   };

//   const handleDocumentLoadSuccess = ({ numPages }) => {
//     console.log(`✅ PDF loaded successfully with ${numPages} pages`);
//     setNumPages(numPages);
//     setError(null);
//   };

//   const handleDocumentLoadError = (error) => {
//     console.error('❌ Document load error:', error);
//     setError(`Error loading PDF: ${error.message || 'Unknown error'}`);
//   };

//   const handlePageLoadError = (error) => {
//     console.error('❌ Page load error:', error);
//     setError(`Error loading page: ${error.message || 'Unknown error'}`);
//   };

//   const retryLoadPdf = () => {
//     setError(null);
//     console.log('Retrying PDF load.');
//     if (fileUrl) {
//       fetchPDF(fileUrl);
//     }
//   };

//   const toggleFullscreen = async () => {
//     const container = document.getElementById('pdf-container');
//     if (!document.fullscreenElement && container) {
//       try {
//         await container.requestFullscreen();
//         console.log('Entered fullscreen mode.');
//       } catch (err) {
//         console.error(`Error attempting to enable fullscreen: ${err.message}`);
//       }
//     } else if (document.fullscreenElement) {
//       try {
//         await document.exitFullscreen();
//         console.log('Exited fullscreen mode.');
//       } catch (err) {
//         console.error(`Error attempting to exit fullscreen: ${err.message}`);
//       }
//     }
//   };

//   if (!workerReady) {
//     return (
//       <div className="bg-gray-900 min-h-screen text-white p-6 flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
//           <p className="text-gray-300">Initializing PDF viewer...</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <ErrorBoundary>
//       <div
//         className="bg-gray-50 min-h-screen text-gray-900 border border-gray-200"
//         id="pdf-container"
//       >
//         <Controls
//           subject={subject}
//           pageNumber={pageNumber}
//           numPages={numPages}
//           currentSlideIndex={currentSlideIndex}
//           slideData={slideData}
//           isTransitioning={isTransitioning}
//           isFullscreen={isFullscreen}
//           getCurrentSlideContent={getCurrentSlideContent}
//           goToPrevPage={goToPrevPage}
//           goToNextPage={goToNextPage}
//           toggleFullscreen={toggleFullscreen}
//           setPageNumber={setPageNumber}
//           setCurrentSlideIndex={setCurrentSlideIndex}
//           setIsTransitioning={setIsTransitioning}
//           setError={setError}
//           setPhonemeData={setPhonemeData}
//         />
        
//         <div className="flex h-[calc(100vh-80px)]">
//           <div className="flex-1 p-6 overflow-auto">
//             {error && (
//               <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
//                 <p className="text-red-700 mb-2">{error}</p>
//                 <button
//                   onClick={retryLoadPdf}
//                   className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors text-white"
//                 >
//                   Retry Loading PDF
//                 </button>
//               </div>
//             )}
//             {loading && (
//               <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
//                 <div className="flex items-center">
//                   <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
//                   <p className="text-blue-700">Loading PDF...</p>
//                 </div>
//               </div>
//             )}
//             {file && !error && (
//               <div
//                 className="bg-white p-4 rounded-xl shadow-lg border border-gray-200"
//                 ref={pageRef}
//               >
//                 <Document
//                   file={file}
//                   onLoadSuccess={handleDocumentLoadSuccess}
//                   onLoadError={handleDocumentLoadError}
//                   loading={
//                     <div className="flex items-center justify-center py-8">
//                       <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
//                       <span className="text-gray-600">Loading document...</span>
//                     </div>
//                   }
//                   error={
//                     <div className="text-center py-8 text-red-600">
//                       <p>Failed to load PDF document.</p>
//                       <button
//                         onClick={retryLoadPdf}
//                         className="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm text-white"
//                       >
//                         Retry
//                       </button>
//                     </div>
//                   }
//                 >
//                   <div className="relative">
//                     <Page
//                       key={`page_${pageNumber}`}
//                       pageNumber={pageNumber}
//                       width={Math.min(800, window.innerWidth * 0.6 - 100)}
//                       className="shadow-lg mx-auto"
//                       onLoadError={handlePageLoadError}
//                       loading={
//                         <div className="flex items-center justify-center py-8">
//                           <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
//                           <span className="text-gray-600">Loading page...</span>
//                         </div>
//                       }
//                       error={
//                         <div className="text-center py-8 text-red-600">
//                           <p>Failed to load page {pageNumber}.</p>
//                         </div>
//                       }
//                     />
//                     {/* AvatarWorld Overlay */}
//                     {/* <div className="absolute bottom-0 -right-10 z-10">
//                       <AvatarsWorld rawPhonemeString={phonemeData} isPlay={isTransitioning} />
//                     </div> */}
//                   </div>
//                 </Document>

//               </div>
//             )}
//             {!file && !loading && !error && (
//               <div className="text-center py-12 text-gray-500">
//                 <p>No PDF file loaded. Please provide a valid file URL.</p>
//               </div>
//             )}
//           </div>
//         </div>
//       </div>
//     </ErrorBoundary>
//   );
// }

// export default PdfViewer;



import React, { useState, useEffect, useRef } from 'react';
import { Document, Page } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { initializeWorker } from './WorkerSetup';
import SlideContent from './SlideContent';
import Controls from './Controls';
import ErrorBoundary from './ErrorBoundary';

function PdfViewer({ fileUrl, fileName, subject, processSelectorId }) {
  const [file, setFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [slideData, setSlideData] = useState([]);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [workerReady, setWorkerReady] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [phonemeData, setPhonemeData] = useState('');
  const [logs, setLogs] = useState([]);
  const pageRef = useRef(null);

  // New states for the popup
  const [showContinuePopup, setShowContinuePopup] = useState(false);
  const [lastSlideNumber, setLastSlideNumber] = useState(null);

  // Initialize SlideContent hook
  const slideContentHook = SlideContent({ 
    processSelectorId, 
    onSlideDataChange: setSlideData,
    onCurrentSlideIndexChange: setCurrentSlideIndex
  });

  // Initialize PDF worker
  useEffect(() => {
    const init = async () => {
      try {
        await initializeWorker();
        setWorkerReady(true);
        console.log('Worker readystate set to true.');
      } catch (err) {
        console.error('Failed to initialize PDF worker:', err);
        setError('Failed to initialize PDF viewer');
      }
    };
    init();
  }, []);

  // Fullscreen change handler
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
      console.log(`Fullscreen state changed: ${!!document.fullscreenElement}`);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Load PDF File
  useEffect(() => {
    if (fileUrl && fileName && workerReady) {
      console.log(`Loading PDF from URL: ${fileUrl}`);
      fetchPDF(fileUrl);
    }
  }, [fileUrl, fileName, workerReady]);

  // Synchronize slide index with page number
  useEffect(() => {
    if (
      slideData.length > 0 &&
      pageNumber !== currentSlideIndex + 1 &&
      pageNumber <= slideData.length
    ) {
      console.log(`Synchronizing slide index to page ${pageNumber}`);
      setCurrentSlideIndex(pageNumber - 1);
      console.log(`Updated slide index to ${pageNumber - 1} to match page ${pageNumber}`);
    }
  }, [slideData, pageNumber]);

  // Fetch last log from endpoint and show popup if matching process_selector_id
  useEffect(() => {
    if (processSelectorId && workerReady) {
      console.log('Attempting to fetch last log for processSelectorId:', processSelectorId);
      const controller = new AbortController();
      fetch('https://testing.sasthra.in/api/tutor-memory/latest', {
        signal: controller.signal,
        headers: { 'Content-Type': 'application/json' }
      })
        .then((res) => {
          if (!res.ok) {
            throw new Error(`Request failed with status ${res.status}`);
          }
          return res.json();
        })
        .then((data) => {
          console.log('Fetched last log data:', data);
          if (data.last_log && data.last_log.process_selector_id === processSelectorId) {
            console.log(`Matching process_selector_id found, last slide: ${data.last_log.current_slide_number}`);
            setLastSlideNumber(data.last_log.current_slide_number);
            setShowContinuePopup(true);
          } else {
            console.log('No matching last log or different process_selector_id.');
          }
        })
        .catch((err) => {
          if (err.name !== 'AbortError') {
            console.error('Error fetching last log:', err.message);
          }
        });
      return () => {
        console.log('Aborting last log fetch request.');
        controller.abort();
      };
    } else {
      console.log('Not fetching last log: processSelectorId or workerReady not ready.', {
        processSelectorId,
        workerReady
      });
    }
  }, [processSelectorId, workerReady]);

  useEffect(() => {
    return () => {
      if (logs.length > 0) {
        const payload = {
          logs,
          timestamp: new Date().toISOString()
        };

        console.log('Saving logs to MongoDB:', payload);

        fetch('https://testing.sasthra.in/api/tutor-memory', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error('Failed to save logs to MongoDB');
            }
            console.log('Logs successfully saved to MongoDB');
          })
          .catch((err) => {
            console.error('Error saving logs to MongoDB:', err);
          });
      }
    };
  }, [logs]);

  // Helper function to get current slide content
  const getCurrentSlideContent = () => {
    const slideIndex = pageNumber - 1;
    return slideData[slideIndex] || '';
  };

  const fetchPDF = async (url) => {
    setLoading(true);
    setError(null);
    console.log(`Fetching PDF from: ${url}`);
    const controller = new AbortController();
    try {
      const res = await fetch(url, {
        signal: controller.signal,
        headers: { Accept: 'application/pdf' }
      });
      if (!res.ok) throw new Error(`PDF fetch failed: ${res.status} ${res.statusText}`);
      const contentType = res.headers.get('content-type');
      if (contentType && !contentType.includes('application/pdf')) {
        console.warn('⚠️ Response may not be a PDF file:', contentType);
      }
      const blob = await res.blob();
      const objectUrl = URL.createObjectURL(blob);
      setFile(objectUrl);
      console.log(`PDF loaded successfully, object URL: ${objectUrl}`);
      return () => {
        if (objectUrl) {
          console.log(`Revoking object URL: ${objectUrl}`);
          URL.revokeObjectURL(objectUrl);
        }
        controller.abort();
      };
    } catch (err) {
      if (err.name !== 'AbortError') {
        console.error('❌ Error loading PDF:', err.message);
        setError(`Failed to load PDF: ${err.message}`);
      }
    } finally {
      setLoading(false);
      console.log('PDF fetch complete, loading state set to false.');
    }
  };

  const goToPrevPage = () => {
    if (pageNumber > 1) {
      const newPageNumber = pageNumber - 1;
      setPageNumber(newPageNumber);
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentSlideIndex(newPageNumber - 1);
        setIsTransitioning(false);
        console.log(`Navigated to previous page: ${newPageNumber}`);
      }, 2000);
    }
  };

  const goToNextPage = () => {
    if (pageNumber < numPages) {
      const newPageNumber = pageNumber + 1;
      setPageNumber(newPageNumber);
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentSlideIndex(newPageNumber - 1);
        setIsTransitioning(false);
        console.log(`Navigated to next page: ${newPageNumber}`);
      }, 2000);
    }
  };

  const handleDocumentLoadSuccess = ({ numPages }) => {
    console.log(`✅ PDF loaded successfully with ${numPages} pages`);
    setNumPages(numPages);
    setError(null);
  };

  const handleDocumentLoadError = (error) => {
    console.error('❌ Document load error:', error);
    setError(`Error loading PDF: ${error.message || 'Unknown error'}`);
  };

  const handlePageLoadError = (error) => {
    console.error('❌ Page load error:', error);
    setError(`Error loading page: ${error.message || 'Unknown error'}`);
  };

  const retryLoadPdf = () => {
    setError(null);
    console.log('Retrying PDF load.');
    if (fileUrl) {
      fetchPDF(fileUrl);
    }
  };

  const toggleFullscreen = async () => {
    const container = document.getElementById('pdf-container');
    if (!document.fullscreenElement && container) {
      try {
        await container.requestFullscreen();
        console.log('Entered fullscreen mode.');
      } catch (err) {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      }
    } else if (document.fullscreenElement) {
      try {
        await document.exitFullscreen();
        console.log('Exited fullscreen mode.');
      } catch (err) {
        console.error(`Error attempting to exit fullscreen: ${err.message}`);
      }
    }
  };

  if (!workerReady) {
    return (
      <div className="bg-gray-900 min-h-screen text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300">Initializing PDF viewer...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div
        className="bg-gray-50 min-h-screen text-gray-900 border border-gray-200"
        id="pdf-container"
      >
        <Controls
          subject={subject}
          pageNumber={pageNumber}
          numPages={numPages}
          currentSlideIndex={currentSlideIndex}
          slideData={slideData}
          isTransitioning={isTransitioning}
          isFullscreen={isFullscreen}
          getCurrentSlideContent={getCurrentSlideContent}
          goToPrevPage={goToPrevPage}
          goToNextPage={goToNextPage}
          toggleFullscreen={toggleFullscreen}
          setPageNumber={setPageNumber}
          setCurrentSlideIndex={setCurrentSlideIndex}
          setIsTransitioning={setIsTransitioning}
          setError={setError}
          setPhonemeData={setPhonemeData}
          setLogs={setLogs}
          processSelectorId={processSelectorId}
        />
        
        <div className="flex h-[calc(100vh-80px)]">
          <div className="flex-1 p-6 overflow-auto">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 mb-2">{error}</p>
                <button
                  onClick={retryLoadPdf}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors text-white"
                >
                  Retry Loading PDF
                </button>
              </div>
            )}
            {loading && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
                  <p className="text-blue-700">Loading PDF...</p>
                </div>
              </div>
            )}
            {file && !error && (
              <div
                className="bg-white p-4 rounded-xl shadow-lg border border-gray-200"
                ref={pageRef}
              >
                <Document
                  file={file}
                  onLoadSuccess={handleDocumentLoadSuccess}
                  onLoadError={handleDocumentLoadError}
                  loading={
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                      <span className="text-gray-600">Loading document...</span>
                    </div>
                  }
                  error={
                    <div className="text-center py-8 text-red-600">
                      <p>Failed to load PDF document.</p>
                      <button
                        onClick={retryLoadPdf}
                        className="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm text-white"
                      >
                        Retry
                      </button>
                    </div>
                  }
                >
                  <div className="relative">
                    <Page
                      key={`page_${pageNumber}`}
                      pageNumber={pageNumber}
                      width={Math.min(800, window.innerWidth * 0.6 - 100)}
                      className="shadow-lg mx-auto"
                      onLoadError={handlePageLoadError}
                      loading={
                        <div className="flex items-center justify-center py-8">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                          <span className="text-gray-600">Loading page...</span>
                        </div>
                      }
                      error={
                        <div className="text-center py-8 text-red-600">
                          <p>Failed to load page {pageNumber}.</p>
                        </div>
                      }
                    />
                    {/* AvatarWorld Overlay */}
                    {/* <div className="absolute bottom-0 -right-10 z-10">
                      <AvatarsWorld rawPhonemeString={phonemeData} isPlay={isTransitioning} />
                    </div> */}
                  </div>
                </Document>
              </div>
            )}
            {!file && !loading && !error && (
              <div className="text-center py-12 text-gray-500">
                <p>No PDF file loaded. Please provide a valid file URL.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Continue Popup Modal */}
{showContinuePopup && (
  <div className="fixed inset-0  bg-opacity-40 flex items-center justify-center z-50">
    <div className="bg-white p-6 rounded-2xl shadow-2xl border-4 border-blue-600 transform transition-all scale-100 hover:scale-105 duration-300">
      <p className="text-xl font-semibold text-gray-800 mb-6">
        Do you want to continue from slide {lastSlideNumber}?
      </p>
      <div className="flex justify-end space-x-4">
        <button
          onClick={() => {
            setPageNumber(lastSlideNumber);
            setCurrentSlideIndex(lastSlideNumber - 1);
            setShowContinuePopup(false);
          }}
          className="px-5 py-2 bg-blue-600 text-white rounded-xl shadow hover:bg-blue-700 hover:shadow-lg transition"
        >
          Yes
        </button>
        <button
          onClick={() => setShowContinuePopup(false)}
          className="px-5 py-2 bg-gray-200 text-gray-800 rounded-xl shadow hover:bg-gray-300 hover:shadow-lg transition"
        >
          No
        </button>
      </div>
    </div>
  </div>
)}

    </ErrorBoundary>
  );
}

export default PdfViewer;
