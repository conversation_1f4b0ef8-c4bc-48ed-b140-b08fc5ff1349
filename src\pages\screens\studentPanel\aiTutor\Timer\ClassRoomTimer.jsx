import React, { useEffect, useState } from "react";

function ClassRoomTimer({ onThreeMinutes }) {
  const STORAGE_KEY = "my_timer_v1";

  const [isRunning, setIsRunning] = useState(false);
  const [startTime, setStartTime] = useState(null); // timestamp in ms
  const [elapsed, setElapsed] = useState(0); // ms
  const [triggered, setTriggered] = useState(false);

  // Load saved timer state on mount and auto-start
  useEffect(() => {
    const raw = localStorage.getItem(STORAGE_KEY);
    if (!raw) {
      // No saved state → start fresh immediately
      setStartTime(Date.now());
      setIsRunning(true);
      return;
    }
    try {
      const saved = JSON.parse(raw);
      setTriggered(!!saved.triggered);

      if (saved.startTime) {
        setIsRunning(true); // auto start
        setStartTime(saved.startTime);
        setElapsed(Math.max(0, Date.now() - saved.startTime));
      } else {
        // If no start time saved, start now
        setStartTime(Date.now());
        setIsRunning(true);
      }
    } catch (e) {
      console.error("Failed to load timer state", e);
      setStartTime(Date.now());
      setIsRunning(true);
    }
  }, []);

  // Handle tab visibility change - pause when hidden, resume when visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        if (isRunning && startTime) {
          setElapsed(Math.max(0, Date.now() - startTime));
          setIsRunning(false);
          setStartTime(null);
        }
      } else {
        if (!isRunning && elapsed > 0) {
          setStartTime(Date.now() - elapsed);
          setIsRunning(true);
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, [isRunning, startTime, elapsed]);

  // Persist timer state
  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEY,
      JSON.stringify({ isRunning, startTime, elapsed, triggered })
    );
  }, [isRunning, startTime, elapsed, triggered]);

  // Update elapsed while running
  useEffect(() => {
    if (!isRunning || !startTime) return;
    const id = setInterval(() => {
      setElapsed(Math.max(0, Date.now() - startTime));
    }, 500);
    return () => clearInterval(id);
  }, [isRunning, startTime]);

  // Trigger popup after 3 minutes and reset timer
  useEffect(() => {
    if (!triggered && elapsed >= 15 * 60 * 1000) {
      setTriggered(true);
      alert("⏳ 15 minutes reached!");
      if (typeof onThreeMinutes === "function") onThreeMinutes(true);
      handleReset(); // auto reset
    }
  }, [elapsed, triggered, onThreeMinutes]);

  const handleReset = () => {
    setIsRunning(true);
    setStartTime(Date.now());
    setElapsed(0);
    setTriggered(false);
    localStorage.removeItem(STORAGE_KEY);
  };

  // Format time
  const totalSeconds = Math.floor(elapsed / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const pad = (n) => String(n).padStart(2, "0");

  // Blinking red style for last 30 seconds
  const isLast30Sec =
    elapsed >= (15 * 60 - 30) * 1000 && elapsed < 15 * 60 * 1000;
  const timerClass = isLast30Sec
    ? "animate-pulse text-red-600"
    : "text-black";

  return (
    <div className="flex flex-col items-center justify-center p-4 space-y-4">
      <div
        className={`text-sm font-mono font-semibold flex items-center space-x-2 ${timerClass}`}
      >
        <span>{pad(hours)}</span>
        <span>:</span>
        <span>{pad(minutes)}</span>
        <span>:</span>
        <span>{pad(seconds)}</span>
      </div>


    </div>
  );
}

export default ClassRoomTimer;
