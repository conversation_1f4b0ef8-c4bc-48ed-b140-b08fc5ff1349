"use client"

// MainFeedback.jsx
import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import ClassroomFeedback from "./AI-Tutor_Feedback/ClassroomFeedback"
import LivestreamFeedback from "./LiveStream_Feedback/LiveStreamFeedback"



// ✅ Loader Component
const Loader = () => {
  return (
    <div className="flex justify-center items-center py-12">
      <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin shadow-lg"></div>
    </div>
  )
}

const MainFeedback = () => {
  const [activeComponent, setActiveComponent] = useState(null)
  const [loading, setLoading] = useState(false)

  const handleSelect = (type) => {
    setLoading(true)
    setActiveComponent(type)
  }

  useEffect(() => {
    if (loading) {
      const timer = setTimeout(() => {
        setLoading(false)
      }, 600) // loader duration
      return () => clearTimeout(timer)
    }
  }, [loading])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-6">
      {!activeComponent ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl w-full">
          {/* LiveStream Feedback Card */}
          <motion.div
            whileHover={{ scale: 1.02, y: -8 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleSelect("live")}
            className="group bg-gradient-to-br from-blue-50 to-indigo-100 shadow-xl hover:shadow-2xl rounded-3xl p-8 cursor-pointer border border-blue-200 hover:border-blue-300 transition-all duration-300 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full -translate-y-16 translate-x-16 opacity-20 group-hover:opacity-30 transition-opacity"></div>
            <div className="relative z-10">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-4 text-gray-800 group-hover:text-blue-700 transition-colors">
                LiveStream Feedback
              </h2>
              <p className="text-gray-600 text-base leading-relaxed">
                View and manage feedback from live classroom sessions with real-time analytics and insights.
              </p>
              <div className="mt-6 flex items-center text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                <span>Get Started</span>
                <svg
                  className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </div>
            </div>
          </motion.div>

          {/* AI-Tutor Feedback Card */}
          <motion.div
            whileHover={{ scale: 1.02, y: -8 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleSelect("ai")}
            className="group bg-gradient-to-br from-purple-50 to-pink-100 shadow-xl hover:shadow-2xl rounded-3xl p-8 cursor-pointer border border-purple-200 hover:border-purple-300 transition-all duration-300 relative overflow-hidden"
          >
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200 to-pink-300 rounded-full -translate-y-16 translate-x-16 opacity-20 group-hover:opacity-30 transition-opacity"></div>
            <div className="relative z-10">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-4 text-gray-800 group-hover:text-purple-700 transition-colors">
                AI-Tutor Feedback
              </h2>
              <p className="text-gray-600 text-base leading-relaxed">
                Explore personalized feedback provided by the AI tutor with intelligent recommendations and progress
                tracking.
              </p>
              <div className="mt-6 flex items-center text-purple-600 font-medium group-hover:text-purple-700 transition-colors">
                <span>Get Started</span>
                <svg
                  className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </div>
            </div>
          </motion.div>
        </div>
      ) : (
        <div className="flex flex-col items-center gap-6 w-full">
          {/* Back Button at Top */}
          <div className="w-full flex justify-start">
            <button
              onClick={() => setActiveComponent(null)}
              className="mb-4 px-8 py-3 rounded-2xl bg-gradient-to-r from-gray-700 to-gray-800 text-white hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl font-medium flex items-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back
            </button>
          </div>

          {/* Loader or Component */}
          {loading ? <Loader /> : activeComponent === "live" ? <LivestreamFeedback     /> : <ClassroomFeedback />}
        </div>
      )}
    </div>
  )
}

export default MainFeedback
